{"version": 3, "sources": ["../../../../../../node_modules/vlq/dist/vlq.es.js", "../../../../../../node_modules/ngx-logger/fesm2020/ngx-logger.mjs"], "sourcesContent": ["var charToInteger = {};\nvar integerToChar = {};\n'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='\n    .split('')\n    .forEach(function (char, i) {\n    charToInteger[char] = i;\n    integerToChar[i] = char;\n});\nfunction decode(string) {\n    var result = [];\n    var shift = 0;\n    var value = 0;\n    for (var i = 0; i < string.length; i += 1) {\n        var integer = charToInteger[string[i]];\n        if (integer === undefined) {\n            throw new Error('Invalid character (' + string[i] + ')');\n        }\n        var hasContinuationBit = integer & 32;\n        integer &= 31;\n        value += integer << shift;\n        if (hasContinuationBit) {\n            shift += 5;\n        }\n        else {\n            var shouldNegate = value & 1;\n            value >>>= 1;\n            if (shouldNegate) {\n                result.push(value === 0 ? -0x80000000 : -value);\n            }\n            else {\n                result.push(value);\n            }\n            // reset\n            value = shift = 0;\n        }\n    }\n    return result;\n}\nfunction encode(value) {\n    var result;\n    if (typeof value === 'number') {\n        result = encodeInteger(value);\n    }\n    else {\n        result = '';\n        for (var i = 0; i < value.length; i += 1) {\n            result += encodeInteger(value[i]);\n        }\n    }\n    return result;\n}\nfunction encodeInteger(num) {\n    var result = '';\n    if (num < 0) {\n        num = (-num << 1) | 1;\n    }\n    else {\n        num <<= 1;\n    }\n    do {\n        var clamped = num & 31;\n        num >>>= 5;\n        if (num > 0) {\n            clamped |= 32;\n        }\n        result += integerToChar[clamped];\n    } while (num > 0);\n    return result;\n}\n\nexport { decode, encode };\n", "import * as i1 from '@angular/common/http';\nimport { HttpRequest, HttpResponse, HttpHeaders, HttpParams } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, PLATFORM_ID, Inject, NgModule } from '@angular/core';\nimport { of, BehaviorSubject, isObservable, throwError, timer } from 'rxjs';\nimport { filter, map, retry, shareReplay, catchError, concatMap, take } from 'rxjs/operators';\nimport * as vlq from 'vlq';\nimport * as i1$1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\n\n/**\r\n * Injection token of logger config\r\n */\nconst TOKEN_LOGGER_CONFIG = 'TOKEN_LOGGER_CONFIG';\nclass NGXLoggerConfigEngine {\n  constructor(config) {\n    this.config = this._clone(config);\n  }\n  /** Get a readonly access to the level configured for the NGXLogger */\n  get level() {\n    return this.config.level;\n  }\n  /** Get a readonly access to the serverLogLevel configured for the NGXLogger */\n  get serverLogLevel() {\n    return this.config.serverLogLevel;\n  }\n  updateConfig(config) {\n    this.config = this._clone(config);\n  }\n  /** Update the config partially\r\n   * This is useful if you want to update only one parameter of the config\r\n   */\n  partialUpdateConfig(partialConfig) {\n    // avoid any error if the config is incorrect\n    if (!partialConfig) {\n      return;\n    }\n    Object.keys(partialConfig).forEach(configParamKey => {\n      this.config[configParamKey] = partialConfig[configParamKey];\n    });\n  }\n  getConfig() {\n    return this._clone(this.config);\n  }\n  // TODO: This is a shallow clone, If the config ever becomes hierarchical we must make this a deep clone\n  _clone(object) {\n    const cloneConfig = {\n      level: null\n    };\n    Object.keys(object).forEach(key => {\n      cloneConfig[key] = object[key];\n    });\n    return cloneConfig;\n  }\n}\n\n/**\r\n * Injection token of logger config engine factory\r\n */\nconst TOKEN_LOGGER_CONFIG_ENGINE_FACTORY = 'TOKEN_LOGGER_CONFIG_ENGINE_FACTORY';\nclass NGXLoggerConfigEngineFactory {\n  provideConfigEngine(config) {\n    return new NGXLoggerConfigEngine(config);\n  }\n}\n\n/**\r\n * Injection token of logger mapper service\r\n */\nconst TOKEN_LOGGER_MAPPER_SERVICE = 'TOKEN_LOGGER_MAPPER_SERVICE';\nclass NGXLoggerMapperService {\n  constructor(httpBackend) {\n    this.httpBackend = httpBackend;\n    /** cache for source maps, key is source map location, ie. 'http://localhost:4200/main.js.map' */\n    this.sourceMapCache = new Map();\n    /** cache for specific log position, key is the dist position, ie 'main.js:339:21' */\n    this.logPositionCache = new Map();\n  }\n  /**\r\n   * Returns the log position of the caller\r\n   * If sourceMaps are enabled, it attemps to get the source map from the server, and use that to parse the position\r\n   * @param config\r\n   * @param metadata\r\n   * @returns\r\n   */\n  getLogPosition(config, metadata) {\n    const stackLine = this.getStackLine(config);\n    // if we were not able to parse the stackLine, just return an empty Log Position\n    if (!stackLine) {\n      return of({\n        fileName: '',\n        lineNumber: 0,\n        columnNumber: 0\n      });\n    }\n    const logPosition = this.getLocalPosition(stackLine);\n    if (!config.enableSourceMaps) {\n      return of(logPosition);\n    }\n    const sourceMapLocation = this.getSourceMapLocation(stackLine);\n    return this.getSourceMap(sourceMapLocation, logPosition);\n  }\n  /**\r\n   * Get the stackline of the original caller\r\n   * @param config\r\n   * @returns null if stackline was not found\r\n   */\n  getStackLine(config) {\n    const error = new Error();\n    try {\n      // noinspection ExceptionCaughtLocallyJS\n      throw error;\n    } catch (e) {\n      try {\n        // Here are different examples of stacktrace \n        // Firefox (last line is the user code, the 4 first are ours):\n        // getStackLine@http://localhost:4200/main.js:358:23\n        // getCallerDetails@http://localhost:4200/main.js:557:44\n        // _log@http://localhost:4200/main.js:830:28\n        // debug@http://localhost:4200/main.js:652:14\n        // handleLog@http://localhost:4200/main.js:1158:29\n        // Chrome and Edge (last line is the user code):\n        // Error\n        // at Function.getStackLine (ngx-logger.js:329)\n        // at NGXMapperService.getCallerDetails (ngx-logger.js:528)\n        // at NGXLogger._log (ngx-logger.js:801)\n        // at NGXLogger.info (ngx-logger.js:631)\n        // at AppComponent.handleLog (app.component.ts:38)\n        let defaultProxy = 4; // We make 4 functions call before getting here\n        const firstStackLine = error.stack.split('\\n')[0];\n        if (!firstStackLine.includes('.js:')) {\n          // The stacktrace starts with no function call (example in Chrome or Edge)\n          defaultProxy = defaultProxy + 1;\n        }\n        return error.stack.split('\\n')[defaultProxy + (config.proxiedSteps || 0)];\n      } catch (e) {\n        return null;\n      }\n    }\n  }\n  /**\r\n   * Get position of caller without using sourceMaps\r\n   * @param stackLine\r\n   * @returns\r\n   */\n  getLocalPosition(stackLine) {\n    // strip base path, then parse filename, line, and column, stackline looks like this :\n    // Firefox\n    // handleLog@http://localhost:4200/main.js:1158:29\n    // Chrome and Edge\n    // at AppComponent.handleLog (app.component.ts:38)\n    const positionStartIndex = stackLine.lastIndexOf('\\/');\n    let positionEndIndex = stackLine.indexOf(')');\n    if (positionEndIndex < 0) {\n      positionEndIndex = undefined;\n    }\n    const position = stackLine.substring(positionStartIndex + 1, positionEndIndex);\n    const dataArray = position.split(':');\n    if (dataArray.length === 3) {\n      return {\n        fileName: dataArray[0],\n        lineNumber: +dataArray[1],\n        columnNumber: +dataArray[2]\n      };\n    }\n    return {\n      fileName: 'unknown',\n      lineNumber: 0,\n      columnNumber: 0\n    };\n  }\n  getTranspileLocation(stackLine) {\n    // Example stackLine:\n    // Firefox : getStackLine@http://localhost:4200/main.js:358:23\n    // Chrome and Edge : at Function.getStackLine (ngx-logger.js:329)\n    let locationStartIndex = stackLine.indexOf('(');\n    if (locationStartIndex < 0) {\n      locationStartIndex = stackLine.lastIndexOf('@');\n      if (locationStartIndex < 0) {\n        locationStartIndex = stackLine.lastIndexOf(' ');\n      }\n    }\n    let locationEndIndex = stackLine.indexOf(')');\n    if (locationEndIndex < 0) {\n      locationEndIndex = undefined;\n    }\n    return stackLine.substring(locationStartIndex + 1, locationEndIndex);\n  }\n  /**\r\n   * Gets the URL of the sourcemap (the URL can be relative or absolute, it is browser dependant)\r\n   * @param stackLine\r\n   * @returns\r\n   */\n  getSourceMapLocation(stackLine) {\n    const file = this.getTranspileLocation(stackLine);\n    const mapFullPath = file.substring(0, file.lastIndexOf(':'));\n    return mapFullPath.substring(0, mapFullPath.lastIndexOf(':')) + '.map';\n  }\n  getMapping(sourceMap, position) {\n    // => ';' indicates end of a line\n    // => ',' separates mappings in a line\n    // decoded mapping => [ generatedCodeColumn, sourceFileIndex, sourceCodeLine, sourceCodeColumn, nameIndex ]\n    let sourceFileIndex = 0,\n      // second field\n      sourceCodeLine = 0,\n      // third field\n      sourceCodeColumn = 0; // fourth field\n    const lines = sourceMap.mappings.split(';');\n    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {\n      // reset column position to 0 after each line\n      let generatedCodeColumn = 0;\n      // decode sections in line\n      const columns = lines[lineIndex].split(',');\n      for (let columnIndex = 0; columnIndex < columns.length; columnIndex++) {\n        const decodedSection = vlq.decode(columns[columnIndex]);\n        if (decodedSection.length >= 4) {\n          // update relative positions\n          generatedCodeColumn += decodedSection[0];\n          sourceFileIndex += decodedSection[1];\n          sourceCodeLine += decodedSection[2];\n          sourceCodeColumn += decodedSection[3];\n        }\n        // check if matching map\n        if (lineIndex === position.lineNumber) {\n          if (generatedCodeColumn === position.columnNumber) {\n            // matching column and line found\n            return {\n              fileName: sourceMap.sources[sourceFileIndex],\n              lineNumber: sourceCodeLine,\n              columnNumber: sourceCodeColumn\n            };\n          } else if (columnIndex + 1 === columns.length) {\n            // matching column not found, but line is correct\n            return {\n              fileName: sourceMap.sources[sourceFileIndex],\n              lineNumber: sourceCodeLine,\n              columnNumber: 0\n            };\n          }\n        }\n      }\n    }\n    // failed if reached\n    return {\n      fileName: 'unknown',\n      lineNumber: 0,\n      columnNumber: 0\n    };\n  }\n  /**\r\n   * does the http get request to get the source map\r\n   * @param sourceMapLocation\r\n   * @param distPosition\r\n   */\n  getSourceMap(sourceMapLocation, distPosition) {\n    const req = new HttpRequest('GET', sourceMapLocation);\n    const distPositionKey = `${distPosition.fileName}:${distPosition.lineNumber}:${distPosition.columnNumber}`;\n    // if the specific log position is already in cache return it\n    if (this.logPositionCache.has(distPositionKey)) {\n      return this.logPositionCache.get(distPositionKey);\n    }\n    // otherwise check if the source map is already cached for given source map location\n    if (!this.sourceMapCache.has(sourceMapLocation)) {\n      if (!this.httpBackend) {\n        console.error('NGXLogger : Can\\'t get sourcemap because HttpBackend is not provided. You need to import HttpClientModule');\n        this.sourceMapCache.set(sourceMapLocation, of(null));\n      } else {\n        // obtain the source map if not cached\n        this.sourceMapCache.set(sourceMapLocation, this.httpBackend.handle(req).pipe(filter(e => e instanceof HttpResponse), map(httpResponse => httpResponse.body), retry(3), shareReplay(1)));\n      }\n    }\n    // at this point the source map is cached, use it to get specific log position mapping\n    const logPosition$ = this.sourceMapCache.get(sourceMapLocation).pipe(map(sourceMap => {\n      // sourceMap can be null if HttpBackend is not provided for example\n      if (!sourceMap) {\n        return distPosition;\n      }\n      // map generated position to source position\n      return this.getMapping(sourceMap, distPosition);\n    }), catchError(() => of(distPosition)), shareReplay(1));\n    // store specific log position in cache for given dest position and return it\n    this.logPositionCache.set(distPositionKey, logPosition$);\n    return logPosition$;\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerMapperService.ɵfac = function NGXLoggerMapperService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NGXLoggerMapperService)(i0.ɵɵinject(i1.HttpBackend, 8));\n};\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerMapperService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NGXLoggerMapperService,\n  factory: NGXLoggerMapperService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NGXLoggerMapperService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.HttpBackend,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\r\n * Injection token of logger metadata service\r\n */\nconst TOKEN_LOGGER_METADATA_SERVICE = 'TOKEN_LOGGER_METADATA_SERVICE';\nclass NGXLoggerMetadataService {\n  constructor(datePipe) {\n    this.datePipe = datePipe;\n  }\n  computeTimestamp(config) {\n    const defaultTimestamp = () => new Date().toISOString();\n    if (config.timestampFormat) {\n      if (!this.datePipe) {\n        console.error('NGXLogger : Can\\'t use timeStampFormat because DatePipe is not provided. You need to provide DatePipe');\n        return defaultTimestamp();\n      } else {\n        return this.datePipe.transform(new Date(), config.timestampFormat);\n      }\n    }\n    return defaultTimestamp();\n  }\n  getMetadata(level, config, message, additional) {\n    const metadata = {\n      level: level,\n      additional: additional\n    };\n    // The user can send a function\n    // This is useful in order to compute string concatenation only when the log will actually be written\n    if (message && typeof message === 'function') {\n      metadata.message = message();\n    } else {\n      metadata.message = message;\n    }\n    metadata.timestamp = this.computeTimestamp(config);\n    return metadata;\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerMetadataService.ɵfac = function NGXLoggerMetadataService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NGXLoggerMetadataService)(i0.ɵɵinject(i1$1.DatePipe, 8));\n};\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerMetadataService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NGXLoggerMetadataService,\n  factory: NGXLoggerMetadataService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NGXLoggerMetadataService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.DatePipe,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n// I kept this class alive only to avoid a breaking change with the old version\n// This class does not implement anything so it is useless and the interface is enough\n/**\r\n * @deprecated this class does not implement anything thus being useless, you should rather implements @see INGXLoggerMonitor\r\n */\nclass NGXLoggerMonitor {}\n\n/**\r\n * Injection token of logger metadata service\r\n */\nconst TOKEN_LOGGER_RULES_SERVICE = 'TOKEN_LOGGER_RULES_SERVICE';\nclass NGXLoggerRulesService {\n  shouldCallWriter(level, config, message, additional) {\n    return !config.disableConsoleLogging && level >= config.level;\n  }\n  shouldCallServer(level, config, message, additional) {\n    return !!config.serverLoggingUrl && level >= config.serverLogLevel;\n  }\n  shouldCallMonitor(level, config, message, additional) {\n    // The default behavior is to call the monitor only if the writer or the server is called\n    return this.shouldCallWriter(level, config, message, additional) || this.shouldCallServer(level, config, message, additional);\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerRulesService.ɵfac = function NGXLoggerRulesService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NGXLoggerRulesService)();\n};\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerRulesService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NGXLoggerRulesService,\n  factory: NGXLoggerRulesService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NGXLoggerRulesService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\r\n * Injection token of logger server service\r\n */\nconst TOKEN_LOGGER_SERVER_SERVICE = 'TOKEN_LOGGER_SERVER_SERVICE';\nclass NGXLoggerServerService {\n  constructor(httpBackend, ngZone) {\n    this.httpBackend = httpBackend;\n    this.ngZone = ngZone;\n    this.serverCallsQueue = [];\n    this.flushingQueue = new BehaviorSubject(false);\n  }\n  ngOnDestroy() {\n    if (this.flushingQueue) {\n      this.flushingQueue.complete();\n      this.flushingQueue = null;\n    }\n    if (this.addToQueueTimer) {\n      this.addToQueueTimer.unsubscribe();\n      this.addToQueueTimer = null;\n    }\n  }\n  /**\r\n   * Transforms an error object into a readable string (taking only the stack)\r\n   * This is needed because JSON.stringify would return \"{}\"\r\n   * @param err the error object\r\n   * @returns The stack of the error\r\n   */\n  secureErrorObject(err) {\n    return err?.stack;\n  }\n  /**\r\n   * Transforms the additional parameters to avoid any json error when sending the data to the server\r\n   * Basically it just replaces unstringifiable object to a string mentioning an error\r\n   * @param additional The additional data to be sent\r\n   * @returns The additional data secured\r\n   */\n  secureAdditionalParameters(additional) {\n    if (additional === null || additional === undefined) {\n      return null;\n    }\n    return additional.map((next, idx) => {\n      try {\n        if (next instanceof Error) {\n          return this.secureErrorObject(next);\n        }\n        // We just want to make sure the JSON can be parsed, we do not want to actually change the type\n        if (typeof next === 'object') {\n          JSON.stringify(next);\n        }\n        return next;\n      } catch (e) {\n        return `The additional[${idx}] value could not be parsed using JSON.stringify().`;\n      }\n    });\n  }\n  /**\r\n   * Transforms the message so that it can be sent to the server\r\n   * @param message the message to be sent\r\n   * @returns the message secured\r\n   */\n  secureMessage(message) {\n    try {\n      if (message instanceof Error) {\n        return this.secureErrorObject(message);\n      }\n      if (typeof message !== 'string') {\n        message = JSON.stringify(message, null, 2);\n      }\n    } catch (e) {\n      message = 'The provided \"message\" value could not be parsed with JSON.stringify().';\n    }\n    return message;\n  }\n  /**\r\n   * Edits HttpRequest object before sending request to server\r\n   * @param httpRequest default request object\r\n   * @returns altered httprequest\r\n   */\n  alterHttpRequest(httpRequest) {\n    return httpRequest;\n  }\n  /**\r\n   * Sends request to server\r\n   * @param url\r\n   * @param logContent\r\n   * @param options\r\n   * @returns\r\n   */\n  logOnServer(url, logContent, options) {\n    if (!this.httpBackend) {\n      console.error('NGXLogger : Can\\'t log on server because HttpBackend is not provided. You need to import HttpClientModule');\n      return of(null);\n    }\n    // HttpBackend skips all HttpInterceptors\n    // They may log errors using this service causing circular calls\n    let defaultRequest = new HttpRequest('POST', url, logContent, options || {});\n    let finalRequest = of(defaultRequest);\n    const alteredRequest = this.alterHttpRequest(defaultRequest);\n    if (isObservable(alteredRequest)) {\n      finalRequest = alteredRequest;\n    } else if (alteredRequest) {\n      finalRequest = of(alteredRequest);\n    } else {\n      console.warn('NGXLogger : alterHttpRequest returned an invalid request. Using default one instead');\n    }\n    return finalRequest.pipe(concatMap(req => {\n      if (!req) {\n        console.warn('NGXLogger : alterHttpRequest returned an invalid request (observable). Using default one instead');\n        return this.httpBackend.handle(defaultRequest);\n      }\n      return this.httpBackend.handle(req);\n    }), filter(e => e instanceof HttpResponse), map(httpResponse => httpResponse.body));\n  }\n  /**\r\n   * Customise the data sent to the API\r\n   * @param metadata the data provided by NGXLogger\r\n   * @returns the data that will be sent to the API in the body\r\n   */\n  customiseRequestBody(metadata) {\n    // In our API the body is not customised\n    return metadata;\n  }\n  /**\r\n   * Flush the queue of the logger\r\n   * @param config\r\n   */\n  flushQueue(config) {\n    this.flushingQueue.next(true);\n    // If a timer was set, we cancel it because the queue is flushed\n    if (this.addToQueueTimer) {\n      this.addToQueueTimer.unsubscribe();\n      this.addToQueueTimer = null;\n    }\n    if (!!this.serverCallsQueue && this.serverCallsQueue.length > 0) {\n      this.sendToServerAction(this.serverCallsQueue, config);\n    }\n    this.serverCallsQueue = [];\n    this.flushingQueue.next(false);\n  }\n  sendToServerAction(metadata, config) {\n    let requestBody;\n    const secureMetadata = pMetadata => {\n      // Copying metadata locally because we don't want to change the object for the caller\n      const securedMetadata = {\n        ...pMetadata\n      };\n      securedMetadata.additional = this.secureAdditionalParameters(securedMetadata.additional);\n      securedMetadata.message = this.secureMessage(securedMetadata.message);\n      return securedMetadata;\n    };\n    if (Array.isArray(metadata)) {\n      requestBody = [];\n      metadata.forEach(m => {\n        requestBody.push(secureMetadata(m));\n      });\n    } else {\n      requestBody = secureMetadata(metadata);\n    }\n    // Allow users to customise the data sent to the API\n    requestBody = this.customiseRequestBody(requestBody);\n    const headers = config.customHttpHeaders || new HttpHeaders();\n    if (!headers.has('Content-Type')) {\n      headers.set('Content-Type', 'application/json');\n    }\n    const logOnServerAction = () => {\n      this.logOnServer(config.serverLoggingUrl, requestBody, {\n        headers,\n        params: config.customHttpParams || new HttpParams(),\n        responseType: config.httpResponseType || 'json',\n        withCredentials: config.withCredentials || false\n      }).pipe(catchError(err => {\n        // Do not use NGXLogger here because this could cause an infinite loop \n        console.error('NGXLogger: Failed to log on server', err);\n        return throwError(err);\n      })).subscribe();\n    };\n    if (config.serverCallsOutsideNgZone === true) {\n      if (!this.ngZone) {\n        console.error('NGXLogger: NgZone is not provided and serverCallsOutsideNgZone is set to true');\n        return;\n      }\n      this.ngZone.runOutsideAngular(logOnServerAction);\n    } else {\n      logOnServerAction();\n    }\n  }\n  /**\r\n   * Sends the content to be logged to the server according to the config\r\n   * @param metadata\r\n   * @param config\r\n   */\n  sendToServer(metadata, config) {\n    // If there is no batch mode in the config, we send the log call straight to the server as usual\n    if ((!config.serverCallsBatchSize || config.serverCallsBatchSize <= 0) && (!config.serverCallsTimer || config.serverCallsTimer <= 0)) {\n      this.sendToServerAction(metadata, config);\n      return;\n    }\n    const addLogToQueueAction = () => {\n      this.serverCallsQueue.push({\n        ...metadata\n      });\n      // Flush queue when size is reached\n      if (!!config.serverCallsBatchSize && this.serverCallsQueue.length > config.serverCallsBatchSize) {\n        this.flushQueue(config);\n      }\n      // Call timer only if it is in the config and timer is not already running\n      if (config.serverCallsTimer > 0 && !this.addToQueueTimer) {\n        this.addToQueueTimer = timer(config.serverCallsTimer).subscribe(_ => {\n          this.flushQueue(config);\n        });\n      }\n    };\n    // If queue is being flushed, we need to wait for it to finish before adding other calls\n    if (this.flushingQueue.value === true) {\n      this.flushingQueue.pipe(filter(fq => fq === false), take(1)).subscribe(_ => {\n        addLogToQueueAction();\n      });\n    } else {\n      addLogToQueueAction();\n    }\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerServerService.ɵfac = function NGXLoggerServerService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NGXLoggerServerService)(i0.ɵɵinject(i1.HttpBackend, 8), i0.ɵɵinject(i0.NgZone, 8));\n};\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerServerService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NGXLoggerServerService,\n  factory: NGXLoggerServerService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NGXLoggerServerService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.HttpBackend,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\r\n * Injection token of logger writer service\r\n */\nconst TOKEN_LOGGER_WRITER_SERVICE = 'TOKEN_LOGGER_WRITER_SERVICE';\nvar NgxLoggerLevel;\n(function (NgxLoggerLevel) {\n  NgxLoggerLevel[NgxLoggerLevel[\"TRACE\"] = 0] = \"TRACE\";\n  NgxLoggerLevel[NgxLoggerLevel[\"DEBUG\"] = 1] = \"DEBUG\";\n  NgxLoggerLevel[NgxLoggerLevel[\"INFO\"] = 2] = \"INFO\";\n  NgxLoggerLevel[NgxLoggerLevel[\"LOG\"] = 3] = \"LOG\";\n  NgxLoggerLevel[NgxLoggerLevel[\"WARN\"] = 4] = \"WARN\";\n  NgxLoggerLevel[NgxLoggerLevel[\"ERROR\"] = 5] = \"ERROR\";\n  NgxLoggerLevel[NgxLoggerLevel[\"FATAL\"] = 6] = \"FATAL\";\n  NgxLoggerLevel[NgxLoggerLevel[\"OFF\"] = 7] = \"OFF\";\n})(NgxLoggerLevel || (NgxLoggerLevel = {}));\nconst DEFAULT_COLOR_SCHEME = ['purple', 'teal', 'gray', 'gray', 'red', 'red', 'red'];\nclass NGXLoggerWriterService {\n  constructor(platformId) {\n    this.platformId = platformId;\n    /** List of functions called when preparing meta string */\n    this.prepareMetaStringFuncs = [this.getTimestampToWrite, this.getLevelToWrite, this.getFileDetailsToWrite, this.getContextToWrite];\n    this.isIE = isPlatformBrowser(platformId) && navigator && navigator.userAgent && !!(navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.match(/Trident\\//) || navigator.userAgent.match(/Edge\\//));\n    this.logFunc = this.isIE ? this.logIE.bind(this) : this.logModern.bind(this);\n  }\n  getTimestampToWrite(metadata, config) {\n    return metadata.timestamp;\n  }\n  getLevelToWrite(metadata, config) {\n    return NgxLoggerLevel[metadata.level];\n  }\n  getFileDetailsToWrite(metadata, config) {\n    return config.disableFileDetails === true ? '' : `[${metadata.fileName}:${metadata.lineNumber}:${metadata.columnNumber}]`;\n  }\n  getContextToWrite(metadata, config) {\n    return config.context ? `{${config.context}}` : '';\n  }\n  /** Generate a \"meta\" string that is displayed before the content sent to the log function */\n  prepareMetaString(metadata, config) {\n    let metaString = '';\n    this.prepareMetaStringFuncs.forEach(prepareMetaStringFunc => {\n      const metaItem = prepareMetaStringFunc(metadata, config);\n      if (metaItem) {\n        metaString = metaString + ' ' + metaItem;\n      }\n    });\n    return metaString.trim();\n  }\n  /** Get the color to use when writing to console */\n  getColor(metadata, config) {\n    const configColorScheme = config.colorScheme ?? DEFAULT_COLOR_SCHEME;\n    // this is needed to avoid a build error\n    if (metadata.level === NgxLoggerLevel.OFF) {\n      return undefined;\n    }\n    return configColorScheme[metadata.level];\n  }\n  /** Log to the console specifically for IE */\n  logIE(metadata, config, metaString) {\n    // Coloring doesn't work in IE\n    // make sure additional isn't null or undefined so that ...additional doesn't error\n    const additional = metadata.additional || [];\n    switch (metadata.level) {\n      case NgxLoggerLevel.WARN:\n        console.warn(`${metaString} `, metadata.message, ...additional);\n        break;\n      case NgxLoggerLevel.ERROR:\n      case NgxLoggerLevel.FATAL:\n        console.error(`${metaString} `, metadata.message, ...additional);\n        break;\n      case NgxLoggerLevel.INFO:\n        console.info(`${metaString} `, metadata.message, ...additional);\n        break;\n      default:\n        console.log(`${metaString} `, metadata.message, ...additional);\n    }\n  }\n  /** Log to the console */\n  logModern(metadata, config, metaString) {\n    const color = this.getColor(metadata, config);\n    // make sure additional isn't null or undefined so that ...additional doesn't error\n    const additional = metadata.additional || [];\n    switch (metadata.level) {\n      case NgxLoggerLevel.WARN:\n        console.warn(`%c${metaString}`, `color:${color}`, metadata.message, ...additional);\n        break;\n      case NgxLoggerLevel.ERROR:\n      case NgxLoggerLevel.FATAL:\n        console.error(`%c${metaString}`, `color:${color}`, metadata.message, ...additional);\n        break;\n      case NgxLoggerLevel.INFO:\n        console.info(`%c${metaString}`, `color:${color}`, metadata.message, ...additional);\n        break;\n      //  Disabling console.trace since the stack trace is not helpful. it is showing the stack trace of\n      // the console.trace statement\n      // case NgxLoggerLevel.TRACE:\n      //   console.trace(`%c${metaString}`, `color:${color}`, message, ...additional);\n      //   break;\n      case NgxLoggerLevel.DEBUG:\n        console.debug(`%c${metaString}`, `color:${color}`, metadata.message, ...additional);\n        break;\n      default:\n        console.log(`%c${metaString}`, `color:${color}`, metadata.message, ...additional);\n    }\n  }\n  /** Write the content sent to the log function to the console */\n  writeMessage(metadata, config) {\n    const metaString = this.prepareMetaString(metadata, config);\n    this.logFunc(metadata, config, metaString);\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerWriterService.ɵfac = function NGXLoggerWriterService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NGXLoggerWriterService)(i0.ɵɵinject(PLATFORM_ID));\n};\n/** @nocollapse */ /** @nocollapse */\nNGXLoggerWriterService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NGXLoggerWriterService,\n  factory: NGXLoggerWriterService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NGXLoggerWriterService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\nclass NGXLogger {\n  constructor(config, configEngineFactory, metadataService, ruleService, mapperService, writerService, serverService) {\n    this.metadataService = metadataService;\n    this.ruleService = ruleService;\n    this.mapperService = mapperService;\n    this.writerService = writerService;\n    this.serverService = serverService;\n    this.configEngine = configEngineFactory.provideConfigEngine(config);\n  }\n  /** Get a readonly access to the level configured for the NGXLogger */\n  get level() {\n    return this.configEngine.level;\n  }\n  /** Get a readonly access to the serverLogLevel configured for the NGXLogger */\n  get serverLogLevel() {\n    return this.configEngine.serverLogLevel;\n  }\n  trace(message, ...additional) {\n    this._log(NgxLoggerLevel.TRACE, message, additional);\n  }\n  debug(message, ...additional) {\n    this._log(NgxLoggerLevel.DEBUG, message, additional);\n  }\n  info(message, ...additional) {\n    this._log(NgxLoggerLevel.INFO, message, additional);\n  }\n  log(message, ...additional) {\n    this._log(NgxLoggerLevel.LOG, message, additional);\n  }\n  warn(message, ...additional) {\n    this._log(NgxLoggerLevel.WARN, message, additional);\n  }\n  error(message, ...additional) {\n    this._log(NgxLoggerLevel.ERROR, message, additional);\n  }\n  fatal(message, ...additional) {\n    this._log(NgxLoggerLevel.FATAL, message, additional);\n  }\n  /** @deprecated customHttpHeaders is now part of the config, this should be updated via @see updateConfig */\n  setCustomHttpHeaders(headers) {\n    const config = this.getConfigSnapshot();\n    config.customHttpHeaders = headers;\n    this.updateConfig(config);\n  }\n  /** @deprecated customHttpParams is now part of the config, this should be updated via @see updateConfig */\n  setCustomParams(params) {\n    const config = this.getConfigSnapshot();\n    config.customHttpParams = params;\n    this.updateConfig(config);\n  }\n  /** @deprecated withCredentials is now part of the config, this should be updated via @see updateConfig */\n  setWithCredentialsOptionValue(withCredentials) {\n    const config = this.getConfigSnapshot();\n    config.withCredentials = withCredentials;\n    this.updateConfig(config);\n  }\n  /**\r\n   * Register a INGXLoggerMonitor that will be trigger when a log is either written or sent to server\r\n   *\r\n   * There is only one monitor, registering one will overwrite the last one if there was one\r\n   * @param monitor\r\n   */\n  registerMonitor(monitor) {\n    this._loggerMonitor = monitor;\n  }\n  /** Set config of logger\r\n   *\r\n   * Warning : This overwrites all the config, if you want to update only one property, you should use @see getConfigSnapshot before\r\n   */\n  updateConfig(config) {\n    this.configEngine.updateConfig(config);\n  }\n  partialUpdateConfig(partialConfig) {\n    this.configEngine.partialUpdateConfig(partialConfig);\n  }\n  /** Get config of logger */\n  getConfigSnapshot() {\n    return this.configEngine.getConfig();\n  }\n  /**\r\n   * Flush the serveur queue\r\n   */\n  flushServerQueue() {\n    this.serverService.flushQueue(this.getConfigSnapshot());\n  }\n  _log(level, message, additional = []) {\n    const config = this.configEngine.getConfig();\n    const shouldCallWriter = this.ruleService.shouldCallWriter(level, config, message, additional);\n    const shouldCallServer = this.ruleService.shouldCallServer(level, config, message, additional);\n    const shouldCallMonitor = this.ruleService.shouldCallMonitor(level, config, message, additional);\n    if (!shouldCallWriter && !shouldCallServer && !shouldCallMonitor) {\n      // If nothing is to be called we return\n      return;\n    }\n    const metadata = this.metadataService.getMetadata(level, config, message, additional);\n    this.mapperService.getLogPosition(config, metadata).pipe(take(1)).subscribe(logPosition => {\n      if (logPosition) {\n        metadata.fileName = logPosition.fileName;\n        metadata.lineNumber = logPosition.lineNumber;\n        metadata.columnNumber = logPosition.columnNumber;\n      }\n      if (shouldCallMonitor && this._loggerMonitor) {\n        this._loggerMonitor.onLog(metadata, config);\n      }\n      if (shouldCallWriter) {\n        this.writerService.writeMessage(metadata, config);\n      }\n      if (shouldCallServer) {\n        this.serverService.sendToServer(metadata, config);\n      }\n    });\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nNGXLogger.ɵfac = function NGXLogger_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NGXLogger)(i0.ɵɵinject(TOKEN_LOGGER_CONFIG), i0.ɵɵinject(TOKEN_LOGGER_CONFIG_ENGINE_FACTORY), i0.ɵɵinject(TOKEN_LOGGER_METADATA_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_RULES_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_MAPPER_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_WRITER_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_SERVER_SERVICE));\n};\n/** @nocollapse */ /** @nocollapse */\nNGXLogger.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NGXLogger,\n  factory: NGXLogger.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NGXLogger, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_CONFIG]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_CONFIG_ENGINE_FACTORY]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_METADATA_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_RULES_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_MAPPER_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_WRITER_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_SERVER_SERVICE]\n      }]\n    }];\n  }, null);\n})();\n\n/**\r\n * CustomNGXLoggerService is designed to allow users to get a new instance of a logger\r\n */\nclass CustomNGXLoggerService {\n  constructor(logger, configEngineFactory, metadataService, ruleService, mapperService, writerService, serverService) {\n    this.logger = logger;\n    this.configEngineFactory = configEngineFactory;\n    this.metadataService = metadataService;\n    this.ruleService = ruleService;\n    this.mapperService = mapperService;\n    this.writerService = writerService;\n    this.serverService = serverService;\n  }\n  /**\r\n   * Create an instance of a logger\r\n   * @deprecated this function does not have all the features, @see getNewInstance for every params available\r\n   * @param config\r\n   * @param serverService\r\n   * @param logMonitor\r\n   * @param mapperService\r\n   * @returns\r\n   */\n  create(config, serverService, logMonitor, mapperService) {\n    return this.getNewInstance({\n      config,\n      serverService,\n      logMonitor,\n      mapperService\n    });\n  }\n  /**\r\n   * Get a new instance of NGXLogger\r\n   * @param params list of optional params to use when creating an instance of NGXLogger\r\n   * @returns the new instance of NGXLogger\r\n   */\n  getNewInstance(params) {\n    const logger = new NGXLogger(params?.config ?? this.logger.getConfigSnapshot(), params?.configEngineFactory ?? this.configEngineFactory, params?.metadataService ?? this.metadataService, params?.ruleService ?? this.ruleService, params?.mapperService ?? this.mapperService, params?.writerService ?? this.writerService, params?.serverService ?? this.serverService);\n    if (params?.partialConfig) {\n      logger.partialUpdateConfig(params.partialConfig);\n    }\n    if (params?.logMonitor) {\n      logger.registerMonitor(params.logMonitor);\n    }\n    return logger;\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nCustomNGXLoggerService.ɵfac = function CustomNGXLoggerService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CustomNGXLoggerService)(i0.ɵɵinject(NGXLogger), i0.ɵɵinject(TOKEN_LOGGER_CONFIG_ENGINE_FACTORY), i0.ɵɵinject(TOKEN_LOGGER_METADATA_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_RULES_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_MAPPER_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_WRITER_SERVICE), i0.ɵɵinject(TOKEN_LOGGER_SERVER_SERVICE));\n};\n/** @nocollapse */ /** @nocollapse */\nCustomNGXLoggerService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CustomNGXLoggerService,\n  factory: CustomNGXLoggerService.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomNGXLoggerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: NGXLogger\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_CONFIG_ENGINE_FACTORY]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_METADATA_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_RULES_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_MAPPER_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_WRITER_SERVICE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOKEN_LOGGER_SERVER_SERVICE]\n      }]\n    }];\n  }, null);\n})();\nclass LoggerModule {\n  static forRoot(config, customProvider) {\n    if (!customProvider) {\n      customProvider = {};\n    }\n    // default config provider\n    if (!customProvider.configProvider) {\n      customProvider.configProvider = {\n        provide: TOKEN_LOGGER_CONFIG,\n        useValue: config || {}\n      };\n    } else {\n      // if the user provided its own config, we just make sure the injection token is correct\n      if (customProvider.configProvider.provide !== TOKEN_LOGGER_CONFIG) {\n        throw new Error(`Wrong injection token for configProvider, it should be ${TOKEN_LOGGER_CONFIG} and you used ${customProvider.configProvider.provide}`);\n      }\n    }\n    // default configEngine provider\n    if (!customProvider.configEngineFactoryProvider) {\n      customProvider.configEngineFactoryProvider = {\n        provide: TOKEN_LOGGER_CONFIG_ENGINE_FACTORY,\n        useClass: NGXLoggerConfigEngineFactory\n      };\n    } else {\n      // if the user provided its own configEngineFactory, we just make sure the injection token is correct\n      if (customProvider.configEngineFactoryProvider.provide !== TOKEN_LOGGER_CONFIG_ENGINE_FACTORY) {\n        throw new Error(`Wrong injection token for configEngineFactoryProvider, it should be '${TOKEN_LOGGER_CONFIG_ENGINE_FACTORY}' and you used '${customProvider.configEngineFactoryProvider.provide}'`);\n      }\n    }\n    // default metadata provider\n    if (!customProvider.metadataProvider) {\n      customProvider.metadataProvider = {\n        provide: TOKEN_LOGGER_METADATA_SERVICE,\n        useClass: NGXLoggerMetadataService\n      };\n    } else {\n      // if the user provided its own metadataService, we just make sure the injection token is correct\n      if (customProvider.metadataProvider.provide !== TOKEN_LOGGER_METADATA_SERVICE) {\n        throw new Error(`Wrong injection token for metadataProvider, it should be '${TOKEN_LOGGER_METADATA_SERVICE}' and you used '${customProvider.metadataProvider.provide}'`);\n      }\n    }\n    // default rule provider\n    if (!customProvider.ruleProvider) {\n      customProvider.ruleProvider = {\n        provide: TOKEN_LOGGER_RULES_SERVICE,\n        useClass: NGXLoggerRulesService\n      };\n    } else {\n      // if the user provided its own ruleService, we just make sure the injection token is correct\n      if (customProvider.ruleProvider.provide !== TOKEN_LOGGER_RULES_SERVICE) {\n        throw new Error(`Wrong injection token for ruleProvider, it should be '${TOKEN_LOGGER_RULES_SERVICE}' and you used '${customProvider.ruleProvider.provide}'`);\n      }\n    }\n    // default mapper provider\n    if (!customProvider.mapperProvider) {\n      customProvider.mapperProvider = {\n        provide: TOKEN_LOGGER_MAPPER_SERVICE,\n        useClass: NGXLoggerMapperService\n      };\n    } else {\n      // if the user provided its own mapperService, we just make sure the injection token is correct\n      if (customProvider.mapperProvider.provide !== TOKEN_LOGGER_MAPPER_SERVICE) {\n        throw new Error(`Wrong injection token for mapperProvider, it should be '${TOKEN_LOGGER_MAPPER_SERVICE}' and you used '${customProvider.mapperProvider.provide}'`);\n      }\n    }\n    // default writer provider\n    if (!customProvider.writerProvider) {\n      customProvider.writerProvider = {\n        provide: TOKEN_LOGGER_WRITER_SERVICE,\n        useClass: NGXLoggerWriterService\n      };\n    } else {\n      // if the user provided its own writerService, we just make sure the injection token is correct\n      if (customProvider.writerProvider.provide !== TOKEN_LOGGER_WRITER_SERVICE) {\n        throw new Error(`Wrong injection token for writerProvider, it should be '${TOKEN_LOGGER_WRITER_SERVICE}' and you used '${customProvider.writerProvider.provide}'`);\n      }\n    }\n    // default server provider\n    if (!customProvider.serverProvider) {\n      customProvider.serverProvider = {\n        provide: TOKEN_LOGGER_SERVER_SERVICE,\n        useClass: NGXLoggerServerService\n      };\n    } else {\n      // if the user provided its own serverService, we just make sure the injection token is correct\n      if (customProvider.serverProvider.provide !== TOKEN_LOGGER_SERVER_SERVICE) {\n        throw new Error(`Wrong injection token for serverProvider, it should be '${TOKEN_LOGGER_SERVER_SERVICE}' and you used '${customProvider.writerProvider.provide}'`);\n      }\n    }\n    return {\n      ngModule: LoggerModule,\n      providers: [NGXLogger, customProvider.configProvider, customProvider.configEngineFactoryProvider, customProvider.metadataProvider, customProvider.ruleProvider, customProvider.mapperProvider, customProvider.writerProvider, customProvider.serverProvider, CustomNGXLoggerService]\n    };\n  }\n  static forChild() {\n    // todo : this forChild is useless for now because nothing is different from forRoot.\n    // This should be implemented so that user can change the providers in the forChild\n    return {\n      ngModule: LoggerModule\n    };\n  }\n}\n/** @nocollapse */ /** @nocollapse */\nLoggerModule.ɵfac = function LoggerModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || LoggerModule)();\n};\n/** @nocollapse */ /** @nocollapse */\nLoggerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LoggerModule,\n  imports: [CommonModule]\n});\n/** @nocollapse */ /** @nocollapse */\nLoggerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoggerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-logger\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { CustomNGXLoggerService, DEFAULT_COLOR_SCHEME, LoggerModule, NGXLogger, NGXLoggerConfigEngine, NGXLoggerConfigEngineFactory, NGXLoggerMapperService, NGXLoggerMetadataService, NGXLoggerMonitor, NGXLoggerRulesService, NGXLoggerServerService, NGXLoggerWriterService, NgxLoggerLevel, TOKEN_LOGGER_CONFIG, TOKEN_LOGGER_CONFIG_ENGINE_FACTORY, TOKEN_LOGGER_MAPPER_SERVICE, TOKEN_LOGGER_METADATA_SERVICE, TOKEN_LOGGER_RULES_SERVICE, TOKEN_LOGGER_SERVER_SERVICE, TOKEN_LOGGER_WRITER_SERVICE };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,gBAAgB,CAAC;AACrB,IAAI,gBAAgB,CAAC;AACrB,oEACK,MAAM,EAAE,EACR,QAAQ,SAAU,MAAM,GAAG;AAC5B,gBAAc,IAAI,IAAI;AACtB,gBAAc,CAAC,IAAI;AACvB,CAAC;AACD,SAAS,OAAO,QAAQ;AACpB,MAAI,SAAS,CAAC;AACd,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvC,QAAI,UAAU,cAAc,OAAO,CAAC,CAAC;AACrC,QAAI,YAAY,QAAW;AACvB,YAAM,IAAI,MAAM,wBAAwB,OAAO,CAAC,IAAI,GAAG;AAAA,IAC3D;AACA,QAAI,qBAAqB,UAAU;AACnC,eAAW;AACX,aAAS,WAAW;AACpB,QAAI,oBAAoB;AACpB,eAAS;AAAA,IACb,OACK;AACD,UAAI,eAAe,QAAQ;AAC3B,iBAAW;AACX,UAAI,cAAc;AACd,eAAO,KAAK,UAAU,IAAI,cAAc,CAAC,KAAK;AAAA,MAClD,OACK;AACD,eAAO,KAAK,KAAK;AAAA,MACrB;AAEA,cAAQ,QAAQ;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;;;ACxBA,IAAM,sBAAsB;AAC5B,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,QAAQ;AAClB,SAAK,SAAS,KAAK,OAAO,MAAM;AAAA,EAClC;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,aAAa,QAAQ;AACnB,SAAK,SAAS,KAAK,OAAO,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,eAAe;AAEjC,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AACA,WAAO,KAAK,aAAa,EAAE,QAAQ,oBAAkB;AACnD,WAAK,OAAO,cAAc,IAAI,cAAc,cAAc;AAAA,IAC5D,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,WAAO,KAAK,OAAO,KAAK,MAAM;AAAA,EAChC;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,kBAAY,GAAG,IAAI,OAAO,GAAG;AAAA,IAC/B,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAKA,IAAM,qCAAqC;AAC3C,IAAM,+BAAN,MAAmC;AAAA,EACjC,oBAAoB,QAAQ;AAC1B,WAAO,IAAI,sBAAsB,MAAM;AAAA,EACzC;AACF;AAKA,IAAM,8BAA8B;AACpC,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,aAAa;AACvB,SAAK,cAAc;AAEnB,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,mBAAmB,oBAAI,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,QAAQ,UAAU;AAC/B,UAAM,YAAY,KAAK,aAAa,MAAM;AAE1C,QAAI,CAAC,WAAW;AACd,aAAO,GAAG;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,UAAM,cAAc,KAAK,iBAAiB,SAAS;AACnD,QAAI,CAAC,OAAO,kBAAkB;AAC5B,aAAO,GAAG,WAAW;AAAA,IACvB;AACA,UAAM,oBAAoB,KAAK,qBAAqB,SAAS;AAC7D,WAAO,KAAK,aAAa,mBAAmB,WAAW;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,QAAQ;AACnB,UAAM,QAAQ,IAAI,MAAM;AACxB,QAAI;AAEF,YAAM;AAAA,IACR,SAAS,GAAG;AACV,UAAI;AAeF,YAAI,eAAe;AACnB,cAAM,iBAAiB,MAAM,MAAM,MAAM,IAAI,EAAE,CAAC;AAChD,YAAI,CAAC,eAAe,SAAS,MAAM,GAAG;AAEpC,yBAAe,eAAe;AAAA,QAChC;AACA,eAAO,MAAM,MAAM,MAAM,IAAI,EAAE,gBAAgB,OAAO,gBAAgB,EAAE;AAAA,MAC1E,SAASA,IAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,WAAW;AAM1B,UAAM,qBAAqB,UAAU,YAAY,GAAI;AACrD,QAAI,mBAAmB,UAAU,QAAQ,GAAG;AAC5C,QAAI,mBAAmB,GAAG;AACxB,yBAAmB;AAAA,IACrB;AACA,UAAM,WAAW,UAAU,UAAU,qBAAqB,GAAG,gBAAgB;AAC7E,UAAM,YAAY,SAAS,MAAM,GAAG;AACpC,QAAI,UAAU,WAAW,GAAG;AAC1B,aAAO;AAAA,QACL,UAAU,UAAU,CAAC;AAAA,QACrB,YAAY,CAAC,UAAU,CAAC;AAAA,QACxB,cAAc,CAAC,UAAU,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,qBAAqB,WAAW;AAI9B,QAAI,qBAAqB,UAAU,QAAQ,GAAG;AAC9C,QAAI,qBAAqB,GAAG;AAC1B,2BAAqB,UAAU,YAAY,GAAG;AAC9C,UAAI,qBAAqB,GAAG;AAC1B,6BAAqB,UAAU,YAAY,GAAG;AAAA,MAChD;AAAA,IACF;AACA,QAAI,mBAAmB,UAAU,QAAQ,GAAG;AAC5C,QAAI,mBAAmB,GAAG;AACxB,yBAAmB;AAAA,IACrB;AACA,WAAO,UAAU,UAAU,qBAAqB,GAAG,gBAAgB;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,WAAW;AAC9B,UAAM,OAAO,KAAK,qBAAqB,SAAS;AAChD,UAAM,cAAc,KAAK,UAAU,GAAG,KAAK,YAAY,GAAG,CAAC;AAC3D,WAAO,YAAY,UAAU,GAAG,YAAY,YAAY,GAAG,CAAC,IAAI;AAAA,EAClE;AAAA,EACA,WAAW,WAAW,UAAU;AAI9B,QAAI,kBAAkB,GAEpB,iBAAiB,GAEjB,mBAAmB;AACrB,UAAM,QAAQ,UAAU,SAAS,MAAM,GAAG;AAC1C,aAAS,YAAY,GAAG,YAAY,MAAM,QAAQ,aAAa;AAE7D,UAAI,sBAAsB;AAE1B,YAAM,UAAU,MAAM,SAAS,EAAE,MAAM,GAAG;AAC1C,eAAS,cAAc,GAAG,cAAc,QAAQ,QAAQ,eAAe;AACrE,cAAM,iBAAqB,OAAO,QAAQ,WAAW,CAAC;AACtD,YAAI,eAAe,UAAU,GAAG;AAE9B,iCAAuB,eAAe,CAAC;AACvC,6BAAmB,eAAe,CAAC;AACnC,4BAAkB,eAAe,CAAC;AAClC,8BAAoB,eAAe,CAAC;AAAA,QACtC;AAEA,YAAI,cAAc,SAAS,YAAY;AACrC,cAAI,wBAAwB,SAAS,cAAc;AAEjD,mBAAO;AAAA,cACL,UAAU,UAAU,QAAQ,eAAe;AAAA,cAC3C,YAAY;AAAA,cACZ,cAAc;AAAA,YAChB;AAAA,UACF,WAAW,cAAc,MAAM,QAAQ,QAAQ;AAE7C,mBAAO;AAAA,cACL,UAAU,UAAU,QAAQ,eAAe;AAAA,cAC3C,YAAY;AAAA,cACZ,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,mBAAmB,cAAc;AAC5C,UAAM,MAAM,IAAI,YAAY,OAAO,iBAAiB;AACpD,UAAM,kBAAkB,GAAG,aAAa,QAAQ,IAAI,aAAa,UAAU,IAAI,aAAa,YAAY;AAExG,QAAI,KAAK,iBAAiB,IAAI,eAAe,GAAG;AAC9C,aAAO,KAAK,iBAAiB,IAAI,eAAe;AAAA,IAClD;AAEA,QAAI,CAAC,KAAK,eAAe,IAAI,iBAAiB,GAAG;AAC/C,UAAI,CAAC,KAAK,aAAa;AACrB,gBAAQ,MAAM,0GAA2G;AACzH,aAAK,eAAe,IAAI,mBAAmB,GAAG,IAAI,CAAC;AAAA,MACrD,OAAO;AAEL,aAAK,eAAe,IAAI,mBAAmB,KAAK,YAAY,OAAO,GAAG,EAAE,KAAK,OAAO,OAAK,aAAa,YAAY,GAAG,IAAI,kBAAgB,aAAa,IAAI,GAAG,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAAA,MACxL;AAAA,IACF;AAEA,UAAM,eAAe,KAAK,eAAe,IAAI,iBAAiB,EAAE,KAAK,IAAI,eAAa;AAEpF,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,WAAW,WAAW,YAAY;AAAA,IAChD,CAAC,GAAG,WAAW,MAAM,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAEtD,SAAK,iBAAiB,IAAI,iBAAiB,YAAY;AACvD,WAAO;AAAA,EACT;AACF;AAEA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,SAAY,aAAa,CAAC,CAAC;AACzF;AAEA,uBAAuB,QAA0B,mBAAmB;AAAA,EAClE,OAAO;AAAA,EACP,SAAS,uBAAuB;AAClC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAKH,IAAM,gCAAgC;AACtC,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,iBAAiB,QAAQ;AACvB,UAAM,mBAAmB,OAAM,oBAAI,KAAK,GAAE,YAAY;AACtD,QAAI,OAAO,iBAAiB;AAC1B,UAAI,CAAC,KAAK,UAAU;AAClB,gBAAQ,MAAM,sGAAuG;AACrH,eAAO,iBAAiB;AAAA,MAC1B,OAAO;AACL,eAAO,KAAK,SAAS,UAAU,oBAAI,KAAK,GAAG,OAAO,eAAe;AAAA,MACnE;AAAA,IACF;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,YAAY,OAAO,QAAQ,SAAS,YAAY;AAC9C,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAGA,QAAI,WAAW,OAAO,YAAY,YAAY;AAC5C,eAAS,UAAU,QAAQ;AAAA,IAC7B,OAAO;AACL,eAAS,UAAU;AAAA,IACrB;AACA,aAAS,YAAY,KAAK,iBAAiB,MAAM;AACjD,WAAO;AAAA,EACT;AACF;AAEA,yBAAyB,OAAO,SAAS,iCAAiC,mBAAmB;AAC3F,SAAO,KAAK,qBAAqB,0BAA6B,SAAc,UAAU,CAAC,CAAC;AAC1F;AAEA,yBAAyB,QAA0B,mBAAmB;AAAA,EACpE,OAAO;AAAA,EACP,SAAS,yBAAyB;AACpC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAW;AAAA,MACX,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAOH,IAAM,mBAAN,MAAuB;AAAC;AAKxB,IAAM,6BAA6B;AACnC,IAAM,wBAAN,MAA4B;AAAA,EAC1B,iBAAiB,OAAO,QAAQ,SAAS,YAAY;AACnD,WAAO,CAAC,OAAO,yBAAyB,SAAS,OAAO;AAAA,EAC1D;AAAA,EACA,iBAAiB,OAAO,QAAQ,SAAS,YAAY;AACnD,WAAO,CAAC,CAAC,OAAO,oBAAoB,SAAS,OAAO;AAAA,EACtD;AAAA,EACA,kBAAkB,OAAO,QAAQ,SAAS,YAAY;AAEpD,WAAO,KAAK,iBAAiB,OAAO,QAAQ,SAAS,UAAU,KAAK,KAAK,iBAAiB,OAAO,QAAQ,SAAS,UAAU;AAAA,EAC9H;AACF;AAEA,sBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,SAAO,KAAK,qBAAqB,uBAAuB;AAC1D;AAEA,sBAAsB,QAA0B,mBAAmB;AAAA,EACjE,OAAO;AAAA,EACP,SAAS,sBAAsB;AACjC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,8BAA8B;AACpC,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,aAAa,QAAQ;AAC/B,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,mBAAmB,CAAC;AACzB,SAAK,gBAAgB,IAAI,gBAAgB,KAAK;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,SAAS;AAC5B,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,YAAY;AACjC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,KAAK;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,YAAY;AACrC,QAAI,eAAe,QAAQ,eAAe,QAAW;AACnD,aAAO;AAAA,IACT;AACA,WAAO,WAAW,IAAI,CAAC,MAAM,QAAQ;AACnC,UAAI;AACF,YAAI,gBAAgB,OAAO;AACzB,iBAAO,KAAK,kBAAkB,IAAI;AAAA,QACpC;AAEA,YAAI,OAAO,SAAS,UAAU;AAC5B,eAAK,UAAU,IAAI;AAAA,QACrB;AACA,eAAO;AAAA,MACT,SAAS,GAAG;AACV,eAAO,kBAAkB,GAAG;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,SAAS;AACrB,QAAI;AACF,UAAI,mBAAmB,OAAO;AAC5B,eAAO,KAAK,kBAAkB,OAAO;AAAA,MACvC;AACA,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,KAAK,UAAU,SAAS,MAAM,CAAC;AAAA,MAC3C;AAAA,IACF,SAAS,GAAG;AACV,gBAAU;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,aAAa;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,KAAK,YAAY,SAAS;AACpC,QAAI,CAAC,KAAK,aAAa;AACrB,cAAQ,MAAM,0GAA2G;AACzH,aAAO,GAAG,IAAI;AAAA,IAChB;AAGA,QAAI,iBAAiB,IAAI,YAAY,QAAQ,KAAK,YAAY,WAAW,CAAC,CAAC;AAC3E,QAAI,eAAe,GAAG,cAAc;AACpC,UAAM,iBAAiB,KAAK,iBAAiB,cAAc;AAC3D,QAAI,aAAa,cAAc,GAAG;AAChC,qBAAe;AAAA,IACjB,WAAW,gBAAgB;AACzB,qBAAe,GAAG,cAAc;AAAA,IAClC,OAAO;AACL,cAAQ,KAAK,qFAAqF;AAAA,IACpG;AACA,WAAO,aAAa,KAAK,UAAU,SAAO;AACxC,UAAI,CAAC,KAAK;AACR,gBAAQ,KAAK,kGAAkG;AAC/G,eAAO,KAAK,YAAY,OAAO,cAAc;AAAA,MAC/C;AACA,aAAO,KAAK,YAAY,OAAO,GAAG;AAAA,IACpC,CAAC,GAAG,OAAO,OAAK,aAAa,YAAY,GAAG,IAAI,kBAAgB,aAAa,IAAI,CAAC;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,UAAU;AAE7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,QAAQ;AACjB,SAAK,cAAc,KAAK,IAAI;AAE5B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,YAAY;AACjC,WAAK,kBAAkB;AAAA,IACzB;AACA,QAAI,CAAC,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,SAAS,GAAG;AAC/D,WAAK,mBAAmB,KAAK,kBAAkB,MAAM;AAAA,IACvD;AACA,SAAK,mBAAmB,CAAC;AACzB,SAAK,cAAc,KAAK,KAAK;AAAA,EAC/B;AAAA,EACA,mBAAmB,UAAU,QAAQ;AACnC,QAAI;AACJ,UAAM,iBAAiB,eAAa;AAElC,YAAM,kBAAkB,mBACnB;AAEL,sBAAgB,aAAa,KAAK,2BAA2B,gBAAgB,UAAU;AACvF,sBAAgB,UAAU,KAAK,cAAc,gBAAgB,OAAO;AACpE,aAAO;AAAA,IACT;AACA,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,oBAAc,CAAC;AACf,eAAS,QAAQ,OAAK;AACpB,oBAAY,KAAK,eAAe,CAAC,CAAC;AAAA,MACpC,CAAC;AAAA,IACH,OAAO;AACL,oBAAc,eAAe,QAAQ;AAAA,IACvC;AAEA,kBAAc,KAAK,qBAAqB,WAAW;AACnD,UAAM,UAAU,OAAO,qBAAqB,IAAI,YAAY;AAC5D,QAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AAChC,cAAQ,IAAI,gBAAgB,kBAAkB;AAAA,IAChD;AACA,UAAM,oBAAoB,MAAM;AAC9B,WAAK,YAAY,OAAO,kBAAkB,aAAa;AAAA,QACrD;AAAA,QACA,QAAQ,OAAO,oBAAoB,IAAI,WAAW;AAAA,QAClD,cAAc,OAAO,oBAAoB;AAAA,QACzC,iBAAiB,OAAO,mBAAmB;AAAA,MAC7C,CAAC,EAAE,KAAK,WAAW,SAAO;AAExB,gBAAQ,MAAM,sCAAsC,GAAG;AACvD,eAAO,WAAW,GAAG;AAAA,MACvB,CAAC,CAAC,EAAE,UAAU;AAAA,IAChB;AACA,QAAI,OAAO,6BAA6B,MAAM;AAC5C,UAAI,CAAC,KAAK,QAAQ;AAChB,gBAAQ,MAAM,+EAA+E;AAC7F;AAAA,MACF;AACA,WAAK,OAAO,kBAAkB,iBAAiB;AAAA,IACjD,OAAO;AACL,wBAAkB;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,UAAU,QAAQ;AAE7B,SAAK,CAAC,OAAO,wBAAwB,OAAO,wBAAwB,OAAO,CAAC,OAAO,oBAAoB,OAAO,oBAAoB,IAAI;AACpI,WAAK,mBAAmB,UAAU,MAAM;AACxC;AAAA,IACF;AACA,UAAM,sBAAsB,MAAM;AAChC,WAAK,iBAAiB,KAAK,mBACtB,SACJ;AAED,UAAI,CAAC,CAAC,OAAO,wBAAwB,KAAK,iBAAiB,SAAS,OAAO,sBAAsB;AAC/F,aAAK,WAAW,MAAM;AAAA,MACxB;AAEA,UAAI,OAAO,mBAAmB,KAAK,CAAC,KAAK,iBAAiB;AACxD,aAAK,kBAAkB,MAAM,OAAO,gBAAgB,EAAE,UAAU,OAAK;AACnE,eAAK,WAAW,MAAM;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,KAAK,cAAc,UAAU,MAAM;AACrC,WAAK,cAAc,KAAK,OAAO,QAAM,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,OAAK;AAC1E,4BAAoB;AAAA,MACtB,CAAC;AAAA,IACH,OAAO;AACL,0BAAoB;AAAA,IACtB;AAAA,EACF;AACF;AAEA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,SAAY,aAAa,CAAC,GAAM,SAAY,QAAQ,CAAC,CAAC;AACpH;AAEA,uBAAuB,QAA0B,mBAAmB;AAAA,EAClE,OAAO;AAAA,EACP,SAAS,uBAAuB;AAClC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAKH,IAAM,8BAA8B;AACpC,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACzB,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,CAAC,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,CAAC,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,CAAC,IAAI;AAC7C,EAAAA,gBAAeA,gBAAe,KAAK,IAAI,CAAC,IAAI;AAC5C,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,CAAC,IAAI;AAC7C,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,CAAC,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,CAAC,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,KAAK,IAAI,CAAC,IAAI;AAC9C,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAM,uBAAuB,CAAC,UAAU,QAAQ,QAAQ,QAAQ,OAAO,OAAO,KAAK;AACnF,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,YAAY;AACtB,SAAK,aAAa;AAElB,SAAK,yBAAyB,CAAC,KAAK,qBAAqB,KAAK,iBAAiB,KAAK,uBAAuB,KAAK,iBAAiB;AACjI,SAAK,OAAO,kBAAkB,UAAU,KAAK,aAAa,UAAU,aAAa,CAAC,EAAE,UAAU,UAAU,QAAQ,MAAM,MAAM,MAAM,UAAU,UAAU,MAAM,WAAW,KAAK,UAAU,UAAU,MAAM,QAAQ;AAC9M,SAAK,UAAU,KAAK,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,EAC7E;AAAA,EACA,oBAAoB,UAAU,QAAQ;AACpC,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,gBAAgB,UAAU,QAAQ;AAChC,WAAO,eAAe,SAAS,KAAK;AAAA,EACtC;AAAA,EACA,sBAAsB,UAAU,QAAQ;AACtC,WAAO,OAAO,uBAAuB,OAAO,KAAK,IAAI,SAAS,QAAQ,IAAI,SAAS,UAAU,IAAI,SAAS,YAAY;AAAA,EACxH;AAAA,EACA,kBAAkB,UAAU,QAAQ;AAClC,WAAO,OAAO,UAAU,IAAI,OAAO,OAAO,MAAM;AAAA,EAClD;AAAA;AAAA,EAEA,kBAAkB,UAAU,QAAQ;AAClC,QAAI,aAAa;AACjB,SAAK,uBAAuB,QAAQ,2BAAyB;AAC3D,YAAM,WAAW,sBAAsB,UAAU,MAAM;AACvD,UAAI,UAAU;AACZ,qBAAa,aAAa,MAAM;AAAA,MAClC;AAAA,IACF,CAAC;AACD,WAAO,WAAW,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,SAAS,UAAU,QAAQ;AACzB,UAAM,oBAAoB,OAAO,eAAe;AAEhD,QAAI,SAAS,UAAU,eAAe,KAAK;AACzC,aAAO;AAAA,IACT;AACA,WAAO,kBAAkB,SAAS,KAAK;AAAA,EACzC;AAAA;AAAA,EAEA,MAAM,UAAU,QAAQ,YAAY;AAGlC,UAAM,aAAa,SAAS,cAAc,CAAC;AAC3C,YAAQ,SAAS,OAAO;AAAA,MACtB,KAAK,eAAe;AAClB,gBAAQ,KAAK,GAAG,UAAU,KAAK,SAAS,SAAS,GAAG,UAAU;AAC9D;AAAA,MACF,KAAK,eAAe;AAAA,MACpB,KAAK,eAAe;AAClB,gBAAQ,MAAM,GAAG,UAAU,KAAK,SAAS,SAAS,GAAG,UAAU;AAC/D;AAAA,MACF,KAAK,eAAe;AAClB,gBAAQ,KAAK,GAAG,UAAU,KAAK,SAAS,SAAS,GAAG,UAAU;AAC9D;AAAA,MACF;AACE,gBAAQ,IAAI,GAAG,UAAU,KAAK,SAAS,SAAS,GAAG,UAAU;AAAA,IACjE;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,UAAU,QAAQ,YAAY;AACtC,UAAM,QAAQ,KAAK,SAAS,UAAU,MAAM;AAE5C,UAAM,aAAa,SAAS,cAAc,CAAC;AAC3C,YAAQ,SAAS,OAAO;AAAA,MACtB,KAAK,eAAe;AAClB,gBAAQ,KAAK,KAAK,UAAU,IAAI,SAAS,KAAK,IAAI,SAAS,SAAS,GAAG,UAAU;AACjF;AAAA,MACF,KAAK,eAAe;AAAA,MACpB,KAAK,eAAe;AAClB,gBAAQ,MAAM,KAAK,UAAU,IAAI,SAAS,KAAK,IAAI,SAAS,SAAS,GAAG,UAAU;AAClF;AAAA,MACF,KAAK,eAAe;AAClB,gBAAQ,KAAK,KAAK,UAAU,IAAI,SAAS,KAAK,IAAI,SAAS,SAAS,GAAG,UAAU;AACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMF,KAAK,eAAe;AAClB,gBAAQ,MAAM,KAAK,UAAU,IAAI,SAAS,KAAK,IAAI,SAAS,SAAS,GAAG,UAAU;AAClF;AAAA,MACF;AACE,gBAAQ,IAAI,KAAK,UAAU,IAAI,SAAS,KAAK,IAAI,SAAS,SAAS,GAAG,UAAU;AAAA,IACpF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,UAAU,QAAQ;AAC7B,UAAM,aAAa,KAAK,kBAAkB,UAAU,MAAM;AAC1D,SAAK,QAAQ,UAAU,QAAQ,UAAU;AAAA,EAC3C;AACF;AAEA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,SAAS,WAAW,CAAC;AACnF;AAEA,uBAAuB,QAA0B,mBAAmB;AAAA,EAClE,OAAO;AAAA,EACP,SAAS,uBAAuB;AAClC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,QAAQ,qBAAqB,iBAAiB,aAAa,eAAe,eAAe,eAAe;AAClH,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,eAAe,oBAAoB,oBAAoB,MAAM;AAAA,EACpE;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,MAAM,YAAY,YAAY;AAC5B,SAAK,KAAK,eAAe,OAAO,SAAS,UAAU;AAAA,EACrD;AAAA,EACA,MAAM,YAAY,YAAY;AAC5B,SAAK,KAAK,eAAe,OAAO,SAAS,UAAU;AAAA,EACrD;AAAA,EACA,KAAK,YAAY,YAAY;AAC3B,SAAK,KAAK,eAAe,MAAM,SAAS,UAAU;AAAA,EACpD;AAAA,EACA,IAAI,YAAY,YAAY;AAC1B,SAAK,KAAK,eAAe,KAAK,SAAS,UAAU;AAAA,EACnD;AAAA,EACA,KAAK,YAAY,YAAY;AAC3B,SAAK,KAAK,eAAe,MAAM,SAAS,UAAU;AAAA,EACpD;AAAA,EACA,MAAM,YAAY,YAAY;AAC5B,SAAK,KAAK,eAAe,OAAO,SAAS,UAAU;AAAA,EACrD;AAAA,EACA,MAAM,YAAY,YAAY;AAC5B,SAAK,KAAK,eAAe,OAAO,SAAS,UAAU;AAAA,EACrD;AAAA;AAAA,EAEA,qBAAqB,SAAS;AAC5B,UAAM,SAAS,KAAK,kBAAkB;AACtC,WAAO,oBAAoB;AAC3B,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM,SAAS,KAAK,kBAAkB;AACtC,WAAO,mBAAmB;AAC1B,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA;AAAA,EAEA,8BAA8B,iBAAiB;AAC7C,UAAM,SAAS,KAAK,kBAAkB;AACtC,WAAO,kBAAkB;AACzB,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,SAAS;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,QAAQ;AACnB,SAAK,aAAa,aAAa,MAAM;AAAA,EACvC;AAAA,EACA,oBAAoB,eAAe;AACjC,SAAK,aAAa,oBAAoB,aAAa;AAAA,EACrD;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACjB,SAAK,cAAc,WAAW,KAAK,kBAAkB,CAAC;AAAA,EACxD;AAAA,EACA,KAAK,OAAO,SAAS,aAAa,CAAC,GAAG;AACpC,UAAM,SAAS,KAAK,aAAa,UAAU;AAC3C,UAAM,mBAAmB,KAAK,YAAY,iBAAiB,OAAO,QAAQ,SAAS,UAAU;AAC7F,UAAM,mBAAmB,KAAK,YAAY,iBAAiB,OAAO,QAAQ,SAAS,UAAU;AAC7F,UAAM,oBAAoB,KAAK,YAAY,kBAAkB,OAAO,QAAQ,SAAS,UAAU;AAC/F,QAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,mBAAmB;AAEhE;AAAA,IACF;AACA,UAAM,WAAW,KAAK,gBAAgB,YAAY,OAAO,QAAQ,SAAS,UAAU;AACpF,SAAK,cAAc,eAAe,QAAQ,QAAQ,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,iBAAe;AACzF,UAAI,aAAa;AACf,iBAAS,WAAW,YAAY;AAChC,iBAAS,aAAa,YAAY;AAClC,iBAAS,eAAe,YAAY;AAAA,MACtC;AACA,UAAI,qBAAqB,KAAK,gBAAgB;AAC5C,aAAK,eAAe,MAAM,UAAU,MAAM;AAAA,MAC5C;AACA,UAAI,kBAAkB;AACpB,aAAK,cAAc,aAAa,UAAU,MAAM;AAAA,MAClD;AACA,UAAI,kBAAkB;AACpB,aAAK,cAAc,aAAa,UAAU,MAAM;AAAA,MAClD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,UAAU,OAAO,SAAS,kBAAkB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,WAAc,SAAS,mBAAmB,GAAM,SAAS,kCAAkC,GAAM,SAAS,6BAA6B,GAAM,SAAS,0BAA0B,GAAM,SAAS,2BAA2B,GAAM,SAAS,2BAA2B,GAAM,SAAS,2BAA2B,CAAC;AAClV;AAEA,UAAU,QAA0B,mBAAmB;AAAA,EACrD,OAAO;AAAA,EACP,SAAS,UAAU;AAAA,EACnB,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,mBAAmB;AAAA,MAC5B,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,kCAAkC;AAAA,MAC3C,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,6BAA6B;AAAA,MACtC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,0BAA0B;AAAA,MACnC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,2BAA2B;AAAA,MACpC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,2BAA2B;AAAA,MACpC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,2BAA2B;AAAA,MACpC,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAKH,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,QAAQ,qBAAqB,iBAAiB,aAAa,eAAe,eAAe,eAAe;AAClH,SAAK,SAAS;AACd,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,QAAQ,eAAe,YAAY,eAAe;AACvD,WAAO,KAAK,eAAe;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,QAAQ;AACrB,UAAM,SAAS,IAAI,UAAU,QAAQ,UAAU,KAAK,OAAO,kBAAkB,GAAG,QAAQ,uBAAuB,KAAK,qBAAqB,QAAQ,mBAAmB,KAAK,iBAAiB,QAAQ,eAAe,KAAK,aAAa,QAAQ,iBAAiB,KAAK,eAAe,QAAQ,iBAAiB,KAAK,eAAe,QAAQ,iBAAiB,KAAK,aAAa;AACxW,QAAI,QAAQ,eAAe;AACzB,aAAO,oBAAoB,OAAO,aAAa;AAAA,IACjD;AACA,QAAI,QAAQ,YAAY;AACtB,aAAO,gBAAgB,OAAO,UAAU;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACF;AAEA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,SAAS,SAAS,GAAM,SAAS,kCAAkC,GAAM,SAAS,6BAA6B,GAAM,SAAS,0BAA0B,GAAM,SAAS,2BAA2B,GAAM,SAAS,2BAA2B,GAAM,SAAS,2BAA2B,CAAC;AACrV;AAEA,uBAAuB,QAA0B,mBAAmB;AAAA,EAClE,OAAO;AAAA,EACP,SAAS,uBAAuB;AAAA,EAChC,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,kCAAkC;AAAA,MAC3C,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,6BAA6B;AAAA,MACtC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,0BAA0B;AAAA,MACnC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,2BAA2B;AAAA,MACpC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,2BAA2B;AAAA,MACpC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,2BAA2B;AAAA,MACpC,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,QAAQ,QAAQ,gBAAgB;AACrC,QAAI,CAAC,gBAAgB;AACnB,uBAAiB,CAAC;AAAA,IACpB;AAEA,QAAI,CAAC,eAAe,gBAAgB;AAClC,qBAAe,iBAAiB;AAAA,QAC9B,SAAS;AAAA,QACT,UAAU,UAAU,CAAC;AAAA,MACvB;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,eAAe,YAAY,qBAAqB;AACjE,cAAM,IAAI,MAAM,0DAA0D,mBAAmB,iBAAiB,eAAe,eAAe,OAAO,EAAE;AAAA,MACvJ;AAAA,IACF;AAEA,QAAI,CAAC,eAAe,6BAA6B;AAC/C,qBAAe,8BAA8B;AAAA,QAC3C,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,4BAA4B,YAAY,oCAAoC;AAC7F,cAAM,IAAI,MAAM,wEAAwE,kCAAkC,mBAAmB,eAAe,4BAA4B,OAAO,GAAG;AAAA,MACpM;AAAA,IACF;AAEA,QAAI,CAAC,eAAe,kBAAkB;AACpC,qBAAe,mBAAmB;AAAA,QAChC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,iBAAiB,YAAY,+BAA+B;AAC7E,cAAM,IAAI,MAAM,6DAA6D,6BAA6B,mBAAmB,eAAe,iBAAiB,OAAO,GAAG;AAAA,MACzK;AAAA,IACF;AAEA,QAAI,CAAC,eAAe,cAAc;AAChC,qBAAe,eAAe;AAAA,QAC5B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,aAAa,YAAY,4BAA4B;AACtE,cAAM,IAAI,MAAM,yDAAyD,0BAA0B,mBAAmB,eAAe,aAAa,OAAO,GAAG;AAAA,MAC9J;AAAA,IACF;AAEA,QAAI,CAAC,eAAe,gBAAgB;AAClC,qBAAe,iBAAiB;AAAA,QAC9B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,eAAe,YAAY,6BAA6B;AACzE,cAAM,IAAI,MAAM,2DAA2D,2BAA2B,mBAAmB,eAAe,eAAe,OAAO,GAAG;AAAA,MACnK;AAAA,IACF;AAEA,QAAI,CAAC,eAAe,gBAAgB;AAClC,qBAAe,iBAAiB;AAAA,QAC9B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,eAAe,YAAY,6BAA6B;AACzE,cAAM,IAAI,MAAM,2DAA2D,2BAA2B,mBAAmB,eAAe,eAAe,OAAO,GAAG;AAAA,MACnK;AAAA,IACF;AAEA,QAAI,CAAC,eAAe,gBAAgB;AAClC,qBAAe,iBAAiB;AAAA,QAC9B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AAEL,UAAI,eAAe,eAAe,YAAY,6BAA6B;AACzE,cAAM,IAAI,MAAM,2DAA2D,2BAA2B,mBAAmB,eAAe,eAAe,OAAO,GAAG;AAAA,MACnK;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,WAAW,eAAe,gBAAgB,eAAe,6BAA6B,eAAe,kBAAkB,eAAe,cAAc,eAAe,gBAAgB,eAAe,gBAAgB,eAAe,gBAAgB,sBAAsB;AAAA,IACrR;AAAA,EACF;AAAA,EACA,OAAO,WAAW;AAGhB,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AAEA,aAAa,OAAO,SAAS,qBAAqB,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,cAAc;AACjD;AAEA,aAAa,OAAyB,iBAAiB;AAAA,EACrD,MAAM;AAAA,EACN,SAAS,CAAC,YAAY;AACxB,CAAC;AAED,aAAa,OAAyB,iBAAiB;AAAA,EACrD,SAAS,CAAC,CAAC,YAAY,CAAC;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["e", "NgxLoggerLevel"]}