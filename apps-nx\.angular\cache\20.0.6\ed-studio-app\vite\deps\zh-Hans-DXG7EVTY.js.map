{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/zh-Hans.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"zh-<PERSON>\", [[\"上午\", \"下午\"], u, u], u, [[\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"], [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"], [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"], [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\", \"10月\", \"11月\", \"12月\"], [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"]], u, [[\"公元前\", \"公元\"], u, u], 0, [6, 0], [\"y/M/d\", \"y年M月d日\", u, \"y年M月d日EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"z HH:mm:ss\", \"zzzz HH:mm:ss\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"CNY\", \"¥\", \"人民币\", { \"AUD\": [\"AU$\", \"$\"], \"BYN\": [u, \"р.\"], \"CNY\": [\"¥\"], \"ILR\": [\"ILS\"], \"JPY\": [\"JP¥\", \"¥\"], \"KRW\": [\"￦\", \"₩\"], \"PHP\": [u, \"₱\"], \"RUR\": [u, \"р.\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"], \"XXX\": [] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI;AACV,SAAO;AACX;AACA,IAAO,kBAAQ,CAAC,WAAW,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,UAAU,GAAG,YAAY,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,aAAa,KAAK,GAAG,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,MAAM;", "names": []}