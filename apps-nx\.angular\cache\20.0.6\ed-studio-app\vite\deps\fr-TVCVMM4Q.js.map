{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/fr.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n    if (i === 0 || i === 1)\n        return 1;\n    if (e === 0 && (!(i === 0) && (i % 1000000 === 0 && v === 0)) || !(e >= 0 && e <= 5))\n        return 4;\n    return 5;\n}\nexport default [\"fr\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"], [\"dimanche\", \"lundi\", \"mardi\", \"mercredi\", \"jeudi\", \"vendredi\", \"samedi\"], [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"janv.\", \"févr.\", \"mars\", \"avr.\", \"mai\", \"juin\", \"juil.\", \"août\", \"sept.\", \"oct.\", \"nov.\", \"déc.\"], [\"janvier\", \"février\", \"mars\", \"avril\", \"mai\", \"juin\", \"juillet\", \"août\", \"septembre\", \"octobre\", \"novembre\", \"décembre\"]], u, [[\"av. J.-C.\", \"ap. J.-C.\"], u, [\"avant Jésus-Christ\", \"après Jésus-Christ\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", \"{1}, {0}\", \"{1} 'à' {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"ARS\": [\"$AR\", \"$\"], \"AUD\": [\"$AU\", \"$\"], \"BEF\": [\"FB\"], \"BMD\": [\"$BM\", \"$\"], \"BND\": [\"$BN\", \"$\"], \"BYN\": [u, \"р.\"], \"BZD\": [\"$BZ\", \"$\"], \"CAD\": [\"$CA\", \"$\"], \"CLP\": [\"$CL\", \"$\"], \"CNY\": [u, \"¥\"], \"COP\": [\"$CO\", \"$\"], \"CYP\": [\"£CY\"], \"EGP\": [u, \"£E\"], \"FJD\": [\"$FJ\", \"$\"], \"FKP\": [\"£FK\", \"£\"], \"FRF\": [\"F\"], \"GBP\": [\"£GB\", \"£\"], \"GIP\": [\"£GI\", \"£\"], \"HKD\": [u, \"$\"], \"IEP\": [\"£IE\"], \"ILP\": [\"£IL\"], \"ITL\": [\"₤IT\"], \"JPY\": [u, \"¥\"], \"KMF\": [u, \"FC\"], \"LBP\": [\"£LB\", \"£L\"], \"MTP\": [\"£MT\"], \"MXN\": [\"$MX\", \"$\"], \"NAD\": [\"$NA\", \"$\"], \"NIO\": [u, \"$C\"], \"NZD\": [\"$NZ\", \"$\"], \"PHP\": [u, \"₱\"], \"RHD\": [\"$RH\"], \"RON\": [u, \"L\"], \"RWF\": [u, \"FR\"], \"SBD\": [\"$SB\", \"$\"], \"SGD\": [\"$SG\", \"$\"], \"SRD\": [\"$SR\", \"$\"], \"TOP\": [u, \"$T\"], \"TTD\": [\"$TT\", \"$\"], \"TWD\": [u, \"NT$\"], \"USD\": [\"$US\", \"$\"], \"UYU\": [\"$UY\", \"$\"], \"WST\": [\"$WS\"], \"XCD\": [u, \"$\"], \"XPF\": [\"FCFP\"], \"ZMW\": [u, \"Kw\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxK,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,MAAI,MAAM,MAAM,EAAE,MAAM,OAAO,IAAI,QAAY,KAAK,MAAM,OAAO,EAAE,KAAK,KAAK,KAAK;AAC9E,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,YAAY,SAAS,SAAS,YAAY,SAAS,YAAY,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,SAAS,QAAQ,QAAQ,OAAO,QAAQ,SAAS,QAAQ,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,WAAW,QAAQ,SAAS,OAAO,QAAQ,WAAW,QAAQ,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,WAAW,GAAG,GAAG,CAAC,sBAAsB,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,WAAW,YAAY,eAAe,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,WAAW,YAAY,eAAe,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,EAAE,GAAG,OAAO,MAAM;", "names": []}