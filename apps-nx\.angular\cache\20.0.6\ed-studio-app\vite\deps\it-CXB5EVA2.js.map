{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/it.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n    if (i === 1 && v === 0)\n        return 1;\n    if (e === 0 && (!(i === 0) && (i % 1000000 === 0 && v === 0)) || !(e >= 0 && e <= 5))\n        return 4;\n    return 5;\n}\nexport default [\"it\", [[\"m.\", \"p.\"], [\"AM\", \"PM\"], u], u, [[\"D\", \"L\", \"M\", \"M\", \"G\", \"V\", \"S\"], [\"dom\", \"lun\", \"mar\", \"mer\", \"gio\", \"ven\", \"sab\"], [\"domenica\", \"lunedì\", \"martedì\", \"mercoledì\", \"giovedì\", \"venerdì\", \"sabato\"], [\"dom\", \"lun\", \"mar\", \"mer\", \"gio\", \"ven\", \"sab\"]], u, [[\"G\", \"F\", \"M\", \"A\", \"M\", \"G\", \"L\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"gen\", \"feb\", \"mar\", \"apr\", \"mag\", \"giu\", \"lug\", \"ago\", \"set\", \"ott\", \"nov\", \"dic\"], [\"gennaio\", \"febbraio\", \"marzo\", \"aprile\", \"maggio\", \"giugno\", \"luglio\", \"agosto\", \"settembre\", \"ottobre\", \"novembre\", \"dicembre\"]], u, [[\"aC\", \"dC\"], [\"a.C.\", \"d.C.\"], [\"avanti Cristo\", \"dopo Cristo\"]], 1, [6, 0], [\"dd/MM/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"BRL\": [u, \"R$\"], \"BYN\": [u, \"Br\"], \"EGP\": [u, \"£E\"], \"HKD\": [u, \"$\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NOK\": [u, \"NKr\"], \"THB\": [\"฿\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxK,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,MAAI,MAAM,MAAM,EAAE,MAAM,OAAO,IAAI,QAAY,KAAK,MAAM,OAAO,EAAE,KAAK,KAAK,KAAK;AAC9E,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,YAAY,UAAU,WAAW,aAAa,WAAW,WAAW,QAAQ,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,YAAY,SAAS,UAAU,UAAU,UAAU,UAAU,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,iBAAiB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,WAAW,YAAY,eAAe,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,MAAM;", "names": []}