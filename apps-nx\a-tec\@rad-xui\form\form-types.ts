import { Signal } from "@angular/core";
import { FormGroup } from "@angular/forms";
import { FormlyFieldConfig, FormlyFieldProps } from "@ngx-formly/core";
import { Observable } from "rxjs";

export interface IEditForm {
  form: FormGroup;
  validate(): boolean;
}

export interface FormFieldProps extends FormlyFieldProps {
  [additionalProperties: string]: any;
}

  export interface RadFormFieldProps extends FormlyFieldProps {
  hideRequiredMarker?: boolean;
  hideLabel?: boolean;
  infoMode?: boolean;
  labelWidth?: string;
  layout?: 'horizontal' | 'vertical';
  dateFormat?: string;

}

export type FormFieldType = 'input'|'checkbox'|'hidden'|'select'|'multiselect'|'treeselect'|'textarea'|'date'|'datetime'|'datepicker'|'password';
export type RadFormConfig = RadFormItem[];

export type AllFieldProps = RadFormFieldProps|SelectFieldProps; 
export interface RadFormItem extends FormlyFieldConfig {
    key?: string;  
    type?: FormFieldType | string;
    label?: string;
    rowNum?: number;
    col?: number;
    span?: number;
    required?: boolean;
    values?: any[] | Observable<any[]>;
    valueProp?: string;
    displayProp?: string;
    hidden?: Signal<boolean>;
    

}

export type LayoutType = 'row'|'col';
export interface LayoutItem{
  layout: LayoutType;
  [key: string|symbol]: any;
}

export type FormItem = RadFormItem|LayoutItem;
export type FormLayout = FormItem[];


export interface FormConfig {
    name?: string
    fields: RadFormConfig
    
}

export interface SelectFieldProps extends FormFieldProps {
    groupProp?: string | ((option: any) => string);
    labelProp?: string | ((option: any) => any);
    valueProp?: string | ((option: any) => boolean);
    disabledProp?: string | ((option: any) => string);
  }
  
  interface TextAreaProps extends FormFieldProps {
    autosize?: boolean;
    autosizeMinRows?: number;
    autosizeMaxRows?: number;
  }
  


  export class ExpressFormConfig{
    name: string;
    fields: FormlyFieldConfig[]
  }



export type FieldLayout = 'horizontal' | 'vertical';

export type FieldMode = 'edit' | 'info';