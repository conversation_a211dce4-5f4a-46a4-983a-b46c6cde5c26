import {
  FullscreenOverlayContainer
} from "./chunk-RVUK5QNM.js";
import {
  BlockScrollStrategy,
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  CloseScrollStrategy,
  ConnectedOverlayPositionChange,
  ConnectionPositionPair,
  FlexibleConnectedPositionStrategy,
  GlobalPositionStrategy,
  NoopScrollStrategy,
  Overlay,
  OverlayConfig,
  OverlayContainer,
  OverlayKeyboardDispatcher,
  OverlayModule,
  OverlayOutsideClickDispatcher,
  OverlayPositionBuilder,
  OverlayRef,
  RepositionScrollStrategy,
  STANDARD_DROPDOWN_ADJACENT_POSITIONS,
  STANDARD_DROPDOWN_BELOW_POSITIONS,
  ScrollStrategyOptions,
  ScrollingVisibility,
  createBlockScrollStrategy,
  createCloseScrollStrategy,
  createFlexibleConnectedPositionStrategy,
  createGlobalPositionStrategy,
  createNoopScrollStrategy,
  createOverlayRef,
  createRepositionScrollStrategy,
  validateHorizontalPosition,
  validateVerticalPosition
} from "./chunk-MRFHGDYA.js";
import "./chunk-XK5UHNJ2.js";
import "./chunk-QCETVJKM.js";
import "./chunk-JVMORM5G.js";
import "./chunk-DQLYDRBB.js";
import "./chunk-LGNNZOMI.js";
import "./chunk-GWE4MCPP.js";
import "./chunk-MPM73DZ3.js";
import {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  ScrollDispatcher,
  ViewportRuler
} from "./chunk-UZB7EAJN.js";
import "./chunk-EOFW2REK.js";
import "./chunk-3KX2S6PK.js";
import {
  Dir
} from "./chunk-5QPZYPEV.js";
import "./chunk-AAWW5C2W.js";
import "./chunk-YFKVMALY.js";
import "./chunk-XJKSSPTD.js";
import "./chunk-QQZDB4KQ.js";
import "./chunk-BYBDDJ2C.js";
import "./chunk-NY55TVYV.js";
import "./chunk-2O4VBYCZ.js";
import "./chunk-GJIVGOXW.js";
import "./chunk-K2JRLIHD.js";
import "./chunk-QDB2FYN3.js";
export {
  BlockScrollStrategy,
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  CdkScrollable,
  CloseScrollStrategy,
  ConnectedOverlayPositionChange,
  ConnectionPositionPair,
  FlexibleConnectedPositionStrategy,
  FullscreenOverlayContainer,
  GlobalPositionStrategy,
  NoopScrollStrategy,
  Overlay,
  OverlayConfig,
  OverlayContainer,
  OverlayKeyboardDispatcher,
  OverlayModule,
  OverlayOutsideClickDispatcher,
  OverlayPositionBuilder,
  OverlayRef,
  RepositionScrollStrategy,
  STANDARD_DROPDOWN_ADJACENT_POSITIONS,
  STANDARD_DROPDOWN_BELOW_POSITIONS,
  ScrollDispatcher,
  ScrollStrategyOptions,
  ScrollingVisibility,
  ViewportRuler,
  createBlockScrollStrategy,
  createCloseScrollStrategy,
  createFlexibleConnectedPositionStrategy,
  createGlobalPositionStrategy,
  createNoopScrollStrategy,
  createOverlayRef,
  createRepositionScrollStrategy,
  validateHorizontalPosition,
  validateVerticalPosition,
  CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll,
  CdkScrollableModule as ɵɵCdkScrollableModule,
  CdkVirtualForOf as ɵɵCdkVirtualForOf,
  CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport,
  CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement,
  CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow,
  Dir as ɵɵDir
};
