{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/pt.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n    if (i === Math.floor(i) && (i >= 0 && i <= 1))\n        return 1;\n    if (e === 0 && (!(i === 0) && (i % 1000000 === 0 && v === 0)) || !(e >= 0 && e <= 5))\n        return 4;\n    return 5;\n}\nexport default [\"pt\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"S\", \"T\", \"Q\", \"Q\", \"S\", \"S\"], [\"dom.\", \"seg.\", \"ter.\", \"qua.\", \"qui.\", \"sex.\", \"sáb.\"], [\"domingo\", \"segunda-feira\", \"terça-feira\", \"quarta-feira\", \"quinta-feira\", \"sexta-feira\", \"sábado\"], [\"dom.\", \"seg.\", \"ter.\", \"qua.\", \"qui.\", \"sex.\", \"sáb.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"fev.\", \"mar.\", \"abr.\", \"mai.\", \"jun.\", \"jul.\", \"ago.\", \"set.\", \"out.\", \"nov.\", \"dez.\"], [\"janeiro\", \"fevereiro\", \"março\", \"abril\", \"maio\", \"junho\", \"julho\", \"agosto\", \"setembro\", \"outubro\", \"novembro\", \"dezembro\"]], u, [[\"a.C.\", \"d.C.\"], u, [\"antes de Cristo\", \"depois de Cristo\"]], 0, [6, 0], [\"dd/MM/y\", \"d 'de' MMM 'de' y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"BRL\", \"R$\", \"Real brasileiro\", { \"AUD\": [\"AU$\", \"$\"], \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"], \"PTE\": [\"Esc.\"], \"RON\": [u, \"L\"], \"SYP\": [u, \"S£\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxK,MAAI,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,KAAK;AACvC,WAAO;AACX,MAAI,MAAM,MAAM,EAAE,MAAM,OAAO,IAAI,QAAY,KAAK,MAAM,OAAO,EAAE,KAAK,KAAK,KAAK;AAC9E,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,iBAAiB,eAAe,gBAAgB,gBAAgB,eAAe,QAAQ,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,aAAa,SAAS,SAAS,QAAQ,SAAS,SAAS,UAAU,YAAY,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,mBAAmB,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,qBAAqB,sBAAsB,0BAA0B,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,MAAM,mBAAmB,EAAE,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,MAAM;", "names": []}