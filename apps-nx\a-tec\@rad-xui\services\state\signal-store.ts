import { SignalState, patchState, signalState } from '@ngrx/signals';
import { IComponentState } from './component.model';
import { computed, inject } from '@angular/core';
import { AppNotificationService, LoggerService } from '@tec/rad-core/abstractions';
import { ApiResult, apiResultFrom } from '@tec/rad-core/utils';
import { Observable } from 'rxjs';

export abstract class RadSignalStore<T extends IComponentState = IComponentState> implements IStore {
  protected notification: AppNotificationService = inject(AppNotificationService);
  protected logger = inject(LoggerService);
  private readonly _state: SignalState<T>;
  get state(): SignalState<T> {
    return this._state;
  }

  loading = computed(() => this._state().loading);

  constructor(initialState: T = {loading: false} as T) {
    const newState = { loading: false, ...initialState } as T;
    this._state = signalState(newState);
  }

  // async initialize(id?: any): Promise<void> {
  //   return;
  // }

  protected patchState(state: Partial<T>) {
    patchState(this._state, state);
  }

  select<R>(selector: (state: SignalState<T>) => R) {
    return computed(() => selector(this._state));
  }

  protected setLoading(loading = true) {
    const patch: Partial<T> = {};
    patch['loading'] = loading;
    this.patchState(patch);
  }

  protected async updateAsync<R>(
    source: Observable<R>,
    updater: (value: R) => Partial<T> = undefined,
    errorMessage: string = undefined,
  ): Promise<ApiResult<R>> {
    this.setLoading(true);
    const error = errorMessage || 'An error occured';
    const result = await apiResultFrom(source);
    this.setLoading(false);
    if (result.isError) {
      this.notifyError(result.error);
      return ApiResult.error(error);
    }
    if (updater) {
      this.patchState(updater(result.value));
    }

    return result;
  }

  protected async executeAsync<R>(source: Observable<R>, updater: (value: R) => void = undefined, errorMessage: string = undefined): Promise<ApiResult<R>> {
    this.setLoading(true);
    const error = errorMessage || 'An error occured';
    const result = await apiResultFrom(source);
    this.setLoading(false);
    if (result.isError) {
      this.notifyError(result.error);
      return ApiResult.error(error);
    }
    if (updater) {
      updater(result.value);
    }

    return result;
  }

  protected async execute<T = any>(action: () => Promise<T>, errorMessage?: string, errorHandler?: (error: any) => void): Promise<T> {
    this.setLoading(true);
    try {
      return await action();
    } catch (error) {
      this.logger.error(error);
      if (errorHandler) {
        errorHandler(error);
      } else {
             this.notifyError(errorMessage || 'An error occurred', 'Error');
      }
      console.error(error);
    } finally {
      this.setLoading(false);
    }
  }

  protected notifyError(message: string, title = 'Error') {
    this.logger.error(message);
    this.notification.error(message, title);
  }

  protected async execute$<T = any>(action: Observable<T>, errorMessage?: string, errorHandler?: (error: any) => void): Promise<T> {
    this.setLoading(true);
    try {
       const result = await apiResultFrom(action);

       if(result.isError) {
          this.logger.error(result.error);
          if (errorHandler) {
            errorHandler(result.error);
          } else {
            this.notifyError(errorMessage || result.error, "Error");
          }
         return undefined;
       }
       return result.value;

    } catch (error) {
        this.logger.error(error);
        if (errorHandler) {
            errorHandler(error);
        } else {
            this.notifyError(errorMessage || "An error occurred", "Error");
        }
    } finally {
        this.setLoading(false);
    }
  }

}

export interface IStore {
  //initialize(id?: any): Promise<void>;
}
