{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/fi.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    return 5;\n}\nexport default [\"fi\", [[\"ap.\", \"ip.\"], u, u], u, [[\"S\", \"M\", \"T\", \"K\", \"T\", \"P\", \"L\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"], [\"sunnuntaina\", \"maanantaina\", \"tiistaina\", \"keskiviikkona\", \"torstaina\", \"perjantaina\", \"lauantaina\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"]], [[\"S\", \"M\", \"T\", \"K\", \"T\", \"P\", \"L\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"], [\"sunnuntai\", \"maanantai\", \"tiistai\", \"keskiviikko\", \"torstai\", \"perjantai\", \"lauantai\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"]], [[\"T\", \"H\", \"M\", \"H\", \"T\", \"K\", \"H\", \"E\", \"S\", \"L\", \"M\", \"J\"], [\"tammik.\", \"helmik.\", \"maalisk.\", \"huhtik.\", \"toukok.\", \"kesäk.\", \"heinäk.\", \"elok.\", \"syysk.\", \"lokak.\", \"marrask.\", \"jouluk.\"], [\"tammikuuta\", \"helmikuuta\", \"maaliskuuta\", \"huhtikuuta\", \"toukokuuta\", \"kesäkuuta\", \"heinäkuuta\", \"elokuuta\", \"syyskuuta\", \"lokakuuta\", \"marraskuuta\", \"joulukuuta\"]], [[\"T\", \"H\", \"M\", \"H\", \"T\", \"K\", \"H\", \"E\", \"S\", \"L\", \"M\", \"J\"], [\"tammi\", \"helmi\", \"maalis\", \"huhti\", \"touko\", \"kesä\", \"heinä\", \"elo\", \"syys\", \"loka\", \"marras\", \"joulu\"], [\"tammikuu\", \"helmikuu\", \"maaliskuu\", \"huhtikuu\", \"toukokuu\", \"kesäkuu\", \"heinäkuu\", \"elokuu\", \"syyskuu\", \"lokakuu\", \"marraskuu\", \"joulukuu\"]], [[\"eKr\", \"jKr\"], [\"eKr.\", \"jKr.\"], [\"ennen Kristuksen syntymää\", \"jälkeen Kristuksen syntymän\"]], 1, [6, 0], [\"d.M.y\", u, \"d. MMMM y\", \"cccc d. MMMM y\"], [\"H.mm\", \"H.mm.ss\", \"H.mm.ss z\", \"H.mm.ss zzzz\"], [\"{1} {0}\", \"{1} 'klo' {0}\", u, u], [\",\", \" \", \";\", \"%\", \"+\", \"−\", \"E\", \"×\", \"‰\", \"∞\", \"epäluku\", \".\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"AOA\": [], \"ARS\": [], \"AUD\": [], \"BAM\": [], \"BBD\": [], \"BDT\": [], \"BMD\": [], \"BND\": [], \"BOB\": [], \"BRL\": [], \"BSD\": [], \"BWP\": [], \"BZD\": [], \"CAD\": [], \"CLP\": [], \"CNY\": [], \"COP\": [], \"CRC\": [], \"CUC\": [], \"CUP\": [], \"CZK\": [], \"DKK\": [], \"DOP\": [], \"EGP\": [], \"ESP\": [], \"FIM\": [\"mk\"], \"FJD\": [], \"FKP\": [], \"GEL\": [], \"GIP\": [], \"GNF\": [], \"GTQ\": [], \"GYD\": [], \"HKD\": [], \"HNL\": [], \"HRK\": [], \"HUF\": [], \"IDR\": [], \"ILS\": [], \"INR\": [], \"ISK\": [], \"JMD\": [], \"KHR\": [], \"KMF\": [], \"KPW\": [], \"KRW\": [], \"KYD\": [], \"KZT\": [], \"LAK\": [], \"LBP\": [], \"LKR\": [], \"LRD\": [], \"LTL\": [], \"LVL\": [], \"MGA\": [], \"MMK\": [], \"MNT\": [], \"MUR\": [], \"MXN\": [], \"MYR\": [], \"NAD\": [], \"NGN\": [], \"NIO\": [], \"NOK\": [], \"NPR\": [], \"NZD\": [], \"PHP\": [], \"PKR\": [], \"PLN\": [], \"PYG\": [], \"RON\": [], \"RWF\": [], \"SBD\": [], \"SEK\": [], \"SGD\": [], \"SHP\": [], \"SRD\": [], \"SSP\": [], \"STN\": [u, \"STD\"], \"SYP\": [], \"THB\": [], \"TOP\": [], \"TRY\": [], \"TTD\": [], \"TWD\": [], \"UAH\": [], \"UYU\": [], \"VEF\": [], \"VND\": [], \"XCD\": [], \"XPF\": [], \"XXX\": [], \"ZAR\": [], \"ZMW\": [] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC1F,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,eAAe,eAAe,aAAa,iBAAiB,aAAa,eAAe,YAAY,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,aAAa,aAAa,WAAW,eAAe,WAAW,aAAa,UAAU,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,WAAW,WAAW,YAAY,WAAW,WAAW,UAAU,WAAW,SAAS,UAAU,UAAU,YAAY,SAAS,GAAG,CAAC,cAAc,cAAc,eAAe,cAAc,cAAc,aAAa,cAAc,YAAY,aAAa,aAAa,eAAe,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,SAAS,UAAU,SAAS,SAAS,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,OAAO,GAAG,CAAC,YAAY,YAAY,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,WAAW,WAAW,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,6BAA6B,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,aAAa,gBAAgB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,iBAAiB,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,MAAM;", "names": []}