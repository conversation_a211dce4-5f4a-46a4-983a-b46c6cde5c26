{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/sl.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (v === 0 && i % 100 === 1)\n        return 1;\n    if (v === 0 && i % 100 === 2)\n        return 2;\n    if (v === 0 && (i % 100 === Math.floor(i % 100) && (i % 100 >= 3 && i % 100 <= 4)) || !(v === 0))\n        return 3;\n    return 5;\n}\nexport default [\"sl\", [[\"d\", \"p\"], [\"dop.\", \"pop.\"], u], [[\"d\", \"p\"], [\"dop.\", \"pop.\"], [\"dopoldne\", \"popoldne\"]], [[\"n\", \"p\", \"t\", \"s\", \"č\", \"p\", \"s\"], [\"ned.\", \"pon.\", \"tor.\", \"sre.\", \"čet.\", \"pet.\", \"sob.\"], [\"nedelja\", \"ponedeljek\", \"torek\", \"sreda\", \"četrtek\", \"petek\", \"sobota\"], [\"ned.\", \"pon.\", \"tor.\", \"sre.\", \"čet.\", \"pet.\", \"sob.\"]], u, [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maj\", \"jun.\", \"jul.\", \"avg.\", \"sep.\", \"okt.\", \"nov.\", \"dec.\"], [\"januar\", \"februar\", \"marec\", \"april\", \"maj\", \"junij\", \"julij\", \"avgust\", \"september\", \"oktober\", \"november\", \"december\"]], u, [[\"pr. Kr.\", \"po Kr.\"], u, [\"pred Kristusom\", \"po Kristusu\"]], 1, [6, 0], [\"d. MM. yy\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"−\", \"e\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"evro\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"GBP\": [u, \"£\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [u, \"NT$\"], \"XCD\": [u, \"$\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC1F,MAAI,MAAM,KAAK,IAAI,QAAQ;AACvB,WAAO;AACX,MAAI,MAAM,KAAK,IAAI,QAAQ;AACvB,WAAO;AACX,MAAI,MAAM,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO,OAAO,EAAE,MAAM;AAC1F,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,cAAc,SAAS,SAAS,WAAW,SAAS,QAAQ,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,SAAS,SAAS,OAAO,SAAS,SAAS,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,WAAW,QAAQ,GAAG,GAAG,CAAC,kBAAkB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,YAAY,aAAa,iBAAiB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,MAAM;", "names": []}