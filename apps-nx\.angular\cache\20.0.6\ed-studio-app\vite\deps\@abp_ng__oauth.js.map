{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.oauth/fesm2022/abp-ng.oauth.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, Inject, signal, provideAppInitializer, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport * as i2 from '@abp/ng.core';\nimport { EnvironmentService, NAVIGATE_TO_MANAGE_PROFILE, AuthService, CORE_OPTIONS, IS_EXTERNAL_REQUEST, TENANT_KEY, AbpLocalStorageService, ConfigStateService, HttpErrorReporterService, SessionStateService, AbpWindowService, noop, AbstractAuthErrorFilter, AuthGuard, authGuard, ApiInterceptor, PIPE_TO_LOGIN_FN_KEY, CHECK_AUTHENTICATION_STATE_FN_KEY, AuthErrorFilterService } from '@abp/ng.core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport * as i1 from 'angular-oauth2-oidc';\nimport { OAuthService, OAuthErrorEvent, OAuthInfoEvent, OAuthModule, OAuthStorage } from 'angular-oauth2-oidc';\nimport compare from 'just-compare';\nimport { map, filter, finalize, switchMap, tap, take } from 'rxjs/operators';\nimport { pipe, of, filter as filter$1, tap as tap$1, take as take$1, from, lastValueFrom, EMPTY } from 'rxjs';\nimport { Router } from '@angular/router';\nconst NavigateToManageProfileProvider = {\n  provide: NAVIGATE_TO_MANAGE_PROFILE,\n  useFactory: () => {\n    const environment = inject(EnvironmentService);\n    return () => {\n      const env = environment.getEnvironment();\n      if (!env.oAuthConfig) {\n        console.warn('The oAuthConfig env is missing on environment.ts');\n        return;\n      }\n      const {\n        issuer\n      } = env.oAuthConfig;\n      const path = issuer.endsWith('/') ? issuer : `${issuer}/`;\n      window.open(`${path}Account/Manage?returnUrl=${window.location.href}`, '_self');\n    };\n  }\n};\n\n/**\n * @deprecated Use `abpOAuthGuard` *function* instead.\n */\nclass AbpOAuthGuard {\n  constructor() {\n    this.oAuthService = inject(OAuthService);\n    this.authService = inject(AuthService);\n  }\n  canActivate(route, state) {\n    const hasValidAccessToken = this.oAuthService.hasValidAccessToken();\n    if (hasValidAccessToken) {\n      return true;\n    }\n    const params = {\n      returnUrl: state.url\n    };\n    this.authService.navigateToLogin(params);\n    return false;\n  }\n  static {\n    this.ɵfac = function AbpOAuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpOAuthGuard)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpOAuthGuard,\n      factory: AbpOAuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpOAuthGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst abpOAuthGuard = (route, state) => {\n  const oAuthService = inject(OAuthService);\n  const authService = inject(AuthService);\n  const hasValidAccessToken = oAuthService.hasValidAccessToken();\n  if (hasValidAccessToken) {\n    return true;\n  }\n  const params = {\n    returnUrl: state.url\n  };\n  authService.navigateToLogin(params);\n  return false;\n};\nclass OAuthConfigurationHandler {\n  constructor(oAuthService, environmentService, options) {\n    this.oAuthService = oAuthService;\n    this.environmentService = environmentService;\n    this.options = options;\n    this.listenToSetEnvironment();\n  }\n  listenToSetEnvironment() {\n    this.environmentService.createOnUpdateStream(state => state).pipe(map(environment => environment.oAuthConfig), filter(config => !compare(config, this.options.environment.oAuthConfig))).subscribe(config => {\n      this.oAuthService.configure(config);\n    });\n  }\n  static {\n    this.ɵfac = function OAuthConfigurationHandler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthConfigurationHandler)(i0.ɵɵinject(i1.OAuthService), i0.ɵɵinject(i2.EnvironmentService), i0.ɵɵinject(CORE_OPTIONS));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OAuthConfigurationHandler,\n      factory: OAuthConfigurationHandler.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OAuthConfigurationHandler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.OAuthService\n  }, {\n    type: i2.EnvironmentService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CORE_OPTIONS]\n    }]\n  }], null);\n})();\nclass OAuthApiInterceptor {\n  constructor(oAuthService, sessionState, httpWaitService, tenantKey) {\n    this.oAuthService = oAuthService;\n    this.sessionState = sessionState;\n    this.httpWaitService = httpWaitService;\n    this.tenantKey = tenantKey;\n  }\n  intercept(request, next) {\n    this.httpWaitService.addRequest(request);\n    const isExternalRequest = request.context?.get(IS_EXTERNAL_REQUEST);\n    const newRequest = isExternalRequest ? request : request.clone({\n      setHeaders: this.getAdditionalHeaders(request.headers)\n    });\n    return next.handle(newRequest).pipe(finalize(() => this.httpWaitService.deleteRequest(request)));\n  }\n  getAdditionalHeaders(existingHeaders) {\n    const headers = {};\n    const token = this.oAuthService.getAccessToken();\n    if (!existingHeaders?.has('Authorization') && token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    const lang = this.sessionState.getLanguage();\n    if (!existingHeaders?.has('Accept-Language') && lang) {\n      headers['Accept-Language'] = lang;\n    }\n    const tenant = this.sessionState.getTenant();\n    if (!existingHeaders?.has(this.tenantKey) && tenant?.id) {\n      headers[this.tenantKey] = tenant.id;\n    }\n    headers['X-Requested-With'] = 'XMLHttpRequest';\n    return headers;\n  }\n  static {\n    this.ɵfac = function OAuthApiInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthApiInterceptor)(i0.ɵɵinject(i1.OAuthService), i0.ɵɵinject(i2.SessionStateService), i0.ɵɵinject(i2.HttpWaitService), i0.ɵɵinject(TENANT_KEY));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OAuthApiInterceptor,\n      factory: OAuthApiInterceptor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OAuthApiInterceptor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.OAuthService\n  }, {\n    type: i2.SessionStateService\n  }, {\n    type: i2.HttpWaitService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TENANT_KEY]\n    }]\n  }], null);\n})();\nconst oAuthStorage = localStorage;\nfunction clearOAuthStorage(storage = oAuthStorage) {\n  const keys = ['access_token', 'id_token', 'refresh_token', 'nonce', 'PKCE_verifier', 'expires_at', 'id_token_claims_obj', 'id_token_expires_at', 'id_token_stored_at', 'access_token_stored_at', 'granted_scopes', 'session_state'];\n  keys.forEach(key => storage.removeItem(key));\n}\nfunction storageFactory() {\n  return oAuthStorage;\n}\nclass RememberMeService {\n  constructor() {\n    this.#rememberMe = 'remember_me';\n    this.localStorageService = inject(AbpLocalStorageService);\n  }\n  #rememberMe;\n  set(remember) {\n    this.localStorageService.setItem(this.#rememberMe, JSON.stringify(remember));\n  }\n  remove() {\n    this.localStorageService.removeItem(this.#rememberMe);\n  }\n  get() {\n    return Boolean(JSON.parse(this.localStorageService.getItem(this.#rememberMe) || 'false'));\n  }\n  getFromToken(accessToken) {\n    const tokenBody = accessToken.split('.')[1].replace(/-/g, '+').replace(/_/g, '/');\n    try {\n      const parsedToken = JSON.parse(atob(tokenBody));\n      return Boolean(parsedToken[this.#rememberMe]);\n    } catch {\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function RememberMeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RememberMeService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RememberMeService,\n      factory: RememberMeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RememberMeService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst pipeToLogin = function (params, injector) {\n  const configState = injector.get(ConfigStateService);\n  const router = injector.get(Router);\n  const rememberMeService = injector.get(RememberMeService);\n  const authService = injector.get(AuthService);\n  return pipe(switchMap(() => configState.refreshAppState()), tap(() => {\n    rememberMeService.set(params.rememberMe || rememberMeService.get() || rememberMeService.getFromToken(authService.getAccessToken()));\n    if (params.redirectUrl) router.navigate([params.redirectUrl]);\n  }));\n};\n//Ref: https://github.com/manfredsteyer/angular-oauth2-oidc/issues/1214\nfunction isTokenExpired(expireDate) {\n  const currentDate = new Date().getTime();\n  return expireDate < currentDate;\n}\nconst checkAccessToken = function (injector) {\n  const configState = injector.get(ConfigStateService);\n  const oAuth = injector.get(OAuthService);\n  if (oAuth.hasValidAccessToken() && !configState.getDeep('currentUser.id')) {\n    clearOAuthStorage();\n  }\n};\nclass AuthFlowStrategy {\n  constructor(injector) {\n    this.injector = injector;\n    this.catchError = err => {\n      this.httpErrorReporter.reportError(err);\n      return of(null);\n    };\n    this.httpErrorReporter = injector.get(HttpErrorReporterService);\n    this.environment = injector.get(EnvironmentService);\n    this.configState = injector.get(ConfigStateService);\n    this.oAuthService = injector.get(OAuthService);\n    this.sessionState = injector.get(SessionStateService);\n    this.localStorageService = injector.get(AbpLocalStorageService);\n    this.oAuthConfig = this.environment.getEnvironment().oAuthConfig || {};\n    this.tenantKey = injector.get(TENANT_KEY);\n    this.router = injector.get(Router);\n    this.oAuthErrorFilterService = injector.get(OAuthErrorFilterService);\n    this.rememberMeService = injector.get(RememberMeService);\n    this.windowService = injector.get(AbpWindowService);\n    this.listenToOauthErrors();\n  }\n  async init() {\n    if (this.oAuthConfig.clientId) {\n      const shouldClear = shouldStorageClear(this.oAuthConfig.clientId, oAuthStorage);\n      if (shouldClear) clearOAuthStorage(oAuthStorage);\n    }\n    this.oAuthService.configure(this.oAuthConfig);\n    this.oAuthService.events.pipe(filter(event => event.type === 'token_refresh_error')).subscribe(() => this.navigateToLogin());\n    this.navigateToPreviousUrl();\n    return this.oAuthService.loadDiscoveryDocument().then(() => {\n      const isTokenExpire = isTokenExpired(this.oAuthService.getAccessTokenExpiration());\n      if (isTokenExpire && this.oAuthService.getRefreshToken()) {\n        return this.refreshToken();\n      }\n      return Promise.resolve();\n    }).catch(this.catchError);\n  }\n  navigateToPreviousUrl() {\n    const {\n      responseType\n    } = this.oAuthConfig;\n    if (responseType === 'code') {\n      this.oAuthService.events.pipe(filter(event => event.type === 'token_received' && !!this.oAuthService.state), take(1), map(() => {\n        const redirectUri = decodeURIComponent(this.oAuthService.state);\n        if (redirectUri && redirectUri !== '/') {\n          return redirectUri;\n        }\n        return '/';\n      }), switchMap(redirectUri => this.configState.getOne$('currentUser').pipe(filter(user => !!user?.isAuthenticated), tap(() => this.router.navigateByUrl(redirectUri))))).subscribe();\n    }\n  }\n  refreshToken() {\n    return this.oAuthService.refreshToken().catch(() => clearOAuthStorage());\n  }\n  listenToOauthErrors() {\n    this.oAuthService.events.pipe(filter(event => event instanceof OAuthErrorEvent), tap(err => {\n      const shouldSkip = this.oAuthErrorFilterService.run(err);\n      if (!shouldSkip) {\n        clearOAuthStorage();\n      }\n    }), switchMap(() => this.configState.refreshAppState())).subscribe();\n  }\n}\nfunction shouldStorageClear(clientId, storage) {\n  const key = 'abpOAuthClientId';\n  if (!storage.getItem(key)) {\n    storage.setItem(key, clientId);\n    return false;\n  }\n  const shouldClear = storage.getItem(key) !== clientId;\n  if (shouldClear) storage.setItem(key, clientId);\n  return shouldClear;\n}\nclass AuthCodeFlowStrategy extends AuthFlowStrategy {\n  constructor() {\n    super(...arguments);\n    this.isInternalAuth = false;\n  }\n  async init() {\n    this.checkRememberMeOption();\n    this.listenToTokenReceived();\n    return super.init().then(() => this.oAuthService.tryLogin().catch(noop)).then(() => this.oAuthService.setupAutomaticSilentRefresh());\n  }\n  checkRememberMeOption() {\n    const accessToken = this.oAuthService.getAccessToken();\n    const isTokenExpire = isTokenExpired(this.oAuthService.getAccessTokenExpiration());\n    let rememberMe = this.rememberMeService.get();\n    if (accessToken && !rememberMe) {\n      const rememberMeValue = this.rememberMeService.getFromToken(accessToken);\n      this.rememberMeService.set(!!rememberMeValue);\n    }\n    rememberMe = this.rememberMeService.get();\n    if (accessToken && isTokenExpire && !rememberMe) {\n      this.rememberMeService.remove();\n      this.oAuthService.logOut();\n    }\n  }\n  getCultureParams(queryParams) {\n    const lang = this.sessionState.getLanguage();\n    const culture = {\n      culture: lang,\n      'ui-culture': lang\n    };\n    return {\n      ...(lang && culture),\n      ...queryParams\n    };\n  }\n  setUICulture() {\n    const urlParams = new URLSearchParams(window.location.search);\n    this.configState.uiCultureFromAuthCodeFlow = urlParams.get('ui-culture');\n  }\n  replaceURLParams() {\n    const location = this.windowService.window.location;\n    const history = this.windowService.window.history;\n    const query = location.search.replace(/([?&])iss=[^&]*&?/, '$1').replace(/([?&])culture=[^&]*&?/, '$1').replace(/([?&])ui-culture=[^&]*&?/, '$1').replace(/[?&]+$/, '');\n    const href = location.origin + location.pathname + query + location.hash;\n    history.replaceState(null, '', href);\n  }\n  listenToTokenReceived() {\n    this.oAuthService.events.pipe(filter$1(event => event.type === 'token_received'), tap$1(() => {\n      this.setUICulture();\n      this.replaceURLParams();\n    }), take$1(1)).subscribe();\n  }\n  navigateToLogin(queryParams) {\n    let additionalState = '';\n    if (queryParams?.returnUrl) {\n      additionalState = queryParams.returnUrl;\n    }\n    const cultureParams = this.getCultureParams(queryParams);\n    this.oAuthService.initCodeFlow(additionalState, cultureParams);\n  }\n  checkIfInternalAuth(queryParams) {\n    this.oAuthService.initCodeFlow('', this.getCultureParams(queryParams));\n    return false;\n  }\n  logout(queryParams) {\n    this.rememberMeService.remove();\n    if (queryParams?.noRedirectToLogoutUrl) {\n      this.router.navigate(['/']);\n      return from(this.oAuthService.revokeTokenAndLogout(true));\n    }\n    return from(this.oAuthService.revokeTokenAndLogout(this.getCultureParams(queryParams)));\n  }\n  login(queryParams) {\n    this.oAuthService.initCodeFlow('', this.getCultureParams(queryParams));\n    return of(null);\n  }\n}\nclass AuthPasswordFlowStrategy extends AuthFlowStrategy {\n  constructor() {\n    super(...arguments);\n    this.isInternalAuth = true;\n  }\n  listenToTokenExpiration() {\n    this.oAuthService.events.pipe(filter(event => event instanceof OAuthInfoEvent && event.type === 'token_expires' && event.info === 'access_token')).subscribe(() => {\n      if (this.oAuthService.getRefreshToken()) {\n        this.refreshToken();\n      } else {\n        this.oAuthService.logOut();\n        this.rememberMeService.remove();\n        this.configState.refreshAppState().subscribe();\n      }\n    });\n  }\n  async init() {\n    this.checkRememberMeOption();\n    return super.init().then(() => this.listenToTokenExpiration());\n  }\n  checkRememberMeOption() {\n    const accessToken = this.oAuthService.getAccessToken();\n    const isTokenExpire = isTokenExpired(this.oAuthService.getAccessTokenExpiration());\n    const rememberMe = this.rememberMeService.get();\n    if (accessToken && isTokenExpire && !rememberMe) {\n      this.rememberMeService.remove();\n      this.oAuthService.logOut();\n    }\n  }\n  navigateToLogin(queryParams) {\n    const router = this.injector.get(Router);\n    return router.navigate(['/account/login'], {\n      queryParams\n    });\n  }\n  checkIfInternalAuth() {\n    return true;\n  }\n  login(params) {\n    const tenant = this.sessionState.getTenant();\n    return from(this.oAuthService.fetchTokenUsingPasswordFlow(params.username, params.password, new HttpHeaders({\n      ...(tenant && tenant.id && {\n        [this.tenantKey]: tenant.id\n      })\n    }))).pipe(pipeToLogin(params, this.injector));\n  }\n  logout() {\n    const router = this.injector.get(Router);\n    const noRedirectToLogoutUrl = true;\n    return from(this.oAuthService.revokeTokenAndLogout(noRedirectToLogoutUrl)).pipe(switchMap(() => this.configState.refreshAppState()), tap(() => {\n      this.rememberMeService.remove();\n      router.navigateByUrl('/');\n    }));\n  }\n  refreshToken() {\n    return this.oAuthService.refreshToken().catch(() => {\n      clearOAuthStorage();\n      this.rememberMeService.remove();\n    });\n  }\n}\nconst AUTH_FLOW_STRATEGY = {\n  Code(injector) {\n    return new AuthCodeFlowStrategy(injector);\n  },\n  Password(injector) {\n    return new AuthPasswordFlowStrategy(injector);\n  }\n};\nclass AbpOAuthService {\n  get oidc() {\n    return this.oAuthService.oidc;\n  }\n  set oidc(value) {\n    this.oAuthService.oidc = value;\n  }\n  get isInternalAuth() {\n    return this.strategy.isInternalAuth;\n  }\n  constructor(injector) {\n    this.injector = injector;\n    this.oAuthService = this.injector.get(OAuthService);\n  }\n  async init() {\n    const environmentService = this.injector.get(EnvironmentService);\n    const result$ = environmentService.getEnvironment$().pipe(map(env => env?.oAuthConfig), filter(Boolean), tap(oAuthConfig => {\n      this.strategy = oAuthConfig.responseType === 'code' ? AUTH_FLOW_STRATEGY.Code(this.injector) : AUTH_FLOW_STRATEGY.Password(this.injector);\n    }), switchMap(() => from(this.strategy.init())), take(1));\n    return await lastValueFrom(result$);\n  }\n  logout(queryParams) {\n    if (!this.strategy) {\n      return EMPTY;\n    }\n    return this.strategy.logout(queryParams);\n  }\n  navigateToLogin(queryParams) {\n    this.strategy.navigateToLogin(queryParams);\n  }\n  login(params) {\n    return this.strategy.login(params);\n  }\n  get isAuthenticated() {\n    return this.oAuthService.hasValidAccessToken();\n  }\n  loginUsingGrant(grantType, parameters, headers) {\n    const {\n      clientId: client_id,\n      dummyClientSecret: client_secret\n    } = this.oAuthService;\n    const access_token = this.oAuthService.getAccessToken();\n    const p = {\n      access_token,\n      grant_type: grantType,\n      client_id,\n      ...parameters\n    };\n    if (client_secret) {\n      p['client_secret'] = client_secret;\n    }\n    return this.oAuthService.fetchTokenUsingGrant(grantType, p, headers);\n  }\n  getRefreshToken() {\n    return this.oAuthService.getRefreshToken();\n  }\n  getAccessToken() {\n    return this.oAuthService.getAccessToken();\n  }\n  refreshToken() {\n    return this.oAuthService.refreshToken();\n  }\n  getAccessTokenExpiration() {\n    return this.oAuthService.getAccessTokenExpiration();\n  }\n  static {\n    this.ɵfac = function AbpOAuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpOAuthService)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpOAuthService,\n      factory: AbpOAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpOAuthService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nclass OAuthErrorFilterService extends AbstractAuthErrorFilter {\n  constructor() {\n    super(...arguments);\n    this._filters = signal([]);\n    this.filters = this._filters.asReadonly();\n  }\n  get(id) {\n    return this._filters().find(({\n      id: _id\n    }) => _id === id);\n  }\n  add(filter) {\n    this._filters.update(items => [...items, filter]);\n  }\n  patch(item) {\n    const _item = this.filters().find(({\n      id\n    }) => id === item.id);\n    if (!_item) {\n      return;\n    }\n    Object.assign(_item, item);\n  }\n  remove(id) {\n    const item = this.filters().find(({\n      id: _id\n    }) => _id === id);\n    if (!item) {\n      return;\n    }\n    this._filters.update(items => items.filter(({\n      id: _id\n    }) => _id !== id));\n  }\n  run(event) {\n    return this.filters().filter(({\n      executable\n    }) => !!executable).map(({\n      execute\n    }) => execute(event)).some(item => item);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵOAuthErrorFilterService_BaseFactory;\n      return function OAuthErrorFilterService_Factory(__ngFactoryType__) {\n        return (ɵOAuthErrorFilterService_BaseFactory || (ɵOAuthErrorFilterService_BaseFactory = i0.ɵɵgetInheritedFactory(OAuthErrorFilterService)))(__ngFactoryType__ || OAuthErrorFilterService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OAuthErrorFilterService,\n      factory: OAuthErrorFilterService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OAuthErrorFilterService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction provideAbpOAuth() {\n  const providers = [{\n    provide: AuthService,\n    useClass: AbpOAuthService\n  }, {\n    provide: AuthGuard,\n    useClass: AbpOAuthGuard\n  }, {\n    provide: authGuard,\n    useValue: abpOAuthGuard\n  }, {\n    provide: ApiInterceptor,\n    useClass: OAuthApiInterceptor\n  }, {\n    provide: PIPE_TO_LOGIN_FN_KEY,\n    useValue: pipeToLogin\n  }, {\n    provide: CHECK_AUTHENTICATION_STATE_FN_KEY,\n    useValue: checkAccessToken\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useExisting: ApiInterceptor,\n    multi: true\n  }, NavigateToManageProfileProvider, provideAppInitializer(() => {\n    inject(OAuthConfigurationHandler);\n  }), OAuthModule.forRoot().providers, {\n    provide: OAuthStorage,\n    useClass: AbpLocalStorageService\n  }, {\n    provide: AuthErrorFilterService,\n    useExisting: OAuthErrorFilterService\n  }];\n  return makeEnvironmentProviders(providers);\n}\n\n/**\n * @deprecated AbpOAuthModule is deprecated use `provideAbpOAuth` *function* instead.\n */\nclass AbpOAuthModule {\n  static forRoot() {\n    return {\n      ngModule: AbpOAuthModule,\n      providers: [provideAbpOAuth()]\n    };\n  }\n  static {\n    this.ɵfac = function AbpOAuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpOAuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AbpOAuthModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpOAuthModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTH_FLOW_STRATEGY, AbpOAuthGuard, AbpOAuthModule, AbpOAuthService, AuthCodeFlowStrategy, AuthFlowStrategy, AuthPasswordFlowStrategy, NavigateToManageProfileProvider, OAuthApiInterceptor, OAuthConfigurationHandler, OAuthErrorFilterService, RememberMeService, abpOAuthGuard, checkAccessToken, clearOAuthStorage, isTokenExpired, oAuthStorage, pipeToLogin, provideAbpOAuth, storageFactory };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,kCAAkC;AAAA,EACtC,SAAS;AAAA,EACT,YAAY,MAAM;AAChB,UAAM,cAAc,OAAO,kBAAkB;AAC7C,WAAO,MAAM;AACX,YAAM,MAAM,YAAY,eAAe;AACvC,UAAI,CAAC,IAAI,aAAa;AACpB,gBAAQ,KAAK,kDAAkD;AAC/D;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,IAAI;AACR,YAAM,OAAO,OAAO,SAAS,GAAG,IAAI,SAAS,GAAG,MAAM;AACtD,aAAO,KAAK,GAAG,IAAI,4BAA4B,OAAO,SAAS,IAAI,IAAI,OAAO;AAAA,IAChF;AAAA,EACF;AACF;AAKA,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,cAAc,OAAO,WAAW;AAAA,EACvC;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,UAAM,sBAAsB,KAAK,aAAa,oBAAoB;AAClE,QAAI,qBAAqB;AACvB,aAAO;AAAA,IACT;AACA,UAAM,SAAS;AAAA,MACb,WAAW,MAAM;AAAA,IACnB;AACA,SAAK,YAAY,gBAAgB,MAAM;AACvC,WAAO;AAAA,EACT;AAaF;AAXI,eAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,SAAO,KAAK,qBAAqB,gBAAe;AAClD;AAGA,eAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,eAAc;AAAA,EACvB,YAAY;AACd,CAAC;AA1BL,IAAM,gBAAN;AAAA,CA6BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAgB,CAAC,OAAO,UAAU;AACtC,QAAM,eAAe,OAAO,YAAY;AACxC,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,sBAAsB,aAAa,oBAAoB;AAC7D,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AAAA,IACb,WAAW,MAAM;AAAA,EACnB;AACA,cAAY,gBAAgB,MAAM;AAClC,SAAO;AACT;AACA,IAAM,6BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,cAAc,oBAAoB,SAAS;AACrD,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,yBAAyB;AACvB,SAAK,mBAAmB,qBAAqB,WAAS,KAAK,EAAE,KAAK,IAAI,iBAAe,YAAY,WAAW,GAAG,OAAO,YAAU,CAAC,kBAAQ,QAAQ,KAAK,QAAQ,YAAY,WAAW,CAAC,CAAC,EAAE,UAAU,YAAU;AAC3M,WAAK,aAAa,UAAU,MAAM;AAAA,IACpC,CAAC;AAAA,EACH;AAaF;AAXI,2BAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,SAAO,KAAK,qBAAqB,4BAA8B,SAAY,YAAY,GAAM,SAAY,kBAAkB,GAAM,SAAS,YAAY,CAAC;AACzJ;AAGA,2BAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,2BAA0B;AAAA,EACnC,YAAY;AACd,CAAC;AAtBL,IAAM,4BAAN;AAAA,CAyBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,cAAc,cAAc,iBAAiB,WAAW;AAClE,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,SAAK,gBAAgB,WAAW,OAAO;AACvC,UAAM,oBAAoB,QAAQ,SAAS,IAAI,mBAAmB;AAClE,UAAM,aAAa,oBAAoB,UAAU,QAAQ,MAAM;AAAA,MAC7D,YAAY,KAAK,qBAAqB,QAAQ,OAAO;AAAA,IACvD,CAAC;AACD,WAAO,KAAK,OAAO,UAAU,EAAE,KAAK,SAAS,MAAM,KAAK,gBAAgB,cAAc,OAAO,CAAC,CAAC;AAAA,EACjG;AAAA,EACA,qBAAqB,iBAAiB;AACpC,UAAM,UAAU,CAAC;AACjB,UAAM,QAAQ,KAAK,aAAa,eAAe;AAC/C,QAAI,CAAC,iBAAiB,IAAI,eAAe,KAAK,OAAO;AACnD,cAAQ,eAAe,IAAI,UAAU,KAAK;AAAA,IAC5C;AACA,UAAM,OAAO,KAAK,aAAa,YAAY;AAC3C,QAAI,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,MAAM;AACpD,cAAQ,iBAAiB,IAAI;AAAA,IAC/B;AACA,UAAM,SAAS,KAAK,aAAa,UAAU;AAC3C,QAAI,CAAC,iBAAiB,IAAI,KAAK,SAAS,KAAK,QAAQ,IAAI;AACvD,cAAQ,KAAK,SAAS,IAAI,OAAO;AAAA,IACnC;AACA,YAAQ,kBAAkB,IAAI;AAC9B,WAAO;AAAA,EACT;AAaF;AAXI,qBAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,SAAO,KAAK,qBAAqB,sBAAwB,SAAY,YAAY,GAAM,SAAY,mBAAmB,GAAM,SAAY,eAAe,GAAM,SAAS,UAAU,CAAC;AACnL;AAGA,qBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,qBAAoB;AAAA,EAC7B,YAAY;AACd,CAAC;AA1CL,IAAM,sBAAN;AAAA,CA6CC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAe;AACrB,SAAS,kBAAkB,UAAU,cAAc;AACjD,QAAM,OAAO,CAAC,gBAAgB,YAAY,iBAAiB,SAAS,iBAAiB,cAAc,uBAAuB,uBAAuB,sBAAsB,0BAA0B,kBAAkB,eAAe;AAClO,OAAK,QAAQ,SAAO,QAAQ,WAAW,GAAG,CAAC;AAC7C;AACA,SAAS,iBAAiB;AACxB,SAAO;AACT;AAtMA;AAuMA,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB,cAAc;AAId;AAHE,uBAAK,aAAc;AACnB,SAAK,sBAAsB,OAAO,sBAAsB;AAAA,EAC1D;AAAA,EAEA,IAAI,UAAU;AACZ,SAAK,oBAAoB,QAAQ,mBAAK,cAAa,KAAK,UAAU,QAAQ,CAAC;AAAA,EAC7E;AAAA,EACA,SAAS;AACP,SAAK,oBAAoB,WAAW,mBAAK,YAAW;AAAA,EACtD;AAAA,EACA,MAAM;AACJ,WAAO,QAAQ,KAAK,MAAM,KAAK,oBAAoB,QAAQ,mBAAK,YAAW,KAAK,OAAO,CAAC;AAAA,EAC1F;AAAA,EACA,aAAa,aAAa;AACxB,UAAM,YAAY,YAAY,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAChF,QAAI;AACF,YAAM,cAAc,KAAK,MAAM,KAAK,SAAS,CAAC;AAC9C,aAAO,QAAQ,YAAY,mBAAK,YAAW,CAAC;AAAA,IAC9C,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAaF;AA/BE;AAoBE,mBAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,oBAAmB;AACtD;AAGA,mBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,mBAAkB;AAAA,EAC3B,YAAY;AACd,CAAC;AAlCL,IAAM,oBAAN;AAAA,CAqCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAc,SAAU,QAAQ,UAAU;AAC9C,QAAM,cAAc,SAAS,IAAI,kBAAkB;AACnD,QAAM,SAAS,SAAS,IAAI,MAAM;AAClC,QAAM,oBAAoB,SAAS,IAAI,iBAAiB;AACxD,QAAM,cAAc,SAAS,IAAI,WAAW;AAC5C,SAAO,KAAK,UAAU,MAAM,YAAY,gBAAgB,CAAC,GAAG,IAAI,MAAM;AACpE,sBAAkB,IAAI,OAAO,cAAc,kBAAkB,IAAI,KAAK,kBAAkB,aAAa,YAAY,eAAe,CAAC,CAAC;AAClI,QAAI,OAAO,YAAa,QAAO,SAAS,CAAC,OAAO,WAAW,CAAC;AAAA,EAC9D,CAAC,CAAC;AACJ;AAEA,SAAS,eAAe,YAAY;AAClC,QAAM,eAAc,oBAAI,KAAK,GAAE,QAAQ;AACvC,SAAO,aAAa;AACtB;AACA,IAAM,mBAAmB,SAAU,UAAU;AAC3C,QAAM,cAAc,SAAS,IAAI,kBAAkB;AACnD,QAAM,QAAQ,SAAS,IAAI,YAAY;AACvC,MAAI,MAAM,oBAAoB,KAAK,CAAC,YAAY,QAAQ,gBAAgB,GAAG;AACzE,sBAAkB;AAAA,EACpB;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,aAAa,SAAO;AACvB,WAAK,kBAAkB,YAAY,GAAG;AACtC,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,SAAK,oBAAoB,SAAS,IAAI,wBAAwB;AAC9D,SAAK,cAAc,SAAS,IAAI,kBAAkB;AAClD,SAAK,cAAc,SAAS,IAAI,kBAAkB;AAClD,SAAK,eAAe,SAAS,IAAI,YAAY;AAC7C,SAAK,eAAe,SAAS,IAAI,mBAAmB;AACpD,SAAK,sBAAsB,SAAS,IAAI,sBAAsB;AAC9D,SAAK,cAAc,KAAK,YAAY,eAAe,EAAE,eAAe,CAAC;AACrE,SAAK,YAAY,SAAS,IAAI,UAAU;AACxC,SAAK,SAAS,SAAS,IAAI,MAAM;AACjC,SAAK,0BAA0B,SAAS,IAAI,uBAAuB;AACnE,SAAK,oBAAoB,SAAS,IAAI,iBAAiB;AACvD,SAAK,gBAAgB,SAAS,IAAI,gBAAgB;AAClD,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACM,OAAO;AAAA;AACX,UAAI,KAAK,YAAY,UAAU;AAC7B,cAAM,cAAc,mBAAmB,KAAK,YAAY,UAAU,YAAY;AAC9E,YAAI,YAAa,mBAAkB,YAAY;AAAA,MACjD;AACA,WAAK,aAAa,UAAU,KAAK,WAAW;AAC5C,WAAK,aAAa,OAAO,KAAK,OAAO,WAAS,MAAM,SAAS,qBAAqB,CAAC,EAAE,UAAU,MAAM,KAAK,gBAAgB,CAAC;AAC3H,WAAK,sBAAsB;AAC3B,aAAO,KAAK,aAAa,sBAAsB,EAAE,KAAK,MAAM;AAC1D,cAAM,gBAAgB,eAAe,KAAK,aAAa,yBAAyB,CAAC;AACjF,YAAI,iBAAiB,KAAK,aAAa,gBAAgB,GAAG;AACxD,iBAAO,KAAK,aAAa;AAAA,QAC3B;AACA,eAAO,QAAQ,QAAQ;AAAA,MACzB,CAAC,EAAE,MAAM,KAAK,UAAU;AAAA,IAC1B;AAAA;AAAA,EACA,wBAAwB;AACtB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,QAAI,iBAAiB,QAAQ;AAC3B,WAAK,aAAa,OAAO,KAAK,OAAO,WAAS,MAAM,SAAS,oBAAoB,CAAC,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,MAAM;AAC9H,cAAM,cAAc,mBAAmB,KAAK,aAAa,KAAK;AAC9D,YAAI,eAAe,gBAAgB,KAAK;AACtC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG,UAAU,iBAAe,KAAK,YAAY,QAAQ,aAAa,EAAE,KAAK,OAAO,UAAQ,CAAC,CAAC,MAAM,eAAe,GAAG,IAAI,MAAM,KAAK,OAAO,cAAc,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU;AAAA,IACpL;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO,KAAK,aAAa,aAAa,EAAE,MAAM,MAAM,kBAAkB,CAAC;AAAA,EACzE;AAAA,EACA,sBAAsB;AACpB,SAAK,aAAa,OAAO,KAAK,OAAO,WAAS,iBAAiB,eAAe,GAAG,IAAI,SAAO;AAC1F,YAAM,aAAa,KAAK,wBAAwB,IAAI,GAAG;AACvD,UAAI,CAAC,YAAY;AACf,0BAAkB;AAAA,MACpB;AAAA,IACF,CAAC,GAAG,UAAU,MAAM,KAAK,YAAY,gBAAgB,CAAC,CAAC,EAAE,UAAU;AAAA,EACrE;AACF;AACA,SAAS,mBAAmB,UAAU,SAAS;AAC7C,QAAM,MAAM;AACZ,MAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG;AACzB,YAAQ,QAAQ,KAAK,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,cAAc,QAAQ,QAAQ,GAAG,MAAM;AAC7C,MAAI,YAAa,SAAQ,QAAQ,KAAK,QAAQ;AAC9C,SAAO;AACT;AACA,IAAM,uBAAN,MAAM,8BAA6B,iBAAiB;AAAA,EAClD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACM,OAAO;AAAA;AACX,WAAK,sBAAsB;AAC3B,WAAK,sBAAsB;AAC3B,aAAO,kDAAM,aAAN,IAAW,EAAE,KAAK,MAAM,KAAK,aAAa,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,aAAa,4BAA4B,CAAC;AAAA,IACrI;AAAA;AAAA,EACA,wBAAwB;AACtB,UAAM,cAAc,KAAK,aAAa,eAAe;AACrD,UAAM,gBAAgB,eAAe,KAAK,aAAa,yBAAyB,CAAC;AACjF,QAAI,aAAa,KAAK,kBAAkB,IAAI;AAC5C,QAAI,eAAe,CAAC,YAAY;AAC9B,YAAM,kBAAkB,KAAK,kBAAkB,aAAa,WAAW;AACvE,WAAK,kBAAkB,IAAI,CAAC,CAAC,eAAe;AAAA,IAC9C;AACA,iBAAa,KAAK,kBAAkB,IAAI;AACxC,QAAI,eAAe,iBAAiB,CAAC,YAAY;AAC/C,WAAK,kBAAkB,OAAO;AAC9B,WAAK,aAAa,OAAO;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,iBAAiB,aAAa;AAC5B,UAAM,OAAO,KAAK,aAAa,YAAY;AAC3C,UAAM,UAAU;AAAA,MACd,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AACA,WAAO,kCACD,QAAQ,UACT;AAAA,EAEP;AAAA,EACA,eAAe;AACb,UAAM,YAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC5D,SAAK,YAAY,4BAA4B,UAAU,IAAI,YAAY;AAAA,EACzE;AAAA,EACA,mBAAmB;AACjB,UAAM,WAAW,KAAK,cAAc,OAAO;AAC3C,UAAM,UAAU,KAAK,cAAc,OAAO;AAC1C,UAAM,QAAQ,SAAS,OAAO,QAAQ,qBAAqB,IAAI,EAAE,QAAQ,yBAAyB,IAAI,EAAE,QAAQ,4BAA4B,IAAI,EAAE,QAAQ,UAAU,EAAE;AACtK,UAAM,OAAO,SAAS,SAAS,SAAS,WAAW,QAAQ,SAAS;AACpE,YAAQ,aAAa,MAAM,IAAI,IAAI;AAAA,EACrC;AAAA,EACA,wBAAwB;AACtB,SAAK,aAAa,OAAO,KAAK,OAAS,WAAS,MAAM,SAAS,gBAAgB,GAAG,IAAM,MAAM;AAC5F,WAAK,aAAa;AAClB,WAAK,iBAAiB;AAAA,IACxB,CAAC,GAAG,KAAO,CAAC,CAAC,EAAE,UAAU;AAAA,EAC3B;AAAA,EACA,gBAAgB,aAAa;AAC3B,QAAI,kBAAkB;AACtB,QAAI,aAAa,WAAW;AAC1B,wBAAkB,YAAY;AAAA,IAChC;AACA,UAAM,gBAAgB,KAAK,iBAAiB,WAAW;AACvD,SAAK,aAAa,aAAa,iBAAiB,aAAa;AAAA,EAC/D;AAAA,EACA,oBAAoB,aAAa;AAC/B,SAAK,aAAa,aAAa,IAAI,KAAK,iBAAiB,WAAW,CAAC;AACrE,WAAO;AAAA,EACT;AAAA,EACA,OAAO,aAAa;AAClB,SAAK,kBAAkB,OAAO;AAC9B,QAAI,aAAa,uBAAuB;AACtC,WAAK,OAAO,SAAS,CAAC,GAAG,CAAC;AAC1B,aAAO,KAAK,KAAK,aAAa,qBAAqB,IAAI,CAAC;AAAA,IAC1D;AACA,WAAO,KAAK,KAAK,aAAa,qBAAqB,KAAK,iBAAiB,WAAW,CAAC,CAAC;AAAA,EACxF;AAAA,EACA,MAAM,aAAa;AACjB,SAAK,aAAa,aAAa,IAAI,KAAK,iBAAiB,WAAW,CAAC;AACrE,WAAO,GAAG,IAAI;AAAA,EAChB;AACF;AACA,IAAM,2BAAN,MAAM,kCAAiC,iBAAiB;AAAA,EACtD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,0BAA0B;AACxB,SAAK,aAAa,OAAO,KAAK,OAAO,WAAS,iBAAiB,kBAAkB,MAAM,SAAS,mBAAmB,MAAM,SAAS,cAAc,CAAC,EAAE,UAAU,MAAM;AACjK,UAAI,KAAK,aAAa,gBAAgB,GAAG;AACvC,aAAK,aAAa;AAAA,MACpB,OAAO;AACL,aAAK,aAAa,OAAO;AACzB,aAAK,kBAAkB,OAAO;AAC9B,aAAK,YAAY,gBAAgB,EAAE,UAAU;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACM,OAAO;AAAA;AACX,WAAK,sBAAsB;AAC3B,aAAO,sDAAM,aAAN,IAAW,EAAE,KAAK,MAAM,KAAK,wBAAwB,CAAC;AAAA,IAC/D;AAAA;AAAA,EACA,wBAAwB;AACtB,UAAM,cAAc,KAAK,aAAa,eAAe;AACrD,UAAM,gBAAgB,eAAe,KAAK,aAAa,yBAAyB,CAAC;AACjF,UAAM,aAAa,KAAK,kBAAkB,IAAI;AAC9C,QAAI,eAAe,iBAAiB,CAAC,YAAY;AAC/C,WAAK,kBAAkB,OAAO;AAC9B,WAAK,aAAa,OAAO;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,gBAAgB,aAAa;AAC3B,UAAM,SAAS,KAAK,SAAS,IAAI,MAAM;AACvC,WAAO,OAAO,SAAS,CAAC,gBAAgB,GAAG;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,SAAS,KAAK,aAAa,UAAU;AAC3C,WAAO,KAAK,KAAK,aAAa,4BAA4B,OAAO,UAAU,OAAO,UAAU,IAAI,YAAY,mBACtG,UAAU,OAAO,MAAM;AAAA,MACzB,CAAC,KAAK,SAAS,GAAG,OAAO;AAAA,IAC3B,EACD,CAAC,CAAC,EAAE,KAAK,YAAY,QAAQ,KAAK,QAAQ,CAAC;AAAA,EAC9C;AAAA,EACA,SAAS;AACP,UAAM,SAAS,KAAK,SAAS,IAAI,MAAM;AACvC,UAAM,wBAAwB;AAC9B,WAAO,KAAK,KAAK,aAAa,qBAAqB,qBAAqB,CAAC,EAAE,KAAK,UAAU,MAAM,KAAK,YAAY,gBAAgB,CAAC,GAAG,IAAI,MAAM;AAC7I,WAAK,kBAAkB,OAAO;AAC9B,aAAO,cAAc,GAAG;AAAA,IAC1B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,eAAe;AACb,WAAO,KAAK,aAAa,aAAa,EAAE,MAAM,MAAM;AAClD,wBAAkB;AAClB,WAAK,kBAAkB,OAAO;AAAA,IAChC,CAAC;AAAA,EACH;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,KAAK,UAAU;AACb,WAAO,IAAI,qBAAqB,QAAQ;AAAA,EAC1C;AAAA,EACA,SAAS,UAAU;AACjB,WAAO,IAAI,yBAAyB,QAAQ;AAAA,EAC9C;AACF;AACA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,aAAa,OAAO;AAAA,EAC3B;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK,SAAS,IAAI,YAAY;AAAA,EACpD;AAAA,EACM,OAAO;AAAA;AACX,YAAM,qBAAqB,KAAK,SAAS,IAAI,kBAAkB;AAC/D,YAAM,UAAU,mBAAmB,gBAAgB,EAAE,KAAK,IAAI,SAAO,KAAK,WAAW,GAAG,OAAO,OAAO,GAAG,IAAI,iBAAe;AAC1H,aAAK,WAAW,YAAY,iBAAiB,SAAS,mBAAmB,KAAK,KAAK,QAAQ,IAAI,mBAAmB,SAAS,KAAK,QAAQ;AAAA,MAC1I,CAAC,GAAG,UAAU,MAAM,KAAK,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACxD,aAAO,MAAM,cAAc,OAAO;AAAA,IACpC;AAAA;AAAA,EACA,OAAO,aAAa;AAClB,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,SAAS,OAAO,WAAW;AAAA,EACzC;AAAA,EACA,gBAAgB,aAAa;AAC3B,SAAK,SAAS,gBAAgB,WAAW;AAAA,EAC3C;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,SAAS,MAAM,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,aAAa,oBAAoB;AAAA,EAC/C;AAAA,EACA,gBAAgB,WAAW,YAAY,SAAS;AAC9C,UAAM;AAAA,MACJ,UAAU;AAAA,MACV,mBAAmB;AAAA,IACrB,IAAI,KAAK;AACT,UAAM,eAAe,KAAK,aAAa,eAAe;AACtD,UAAM,IAAI;AAAA,MACR;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,OACG;AAEL,QAAI,eAAe;AACjB,QAAE,eAAe,IAAI;AAAA,IACvB;AACA,WAAO,KAAK,aAAa,qBAAqB,WAAW,GAAG,OAAO;AAAA,EACrE;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,aAAa,gBAAgB;AAAA,EAC3C;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,aAAa,eAAe;AAAA,EAC1C;AAAA,EACA,eAAe;AACb,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,2BAA2B;AACzB,WAAO,KAAK,aAAa,yBAAyB;AAAA,EACpD;AAaF;AAXI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAoB,SAAY,QAAQ,CAAC;AAC5E;AAGA,iBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,iBAAgB;AAAA,EACzB,YAAY;AACd,CAAC;AA3EL,IAAM,kBAAN;AAAA,CA8EC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,iCAAgC,wBAAwB;AAAA,EAC5D,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,WAAW,OAAO,CAAC,CAAC;AACzB,SAAK,UAAU,KAAK,SAAS,WAAW;AAAA,EAC1C;AAAA,EACA,IAAI,IAAI;AACN,WAAO,KAAK,SAAS,EAAE,KAAK,CAAC;AAAA,MAC3B,IAAI;AAAA,IACN,MAAM,QAAQ,EAAE;AAAA,EAClB;AAAA,EACA,IAAIA,SAAQ;AACV,SAAK,SAAS,OAAO,WAAS,CAAC,GAAG,OAAOA,OAAM,CAAC;AAAA,EAClD;AAAA,EACA,MAAM,MAAM;AACV,UAAM,QAAQ,KAAK,QAAQ,EAAE,KAAK,CAAC;AAAA,MACjC;AAAA,IACF,MAAM,OAAO,KAAK,EAAE;AACpB,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAAA,EACA,OAAO,IAAI;AACT,UAAM,OAAO,KAAK,QAAQ,EAAE,KAAK,CAAC;AAAA,MAChC,IAAI;AAAA,IACN,MAAM,QAAQ,EAAE;AAChB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,SAAK,SAAS,OAAO,WAAS,MAAM,OAAO,CAAC;AAAA,MAC1C,IAAI;AAAA,IACN,MAAM,QAAQ,EAAE,CAAC;AAAA,EACnB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,EAAE,OAAO,CAAC;AAAA,MAC5B;AAAA,IACF,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC;AAAA,MACvB;AAAA,IACF,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK,UAAQ,IAAI;AAAA,EACzC;AAgBF;AAdI,yBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,gCAAgC,mBAAmB;AACjE,YAAQ,yCAAyC,uCAA0C,sBAAsB,wBAAuB,IAAI,qBAAqB,wBAAuB;AAAA,EAC1L;AACF,GAAG;AAGH,yBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,yBAAwB;AAAA,EACjC,YAAY;AACd,CAAC;AAtDL,IAAM,0BAAN;AAAA,CAyDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,kBAAkB;AACzB,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,EACT,GAAG,iCAAiC,sBAAsB,MAAM;AAC9D,WAAO,yBAAyB;AAAA,EAClC,CAAC,GAAG,YAAY,QAAQ,EAAE,WAAW;AAAA,IACnC,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC;AACD,SAAO,yBAAyB,SAAS;AAC3C;AAKA,IAAM,kBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AAcF;AAZI,gBAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,iBAAgB;AACnD;AAGA,gBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AACR,CAAC;AAGD,gBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAlBrD,IAAM,iBAAN;AAAA,CAqBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["filter"]}