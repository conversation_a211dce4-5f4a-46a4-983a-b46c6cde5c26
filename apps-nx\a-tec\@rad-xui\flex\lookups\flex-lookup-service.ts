import { inject, Injectable } from "@angular/core";
import { apiResultFrom, KeyedCollection, SelectItem } from "@tec/rad-core/utils";
import { LookupsDataClient } from "@tec/rad-xui/data";
import { Observable } from "rxjs";



type LookupGroup = {
    group: string;
    items: SelectItem[];
}

@Injectable({providedIn: 'root'})
export class RadLookupService {


    #lookupApi = inject(LookupsDataClient);

    private _lookups = new KeyedCollection<LookupGroup>();

    async getLookups(group: string): Promise<SelectItem[]> {

        if(this._lookups.contains<PERSON>ey(group)){
            return this._lookups.get(group).items;
        }

        const result = await apiResultFrom(this.#lookupApi.getLookups(group));

        if(result.isError){
            return [];
        }

        const items = result.value.items.map(n => new SelectItem(n.value, n.label));
        this._lookups.add(group, {group, items});
        return items;

    }

    getLookup$(group: string): Observable<SelectItem[]> {

        if(this._lookups.containsKey(group)){
            return of(this._lookups.get(group).items);
        }

        return this.#lookupApi.getLookups(group).pipe(
            map(n => n.items.map(m => new SelectItem(m.value, m.label)))
        );
    }




}