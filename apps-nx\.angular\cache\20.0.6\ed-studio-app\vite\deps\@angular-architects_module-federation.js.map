{"version": 3, "sources": ["../../../../../../node_modules/@angular-architects/module-federation-runtime/fesm2022/angular-architects-module-federation-runtime.mjs", "../../../../../../node_modules/@angular-architects/module-federation/src/index.js"], "sourcesContent": ["let config = {};\nconst containerMap = {};\nconst remoteMap = {};\nlet isDefaultScopeInitialized = false;\nasync function lookupExposedModule(key, exposedModule) {\n    const container = containerMap[key];\n    const factory = await container.get(exposedModule);\n    const Module = factory();\n    return Module;\n}\nasync function initRemote(container, key) {\n    // const container = window[key] as Container;\n    // Do we still need to initialize the remote?\n    if (remoteMap[key]) {\n        return container;\n    }\n    // Do we still need to initialize the share scope?\n    if (!isDefaultScopeInitialized) {\n        await __webpack_init_sharing__('default');\n        isDefaultScopeInitialized = true;\n    }\n    await container.init(__webpack_share_scopes__.default);\n    remoteMap[key] = true;\n    return container;\n}\nasync function loadRemoteEntry(remoteEntryOrOptions, remoteName) {\n    if (typeof remoteEntryOrOptions === 'string') {\n        const remoteEntry = remoteEntryOrOptions;\n        return await loadRemoteScriptEntry(remoteEntry, remoteName);\n    }\n    else if (remoteEntryOrOptions.type === 'script') {\n        const options = remoteEntryOrOptions;\n        return await loadRemoteScriptEntry(options.remoteEntry, options.remoteName);\n    }\n    else if (remoteEntryOrOptions.type === 'module') {\n        const options = remoteEntryOrOptions;\n        await loadRemoteModuleEntry(options.remoteEntry);\n    }\n}\nasync function loadRemoteModuleEntry(remoteEntry) {\n    if (containerMap[remoteEntry]) {\n        return Promise.resolve();\n    }\n    return await import(/* webpackIgnore:true */ remoteEntry).then((container) => {\n        initRemote(container, remoteEntry);\n        containerMap[remoteEntry] = container;\n    });\n}\nasync function loadRemoteScriptEntry(remoteEntry, remoteName) {\n    return new Promise((resolve, reject) => {\n        // Is remoteEntry already loaded?\n        if (containerMap[remoteName]) {\n            resolve();\n            return;\n        }\n        const script = document.createElement('script');\n        script.src = remoteEntry;\n        script.onerror = reject;\n        script.onload = () => {\n            const container = window[remoteName];\n            initRemote(container, remoteName);\n            containerMap[remoteName] = container;\n            resolve();\n        };\n        document.body.appendChild(script);\n    });\n}\nasync function loadRemoteModule(optionsOrRemoteName, exposedModule) {\n    let loadRemoteEntryOptions;\n    let key;\n    let remoteEntry;\n    let options;\n    if (typeof optionsOrRemoteName === 'string') {\n        options = {\n            type: 'manifest',\n            remoteName: optionsOrRemoteName,\n            exposedModule: exposedModule,\n        };\n    }\n    else {\n        options = optionsOrRemoteName;\n    }\n    // To support legacy API (< ng 13)\n    if (!options.type) {\n        const hasManifest = Object.keys(config).length > 0;\n        options.type = hasManifest ? 'manifest' : 'script';\n    }\n    if (options.type === 'manifest') {\n        const manifestEntry = config[options.remoteName];\n        if (!manifestEntry) {\n            throw new Error('Manifest does not contain ' + options.remoteName);\n        }\n        options = {\n            type: manifestEntry.type,\n            exposedModule: options.exposedModule,\n            remoteEntry: manifestEntry.remoteEntry,\n            remoteName: manifestEntry.type === 'script' ? options.remoteName : undefined,\n        };\n        remoteEntry = manifestEntry.remoteEntry;\n    }\n    else {\n        remoteEntry = options.remoteEntry;\n    }\n    if (options.type === 'script') {\n        loadRemoteEntryOptions = {\n            type: 'script',\n            remoteEntry: options.remoteEntry,\n            remoteName: options.remoteName,\n        };\n        key = options.remoteName;\n    }\n    else if (options.type === 'module') {\n        loadRemoteEntryOptions = {\n            type: 'module',\n            remoteEntry: options.remoteEntry,\n        };\n        key = options.remoteEntry;\n    }\n    if (remoteEntry) {\n        await loadRemoteEntry(loadRemoteEntryOptions);\n    }\n    return await lookupExposedModule(key, options.exposedModule);\n}\nasync function setManifest(manifest, skipRemoteEntries = false) {\n    config = parseConfig(manifest);\n    if (!skipRemoteEntries) {\n        await loadRemoteEntries();\n    }\n}\nfunction getManifest() {\n    return config;\n}\nasync function initFederation(manifest, skipRemoteEntries = false) {\n    if (typeof manifest === 'string') {\n        return loadManifest(manifest, skipRemoteEntries);\n    }\n    else {\n        return setManifest(manifest, skipRemoteEntries);\n    }\n}\nasync function loadManifest(configFile, skipRemoteEntries = false) {\n    const result = await fetch(configFile);\n    if (!result.ok) {\n        throw Error('could not load configFile: ' + configFile);\n    }\n    config = parseConfig(await result.json());\n    if (!skipRemoteEntries) {\n        await loadRemoteEntries();\n    }\n}\nfunction parseConfig(config) {\n    const result = {};\n    for (const key in config) {\n        const value = config[key];\n        let entry;\n        if (typeof value === 'string') {\n            entry = {\n                remoteEntry: value,\n                type: 'module',\n            };\n        }\n        else {\n            entry = {\n                ...value,\n                type: value.type || 'module',\n            };\n        }\n        result[key] = entry;\n    }\n    return result;\n}\nasync function loadRemoteEntries() {\n    const promises = [];\n    for (const key in config) {\n        const entry = config[key];\n        if (entry.type === 'module') {\n            promises.push(loadRemoteEntry({ type: 'module', remoteEntry: entry.remoteEntry }));\n        }\n        else {\n            promises.push(loadRemoteEntry({\n                type: 'script',\n                remoteEntry: entry.remoteEntry,\n                remoteName: key,\n            }));\n        }\n    }\n    await Promise.all(promises);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { getManifest, initFederation, loadManifest, loadRemoteEntry, loadRemoteModule, setManifest };\n\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"@angular-architects/module-federation-runtime\"), exports);\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,SAAe,oBAAoB,KAAK,eAAe;AAAA;AACnD,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,UAAU,MAAM,UAAU,IAAI,aAAa;AACjD,UAAM,SAAS,QAAQ;AACvB,WAAO;AAAA,EACX;AAAA;AACA,SAAe,WAAW,WAAW,KAAK;AAAA;AAGtC,QAAI,UAAU,GAAG,GAAG;AAChB,aAAO;AAAA,IACX;AAEA,QAAI,CAAC,2BAA2B;AAC5B,YAAM,yBAAyB,SAAS;AACxC,kCAA4B;AAAA,IAChC;AACA,UAAM,UAAU,KAAK,yBAAyB,OAAO;AACrD,cAAU,GAAG,IAAI;AACjB,WAAO;AAAA,EACX;AAAA;AACA,SAAe,gBAAgB,sBAAsB,YAAY;AAAA;AAC7D,QAAI,OAAO,yBAAyB,UAAU;AAC1C,YAAM,cAAc;AACpB,aAAO,MAAM,sBAAsB,aAAa,UAAU;AAAA,IAC9D,WACS,qBAAqB,SAAS,UAAU;AAC7C,YAAM,UAAU;AAChB,aAAO,MAAM,sBAAsB,QAAQ,aAAa,QAAQ,UAAU;AAAA,IAC9E,WACS,qBAAqB,SAAS,UAAU;AAC7C,YAAM,UAAU;AAChB,YAAM,sBAAsB,QAAQ,WAAW;AAAA,IACnD;AAAA,EACJ;AAAA;AACA,SAAe,sBAAsB,aAAa;AAAA;AAC9C,QAAI,aAAa,WAAW,GAAG;AAC3B,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,WAAO,MAAM;AAAA;AAAA,MAAgC;AAAA,MAAa,KAAK,CAAC,cAAc;AAC1E,iBAAW,WAAW,WAAW;AACjC,mBAAa,WAAW,IAAI;AAAA,IAChC,CAAC;AAAA,EACL;AAAA;AACA,SAAe,sBAAsB,aAAa,YAAY;AAAA;AAC1D,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEpC,UAAI,aAAa,UAAU,GAAG;AAC1B,gBAAQ;AACR;AAAA,MACJ;AACA,YAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,aAAO,MAAM;AACb,aAAO,UAAU;AACjB,aAAO,SAAS,MAAM;AAClB,cAAM,YAAY,OAAO,UAAU;AACnC,mBAAW,WAAW,UAAU;AAChC,qBAAa,UAAU,IAAI;AAC3B,gBAAQ;AAAA,MACZ;AACA,eAAS,KAAK,YAAY,MAAM;AAAA,IACpC,CAAC;AAAA,EACL;AAAA;AACA,SAAe,iBAAiB,qBAAqB,eAAe;AAAA;AAChE,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,wBAAwB,UAAU;AACzC,gBAAU;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ,OACK;AACD,gBAAU;AAAA,IACd;AAEA,QAAI,CAAC,QAAQ,MAAM;AACf,YAAM,cAAc,OAAO,KAAK,MAAM,EAAE,SAAS;AACjD,cAAQ,OAAO,cAAc,aAAa;AAAA,IAC9C;AACA,QAAI,QAAQ,SAAS,YAAY;AAC7B,YAAM,gBAAgB,OAAO,QAAQ,UAAU;AAC/C,UAAI,CAAC,eAAe;AAChB,cAAM,IAAI,MAAM,+BAA+B,QAAQ,UAAU;AAAA,MACrE;AACA,gBAAU;AAAA,QACN,MAAM,cAAc;AAAA,QACpB,eAAe,QAAQ;AAAA,QACvB,aAAa,cAAc;AAAA,QAC3B,YAAY,cAAc,SAAS,WAAW,QAAQ,aAAa;AAAA,MACvE;AACA,oBAAc,cAAc;AAAA,IAChC,OACK;AACD,oBAAc,QAAQ;AAAA,IAC1B;AACA,QAAI,QAAQ,SAAS,UAAU;AAC3B,+BAAyB;AAAA,QACrB,MAAM;AAAA,QACN,aAAa,QAAQ;AAAA,QACrB,YAAY,QAAQ;AAAA,MACxB;AACA,YAAM,QAAQ;AAAA,IAClB,WACS,QAAQ,SAAS,UAAU;AAChC,+BAAyB;AAAA,QACrB,MAAM;AAAA,QACN,aAAa,QAAQ;AAAA,MACzB;AACA,YAAM,QAAQ;AAAA,IAClB;AACA,QAAI,aAAa;AACb,YAAM,gBAAgB,sBAAsB;AAAA,IAChD;AACA,WAAO,MAAM,oBAAoB,KAAK,QAAQ,aAAa;AAAA,EAC/D;AAAA;AACA,SAAe,YAAY,UAAU,oBAAoB,OAAO;AAAA;AAC5D,aAAS,YAAY,QAAQ;AAC7B,QAAI,CAAC,mBAAmB;AACpB,YAAM,kBAAkB;AAAA,IAC5B;AAAA,EACJ;AAAA;AACA,SAAS,cAAc;AACnB,SAAO;AACX;AACA,SAAe,eAAe,UAAU,oBAAoB,OAAO;AAAA;AAC/D,QAAI,OAAO,aAAa,UAAU;AAC9B,aAAO,aAAa,UAAU,iBAAiB;AAAA,IACnD,OACK;AACD,aAAO,YAAY,UAAU,iBAAiB;AAAA,IAClD;AAAA,EACJ;AAAA;AACA,SAAe,aAAa,YAAY,oBAAoB,OAAO;AAAA;AAC/D,UAAM,SAAS,MAAM,MAAM,UAAU;AACrC,QAAI,CAAC,OAAO,IAAI;AACZ,YAAM,MAAM,gCAAgC,UAAU;AAAA,IAC1D;AACA,aAAS,YAAY,MAAM,OAAO,KAAK,CAAC;AACxC,QAAI,CAAC,mBAAmB;AACpB,YAAM,kBAAkB;AAAA,IAC5B;AAAA,EACJ;AAAA;AACA,SAAS,YAAYA,SAAQ;AACzB,QAAM,SAAS,CAAC;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,QAAQA,QAAO,GAAG;AACxB,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ;AAAA,QACJ,aAAa;AAAA,QACb,MAAM;AAAA,MACV;AAAA,IACJ,OACK;AACD,cAAQ,iCACD,QADC;AAAA,QAEJ,MAAM,MAAM,QAAQ;AAAA,MACxB;AAAA,IACJ;AACA,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAe,oBAAoB;AAAA;AAC/B,UAAM,WAAW,CAAC;AAClB,eAAW,OAAO,QAAQ;AACtB,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,MAAM,SAAS,UAAU;AACzB,iBAAS,KAAK,gBAAgB,EAAE,MAAM,UAAU,aAAa,MAAM,YAAY,CAAC,CAAC;AAAA,MACrF,OACK;AACD,iBAAS,KAAK,gBAAgB;AAAA,UAC1B,MAAM;AAAA,UACN,aAAa,MAAM;AAAA,UACnB,YAAY;AAAA,QAChB,CAAC,CAAC;AAAA,MACN;AAAA,IACJ;AACA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC9B;AAAA;AA3LA,IAAI,QACE,cACA,WACF;AAHJ;AAAA;AAAA,IAAI,SAAS,CAAC;AACd,IAAM,eAAe,CAAC;AACtB,IAAM,YAAY,CAAC;AACnB,IAAI,4BAA4B;AAAA;AAAA;;;ACHhC;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAM,UAAU;AAChB,YAAQ,aAAa,2HAA0D,OAAO;AAAA;AAAA;", "names": ["config"]}