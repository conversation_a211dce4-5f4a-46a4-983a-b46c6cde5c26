import { Injectable, Injector, inject } from "@angular/core";
import { FormlyFieldConfig } from "@ngx-formly/core";
import { RadFormItem, FormConfig,  RadFormConfig } from "./form-types";
import { StringHelper } from "@tec/rad-core/utils";
import { toObservable } from "@angular/core/rxjs-interop";
import { FormlyFieldProps } from "@ngx-formly/material/form-field";
import { runInInjectionContext } from '@angular/core';


export interface customFieldProps extends FormlyFieldProps  {
    [additionalProperties: string]: any;
}

@Injectable({ providedIn: 'root' })
export class FormBuilder {



    injector = inject(Injector)

    private defaultFields: { [key: string]: FormlyFieldConfig } = {
        'input': {},

    }



    async configureForm(config: FormConfig): Promise<FormlyFieldConfig[]> {
        return this.createFields(config.fields);
    }

    createFields(configFields: RadFormConfig): FormlyFieldConfig[] {
        const fields: FormlyFieldConfig[] = [];
        let i = 1000;
        if(!configFields || !configFields.length || configFields.length === 0){
            return fields;
        }
        for (const field of configFields) {
            const formlyField = runInInjectionContext(this.injector,()=>  this.createField(field.key, field, i));
            fields.push(formlyField);
            i++;
        }

        let rows = fields.map((field) => {return field.props.row});
        //get unique rows
        rows = rows.filter((value, index, self) => self.indexOf(value) === index);
        rows = rows.sort((a,b)=>a-b);

        const formFields: FormlyFieldConfig[] = [];

        //var create row groups
        for (const row of rows) {
            const colFields = fields.filter((field) => field.props.row === row);
            const rowGroup = this.createRow(...colFields);
            formFields.push(rowGroup);

        }

        return formFields;
    }



    createField(key: string, def: RadFormItem, index: number): FormlyFieldConfig {


        const row = def.rowNum || index;
        const col = def.col || index;

        const colSpan = def.span || 1;
        const type = def.type;
        let selectOptions: any = def.values;
        if (def.values && typeof def.values === 'function') {
            selectOptions = toObservable(def.values);
        }



        const classes = `formly-${key} flex-${colSpan} formly-${type} rad-mat-dense`;
        const className = def.className ? `${def.className} ${classes}` : classes;
        let props: customFieldProps = {
            row: row,
            col: col,
            type: type.toLowerCase(),
            label: this.getLabel(key, def),
            required: def.required,
            options: selectOptions
        }

        def.className = className;

        const typeProps = this.getCustomProps(def);
        if(typeProps){
            props = Object.assign(props, typeProps);
        }

        if(def.props){
            props = Object.assign(props, def.props);
        }

        def.props = props;



        def.expressions = {
            ...this.getDefaultExpressions(),
            ...def.expressions
        }



        return def;
    }


    private createRow(...fieldConfig: FormlyFieldConfig[]): any {
        return {
            fieldGroupClassName: 'display-form-flex',
            fieldGroup: fieldConfig
        }
    }


    private getLabel(key: string, def: RadFormItem): string {
        return def.label || StringHelper.toSentenceCase(key);
    }

    private getCustomProps(def: RadFormItem): customFieldProps {
        if(def.type === 'select' || def.type === 'treeselect') {
            return {
                disableOptionCentering: true,
                labelProp:def.displayProp || 'label',
                valueProp: def.valueProp || 'value',
                appendTo: 'body'
            };
        }
        return undefined;
    }

    private getDefaultExpressions(): any{
        return {
            'props.disabled': 'formState.disabled',
        }
    }

}
