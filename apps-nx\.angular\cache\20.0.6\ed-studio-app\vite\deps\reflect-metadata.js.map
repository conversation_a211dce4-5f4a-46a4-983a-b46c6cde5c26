{"version": 3, "sources": ["../../../../../../node_modules/reflect-metadata/Reflect.js"], "sourcesContent": ["/*! *****************************************************************************\nCopyright (C) Microsoft. All rights reserved.\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\nthis file except in compliance with the License. You may obtain a copy of the\nLicense at http://www.apache.org/licenses/LICENSE-2.0\n\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\nMERCHANTABLITY OR NON-INFRINGEMENT.\n\nSee the Apache Version 2.0 License for specific language governing permissions\nand limitations under the License.\n***************************************************************************** */\nvar Reflect;\n(function (Reflect) {\n    // Metadata Proposal\n    // https://rbuckton.github.io/reflect-metadata/\n    (function (factory) {\n        var root = typeof globalThis === \"object\" ? globalThis :\n            typeof global === \"object\" ? global :\n                typeof self === \"object\" ? self :\n                    typeof this === \"object\" ? this :\n                        sloppyModeThis();\n        var exporter = makeExporter(Reflect);\n        if (typeof root.Reflect !== \"undefined\") {\n            exporter = makeExporter(root.Reflect, exporter);\n        }\n        factory(exporter, root);\n        if (typeof root.Reflect === \"undefined\") {\n            root.Reflect = Reflect;\n        }\n        function makeExporter(target, previous) {\n            return function (key, value) {\n                Object.defineProperty(target, key, { configurable: true, writable: true, value: value });\n                if (previous)\n                    previous(key, value);\n            };\n        }\n        function functionThis() {\n            try {\n                return Function(\"return this;\")();\n            }\n            catch (_) { }\n        }\n        function indirectEvalThis() {\n            try {\n                return (void 0, eval)(\"(function() { return this; })()\");\n            }\n            catch (_) { }\n        }\n        function sloppyModeThis() {\n            return functionThis() || indirectEvalThis();\n        }\n    })(function (exporter, root) {\n        var hasOwn = Object.prototype.hasOwnProperty;\n        // feature test for Symbol support\n        var supportsSymbol = typeof Symbol === \"function\";\n        var toPrimitiveSymbol = supportsSymbol && typeof Symbol.toPrimitive !== \"undefined\" ? Symbol.toPrimitive : \"@@toPrimitive\";\n        var iteratorSymbol = supportsSymbol && typeof Symbol.iterator !== \"undefined\" ? Symbol.iterator : \"@@iterator\";\n        var supportsCreate = typeof Object.create === \"function\"; // feature test for Object.create support\n        var supportsProto = { __proto__: [] } instanceof Array; // feature test for __proto__ support\n        var downLevel = !supportsCreate && !supportsProto;\n        var HashMap = {\n            // create an object in dictionary mode (a.k.a. \"slow\" mode in v8)\n            create: supportsCreate\n                ? function () { return MakeDictionary(Object.create(null)); }\n                : supportsProto\n                    ? function () { return MakeDictionary({ __proto__: null }); }\n                    : function () { return MakeDictionary({}); },\n            has: downLevel\n                ? function (map, key) { return hasOwn.call(map, key); }\n                : function (map, key) { return key in map; },\n            get: downLevel\n                ? function (map, key) { return hasOwn.call(map, key) ? map[key] : undefined; }\n                : function (map, key) { return map[key]; },\n        };\n        // Load global or shim versions of Map, Set, and WeakMap\n        var functionPrototype = Object.getPrototypeOf(Function);\n        var _Map = typeof Map === \"function\" && typeof Map.prototype.entries === \"function\" ? Map : CreateMapPolyfill();\n        var _Set = typeof Set === \"function\" && typeof Set.prototype.entries === \"function\" ? Set : CreateSetPolyfill();\n        var _WeakMap = typeof WeakMap === \"function\" ? WeakMap : CreateWeakMapPolyfill();\n        var registrySymbol = supportsSymbol ? Symbol.for(\"@reflect-metadata:registry\") : undefined;\n        var metadataRegistry = GetOrCreateMetadataRegistry();\n        var metadataProvider = CreateMetadataProvider(metadataRegistry);\n        /**\n         * Applies a set of decorators to a property of a target object.\n         * @param decorators An array of decorators.\n         * @param target The target object.\n         * @param propertyKey (Optional) The property key to decorate.\n         * @param attributes (Optional) The property descriptor for the target key.\n         * @remarks Decorators are applied in reverse order.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     Example = Reflect.decorate(decoratorsArray, Example);\n         *\n         *     // property (on constructor)\n         *     Reflect.decorate(decoratorsArray, Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     Reflect.decorate(decoratorsArray, Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     Object.defineProperty(Example, \"staticMethod\",\n         *         Reflect.decorate(decoratorsArray, Example, \"staticMethod\",\n         *             Object.getOwnPropertyDescriptor(Example, \"staticMethod\")));\n         *\n         *     // method (on prototype)\n         *     Object.defineProperty(Example.prototype, \"method\",\n         *         Reflect.decorate(decoratorsArray, Example.prototype, \"method\",\n         *             Object.getOwnPropertyDescriptor(Example.prototype, \"method\")));\n         *\n         */\n        function decorate(decorators, target, propertyKey, attributes) {\n            if (!IsUndefined(propertyKey)) {\n                if (!IsArray(decorators))\n                    throw new TypeError();\n                if (!IsObject(target))\n                    throw new TypeError();\n                if (!IsObject(attributes) && !IsUndefined(attributes) && !IsNull(attributes))\n                    throw new TypeError();\n                if (IsNull(attributes))\n                    attributes = undefined;\n                propertyKey = ToPropertyKey(propertyKey);\n                return DecorateProperty(decorators, target, propertyKey, attributes);\n            }\n            else {\n                if (!IsArray(decorators))\n                    throw new TypeError();\n                if (!IsConstructor(target))\n                    throw new TypeError();\n                return DecorateConstructor(decorators, target);\n            }\n        }\n        exporter(\"decorate\", decorate);\n        // 4.1.2 Reflect.metadata(metadataKey, metadataValue)\n        // https://rbuckton.github.io/reflect-metadata/#reflect.metadata\n        /**\n         * A default metadata decorator factory that can be used on a class, class member, or parameter.\n         * @param metadataKey The key for the metadata entry.\n         * @param metadataValue The value for the metadata entry.\n         * @returns A decorator function.\n         * @remarks\n         * If `metadataKey` is already defined for the target and target key, the\n         * metadataValue for that key will be overwritten.\n         * @example\n         *\n         *     // constructor\n         *     @Reflect.metadata(key, value)\n         *     class Example {\n         *     }\n         *\n         *     // property (on constructor, TypeScript only)\n         *     class Example {\n         *         @Reflect.metadata(key, value)\n         *         static staticProperty;\n         *     }\n         *\n         *     // property (on prototype, TypeScript only)\n         *     class Example {\n         *         @Reflect.metadata(key, value)\n         *         property;\n         *     }\n         *\n         *     // method (on constructor)\n         *     class Example {\n         *         @Reflect.metadata(key, value)\n         *         static staticMethod() { }\n         *     }\n         *\n         *     // method (on prototype)\n         *     class Example {\n         *         @Reflect.metadata(key, value)\n         *         method() { }\n         *     }\n         *\n         */\n        function metadata(metadataKey, metadataValue) {\n            function decorator(target, propertyKey) {\n                if (!IsObject(target))\n                    throw new TypeError();\n                if (!IsUndefined(propertyKey) && !IsPropertyKey(propertyKey))\n                    throw new TypeError();\n                OrdinaryDefineOwnMetadata(metadataKey, metadataValue, target, propertyKey);\n            }\n            return decorator;\n        }\n        exporter(\"metadata\", metadata);\n        /**\n         * Define a unique metadata entry on the target.\n         * @param metadataKey A key used to store and retrieve metadata.\n         * @param metadataValue A value that contains attached metadata.\n         * @param target The target object on which to define metadata.\n         * @param propertyKey (Optional) The property key for the target.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     Reflect.defineMetadata(\"custom:annotation\", options, Example);\n         *\n         *     // property (on constructor)\n         *     Reflect.defineMetadata(\"custom:annotation\", options, Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     Reflect.defineMetadata(\"custom:annotation\", options, Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     Reflect.defineMetadata(\"custom:annotation\", options, Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     Reflect.defineMetadata(\"custom:annotation\", options, Example.prototype, \"method\");\n         *\n         *     // decorator factory as metadata-producing annotation.\n         *     function MyAnnotation(options): Decorator {\n         *         return (target, key?) => Reflect.defineMetadata(\"custom:annotation\", options, target, key);\n         *     }\n         *\n         */\n        function defineMetadata(metadataKey, metadataValue, target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryDefineOwnMetadata(metadataKey, metadataValue, target, propertyKey);\n        }\n        exporter(\"defineMetadata\", defineMetadata);\n        /**\n         * Gets a value indicating whether the target object or its prototype chain has the provided metadata key defined.\n         * @param metadataKey A key used to store and retrieve metadata.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns `true` if the metadata key was defined on the target object or its prototype chain; otherwise, `false`.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.hasMetadata(\"custom:annotation\", Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.hasMetadata(\"custom:annotation\", Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.hasMetadata(\"custom:annotation\", Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.hasMetadata(\"custom:annotation\", Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.hasMetadata(\"custom:annotation\", Example.prototype, \"method\");\n         *\n         */\n        function hasMetadata(metadataKey, target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryHasMetadata(metadataKey, target, propertyKey);\n        }\n        exporter(\"hasMetadata\", hasMetadata);\n        /**\n         * Gets a value indicating whether the target object has the provided metadata key defined.\n         * @param metadataKey A key used to store and retrieve metadata.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns `true` if the metadata key was defined on the target object; otherwise, `false`.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example.prototype, \"method\");\n         *\n         */\n        function hasOwnMetadata(metadataKey, target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryHasOwnMetadata(metadataKey, target, propertyKey);\n        }\n        exporter(\"hasOwnMetadata\", hasOwnMetadata);\n        /**\n         * Gets the metadata value for the provided metadata key on the target object or its prototype chain.\n         * @param metadataKey A key used to store and retrieve metadata.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns The metadata value for the metadata key if found; otherwise, `undefined`.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.getMetadata(\"custom:annotation\", Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.getMetadata(\"custom:annotation\", Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.getMetadata(\"custom:annotation\", Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.getMetadata(\"custom:annotation\", Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.getMetadata(\"custom:annotation\", Example.prototype, \"method\");\n         *\n         */\n        function getMetadata(metadataKey, target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryGetMetadata(metadataKey, target, propertyKey);\n        }\n        exporter(\"getMetadata\", getMetadata);\n        /**\n         * Gets the metadata value for the provided metadata key on the target object.\n         * @param metadataKey A key used to store and retrieve metadata.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns The metadata value for the metadata key if found; otherwise, `undefined`.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example.prototype, \"method\");\n         *\n         */\n        function getOwnMetadata(metadataKey, target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryGetOwnMetadata(metadataKey, target, propertyKey);\n        }\n        exporter(\"getOwnMetadata\", getOwnMetadata);\n        /**\n         * Gets the metadata keys defined on the target object or its prototype chain.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns An array of unique metadata keys.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.getMetadataKeys(Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.getMetadataKeys(Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.getMetadataKeys(Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.getMetadataKeys(Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.getMetadataKeys(Example.prototype, \"method\");\n         *\n         */\n        function getMetadataKeys(target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryMetadataKeys(target, propertyKey);\n        }\n        exporter(\"getMetadataKeys\", getMetadataKeys);\n        /**\n         * Gets the unique metadata keys defined on the target object.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns An array of unique metadata keys.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.getOwnMetadataKeys(Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.getOwnMetadataKeys(Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.getOwnMetadataKeys(Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.getOwnMetadataKeys(Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.getOwnMetadataKeys(Example.prototype, \"method\");\n         *\n         */\n        function getOwnMetadataKeys(target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            return OrdinaryOwnMetadataKeys(target, propertyKey);\n        }\n        exporter(\"getOwnMetadataKeys\", getOwnMetadataKeys);\n        /**\n         * Deletes the metadata entry from the target object with the provided key.\n         * @param metadataKey A key used to store and retrieve metadata.\n         * @param target The target object on which the metadata is defined.\n         * @param propertyKey (Optional) The property key for the target.\n         * @returns `true` if the metadata entry was found and deleted; otherwise, false.\n         * @example\n         *\n         *     class Example {\n         *         // property declarations are not part of ES6, though they are valid in TypeScript:\n         *         // static staticProperty;\n         *         // property;\n         *\n         *         constructor(p) { }\n         *         static staticMethod(p) { }\n         *         method(p) { }\n         *     }\n         *\n         *     // constructor\n         *     result = Reflect.deleteMetadata(\"custom:annotation\", Example);\n         *\n         *     // property (on constructor)\n         *     result = Reflect.deleteMetadata(\"custom:annotation\", Example, \"staticProperty\");\n         *\n         *     // property (on prototype)\n         *     result = Reflect.deleteMetadata(\"custom:annotation\", Example.prototype, \"property\");\n         *\n         *     // method (on constructor)\n         *     result = Reflect.deleteMetadata(\"custom:annotation\", Example, \"staticMethod\");\n         *\n         *     // method (on prototype)\n         *     result = Reflect.deleteMetadata(\"custom:annotation\", Example.prototype, \"method\");\n         *\n         */\n        function deleteMetadata(metadataKey, target, propertyKey) {\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            if (!IsObject(target))\n                throw new TypeError();\n            if (!IsUndefined(propertyKey))\n                propertyKey = ToPropertyKey(propertyKey);\n            var provider = GetMetadataProvider(target, propertyKey, /*Create*/ false);\n            if (IsUndefined(provider))\n                return false;\n            return provider.OrdinaryDeleteMetadata(metadataKey, target, propertyKey);\n        }\n        exporter(\"deleteMetadata\", deleteMetadata);\n        function DecorateConstructor(decorators, target) {\n            for (var i = decorators.length - 1; i >= 0; --i) {\n                var decorator = decorators[i];\n                var decorated = decorator(target);\n                if (!IsUndefined(decorated) && !IsNull(decorated)) {\n                    if (!IsConstructor(decorated))\n                        throw new TypeError();\n                    target = decorated;\n                }\n            }\n            return target;\n        }\n        function DecorateProperty(decorators, target, propertyKey, descriptor) {\n            for (var i = decorators.length - 1; i >= 0; --i) {\n                var decorator = decorators[i];\n                var decorated = decorator(target, propertyKey, descriptor);\n                if (!IsUndefined(decorated) && !IsNull(decorated)) {\n                    if (!IsObject(decorated))\n                        throw new TypeError();\n                    descriptor = decorated;\n                }\n            }\n            return descriptor;\n        }\n        // ******* OrdinaryHasMetadata(MetadataKey, O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinaryhasmetadata\n        function OrdinaryHasMetadata(MetadataKey, O, P) {\n            var hasOwn = OrdinaryHasOwnMetadata(MetadataKey, O, P);\n            if (hasOwn)\n                return true;\n            var parent = OrdinaryGetPrototypeOf(O);\n            if (!IsNull(parent))\n                return OrdinaryHasMetadata(MetadataKey, parent, P);\n            return false;\n        }\n        // ******* OrdinaryHasOwnMetadata(MetadataKey, O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinaryhasownmetadata\n        function OrdinaryHasOwnMetadata(MetadataKey, O, P) {\n            var provider = GetMetadataProvider(O, P, /*Create*/ false);\n            if (IsUndefined(provider))\n                return false;\n            return ToBoolean(provider.OrdinaryHasOwnMetadata(MetadataKey, O, P));\n        }\n        // ******* OrdinaryGetMetadata(MetadataKey, O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinarygetmetadata\n        function OrdinaryGetMetadata(MetadataKey, O, P) {\n            var hasOwn = OrdinaryHasOwnMetadata(MetadataKey, O, P);\n            if (hasOwn)\n                return OrdinaryGetOwnMetadata(MetadataKey, O, P);\n            var parent = OrdinaryGetPrototypeOf(O);\n            if (!IsNull(parent))\n                return OrdinaryGetMetadata(MetadataKey, parent, P);\n            return undefined;\n        }\n        // 3.1.4.1 OrdinaryGetOwnMetadata(MetadataKey, O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinarygetownmetadata\n        function OrdinaryGetOwnMetadata(MetadataKey, O, P) {\n            var provider = GetMetadataProvider(O, P, /*Create*/ false);\n            if (IsUndefined(provider))\n                return;\n            return provider.OrdinaryGetOwnMetadata(MetadataKey, O, P);\n        }\n        // 3.1.5.1 OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinarydefineownmetadata\n        function OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P) {\n            var provider = GetMetadataProvider(O, P, /*Create*/ true);\n            provider.OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P);\n        }\n        // 3.1.6.1 OrdinaryMetadataKeys(O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinarymetadatakeys\n        function OrdinaryMetadataKeys(O, P) {\n            var ownKeys = OrdinaryOwnMetadataKeys(O, P);\n            var parent = OrdinaryGetPrototypeOf(O);\n            if (parent === null)\n                return ownKeys;\n            var parentKeys = OrdinaryMetadataKeys(parent, P);\n            if (parentKeys.length <= 0)\n                return ownKeys;\n            if (ownKeys.length <= 0)\n                return parentKeys;\n            var set = new _Set();\n            var keys = [];\n            for (var _i = 0, ownKeys_1 = ownKeys; _i < ownKeys_1.length; _i++) {\n                var key = ownKeys_1[_i];\n                var hasKey = set.has(key);\n                if (!hasKey) {\n                    set.add(key);\n                    keys.push(key);\n                }\n            }\n            for (var _a = 0, parentKeys_1 = parentKeys; _a < parentKeys_1.length; _a++) {\n                var key = parentKeys_1[_a];\n                var hasKey = set.has(key);\n                if (!hasKey) {\n                    set.add(key);\n                    keys.push(key);\n                }\n            }\n            return keys;\n        }\n        // ******* OrdinaryOwnMetadataKeys(O, P)\n        // https://rbuckton.github.io/reflect-metadata/#ordinaryownmetadatakeys\n        function OrdinaryOwnMetadataKeys(O, P) {\n            var provider = GetMetadataProvider(O, P, /*create*/ false);\n            if (!provider) {\n                return [];\n            }\n            return provider.OrdinaryOwnMetadataKeys(O, P);\n        }\n        // 6 ECMAScript Data Types and Values\n        // https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values\n        function Type(x) {\n            if (x === null)\n                return 1 /* Null */;\n            switch (typeof x) {\n                case \"undefined\": return 0 /* Undefined */;\n                case \"boolean\": return 2 /* Boolean */;\n                case \"string\": return 3 /* String */;\n                case \"symbol\": return 4 /* Symbol */;\n                case \"number\": return 5 /* Number */;\n                case \"object\": return x === null ? 1 /* Null */ : 6 /* Object */;\n                default: return 6 /* Object */;\n            }\n        }\n        // 6.1.1 The Undefined Type\n        // https://tc39.github.io/ecma262/#sec-ecmascript-language-types-undefined-type\n        function IsUndefined(x) {\n            return x === undefined;\n        }\n        // 6.1.2 The Null Type\n        // https://tc39.github.io/ecma262/#sec-ecmascript-language-types-null-type\n        function IsNull(x) {\n            return x === null;\n        }\n        // 6.1.5 The Symbol Type\n        // https://tc39.github.io/ecma262/#sec-ecmascript-language-types-symbol-type\n        function IsSymbol(x) {\n            return typeof x === \"symbol\";\n        }\n        // 6.1.7 The Object Type\n        // https://tc39.github.io/ecma262/#sec-object-type\n        function IsObject(x) {\n            return typeof x === \"object\" ? x !== null : typeof x === \"function\";\n        }\n        // 7.1 Type Conversion\n        // https://tc39.github.io/ecma262/#sec-type-conversion\n        // 7.1.1 ToPrimitive(input [, PreferredType])\n        // https://tc39.github.io/ecma262/#sec-toprimitive\n        function ToPrimitive(input, PreferredType) {\n            switch (Type(input)) {\n                case 0 /* Undefined */: return input;\n                case 1 /* Null */: return input;\n                case 2 /* Boolean */: return input;\n                case 3 /* String */: return input;\n                case 4 /* Symbol */: return input;\n                case 5 /* Number */: return input;\n            }\n            var hint = PreferredType === 3 /* String */ ? \"string\" : PreferredType === 5 /* Number */ ? \"number\" : \"default\";\n            var exoticToPrim = GetMethod(input, toPrimitiveSymbol);\n            if (exoticToPrim !== undefined) {\n                var result = exoticToPrim.call(input, hint);\n                if (IsObject(result))\n                    throw new TypeError();\n                return result;\n            }\n            return OrdinaryToPrimitive(input, hint === \"default\" ? \"number\" : hint);\n        }\n        // 7.1.1.1 OrdinaryToPrimitive(O, hint)\n        // https://tc39.github.io/ecma262/#sec-ordinarytoprimitive\n        function OrdinaryToPrimitive(O, hint) {\n            if (hint === \"string\") {\n                var toString_1 = O.toString;\n                if (IsCallable(toString_1)) {\n                    var result = toString_1.call(O);\n                    if (!IsObject(result))\n                        return result;\n                }\n                var valueOf = O.valueOf;\n                if (IsCallable(valueOf)) {\n                    var result = valueOf.call(O);\n                    if (!IsObject(result))\n                        return result;\n                }\n            }\n            else {\n                var valueOf = O.valueOf;\n                if (IsCallable(valueOf)) {\n                    var result = valueOf.call(O);\n                    if (!IsObject(result))\n                        return result;\n                }\n                var toString_2 = O.toString;\n                if (IsCallable(toString_2)) {\n                    var result = toString_2.call(O);\n                    if (!IsObject(result))\n                        return result;\n                }\n            }\n            throw new TypeError();\n        }\n        // 7.1.2 ToBoolean(argument)\n        // https://tc39.github.io/ecma262/2016/#sec-toboolean\n        function ToBoolean(argument) {\n            return !!argument;\n        }\n        // 7.1.12 ToString(argument)\n        // https://tc39.github.io/ecma262/#sec-tostring\n        function ToString(argument) {\n            return \"\" + argument;\n        }\n        // 7.1.14 ToPropertyKey(argument)\n        // https://tc39.github.io/ecma262/#sec-topropertykey\n        function ToPropertyKey(argument) {\n            var key = ToPrimitive(argument, 3 /* String */);\n            if (IsSymbol(key))\n                return key;\n            return ToString(key);\n        }\n        // 7.2 Testing and Comparison Operations\n        // https://tc39.github.io/ecma262/#sec-testing-and-comparison-operations\n        // 7.2.2 IsArray(argument)\n        // https://tc39.github.io/ecma262/#sec-isarray\n        function IsArray(argument) {\n            return Array.isArray\n                ? Array.isArray(argument)\n                : argument instanceof Object\n                    ? argument instanceof Array\n                    : Object.prototype.toString.call(argument) === \"[object Array]\";\n        }\n        // 7.2.3 IsCallable(argument)\n        // https://tc39.github.io/ecma262/#sec-iscallable\n        function IsCallable(argument) {\n            // NOTE: This is an approximation as we cannot check for [[Call]] internal method.\n            return typeof argument === \"function\";\n        }\n        // 7.2.4 IsConstructor(argument)\n        // https://tc39.github.io/ecma262/#sec-isconstructor\n        function IsConstructor(argument) {\n            // NOTE: This is an approximation as we cannot check for [[Construct]] internal method.\n            return typeof argument === \"function\";\n        }\n        // 7.2.7 IsPropertyKey(argument)\n        // https://tc39.github.io/ecma262/#sec-ispropertykey\n        function IsPropertyKey(argument) {\n            switch (Type(argument)) {\n                case 3 /* String */: return true;\n                case 4 /* Symbol */: return true;\n                default: return false;\n            }\n        }\n        function SameValueZero(x, y) {\n            return x === y || x !== x && y !== y;\n        }\n        // 7.3 Operations on Objects\n        // https://tc39.github.io/ecma262/#sec-operations-on-objects\n        // 7.3.9 GetMethod(V, P)\n        // https://tc39.github.io/ecma262/#sec-getmethod\n        function GetMethod(V, P) {\n            var func = V[P];\n            if (func === undefined || func === null)\n                return undefined;\n            if (!IsCallable(func))\n                throw new TypeError();\n            return func;\n        }\n        // 7.4 Operations on Iterator Objects\n        // https://tc39.github.io/ecma262/#sec-operations-on-iterator-objects\n        function GetIterator(obj) {\n            var method = GetMethod(obj, iteratorSymbol);\n            if (!IsCallable(method))\n                throw new TypeError(); // from Call\n            var iterator = method.call(obj);\n            if (!IsObject(iterator))\n                throw new TypeError();\n            return iterator;\n        }\n        // 7.4.4 IteratorValue(iterResult)\n        // https://tc39.github.io/ecma262/2016/#sec-iteratorvalue\n        function IteratorValue(iterResult) {\n            return iterResult.value;\n        }\n        // 7.4.5 IteratorStep(iterator)\n        // https://tc39.github.io/ecma262/#sec-iteratorstep\n        function IteratorStep(iterator) {\n            var result = iterator.next();\n            return result.done ? false : result;\n        }\n        // 7.4.6 IteratorClose(iterator, completion)\n        // https://tc39.github.io/ecma262/#sec-iteratorclose\n        function IteratorClose(iterator) {\n            var f = iterator[\"return\"];\n            if (f)\n                f.call(iterator);\n        }\n        // 9.1 Ordinary Object Internal Methods and Internal Slots\n        // https://tc39.github.io/ecma262/#sec-ordinary-object-internal-methods-and-internal-slots\n        // ******* OrdinaryGetPrototypeOf(O)\n        // https://tc39.github.io/ecma262/#sec-ordinarygetprototypeof\n        function OrdinaryGetPrototypeOf(O) {\n            var proto = Object.getPrototypeOf(O);\n            if (typeof O !== \"function\" || O === functionPrototype)\n                return proto;\n            // TypeScript doesn't set __proto__ in ES5, as it's non-standard.\n            // Try to determine the superclass constructor. Compatible implementations\n            // must either set __proto__ on a subclass constructor to the superclass constructor,\n            // or ensure each class has a valid `constructor` property on its prototype that\n            // points back to the constructor.\n            // If this is not the same as Function.[[Prototype]], then this is definately inherited.\n            // This is the case when in ES6 or when using __proto__ in a compatible browser.\n            if (proto !== functionPrototype)\n                return proto;\n            // If the super prototype is Object.prototype, null, or undefined, then we cannot determine the heritage.\n            var prototype = O.prototype;\n            var prototypeProto = prototype && Object.getPrototypeOf(prototype);\n            if (prototypeProto == null || prototypeProto === Object.prototype)\n                return proto;\n            // If the constructor was not a function, then we cannot determine the heritage.\n            var constructor = prototypeProto.constructor;\n            if (typeof constructor !== \"function\")\n                return proto;\n            // If we have some kind of self-reference, then we cannot determine the heritage.\n            if (constructor === O)\n                return proto;\n            // we have a pretty good guess at the heritage.\n            return constructor;\n        }\n        // Global metadata registry\n        // - Allows `import \"reflect-metadata\"` and `import \"reflect-metadata/no-conflict\"` to interoperate.\n        // - Uses isolated metadata if `Reflect` is frozen before the registry can be installed.\n        /**\n         * Creates a registry used to allow multiple `reflect-metadata` providers.\n         */\n        function CreateMetadataRegistry() {\n            var fallback;\n            if (!IsUndefined(registrySymbol) &&\n                typeof root.Reflect !== \"undefined\" &&\n                !(registrySymbol in root.Reflect) &&\n                typeof root.Reflect.defineMetadata === \"function\") {\n                // interoperate with older version of `reflect-metadata` that did not support a registry.\n                fallback = CreateFallbackProvider(root.Reflect);\n            }\n            var first;\n            var second;\n            var rest;\n            var targetProviderMap = new _WeakMap();\n            var registry = {\n                registerProvider: registerProvider,\n                getProvider: getProvider,\n                setProvider: setProvider,\n            };\n            return registry;\n            function registerProvider(provider) {\n                if (!Object.isExtensible(registry)) {\n                    throw new Error(\"Cannot add provider to a frozen registry.\");\n                }\n                switch (true) {\n                    case fallback === provider: break;\n                    case IsUndefined(first):\n                        first = provider;\n                        break;\n                    case first === provider: break;\n                    case IsUndefined(second):\n                        second = provider;\n                        break;\n                    case second === provider: break;\n                    default:\n                        if (rest === undefined)\n                            rest = new _Set();\n                        rest.add(provider);\n                        break;\n                }\n            }\n            function getProviderNoCache(O, P) {\n                if (!IsUndefined(first)) {\n                    if (first.isProviderFor(O, P))\n                        return first;\n                    if (!IsUndefined(second)) {\n                        if (second.isProviderFor(O, P))\n                            return first;\n                        if (!IsUndefined(rest)) {\n                            var iterator = GetIterator(rest);\n                            while (true) {\n                                var next = IteratorStep(iterator);\n                                if (!next) {\n                                    return undefined;\n                                }\n                                var provider = IteratorValue(next);\n                                if (provider.isProviderFor(O, P)) {\n                                    IteratorClose(iterator);\n                                    return provider;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (!IsUndefined(fallback) && fallback.isProviderFor(O, P)) {\n                    return fallback;\n                }\n                return undefined;\n            }\n            function getProvider(O, P) {\n                var providerMap = targetProviderMap.get(O);\n                var provider;\n                if (!IsUndefined(providerMap)) {\n                    provider = providerMap.get(P);\n                }\n                if (!IsUndefined(provider)) {\n                    return provider;\n                }\n                provider = getProviderNoCache(O, P);\n                if (!IsUndefined(provider)) {\n                    if (IsUndefined(providerMap)) {\n                        providerMap = new _Map();\n                        targetProviderMap.set(O, providerMap);\n                    }\n                    providerMap.set(P, provider);\n                }\n                return provider;\n            }\n            function hasProvider(provider) {\n                if (IsUndefined(provider))\n                    throw new TypeError();\n                return first === provider || second === provider || !IsUndefined(rest) && rest.has(provider);\n            }\n            function setProvider(O, P, provider) {\n                if (!hasProvider(provider)) {\n                    throw new Error(\"Metadata provider not registered.\");\n                }\n                var existingProvider = getProvider(O, P);\n                if (existingProvider !== provider) {\n                    if (!IsUndefined(existingProvider)) {\n                        return false;\n                    }\n                    var providerMap = targetProviderMap.get(O);\n                    if (IsUndefined(providerMap)) {\n                        providerMap = new _Map();\n                        targetProviderMap.set(O, providerMap);\n                    }\n                    providerMap.set(P, provider);\n                }\n                return true;\n            }\n        }\n        /**\n         * Gets or creates the shared registry of metadata providers.\n         */\n        function GetOrCreateMetadataRegistry() {\n            var metadataRegistry;\n            if (!IsUndefined(registrySymbol) && IsObject(root.Reflect) && Object.isExtensible(root.Reflect)) {\n                metadataRegistry = root.Reflect[registrySymbol];\n            }\n            if (IsUndefined(metadataRegistry)) {\n                metadataRegistry = CreateMetadataRegistry();\n            }\n            if (!IsUndefined(registrySymbol) && IsObject(root.Reflect) && Object.isExtensible(root.Reflect)) {\n                Object.defineProperty(root.Reflect, registrySymbol, {\n                    enumerable: false,\n                    configurable: false,\n                    writable: false,\n                    value: metadataRegistry\n                });\n            }\n            return metadataRegistry;\n        }\n        function CreateMetadataProvider(registry) {\n            // [[Metadata]] internal slot\n            // https://rbuckton.github.io/reflect-metadata/#ordinary-object-internal-methods-and-internal-slots\n            var metadata = new _WeakMap();\n            var provider = {\n                isProviderFor: function (O, P) {\n                    var targetMetadata = metadata.get(O);\n                    if (IsUndefined(targetMetadata))\n                        return false;\n                    return targetMetadata.has(P);\n                },\n                OrdinaryDefineOwnMetadata: OrdinaryDefineOwnMetadata,\n                OrdinaryHasOwnMetadata: OrdinaryHasOwnMetadata,\n                OrdinaryGetOwnMetadata: OrdinaryGetOwnMetadata,\n                OrdinaryOwnMetadataKeys: OrdinaryOwnMetadataKeys,\n                OrdinaryDeleteMetadata: OrdinaryDeleteMetadata,\n            };\n            metadataRegistry.registerProvider(provider);\n            return provider;\n            function GetOrCreateMetadataMap(O, P, Create) {\n                var targetMetadata = metadata.get(O);\n                var createdTargetMetadata = false;\n                if (IsUndefined(targetMetadata)) {\n                    if (!Create)\n                        return undefined;\n                    targetMetadata = new _Map();\n                    metadata.set(O, targetMetadata);\n                    createdTargetMetadata = true;\n                }\n                var metadataMap = targetMetadata.get(P);\n                if (IsUndefined(metadataMap)) {\n                    if (!Create)\n                        return undefined;\n                    metadataMap = new _Map();\n                    targetMetadata.set(P, metadataMap);\n                    if (!registry.setProvider(O, P, provider)) {\n                        targetMetadata.delete(P);\n                        if (createdTargetMetadata) {\n                            metadata.delete(O);\n                        }\n                        throw new Error(\"Wrong provider for target.\");\n                    }\n                }\n                return metadataMap;\n            }\n            // ******* OrdinaryHasOwnMetadata(MetadataKey, O, P)\n            // https://rbuckton.github.io/reflect-metadata/#ordinaryhasownmetadata\n            function OrdinaryHasOwnMetadata(MetadataKey, O, P) {\n                var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n                if (IsUndefined(metadataMap))\n                    return false;\n                return ToBoolean(metadataMap.has(MetadataKey));\n            }\n            // 3.1.4.1 OrdinaryGetOwnMetadata(MetadataKey, O, P)\n            // https://rbuckton.github.io/reflect-metadata/#ordinarygetownmetadata\n            function OrdinaryGetOwnMetadata(MetadataKey, O, P) {\n                var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n                if (IsUndefined(metadataMap))\n                    return undefined;\n                return metadataMap.get(MetadataKey);\n            }\n            // 3.1.5.1 OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P)\n            // https://rbuckton.github.io/reflect-metadata/#ordinarydefineownmetadata\n            function OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P) {\n                var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ true);\n                metadataMap.set(MetadataKey, MetadataValue);\n            }\n            // ******* OrdinaryOwnMetadataKeys(O, P)\n            // https://rbuckton.github.io/reflect-metadata/#ordinaryownmetadatakeys\n            function OrdinaryOwnMetadataKeys(O, P) {\n                var keys = [];\n                var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n                if (IsUndefined(metadataMap))\n                    return keys;\n                var keysObj = metadataMap.keys();\n                var iterator = GetIterator(keysObj);\n                var k = 0;\n                while (true) {\n                    var next = IteratorStep(iterator);\n                    if (!next) {\n                        keys.length = k;\n                        return keys;\n                    }\n                    var nextValue = IteratorValue(next);\n                    try {\n                        keys[k] = nextValue;\n                    }\n                    catch (e) {\n                        try {\n                            IteratorClose(iterator);\n                        }\n                        finally {\n                            throw e;\n                        }\n                    }\n                    k++;\n                }\n            }\n            function OrdinaryDeleteMetadata(MetadataKey, O, P) {\n                var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n                if (IsUndefined(metadataMap))\n                    return false;\n                if (!metadataMap.delete(MetadataKey))\n                    return false;\n                if (metadataMap.size === 0) {\n                    var targetMetadata = metadata.get(O);\n                    if (!IsUndefined(targetMetadata)) {\n                        targetMetadata.delete(P);\n                        if (targetMetadata.size === 0) {\n                            metadata.delete(targetMetadata);\n                        }\n                    }\n                }\n                return true;\n            }\n        }\n        function CreateFallbackProvider(reflect) {\n            var defineMetadata = reflect.defineMetadata, hasOwnMetadata = reflect.hasOwnMetadata, getOwnMetadata = reflect.getOwnMetadata, getOwnMetadataKeys = reflect.getOwnMetadataKeys, deleteMetadata = reflect.deleteMetadata;\n            var metadataOwner = new _WeakMap();\n            var provider = {\n                isProviderFor: function (O, P) {\n                    var metadataPropertySet = metadataOwner.get(O);\n                    if (!IsUndefined(metadataPropertySet) && metadataPropertySet.has(P)) {\n                        return true;\n                    }\n                    if (getOwnMetadataKeys(O, P).length) {\n                        if (IsUndefined(metadataPropertySet)) {\n                            metadataPropertySet = new _Set();\n                            metadataOwner.set(O, metadataPropertySet);\n                        }\n                        metadataPropertySet.add(P);\n                        return true;\n                    }\n                    return false;\n                },\n                OrdinaryDefineOwnMetadata: defineMetadata,\n                OrdinaryHasOwnMetadata: hasOwnMetadata,\n                OrdinaryGetOwnMetadata: getOwnMetadata,\n                OrdinaryOwnMetadataKeys: getOwnMetadataKeys,\n                OrdinaryDeleteMetadata: deleteMetadata,\n            };\n            return provider;\n        }\n        /**\n         * Gets the metadata provider for an object. If the object has no metadata provider and this is for a create operation,\n         * then this module's metadata provider is assigned to the object.\n         */\n        function GetMetadataProvider(O, P, Create) {\n            var registeredProvider = metadataRegistry.getProvider(O, P);\n            if (!IsUndefined(registeredProvider)) {\n                return registeredProvider;\n            }\n            if (Create) {\n                if (metadataRegistry.setProvider(O, P, metadataProvider)) {\n                    return metadataProvider;\n                }\n                throw new Error(\"Illegal state.\");\n            }\n            return undefined;\n        }\n        // naive Map shim\n        function CreateMapPolyfill() {\n            var cacheSentinel = {};\n            var arraySentinel = [];\n            var MapIterator = /** @class */ (function () {\n                function MapIterator(keys, values, selector) {\n                    this._index = 0;\n                    this._keys = keys;\n                    this._values = values;\n                    this._selector = selector;\n                }\n                MapIterator.prototype[\"@@iterator\"] = function () { return this; };\n                MapIterator.prototype[iteratorSymbol] = function () { return this; };\n                MapIterator.prototype.next = function () {\n                    var index = this._index;\n                    if (index >= 0 && index < this._keys.length) {\n                        var result = this._selector(this._keys[index], this._values[index]);\n                        if (index + 1 >= this._keys.length) {\n                            this._index = -1;\n                            this._keys = arraySentinel;\n                            this._values = arraySentinel;\n                        }\n                        else {\n                            this._index++;\n                        }\n                        return { value: result, done: false };\n                    }\n                    return { value: undefined, done: true };\n                };\n                MapIterator.prototype.throw = function (error) {\n                    if (this._index >= 0) {\n                        this._index = -1;\n                        this._keys = arraySentinel;\n                        this._values = arraySentinel;\n                    }\n                    throw error;\n                };\n                MapIterator.prototype.return = function (value) {\n                    if (this._index >= 0) {\n                        this._index = -1;\n                        this._keys = arraySentinel;\n                        this._values = arraySentinel;\n                    }\n                    return { value: value, done: true };\n                };\n                return MapIterator;\n            }());\n            var Map = /** @class */ (function () {\n                function Map() {\n                    this._keys = [];\n                    this._values = [];\n                    this._cacheKey = cacheSentinel;\n                    this._cacheIndex = -2;\n                }\n                Object.defineProperty(Map.prototype, \"size\", {\n                    get: function () { return this._keys.length; },\n                    enumerable: true,\n                    configurable: true\n                });\n                Map.prototype.has = function (key) { return this._find(key, /*insert*/ false) >= 0; };\n                Map.prototype.get = function (key) {\n                    var index = this._find(key, /*insert*/ false);\n                    return index >= 0 ? this._values[index] : undefined;\n                };\n                Map.prototype.set = function (key, value) {\n                    var index = this._find(key, /*insert*/ true);\n                    this._values[index] = value;\n                    return this;\n                };\n                Map.prototype.delete = function (key) {\n                    var index = this._find(key, /*insert*/ false);\n                    if (index >= 0) {\n                        var size = this._keys.length;\n                        for (var i = index + 1; i < size; i++) {\n                            this._keys[i - 1] = this._keys[i];\n                            this._values[i - 1] = this._values[i];\n                        }\n                        this._keys.length--;\n                        this._values.length--;\n                        if (SameValueZero(key, this._cacheKey)) {\n                            this._cacheKey = cacheSentinel;\n                            this._cacheIndex = -2;\n                        }\n                        return true;\n                    }\n                    return false;\n                };\n                Map.prototype.clear = function () {\n                    this._keys.length = 0;\n                    this._values.length = 0;\n                    this._cacheKey = cacheSentinel;\n                    this._cacheIndex = -2;\n                };\n                Map.prototype.keys = function () { return new MapIterator(this._keys, this._values, getKey); };\n                Map.prototype.values = function () { return new MapIterator(this._keys, this._values, getValue); };\n                Map.prototype.entries = function () { return new MapIterator(this._keys, this._values, getEntry); };\n                Map.prototype[\"@@iterator\"] = function () { return this.entries(); };\n                Map.prototype[iteratorSymbol] = function () { return this.entries(); };\n                Map.prototype._find = function (key, insert) {\n                    if (!SameValueZero(this._cacheKey, key)) {\n                        this._cacheIndex = -1;\n                        for (var i = 0; i < this._keys.length; i++) {\n                            if (SameValueZero(this._keys[i], key)) {\n                                this._cacheIndex = i;\n                                break;\n                            }\n                        }\n                    }\n                    if (this._cacheIndex < 0 && insert) {\n                        this._cacheIndex = this._keys.length;\n                        this._keys.push(key);\n                        this._values.push(undefined);\n                    }\n                    return this._cacheIndex;\n                };\n                return Map;\n            }());\n            return Map;\n            function getKey(key, _) {\n                return key;\n            }\n            function getValue(_, value) {\n                return value;\n            }\n            function getEntry(key, value) {\n                return [key, value];\n            }\n        }\n        // naive Set shim\n        function CreateSetPolyfill() {\n            var Set = /** @class */ (function () {\n                function Set() {\n                    this._map = new _Map();\n                }\n                Object.defineProperty(Set.prototype, \"size\", {\n                    get: function () { return this._map.size; },\n                    enumerable: true,\n                    configurable: true\n                });\n                Set.prototype.has = function (value) { return this._map.has(value); };\n                Set.prototype.add = function (value) { return this._map.set(value, value), this; };\n                Set.prototype.delete = function (value) { return this._map.delete(value); };\n                Set.prototype.clear = function () { this._map.clear(); };\n                Set.prototype.keys = function () { return this._map.keys(); };\n                Set.prototype.values = function () { return this._map.keys(); };\n                Set.prototype.entries = function () { return this._map.entries(); };\n                Set.prototype[\"@@iterator\"] = function () { return this.keys(); };\n                Set.prototype[iteratorSymbol] = function () { return this.keys(); };\n                return Set;\n            }());\n            return Set;\n        }\n        // naive WeakMap shim\n        function CreateWeakMapPolyfill() {\n            var UUID_SIZE = 16;\n            var keys = HashMap.create();\n            var rootKey = CreateUniqueKey();\n            return /** @class */ (function () {\n                function WeakMap() {\n                    this._key = CreateUniqueKey();\n                }\n                WeakMap.prototype.has = function (target) {\n                    var table = GetOrCreateWeakMapTable(target, /*create*/ false);\n                    return table !== undefined ? HashMap.has(table, this._key) : false;\n                };\n                WeakMap.prototype.get = function (target) {\n                    var table = GetOrCreateWeakMapTable(target, /*create*/ false);\n                    return table !== undefined ? HashMap.get(table, this._key) : undefined;\n                };\n                WeakMap.prototype.set = function (target, value) {\n                    var table = GetOrCreateWeakMapTable(target, /*create*/ true);\n                    table[this._key] = value;\n                    return this;\n                };\n                WeakMap.prototype.delete = function (target) {\n                    var table = GetOrCreateWeakMapTable(target, /*create*/ false);\n                    return table !== undefined ? delete table[this._key] : false;\n                };\n                WeakMap.prototype.clear = function () {\n                    // NOTE: not a real clear, just makes the previous data unreachable\n                    this._key = CreateUniqueKey();\n                };\n                return WeakMap;\n            }());\n            function CreateUniqueKey() {\n                var key;\n                do\n                    key = \"@@WeakMap@@\" + CreateUUID();\n                while (HashMap.has(keys, key));\n                keys[key] = true;\n                return key;\n            }\n            function GetOrCreateWeakMapTable(target, create) {\n                if (!hasOwn.call(target, rootKey)) {\n                    if (!create)\n                        return undefined;\n                    Object.defineProperty(target, rootKey, { value: HashMap.create() });\n                }\n                return target[rootKey];\n            }\n            function FillRandomBytes(buffer, size) {\n                for (var i = 0; i < size; ++i)\n                    buffer[i] = Math.random() * 0xff | 0;\n                return buffer;\n            }\n            function GenRandomBytes(size) {\n                if (typeof Uint8Array === \"function\") {\n                    var array = new Uint8Array(size);\n                    if (typeof crypto !== \"undefined\") {\n                        crypto.getRandomValues(array);\n                    }\n                    else if (typeof msCrypto !== \"undefined\") {\n                        msCrypto.getRandomValues(array);\n                    }\n                    else {\n                        FillRandomBytes(array, size);\n                    }\n                    return array;\n                }\n                return FillRandomBytes(new Array(size), size);\n            }\n            function CreateUUID() {\n                var data = GenRandomBytes(UUID_SIZE);\n                // mark as random - RFC 4122 § 4.4\n                data[6] = data[6] & 0x4f | 0x40;\n                data[8] = data[8] & 0xbf | 0x80;\n                var result = \"\";\n                for (var offset = 0; offset < UUID_SIZE; ++offset) {\n                    var byte = data[offset];\n                    if (offset === 4 || offset === 6 || offset === 8)\n                        result += \"-\";\n                    if (byte < 16)\n                        result += \"0\";\n                    result += byte.toString(16).toLowerCase();\n                }\n                return result;\n            }\n        }\n        // uses a heuristic used by v8 and chakra to force an object into dictionary mode.\n        function MakeDictionary(obj) {\n            obj.__ = undefined;\n            delete obj.__;\n            return obj;\n        }\n    });\n})(Reflect || (Reflect = {}));\n"], "mappings": ";AAcA,IAAI;AAAA,CACH,SAAUA,UAAS;AAGhB,GAAC,SAAU,SAAS;AAChB,QAAI,OAAO,OAAO,eAAe,WAAW,aACxC,OAAO,WAAW,WAAW,SACzB,OAAO,SAAS,WAAW,OACvB,OAAO,SAAS,WAAW,OACvB,eAAe;AAC/B,QAAI,WAAW,aAAaA,QAAO;AACnC,QAAI,OAAO,KAAK,YAAY,aAAa;AACrC,iBAAW,aAAa,KAAK,SAAS,QAAQ;AAAA,IAClD;AACA,YAAQ,UAAU,IAAI;AACtB,QAAI,OAAO,KAAK,YAAY,aAAa;AACrC,WAAK,UAAUA;AAAA,IACnB;AACA,aAAS,aAAa,QAAQ,UAAU;AACpC,aAAO,SAAU,KAAK,OAAO;AACzB,eAAO,eAAe,QAAQ,KAAK,EAAE,cAAc,MAAM,UAAU,MAAM,MAAa,CAAC;AACvF,YAAI;AACA,mBAAS,KAAK,KAAK;AAAA,MAC3B;AAAA,IACJ;AACA,aAAS,eAAe;AACpB,UAAI;AACA,eAAO,SAAS,cAAc,EAAE;AAAA,MACpC,SACO,GAAG;AAAA,MAAE;AAAA,IAChB;AACA,aAAS,mBAAmB;AACxB,UAAI;AACA,gBAAQ,QAAQ,MAAM,iCAAiC;AAAA,MAC3D,SACO,GAAG;AAAA,MAAE;AAAA,IAChB;AACA,aAAS,iBAAiB;AACtB,aAAO,aAAa,KAAK,iBAAiB;AAAA,IAC9C;AAAA,EACJ,GAAG,SAAU,UAAU,MAAM;AACzB,QAAI,SAAS,OAAO,UAAU;AAE9B,QAAI,iBAAiB,OAAO,WAAW;AACvC,QAAI,oBAAoB,kBAAkB,OAAO,OAAO,gBAAgB,cAAc,OAAO,cAAc;AAC3G,QAAI,iBAAiB,kBAAkB,OAAO,OAAO,aAAa,cAAc,OAAO,WAAW;AAClG,QAAI,iBAAiB,OAAO,OAAO,WAAW;AAC9C,QAAI,gBAAgB,EAAE,WAAW,CAAC,EAAE,aAAa;AACjD,QAAI,YAAY,CAAC,kBAAkB,CAAC;AACpC,QAAI,UAAU;AAAA;AAAA,MAEV,QAAQ,iBACF,WAAY;AAAE,eAAO,eAAe,uBAAO,OAAO,IAAI,CAAC;AAAA,MAAG,IAC1D,gBACI,WAAY;AAAE,eAAO,eAAe,EAAE,WAAW,KAAK,CAAC;AAAA,MAAG,IAC1D,WAAY;AAAE,eAAO,eAAe,CAAC,CAAC;AAAA,MAAG;AAAA,MACnD,KAAK,YACC,SAAU,KAAK,KAAK;AAAE,eAAO,OAAO,KAAK,KAAK,GAAG;AAAA,MAAG,IACpD,SAAU,KAAK,KAAK;AAAE,eAAO,OAAO;AAAA,MAAK;AAAA,MAC/C,KAAK,YACC,SAAU,KAAK,KAAK;AAAE,eAAO,OAAO,KAAK,KAAK,GAAG,IAAI,IAAI,GAAG,IAAI;AAAA,MAAW,IAC3E,SAAU,KAAK,KAAK;AAAE,eAAO,IAAI,GAAG;AAAA,MAAG;AAAA,IACjD;AAEA,QAAI,oBAAoB,OAAO,eAAe,QAAQ;AACtD,QAAI,OAAO,OAAO,QAAQ,cAAc,OAAO,IAAI,UAAU,YAAY,aAAa,MAAM,kBAAkB;AAC9G,QAAI,OAAO,OAAO,QAAQ,cAAc,OAAO,IAAI,UAAU,YAAY,aAAa,MAAM,kBAAkB;AAC9G,QAAI,WAAW,OAAO,YAAY,aAAa,UAAU,sBAAsB;AAC/E,QAAI,iBAAiB,iBAAiB,OAAO,IAAI,4BAA4B,IAAI;AACjF,QAAI,mBAAmB,4BAA4B;AACnD,QAAI,mBAAmB,uBAAuB,gBAAgB;AAwC9D,aAAS,SAAS,YAAY,QAAQ,aAAa,YAAY;AAC3D,UAAI,CAAC,YAAY,WAAW,GAAG;AAC3B,YAAI,CAAC,QAAQ,UAAU;AACnB,gBAAM,IAAI,UAAU;AACxB,YAAI,CAAC,SAAS,MAAM;AAChB,gBAAM,IAAI,UAAU;AACxB,YAAI,CAAC,SAAS,UAAU,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,OAAO,UAAU;AACvE,gBAAM,IAAI,UAAU;AACxB,YAAI,OAAO,UAAU;AACjB,uBAAa;AACjB,sBAAc,cAAc,WAAW;AACvC,eAAO,iBAAiB,YAAY,QAAQ,aAAa,UAAU;AAAA,MACvE,OACK;AACD,YAAI,CAAC,QAAQ,UAAU;AACnB,gBAAM,IAAI,UAAU;AACxB,YAAI,CAAC,cAAc,MAAM;AACrB,gBAAM,IAAI,UAAU;AACxB,eAAO,oBAAoB,YAAY,MAAM;AAAA,MACjD;AAAA,IACJ;AACA,aAAS,YAAY,QAAQ;AA2C7B,aAAS,SAAS,aAAa,eAAe;AAC1C,eAAS,UAAU,QAAQ,aAAa;AACpC,YAAI,CAAC,SAAS,MAAM;AAChB,gBAAM,IAAI,UAAU;AACxB,YAAI,CAAC,YAAY,WAAW,KAAK,CAAC,cAAc,WAAW;AACvD,gBAAM,IAAI,UAAU;AACxB,kCAA0B,aAAa,eAAe,QAAQ,WAAW;AAAA,MAC7E;AACA,aAAO;AAAA,IACX;AACA,aAAS,YAAY,QAAQ;AAwC7B,aAAS,eAAe,aAAa,eAAe,QAAQ,aAAa;AACrE,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,0BAA0B,aAAa,eAAe,QAAQ,WAAW;AAAA,IACpF;AACA,aAAS,kBAAkB,cAAc;AAmCzC,aAAS,YAAY,aAAa,QAAQ,aAAa;AACnD,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,oBAAoB,aAAa,QAAQ,WAAW;AAAA,IAC/D;AACA,aAAS,eAAe,WAAW;AAmCnC,aAAS,eAAe,aAAa,QAAQ,aAAa;AACtD,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,uBAAuB,aAAa,QAAQ,WAAW;AAAA,IAClE;AACA,aAAS,kBAAkB,cAAc;AAmCzC,aAAS,YAAY,aAAa,QAAQ,aAAa;AACnD,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,oBAAoB,aAAa,QAAQ,WAAW;AAAA,IAC/D;AACA,aAAS,eAAe,WAAW;AAmCnC,aAAS,eAAe,aAAa,QAAQ,aAAa;AACtD,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,uBAAuB,aAAa,QAAQ,WAAW;AAAA,IAClE;AACA,aAAS,kBAAkB,cAAc;AAkCzC,aAAS,gBAAgB,QAAQ,aAAa;AAC1C,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,qBAAqB,QAAQ,WAAW;AAAA,IACnD;AACA,aAAS,mBAAmB,eAAe;AAkC3C,aAAS,mBAAmB,QAAQ,aAAa;AAC7C,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,aAAO,wBAAwB,QAAQ,WAAW;AAAA,IACtD;AACA,aAAS,sBAAsB,kBAAkB;AAmCjD,aAAS,eAAe,aAAa,QAAQ,aAAa;AACtD,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,UAAI,CAAC,SAAS,MAAM;AAChB,cAAM,IAAI,UAAU;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,sBAAc,cAAc,WAAW;AAC3C,UAAI,WAAW;AAAA,QAAoB;AAAA,QAAQ;AAAA;AAAA,QAAwB;AAAA,MAAK;AACxE,UAAI,YAAY,QAAQ;AACpB,eAAO;AACX,aAAO,SAAS,uBAAuB,aAAa,QAAQ,WAAW;AAAA,IAC3E;AACA,aAAS,kBAAkB,cAAc;AACzC,aAAS,oBAAoB,YAAY,QAAQ;AAC7C,eAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC7C,YAAI,YAAY,WAAW,CAAC;AAC5B,YAAI,YAAY,UAAU,MAAM;AAChC,YAAI,CAAC,YAAY,SAAS,KAAK,CAAC,OAAO,SAAS,GAAG;AAC/C,cAAI,CAAC,cAAc,SAAS;AACxB,kBAAM,IAAI,UAAU;AACxB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,aAAS,iBAAiB,YAAY,QAAQ,aAAa,YAAY;AACnE,eAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC7C,YAAI,YAAY,WAAW,CAAC;AAC5B,YAAI,YAAY,UAAU,QAAQ,aAAa,UAAU;AACzD,YAAI,CAAC,YAAY,SAAS,KAAK,CAAC,OAAO,SAAS,GAAG;AAC/C,cAAI,CAAC,SAAS,SAAS;AACnB,kBAAM,IAAI,UAAU;AACxB,uBAAa;AAAA,QACjB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAGA,aAAS,oBAAoB,aAAa,GAAG,GAAG;AAC5C,UAAIC,UAAS,uBAAuB,aAAa,GAAG,CAAC;AACrD,UAAIA;AACA,eAAO;AACX,UAAI,SAAS,uBAAuB,CAAC;AACrC,UAAI,CAAC,OAAO,MAAM;AACd,eAAO,oBAAoB,aAAa,QAAQ,CAAC;AACrD,aAAO;AAAA,IACX;AAGA,aAAS,uBAAuB,aAAa,GAAG,GAAG;AAC/C,UAAI,WAAW;AAAA,QAAoB;AAAA,QAAG;AAAA;AAAA,QAAc;AAAA,MAAK;AACzD,UAAI,YAAY,QAAQ;AACpB,eAAO;AACX,aAAO,UAAU,SAAS,uBAAuB,aAAa,GAAG,CAAC,CAAC;AAAA,IACvE;AAGA,aAAS,oBAAoB,aAAa,GAAG,GAAG;AAC5C,UAAIA,UAAS,uBAAuB,aAAa,GAAG,CAAC;AACrD,UAAIA;AACA,eAAO,uBAAuB,aAAa,GAAG,CAAC;AACnD,UAAI,SAAS,uBAAuB,CAAC;AACrC,UAAI,CAAC,OAAO,MAAM;AACd,eAAO,oBAAoB,aAAa,QAAQ,CAAC;AACrD,aAAO;AAAA,IACX;AAGA,aAAS,uBAAuB,aAAa,GAAG,GAAG;AAC/C,UAAI,WAAW;AAAA,QAAoB;AAAA,QAAG;AAAA;AAAA,QAAc;AAAA,MAAK;AACzD,UAAI,YAAY,QAAQ;AACpB;AACJ,aAAO,SAAS,uBAAuB,aAAa,GAAG,CAAC;AAAA,IAC5D;AAGA,aAAS,0BAA0B,aAAa,eAAe,GAAG,GAAG;AACjE,UAAI,WAAW;AAAA,QAAoB;AAAA,QAAG;AAAA;AAAA,QAAc;AAAA,MAAI;AACxD,eAAS,0BAA0B,aAAa,eAAe,GAAG,CAAC;AAAA,IACvE;AAGA,aAAS,qBAAqB,GAAG,GAAG;AAChC,UAAI,UAAU,wBAAwB,GAAG,CAAC;AAC1C,UAAI,SAAS,uBAAuB,CAAC;AACrC,UAAI,WAAW;AACX,eAAO;AACX,UAAI,aAAa,qBAAqB,QAAQ,CAAC;AAC/C,UAAI,WAAW,UAAU;AACrB,eAAO;AACX,UAAI,QAAQ,UAAU;AAClB,eAAO;AACX,UAAI,MAAM,IAAI,KAAK;AACnB,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,YAAI,MAAM,UAAU,EAAE;AACtB,YAAI,SAAS,IAAI,IAAI,GAAG;AACxB,YAAI,CAAC,QAAQ;AACT,cAAI,IAAI,GAAG;AACX,eAAK,KAAK,GAAG;AAAA,QACjB;AAAA,MACJ;AACA,eAAS,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,QAAQ,MAAM;AACxE,YAAI,MAAM,aAAa,EAAE;AACzB,YAAI,SAAS,IAAI,IAAI,GAAG;AACxB,YAAI,CAAC,QAAQ;AACT,cAAI,IAAI,GAAG;AACX,eAAK,KAAK,GAAG;AAAA,QACjB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAGA,aAAS,wBAAwB,GAAG,GAAG;AACnC,UAAI,WAAW;AAAA,QAAoB;AAAA,QAAG;AAAA;AAAA,QAAc;AAAA,MAAK;AACzD,UAAI,CAAC,UAAU;AACX,eAAO,CAAC;AAAA,MACZ;AACA,aAAO,SAAS,wBAAwB,GAAG,CAAC;AAAA,IAChD;AAGA,aAAS,KAAK,GAAG;AACb,UAAI,MAAM;AACN,eAAO;AACX,cAAQ,OAAO,GAAG;AAAA,QACd,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAW,iBAAO;AAAA,QACvB,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAU,iBAAO,MAAM,OAAO,IAAe;AAAA,QAClD;AAAS,iBAAO;AAAA,MACpB;AAAA,IACJ;AAGA,aAAS,YAAY,GAAG;AACpB,aAAO,MAAM;AAAA,IACjB;AAGA,aAAS,OAAO,GAAG;AACf,aAAO,MAAM;AAAA,IACjB;AAGA,aAAS,SAAS,GAAG;AACjB,aAAO,OAAO,MAAM;AAAA,IACxB;AAGA,aAAS,SAAS,GAAG;AACjB,aAAO,OAAO,MAAM,WAAW,MAAM,OAAO,OAAO,MAAM;AAAA,IAC7D;AAKA,aAAS,YAAY,OAAO,eAAe;AACvC,cAAQ,KAAK,KAAK,GAAG;AAAA,QACjB,KAAK;AAAmB,iBAAO;AAAA,QAC/B,KAAK;AAAc,iBAAO;AAAA,QAC1B,KAAK;AAAiB,iBAAO;AAAA,QAC7B,KAAK;AAAgB,iBAAO;AAAA,QAC5B,KAAK;AAAgB,iBAAO;AAAA,QAC5B,KAAK;AAAgB,iBAAO;AAAA,MAChC;AACA,UAAI,OAAO,kBAAkB,IAAiB,WAAW,kBAAkB,IAAiB,WAAW;AACvG,UAAI,eAAe,UAAU,OAAO,iBAAiB;AACrD,UAAI,iBAAiB,QAAW;AAC5B,YAAI,SAAS,aAAa,KAAK,OAAO,IAAI;AAC1C,YAAI,SAAS,MAAM;AACf,gBAAM,IAAI,UAAU;AACxB,eAAO;AAAA,MACX;AACA,aAAO,oBAAoB,OAAO,SAAS,YAAY,WAAW,IAAI;AAAA,IAC1E;AAGA,aAAS,oBAAoB,GAAG,MAAM;AAClC,UAAI,SAAS,UAAU;AACnB,YAAI,aAAa,EAAE;AACnB,YAAI,WAAW,UAAU,GAAG;AACxB,cAAI,SAAS,WAAW,KAAK,CAAC;AAC9B,cAAI,CAAC,SAAS,MAAM;AAChB,mBAAO;AAAA,QACf;AACA,YAAI,UAAU,EAAE;AAChB,YAAI,WAAW,OAAO,GAAG;AACrB,cAAI,SAAS,QAAQ,KAAK,CAAC;AAC3B,cAAI,CAAC,SAAS,MAAM;AAChB,mBAAO;AAAA,QACf;AAAA,MACJ,OACK;AACD,YAAI,UAAU,EAAE;AAChB,YAAI,WAAW,OAAO,GAAG;AACrB,cAAI,SAAS,QAAQ,KAAK,CAAC;AAC3B,cAAI,CAAC,SAAS,MAAM;AAChB,mBAAO;AAAA,QACf;AACA,YAAI,aAAa,EAAE;AACnB,YAAI,WAAW,UAAU,GAAG;AACxB,cAAI,SAAS,WAAW,KAAK,CAAC;AAC9B,cAAI,CAAC,SAAS,MAAM;AAChB,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,YAAM,IAAI,UAAU;AAAA,IACxB;AAGA,aAAS,UAAU,UAAU;AACzB,aAAO,CAAC,CAAC;AAAA,IACb;AAGA,aAAS,SAAS,UAAU;AACxB,aAAO,KAAK;AAAA,IAChB;AAGA,aAAS,cAAc,UAAU;AAC7B,UAAI,MAAM;AAAA,QAAY;AAAA,QAAU;AAAA;AAAA,MAAc;AAC9C,UAAI,SAAS,GAAG;AACZ,eAAO;AACX,aAAO,SAAS,GAAG;AAAA,IACvB;AAKA,aAAS,QAAQ,UAAU;AACvB,aAAO,MAAM,UACP,MAAM,QAAQ,QAAQ,IACtB,oBAAoB,SAChB,oBAAoB,QACpB,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM;AAAA,IAC3D;AAGA,aAAS,WAAW,UAAU;AAE1B,aAAO,OAAO,aAAa;AAAA,IAC/B;AAGA,aAAS,cAAc,UAAU;AAE7B,aAAO,OAAO,aAAa;AAAA,IAC/B;AAGA,aAAS,cAAc,UAAU;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACpB,KAAK;AAAgB,iBAAO;AAAA,QAC5B,KAAK;AAAgB,iBAAO;AAAA,QAC5B;AAAS,iBAAO;AAAA,MACpB;AAAA,IACJ;AACA,aAAS,cAAc,GAAG,GAAG;AACzB,aAAO,MAAM,KAAK,MAAM,KAAK,MAAM;AAAA,IACvC;AAKA,aAAS,UAAU,GAAG,GAAG;AACrB,UAAI,OAAO,EAAE,CAAC;AACd,UAAI,SAAS,UAAa,SAAS;AAC/B,eAAO;AACX,UAAI,CAAC,WAAW,IAAI;AAChB,cAAM,IAAI,UAAU;AACxB,aAAO;AAAA,IACX;AAGA,aAAS,YAAY,KAAK;AACtB,UAAI,SAAS,UAAU,KAAK,cAAc;AAC1C,UAAI,CAAC,WAAW,MAAM;AAClB,cAAM,IAAI,UAAU;AACxB,UAAI,WAAW,OAAO,KAAK,GAAG;AAC9B,UAAI,CAAC,SAAS,QAAQ;AAClB,cAAM,IAAI,UAAU;AACxB,aAAO;AAAA,IACX;AAGA,aAAS,cAAc,YAAY;AAC/B,aAAO,WAAW;AAAA,IACtB;AAGA,aAAS,aAAa,UAAU;AAC5B,UAAI,SAAS,SAAS,KAAK;AAC3B,aAAO,OAAO,OAAO,QAAQ;AAAA,IACjC;AAGA,aAAS,cAAc,UAAU;AAC7B,UAAI,IAAI,SAAS,QAAQ;AACzB,UAAI;AACA,UAAE,KAAK,QAAQ;AAAA,IACvB;AAKA,aAAS,uBAAuB,GAAG;AAC/B,UAAI,QAAQ,OAAO,eAAe,CAAC;AACnC,UAAI,OAAO,MAAM,cAAc,MAAM;AACjC,eAAO;AAQX,UAAI,UAAU;AACV,eAAO;AAEX,UAAI,YAAY,EAAE;AAClB,UAAI,iBAAiB,aAAa,OAAO,eAAe,SAAS;AACjE,UAAI,kBAAkB,QAAQ,mBAAmB,OAAO;AACpD,eAAO;AAEX,UAAI,cAAc,eAAe;AACjC,UAAI,OAAO,gBAAgB;AACvB,eAAO;AAEX,UAAI,gBAAgB;AAChB,eAAO;AAEX,aAAO;AAAA,IACX;AAOA,aAAS,yBAAyB;AAC9B,UAAI;AACJ,UAAI,CAAC,YAAY,cAAc,KAC3B,OAAO,KAAK,YAAY,eACxB,EAAE,kBAAkB,KAAK,YACzB,OAAO,KAAK,QAAQ,mBAAmB,YAAY;AAEnD,mBAAW,uBAAuB,KAAK,OAAO;AAAA,MAClD;AACA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,oBAAoB,IAAI,SAAS;AACrC,UAAI,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,aAAO;AACP,eAAS,iBAAiB,UAAU;AAChC,YAAI,CAAC,OAAO,aAAa,QAAQ,GAAG;AAChC,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC/D;AACA,gBAAQ,MAAM;AAAA,UACV,KAAK,aAAa;AAAU;AAAA,UAC5B,KAAK,YAAY,KAAK;AAClB,oBAAQ;AACR;AAAA,UACJ,KAAK,UAAU;AAAU;AAAA,UACzB,KAAK,YAAY,MAAM;AACnB,qBAAS;AACT;AAAA,UACJ,KAAK,WAAW;AAAU;AAAA,UAC1B;AACI,gBAAI,SAAS;AACT,qBAAO,IAAI,KAAK;AACpB,iBAAK,IAAI,QAAQ;AACjB;AAAA,QACR;AAAA,MACJ;AACA,eAAS,mBAAmB,GAAG,GAAG;AAC9B,YAAI,CAAC,YAAY,KAAK,GAAG;AACrB,cAAI,MAAM,cAAc,GAAG,CAAC;AACxB,mBAAO;AACX,cAAI,CAAC,YAAY,MAAM,GAAG;AACtB,gBAAI,OAAO,cAAc,GAAG,CAAC;AACzB,qBAAO;AACX,gBAAI,CAAC,YAAY,IAAI,GAAG;AACpB,kBAAI,WAAW,YAAY,IAAI;AAC/B,qBAAO,MAAM;AACT,oBAAI,OAAO,aAAa,QAAQ;AAChC,oBAAI,CAAC,MAAM;AACP,yBAAO;AAAA,gBACX;AACA,oBAAI,WAAW,cAAc,IAAI;AACjC,oBAAI,SAAS,cAAc,GAAG,CAAC,GAAG;AAC9B,gCAAc,QAAQ;AACtB,yBAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,CAAC,YAAY,QAAQ,KAAK,SAAS,cAAc,GAAG,CAAC,GAAG;AACxD,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,eAAS,YAAY,GAAG,GAAG;AACvB,YAAI,cAAc,kBAAkB,IAAI,CAAC;AACzC,YAAI;AACJ,YAAI,CAAC,YAAY,WAAW,GAAG;AAC3B,qBAAW,YAAY,IAAI,CAAC;AAAA,QAChC;AACA,YAAI,CAAC,YAAY,QAAQ,GAAG;AACxB,iBAAO;AAAA,QACX;AACA,mBAAW,mBAAmB,GAAG,CAAC;AAClC,YAAI,CAAC,YAAY,QAAQ,GAAG;AACxB,cAAI,YAAY,WAAW,GAAG;AAC1B,0BAAc,IAAI,KAAK;AACvB,8BAAkB,IAAI,GAAG,WAAW;AAAA,UACxC;AACA,sBAAY,IAAI,GAAG,QAAQ;AAAA,QAC/B;AACA,eAAO;AAAA,MACX;AACA,eAAS,YAAY,UAAU;AAC3B,YAAI,YAAY,QAAQ;AACpB,gBAAM,IAAI,UAAU;AACxB,eAAO,UAAU,YAAY,WAAW,YAAY,CAAC,YAAY,IAAI,KAAK,KAAK,IAAI,QAAQ;AAAA,MAC/F;AACA,eAAS,YAAY,GAAG,GAAG,UAAU;AACjC,YAAI,CAAC,YAAY,QAAQ,GAAG;AACxB,gBAAM,IAAI,MAAM,mCAAmC;AAAA,QACvD;AACA,YAAI,mBAAmB,YAAY,GAAG,CAAC;AACvC,YAAI,qBAAqB,UAAU;AAC/B,cAAI,CAAC,YAAY,gBAAgB,GAAG;AAChC,mBAAO;AAAA,UACX;AACA,cAAI,cAAc,kBAAkB,IAAI,CAAC;AACzC,cAAI,YAAY,WAAW,GAAG;AAC1B,0BAAc,IAAI,KAAK;AACvB,8BAAkB,IAAI,GAAG,WAAW;AAAA,UACxC;AACA,sBAAY,IAAI,GAAG,QAAQ;AAAA,QAC/B;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAIA,aAAS,8BAA8B;AACnC,UAAIC;AACJ,UAAI,CAAC,YAAY,cAAc,KAAK,SAAS,KAAK,OAAO,KAAK,OAAO,aAAa,KAAK,OAAO,GAAG;AAC7F,QAAAA,oBAAmB,KAAK,QAAQ,cAAc;AAAA,MAClD;AACA,UAAI,YAAYA,iBAAgB,GAAG;AAC/B,QAAAA,oBAAmB,uBAAuB;AAAA,MAC9C;AACA,UAAI,CAAC,YAAY,cAAc,KAAK,SAAS,KAAK,OAAO,KAAK,OAAO,aAAa,KAAK,OAAO,GAAG;AAC7F,eAAO,eAAe,KAAK,SAAS,gBAAgB;AAAA,UAChD,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA,UACV,OAAOA;AAAA,QACX,CAAC;AAAA,MACL;AACA,aAAOA;AAAA,IACX;AACA,aAAS,uBAAuB,UAAU;AAGtC,UAAIC,YAAW,IAAI,SAAS;AAC5B,UAAI,WAAW;AAAA,QACX,eAAe,SAAU,GAAG,GAAG;AAC3B,cAAI,iBAAiBA,UAAS,IAAI,CAAC;AACnC,cAAI,YAAY,cAAc;AAC1B,mBAAO;AACX,iBAAO,eAAe,IAAI,CAAC;AAAA,QAC/B;AAAA,QACA,2BAA2BC;AAAA,QAC3B,wBAAwBC;AAAA,QACxB,wBAAwBC;AAAA,QACxB,yBAAyBC;AAAA,QACzB;AAAA,MACJ;AACA,uBAAiB,iBAAiB,QAAQ;AAC1C,aAAO;AACP,eAAS,uBAAuB,GAAG,GAAG,QAAQ;AAC1C,YAAI,iBAAiBJ,UAAS,IAAI,CAAC;AACnC,YAAI,wBAAwB;AAC5B,YAAI,YAAY,cAAc,GAAG;AAC7B,cAAI,CAAC;AACD,mBAAO;AACX,2BAAiB,IAAI,KAAK;AAC1B,UAAAA,UAAS,IAAI,GAAG,cAAc;AAC9B,kCAAwB;AAAA,QAC5B;AACA,YAAI,cAAc,eAAe,IAAI,CAAC;AACtC,YAAI,YAAY,WAAW,GAAG;AAC1B,cAAI,CAAC;AACD,mBAAO;AACX,wBAAc,IAAI,KAAK;AACvB,yBAAe,IAAI,GAAG,WAAW;AACjC,cAAI,CAAC,SAAS,YAAY,GAAG,GAAG,QAAQ,GAAG;AACvC,2BAAe,OAAO,CAAC;AACvB,gBAAI,uBAAuB;AACvB,cAAAA,UAAS,OAAO,CAAC;AAAA,YACrB;AACA,kBAAM,IAAI,MAAM,4BAA4B;AAAA,UAChD;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAGA,eAASE,wBAAuB,aAAa,GAAG,GAAG;AAC/C,YAAI,cAAc;AAAA,UAAuB;AAAA,UAAG;AAAA;AAAA,UAAc;AAAA,QAAK;AAC/D,YAAI,YAAY,WAAW;AACvB,iBAAO;AACX,eAAO,UAAU,YAAY,IAAI,WAAW,CAAC;AAAA,MACjD;AAGA,eAASC,wBAAuB,aAAa,GAAG,GAAG;AAC/C,YAAI,cAAc;AAAA,UAAuB;AAAA,UAAG;AAAA;AAAA,UAAc;AAAA,QAAK;AAC/D,YAAI,YAAY,WAAW;AACvB,iBAAO;AACX,eAAO,YAAY,IAAI,WAAW;AAAA,MACtC;AAGA,eAASF,2BAA0B,aAAa,eAAe,GAAG,GAAG;AACjE,YAAI,cAAc;AAAA,UAAuB;AAAA,UAAG;AAAA;AAAA,UAAc;AAAA,QAAI;AAC9D,oBAAY,IAAI,aAAa,aAAa;AAAA,MAC9C;AAGA,eAASG,yBAAwB,GAAG,GAAG;AACnC,YAAI,OAAO,CAAC;AACZ,YAAI,cAAc;AAAA,UAAuB;AAAA,UAAG;AAAA;AAAA,UAAc;AAAA,QAAK;AAC/D,YAAI,YAAY,WAAW;AACvB,iBAAO;AACX,YAAI,UAAU,YAAY,KAAK;AAC/B,YAAI,WAAW,YAAY,OAAO;AAClC,YAAI,IAAI;AACR,eAAO,MAAM;AACT,cAAI,OAAO,aAAa,QAAQ;AAChC,cAAI,CAAC,MAAM;AACP,iBAAK,SAAS;AACd,mBAAO;AAAA,UACX;AACA,cAAI,YAAY,cAAc,IAAI;AAClC,cAAI;AACA,iBAAK,CAAC,IAAI;AAAA,UACd,SACO,GAAG;AACN,gBAAI;AACA,4BAAc,QAAQ;AAAA,YAC1B,UACA;AACI,oBAAM;AAAA,YACV;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,uBAAuB,aAAa,GAAG,GAAG;AAC/C,YAAI,cAAc;AAAA,UAAuB;AAAA,UAAG;AAAA;AAAA,UAAc;AAAA,QAAK;AAC/D,YAAI,YAAY,WAAW;AACvB,iBAAO;AACX,YAAI,CAAC,YAAY,OAAO,WAAW;AAC/B,iBAAO;AACX,YAAI,YAAY,SAAS,GAAG;AACxB,cAAI,iBAAiBJ,UAAS,IAAI,CAAC;AACnC,cAAI,CAAC,YAAY,cAAc,GAAG;AAC9B,2BAAe,OAAO,CAAC;AACvB,gBAAI,eAAe,SAAS,GAAG;AAC3B,cAAAA,UAAS,OAAO,cAAc;AAAA,YAClC;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,aAAS,uBAAuB,SAAS;AACrC,UAAIK,kBAAiB,QAAQ,gBAAgBC,kBAAiB,QAAQ,gBAAgBC,kBAAiB,QAAQ,gBAAgBC,sBAAqB,QAAQ,oBAAoBC,kBAAiB,QAAQ;AACzM,UAAI,gBAAgB,IAAI,SAAS;AACjC,UAAI,WAAW;AAAA,QACX,eAAe,SAAU,GAAG,GAAG;AAC3B,cAAI,sBAAsB,cAAc,IAAI,CAAC;AAC7C,cAAI,CAAC,YAAY,mBAAmB,KAAK,oBAAoB,IAAI,CAAC,GAAG;AACjE,mBAAO;AAAA,UACX;AACA,cAAID,oBAAmB,GAAG,CAAC,EAAE,QAAQ;AACjC,gBAAI,YAAY,mBAAmB,GAAG;AAClC,oCAAsB,IAAI,KAAK;AAC/B,4BAAc,IAAI,GAAG,mBAAmB;AAAA,YAC5C;AACA,gCAAoB,IAAI,CAAC;AACzB,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AAAA,QACA,2BAA2BH;AAAA,QAC3B,wBAAwBC;AAAA,QACxB,wBAAwBC;AAAA,QACxB,yBAAyBC;AAAA,QACzB,wBAAwBC;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AAKA,aAAS,oBAAoB,GAAG,GAAG,QAAQ;AACvC,UAAI,qBAAqB,iBAAiB,YAAY,GAAG,CAAC;AAC1D,UAAI,CAAC,YAAY,kBAAkB,GAAG;AAClC,eAAO;AAAA,MACX;AACA,UAAI,QAAQ;AACR,YAAI,iBAAiB,YAAY,GAAG,GAAG,gBAAgB,GAAG;AACtD,iBAAO;AAAA,QACX;AACA,cAAM,IAAI,MAAM,gBAAgB;AAAA,MACpC;AACA,aAAO;AAAA,IACX;AAEA,aAAS,oBAAoB;AACzB,UAAI,gBAAgB,CAAC;AACrB,UAAI,gBAAgB,CAAC;AACrB,UAAI;AAAA;AAAA,QAA6B,WAAY;AACzC,mBAASC,aAAY,MAAM,QAAQ,UAAU;AACzC,iBAAK,SAAS;AACd,iBAAK,QAAQ;AACb,iBAAK,UAAU;AACf,iBAAK,YAAY;AAAA,UACrB;AACA,UAAAA,aAAY,UAAU,YAAY,IAAI,WAAY;AAAE,mBAAO;AAAA,UAAM;AACjE,UAAAA,aAAY,UAAU,cAAc,IAAI,WAAY;AAAE,mBAAO;AAAA,UAAM;AACnE,UAAAA,aAAY,UAAU,OAAO,WAAY;AACrC,gBAAI,QAAQ,KAAK;AACjB,gBAAI,SAAS,KAAK,QAAQ,KAAK,MAAM,QAAQ;AACzC,kBAAI,SAAS,KAAK,UAAU,KAAK,MAAM,KAAK,GAAG,KAAK,QAAQ,KAAK,CAAC;AAClE,kBAAI,QAAQ,KAAK,KAAK,MAAM,QAAQ;AAChC,qBAAK,SAAS;AACd,qBAAK,QAAQ;AACb,qBAAK,UAAU;AAAA,cACnB,OACK;AACD,qBAAK;AAAA,cACT;AACA,qBAAO,EAAE,OAAO,QAAQ,MAAM,MAAM;AAAA,YACxC;AACA,mBAAO,EAAE,OAAO,QAAW,MAAM,KAAK;AAAA,UAC1C;AACA,UAAAA,aAAY,UAAU,QAAQ,SAAU,OAAO;AAC3C,gBAAI,KAAK,UAAU,GAAG;AAClB,mBAAK,SAAS;AACd,mBAAK,QAAQ;AACb,mBAAK,UAAU;AAAA,YACnB;AACA,kBAAM;AAAA,UACV;AACA,UAAAA,aAAY,UAAU,SAAS,SAAU,OAAO;AAC5C,gBAAI,KAAK,UAAU,GAAG;AAClB,mBAAK,SAAS;AACd,mBAAK,QAAQ;AACb,mBAAK,UAAU;AAAA,YACnB;AACA,mBAAO,EAAE,OAAc,MAAM,KAAK;AAAA,UACtC;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,UAAIC;AAAA;AAAA,QAAqB,WAAY;AACjC,mBAASA,OAAM;AACX,iBAAK,QAAQ,CAAC;AACd,iBAAK,UAAU,CAAC;AAChB,iBAAK,YAAY;AACjB,iBAAK,cAAc;AAAA,UACvB;AACA,iBAAO,eAAeA,KAAI,WAAW,QAAQ;AAAA,YACzC,KAAK,WAAY;AAAE,qBAAO,KAAK,MAAM;AAAA,YAAQ;AAAA,YAC7C,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,KAAI,UAAU,MAAM,SAAU,KAAK;AAAE,mBAAO,KAAK;AAAA,cAAM;AAAA;AAAA,cAAgB;AAAA,YAAK,KAAK;AAAA,UAAG;AACpF,UAAAA,KAAI,UAAU,MAAM,SAAU,KAAK;AAC/B,gBAAI,QAAQ,KAAK;AAAA,cAAM;AAAA;AAAA,cAAgB;AAAA,YAAK;AAC5C,mBAAO,SAAS,IAAI,KAAK,QAAQ,KAAK,IAAI;AAAA,UAC9C;AACA,UAAAA,KAAI,UAAU,MAAM,SAAU,KAAK,OAAO;AACtC,gBAAI,QAAQ,KAAK;AAAA,cAAM;AAAA;AAAA,cAAgB;AAAA,YAAI;AAC3C,iBAAK,QAAQ,KAAK,IAAI;AACtB,mBAAO;AAAA,UACX;AACA,UAAAA,KAAI,UAAU,SAAS,SAAU,KAAK;AAClC,gBAAI,QAAQ,KAAK;AAAA,cAAM;AAAA;AAAA,cAAgB;AAAA,YAAK;AAC5C,gBAAI,SAAS,GAAG;AACZ,kBAAI,OAAO,KAAK,MAAM;AACtB,uBAAS,IAAI,QAAQ,GAAG,IAAI,MAAM,KAAK;AACnC,qBAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;AAChC,qBAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AAAA,cACxC;AACA,mBAAK,MAAM;AACX,mBAAK,QAAQ;AACb,kBAAI,cAAc,KAAK,KAAK,SAAS,GAAG;AACpC,qBAAK,YAAY;AACjB,qBAAK,cAAc;AAAA,cACvB;AACA,qBAAO;AAAA,YACX;AACA,mBAAO;AAAA,UACX;AACA,UAAAA,KAAI,UAAU,QAAQ,WAAY;AAC9B,iBAAK,MAAM,SAAS;AACpB,iBAAK,QAAQ,SAAS;AACtB,iBAAK,YAAY;AACjB,iBAAK,cAAc;AAAA,UACvB;AACA,UAAAA,KAAI,UAAU,OAAO,WAAY;AAAE,mBAAO,IAAI,YAAY,KAAK,OAAO,KAAK,SAAS,MAAM;AAAA,UAAG;AAC7F,UAAAA,KAAI,UAAU,SAAS,WAAY;AAAE,mBAAO,IAAI,YAAY,KAAK,OAAO,KAAK,SAAS,QAAQ;AAAA,UAAG;AACjG,UAAAA,KAAI,UAAU,UAAU,WAAY;AAAE,mBAAO,IAAI,YAAY,KAAK,OAAO,KAAK,SAAS,QAAQ;AAAA,UAAG;AAClG,UAAAA,KAAI,UAAU,YAAY,IAAI,WAAY;AAAE,mBAAO,KAAK,QAAQ;AAAA,UAAG;AACnE,UAAAA,KAAI,UAAU,cAAc,IAAI,WAAY;AAAE,mBAAO,KAAK,QAAQ;AAAA,UAAG;AACrE,UAAAA,KAAI,UAAU,QAAQ,SAAU,KAAK,QAAQ;AACzC,gBAAI,CAAC,cAAc,KAAK,WAAW,GAAG,GAAG;AACrC,mBAAK,cAAc;AACnB,uBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,oBAAI,cAAc,KAAK,MAAM,CAAC,GAAG,GAAG,GAAG;AACnC,uBAAK,cAAc;AACnB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,KAAK,QAAQ;AAChC,mBAAK,cAAc,KAAK,MAAM;AAC9B,mBAAK,MAAM,KAAK,GAAG;AACnB,mBAAK,QAAQ,KAAK,MAAS;AAAA,YAC/B;AACA,mBAAO,KAAK;AAAA,UAChB;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,aAAOA;AACP,eAAS,OAAO,KAAK,GAAG;AACpB,eAAO;AAAA,MACX;AACA,eAAS,SAAS,GAAG,OAAO;AACxB,eAAO;AAAA,MACX;AACA,eAAS,SAAS,KAAK,OAAO;AAC1B,eAAO,CAAC,KAAK,KAAK;AAAA,MACtB;AAAA,IACJ;AAEA,aAAS,oBAAoB;AACzB,UAAIC;AAAA;AAAA,QAAqB,WAAY;AACjC,mBAASA,OAAM;AACX,iBAAK,OAAO,IAAI,KAAK;AAAA,UACzB;AACA,iBAAO,eAAeA,KAAI,WAAW,QAAQ;AAAA,YACzC,KAAK,WAAY;AAAE,qBAAO,KAAK,KAAK;AAAA,YAAM;AAAA,YAC1C,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,KAAI,UAAU,MAAM,SAAU,OAAO;AAAE,mBAAO,KAAK,KAAK,IAAI,KAAK;AAAA,UAAG;AACpE,UAAAA,KAAI,UAAU,MAAM,SAAU,OAAO;AAAE,mBAAO,KAAK,KAAK,IAAI,OAAO,KAAK,GAAG;AAAA,UAAM;AACjF,UAAAA,KAAI,UAAU,SAAS,SAAU,OAAO;AAAE,mBAAO,KAAK,KAAK,OAAO,KAAK;AAAA,UAAG;AAC1E,UAAAA,KAAI,UAAU,QAAQ,WAAY;AAAE,iBAAK,KAAK,MAAM;AAAA,UAAG;AACvD,UAAAA,KAAI,UAAU,OAAO,WAAY;AAAE,mBAAO,KAAK,KAAK,KAAK;AAAA,UAAG;AAC5D,UAAAA,KAAI,UAAU,SAAS,WAAY;AAAE,mBAAO,KAAK,KAAK,KAAK;AAAA,UAAG;AAC9D,UAAAA,KAAI,UAAU,UAAU,WAAY;AAAE,mBAAO,KAAK,KAAK,QAAQ;AAAA,UAAG;AAClE,UAAAA,KAAI,UAAU,YAAY,IAAI,WAAY;AAAE,mBAAO,KAAK,KAAK;AAAA,UAAG;AAChE,UAAAA,KAAI,UAAU,cAAc,IAAI,WAAY;AAAE,mBAAO,KAAK,KAAK;AAAA,UAAG;AAClE,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,aAAOA;AAAA,IACX;AAEA,aAAS,wBAAwB;AAC7B,UAAI,YAAY;AAChB,UAAI,OAAO,QAAQ,OAAO;AAC1B,UAAI,UAAU,gBAAgB;AAC9B;AAAA;AAAA,QAAsB,WAAY;AAC9B,mBAASC,WAAU;AACf,iBAAK,OAAO,gBAAgB;AAAA,UAChC;AACA,UAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ;AACtC,gBAAI,QAAQ;AAAA,cAAwB;AAAA;AAAA,cAAmB;AAAA,YAAK;AAC5D,mBAAO,UAAU,SAAY,QAAQ,IAAI,OAAO,KAAK,IAAI,IAAI;AAAA,UACjE;AACA,UAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ;AACtC,gBAAI,QAAQ;AAAA,cAAwB;AAAA;AAAA,cAAmB;AAAA,YAAK;AAC5D,mBAAO,UAAU,SAAY,QAAQ,IAAI,OAAO,KAAK,IAAI,IAAI;AAAA,UACjE;AACA,UAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ,OAAO;AAC7C,gBAAI,QAAQ;AAAA,cAAwB;AAAA;AAAA,cAAmB;AAAA,YAAI;AAC3D,kBAAM,KAAK,IAAI,IAAI;AACnB,mBAAO;AAAA,UACX;AACA,UAAAA,SAAQ,UAAU,SAAS,SAAU,QAAQ;AACzC,gBAAI,QAAQ;AAAA,cAAwB;AAAA;AAAA,cAAmB;AAAA,YAAK;AAC5D,mBAAO,UAAU,SAAY,OAAO,MAAM,KAAK,IAAI,IAAI;AAAA,UAC3D;AACA,UAAAA,SAAQ,UAAU,QAAQ,WAAY;AAElC,iBAAK,OAAO,gBAAgB;AAAA,UAChC;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,eAAS,kBAAkB;AACvB,YAAI;AACJ;AACI,gBAAM,gBAAgB,WAAW;AAAA,eAC9B,QAAQ,IAAI,MAAM,GAAG;AAC5B,aAAK,GAAG,IAAI;AACZ,eAAO;AAAA,MACX;AACA,eAAS,wBAAwB,QAAQ,QAAQ;AAC7C,YAAI,CAAC,OAAO,KAAK,QAAQ,OAAO,GAAG;AAC/B,cAAI,CAAC;AACD,mBAAO;AACX,iBAAO,eAAe,QAAQ,SAAS,EAAE,OAAO,QAAQ,OAAO,EAAE,CAAC;AAAA,QACtE;AACA,eAAO,OAAO,OAAO;AAAA,MACzB;AACA,eAAS,gBAAgB,QAAQ,MAAM;AACnC,iBAAS,IAAI,GAAG,IAAI,MAAM,EAAE;AACxB,iBAAO,CAAC,IAAI,KAAK,OAAO,IAAI,MAAO;AACvC,eAAO;AAAA,MACX;AACA,eAAS,eAAe,MAAM;AAC1B,YAAI,OAAO,eAAe,YAAY;AAClC,cAAI,QAAQ,IAAI,WAAW,IAAI;AAC/B,cAAI,OAAO,WAAW,aAAa;AAC/B,mBAAO,gBAAgB,KAAK;AAAA,UAChC,WACS,OAAO,aAAa,aAAa;AACtC,qBAAS,gBAAgB,KAAK;AAAA,UAClC,OACK;AACD,4BAAgB,OAAO,IAAI;AAAA,UAC/B;AACA,iBAAO;AAAA,QACX;AACA,eAAO,gBAAgB,IAAI,MAAM,IAAI,GAAG,IAAI;AAAA,MAChD;AACA,eAAS,aAAa;AAClB,YAAI,OAAO,eAAe,SAAS;AAEnC,aAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,aAAK,CAAC,IAAI,KAAK,CAAC,IAAI,MAAO;AAC3B,YAAI,SAAS;AACb,iBAAS,SAAS,GAAG,SAAS,WAAW,EAAE,QAAQ;AAC/C,cAAI,OAAO,KAAK,MAAM;AACtB,cAAI,WAAW,KAAK,WAAW,KAAK,WAAW;AAC3C,sBAAU;AACd,cAAI,OAAO;AACP,sBAAU;AACd,oBAAU,KAAK,SAAS,EAAE,EAAE,YAAY;AAAA,QAC5C;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,eAAe,KAAK;AACzB,UAAI,KAAK;AACT,aAAO,IAAI;AACX,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL,GAAG,YAAY,UAAU,CAAC,EAAE;", "names": ["Reflect", "hasOwn", "metadataRegistry", "metadata", "OrdinaryDefineOwnMetadata", "OrdinaryHasOwnMetadata", "OrdinaryGetOwnMetadata", "OrdinaryOwnMetadataKeys", "defineMetadata", "hasOwnMetadata", "getOwnMetadata", "getOwnMetadataKeys", "deleteMetadata", "MapIterator", "Map", "Set", "WeakMap"]}