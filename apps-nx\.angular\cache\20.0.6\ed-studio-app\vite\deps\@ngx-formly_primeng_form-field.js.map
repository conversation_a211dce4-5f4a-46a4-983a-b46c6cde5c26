{"version": 3, "sources": ["../../../../../../node_modules/@ngx-formly/primeng/fesm2022/ngx-formly-primeng-form-field.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, NgModule } from '@angular/core';\nimport * as i2 from '@ngx-formly/core';\nimport { FieldWrapper, FormlyModule } from '@ngx-formly/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nfunction FormlyWrapperFormField_label_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormlyWrapperFormField_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, FormlyWrapperFormField_label_1_span_2_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"for\", ctx_r0.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.props.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.props.required && ctx_r0.props.hideRequiredMarker !== true);\n  }\n}\nfunction FormlyWrapperFormField_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 7);\n    i0.ɵɵelement(1, \"formly-validation-message\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"field\", ctx_r0.field);\n  }\n}\nclass FormlyWrapperFormField extends FieldWrapper {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵFormlyWrapperFormField_BaseFactory;\n      return function FormlyWrapperFormField_Factory(__ngFactoryType__) {\n        return (ɵFormlyWrapperFormField_BaseFactory || (ɵFormlyWrapperFormField_BaseFactory = i0.ɵɵgetInheritedFactory(FormlyWrapperFormField)))(__ngFactoryType__ || FormlyWrapperFormField);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FormlyWrapperFormField,\n      selectors: [[\"formly-wrapper-primeng-form-field\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 5,\n      vars: 2,\n      consts: [[\"fieldComponent\", \"\"], [1, \"p-field\"], [3, \"for\", 4, \"ngIf\"], [\"class\", \"p-error\", 4, \"ngIf\"], [3, \"for\"], [\"aria-hidden\", \"true\", 4, \"ngIf\"], [\"aria-hidden\", \"true\"], [1, \"p-error\"], [1, \"ui-message-text\", 3, \"field\"]],\n      template: function FormlyWrapperFormField_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, FormlyWrapperFormField_label_1_Template, 3, 3, \"label\", 2);\n          i0.ɵɵelementContainer(2, null, 0);\n          i0.ɵɵtemplate(4, FormlyWrapperFormField_small_4_Template, 2, 1, \"small\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.props.label && ctx.props.hideLabel !== true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showError);\n        }\n      },\n      dependencies: [i1.NgIf, i2.LegacyFormlyValidationMessage],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyWrapperFormField, [{\n    type: Component,\n    args: [{\n      selector: 'formly-wrapper-primeng-form-field',\n      template: `\n    <div class=\"p-field\">\n      <label *ngIf=\"props.label && props.hideLabel !== true\" [for]=\"id\">\n        {{ props.label }}\n        <span *ngIf=\"props.required && props.hideRequiredMarker !== true\" aria-hidden=\"true\">*</span>\n      </label>\n      <ng-container #fieldComponent></ng-container>\n\n      <small *ngIf=\"showError\" class=\"p-error\">\n        <formly-validation-message class=\"ui-message-text\" [field]=\"field\"></formly-validation-message>\n      </small>\n    </div>\n  `\n    }]\n  }], null, null);\n})();\nfunction withFormlyFormField() {\n  return {\n    wrappers: [{\n      name: 'form-field',\n      component: FormlyWrapperFormField\n    }]\n  };\n}\nclass FormlyFormFieldModule {\n  static {\n    this.ɵfac = function FormlyFormFieldModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyFormFieldModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FormlyFormFieldModule,\n      declarations: [FormlyWrapperFormField],\n      imports: [CommonModule, ReactiveFormsModule, i2.FormlyModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormlyModule.forChild(withFormlyFormField())]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyFormFieldModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [FormlyWrapperFormField],\n      imports: [CommonModule, ReactiveFormsModule, FormlyModule.forChild(withFormlyFormField())]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FormlyFormFieldModule, withFormlyFormField };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO,EAAE;AAC9B,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,MAAM,OAAO,GAAG;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM,YAAY,OAAO,MAAM,uBAAuB,IAAI;AAAA,EACzF;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,UAAU,GAAG,6BAA6B,CAAC;AAC9C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,OAAO,KAAK;AAAA,EACrC;AACF;AACA,IAAM,0BAAN,MAAM,gCAA+B,aAAa;AAqClD;AAnCI,wBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,+BAA+B,mBAAmB;AAChE,YAAQ,wCAAwC,sCAAyC,sBAAsB,uBAAsB,IAAI,qBAAqB,uBAAsB;AAAA,EACtL;AACF,GAAG;AAGH,wBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,mCAAmC,CAAC;AAAA,EACjD,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM,GAAG,CAAC,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,eAAe,QAAQ,GAAG,MAAM,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,OAAO,CAAC;AAAA,EACpO,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,SAAS,CAAC;AAC1E,MAAG,mBAAmB,GAAG,MAAM,CAAC;AAChC,MAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,SAAS,CAAC;AAC1E,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,MAAM,SAAS,IAAI,MAAM,cAAc,IAAI;AACrE,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,IAAI,SAAS;AAAA,IACrC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,MAAS,6BAA6B;AAAA,EACxD,eAAe;AACjB,CAAC;AAnCL,IAAM,yBAAN;AAAA,CAsCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,sBAAsB;AAC7B,SAAO;AAAA,IACL,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACF;AACA,IAAM,yBAAN,MAAM,uBAAsB;AAkB5B;AAhBI,uBAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,SAAO,KAAK,qBAAqB,wBAAuB;AAC1D;AAGA,uBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,cAAc,CAAC,sBAAsB;AAAA,EACrC,SAAS,CAAC,cAAc,qBAAwB,YAAY;AAC9D,CAAC;AAGD,uBAAK,OAAyB,iBAAiB;AAAA,EAC7C,SAAS,CAAC,cAAc,qBAAqB,aAAa,SAAS,oBAAoB,CAAC,CAAC;AAC3F,CAAC;AAhBL,IAAM,wBAAN;AAAA,CAmBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,sBAAsB;AAAA,MACrC,SAAS,CAAC,cAAc,qBAAqB,aAAa,SAAS,oBAAoB,CAAC,CAAC;AAAA,IAC3F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}