import {
  NgModule,
  Pipe,
  setClassMetadata,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵgetInheritedFactory
} from "./chunk-QQZDB4KQ.js";
import {
  ɵɵdefineInjector
} from "./chunk-BYBDDJ2C.js";
import "./chunk-NY55TVYV.js";
import "./chunk-2O4VBYCZ.js";
import {
  BehaviorSubject,
  Observable,
  filter,
  map,
  tap
} from "./chunk-GJIVGOXW.js";
import "./chunk-K2JRLIHD.js";
import "./chunk-QDB2FYN3.js";

// node_modules/@ngx-formly/core/fesm2022/ngx-formly-core-select.mjs
var _FormlySelectOptionsPipe = class _FormlySelectOptionsPipe {
  transform(options, field) {
    if (!(options instanceof Observable)) {
      options = this.observableOf(options, field);
    } else {
      this.dispose();
    }
    return options.pipe(map((value) => this.transformOptions(value, field)));
  }
  ngOnDestroy() {
    this.dispose();
  }
  transformOptions(options, field) {
    const to = this.transformSelectProps(field);
    const opts = [];
    const groups = {};
    options?.forEach((option) => {
      const o = this.transformOption(option, to);
      if (o.group) {
        const id = groups[o.label];
        if (id === void 0) {
          groups[o.label] = opts.push(o) - 1;
        } else {
          o.group.forEach((o2) => opts[id].group.push(o2));
        }
      } else {
        opts.push(o);
      }
    });
    return opts;
  }
  transformOption(option, props) {
    const group = props.groupProp(option);
    if (Array.isArray(group)) {
      return {
        label: props.labelProp(option),
        group: group.map((opt) => this.transformOption(opt, props))
      };
    }
    option = {
      label: props.labelProp(option),
      value: props.valueProp(option),
      disabled: !!props.disabledProp(option)
    };
    if (group) {
      return {
        label: group,
        group: [option]
      };
    }
    return option;
  }
  transformSelectProps(field) {
    const props = field?.props || field?.templateOptions || {};
    const selectPropFn = (prop) => typeof prop === "function" ? prop : (o) => o[prop];
    return {
      groupProp: selectPropFn(props.groupProp || "group"),
      labelProp: selectPropFn(props.labelProp || "label"),
      valueProp: selectPropFn(props.valueProp || "value"),
      disabledProp: selectPropFn(props.disabledProp || "disabled")
    };
  }
  dispose() {
    if (this._options) {
      this._options.complete();
      this._options = null;
    }
    if (this._subscription) {
      this._subscription.unsubscribe();
      this._subscription = null;
    }
  }
  observableOf(options, f) {
    this.dispose();
    if (f && f.options && f.options.fieldChanges) {
      this._subscription = f.options.fieldChanges.pipe(filter(({
        property,
        type,
        field
      }) => {
        return type === "expressionChanges" && (property.indexOf("templateOptions.options") === 0 || property.indexOf("props.options") === 0) && field === f && Array.isArray(field.props.options) && !!this._options;
      }), tap(() => this._options.next(f.props.options))).subscribe();
    }
    this._options = new BehaviorSubject(options);
    return this._options.asObservable();
  }
};
_FormlySelectOptionsPipe.ɵfac = function FormlySelectOptionsPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _FormlySelectOptionsPipe)();
};
_FormlySelectOptionsPipe.ɵpipe = ɵɵdefinePipe({
  name: "formlySelectOptions",
  type: _FormlySelectOptionsPipe,
  pure: true
});
var FormlySelectOptionsPipe = _FormlySelectOptionsPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FormlySelectOptionsPipe, [{
    type: Pipe,
    args: [{
      name: "formlySelectOptions",
      standalone: true
    }]
  }], null, null);
})();
var _LegacyFormlySelectOptionsPipe = class _LegacyFormlySelectOptionsPipe extends FormlySelectOptionsPipe {
};
_LegacyFormlySelectOptionsPipe.ɵfac = /* @__PURE__ */ (() => {
  let ɵLegacyFormlySelectOptionsPipe_BaseFactory;
  return function LegacyFormlySelectOptionsPipe_Factory(__ngFactoryType__) {
    return (ɵLegacyFormlySelectOptionsPipe_BaseFactory || (ɵLegacyFormlySelectOptionsPipe_BaseFactory = ɵɵgetInheritedFactory(_LegacyFormlySelectOptionsPipe)))(__ngFactoryType__ || _LegacyFormlySelectOptionsPipe);
  };
})();
_LegacyFormlySelectOptionsPipe.ɵpipe = ɵɵdefinePipe({
  name: "formlySelectOptions",
  type: _LegacyFormlySelectOptionsPipe,
  pure: true,
  standalone: false
});
var LegacyFormlySelectOptionsPipe = _LegacyFormlySelectOptionsPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LegacyFormlySelectOptionsPipe, [{
    type: Pipe,
    args: [{
      name: "formlySelectOptions",
      standalone: false
    }]
  }], null, null);
})();
var _FormlySelectModule = class _FormlySelectModule {
};
_FormlySelectModule.ɵfac = function FormlySelectModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _FormlySelectModule)();
};
_FormlySelectModule.ɵmod = ɵɵdefineNgModule({
  type: _FormlySelectModule,
  declarations: [LegacyFormlySelectOptionsPipe],
  exports: [LegacyFormlySelectOptionsPipe]
});
_FormlySelectModule.ɵinj = ɵɵdefineInjector({});
var FormlySelectModule = _FormlySelectModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FormlySelectModule, [{
    type: NgModule,
    args: [{
      declarations: [LegacyFormlySelectOptionsPipe],
      exports: [LegacyFormlySelectOptionsPipe]
    }]
  }], null, null);
})();
export {
  FormlySelectModule,
  FormlySelectOptionsPipe,
  LegacyFormlySelectOptionsPipe
};
//# sourceMappingURL=@ngx-formly_core_select.js.map
