import { Injectable, inject } from "@angular/core";
import { ExpressDataDataClient, FormDefinition, FormFieldDefinition, FormFieldType, FormsDataClient } from "@tec/rad-xui/data";

import { SelectItem, apiResultFrom } from "@tec/rad-core/utils";

import { FormlyFieldConfig } from "@ngx-formly/core";
import { ExpressFormConfig, FormBuilder, FormConfig, RadFormItem } from "@tec/rad-xui/form";

@Injectable({ providedIn: 'root'})
export class ExpressFormService {



    private service = inject(FormsDataClient)
    private formBuilder = inject(FormBuilder);
    private forms: Map<string, ExpressFormConfig> = new Map<string, ExpressFormConfig>();

    private fieldTypeMap: Map<FormFieldType, string> = new Map<FormFieldType, string>([
        [FormFieldType.Text , 'input'],
        [FormFieldType.Select, 'select'],
        [FormFieldType.Number, 'input'],
        [FormFieldType.Date, 'datepicker'],
        [FormFieldType.DateTime, 'datetimepicker'],
        [FormFieldType.Radio, 'radio'],
    ]);


    //get field type
    private getFieldType(type: FormFieldType, defaultType = 'input'): string {
        //check if type is in map
        if (!this.fieldTypeMap.has(type)) {
            return defaultType;
        }
        return this.fieldTypeMap.get(type);
    }

    private toFieldConfig(def: FormFieldDefinition): RadFormItem {

        //create key from name with first letter lower case
        const key = def.name.charAt(0).toLowerCase() + def.name.slice(1);

        const config: RadFormItem = {
            key: key,
            type: this.fieldTypeMap.get(def.fieldType),
            label: def.label,
            rowNum: def.row,
            col: def.column,
            required: def.isRequired
        }


        if(def.options){
            //map to select items
            config.values = def.options.map((item) => {
                return SelectItem.create(item.value, item.group);
            });
        
        }

        return config;

    }

    //get form config from form definition
    private toFormConfig(def: FormDefinition): FormConfig {
        
        const config: FormConfig = {
            name: def.name,
            fields: []
        };

        
    
        def.fields.forEach((field) => {
            const fieldConfig = this.toFieldConfig(field);
            config.fields[fieldConfig.key] = fieldConfig;
        });
        return config;
    }


    //get form config by name
    public async getForm(name: string): Promise<ExpressFormConfig> {
        if(this.forms.size === 0){
            await this.loadFormConfigs();
        }
        return this.forms.get(name);
    }

    async configureForm(config: FormConfig): Promise<FormlyFieldConfig[]>{
        return await this.formBuilder.configureForm(config);
    }


    //load form configs 
    public async loadFormConfigs(): Promise<void> {
        const result = await apiResultFrom(this.service.getFormDefinitions());

        if(result.isError){
            return;
        }


        for (const formDef of result.value) {
            const config = this.toFormConfig(formDef);
            const fields = await this.formBuilder.configureForm(config);
            const expressForm = new ExpressFormConfig();
            expressForm.name = formDef.name;
            expressForm.fields = fields;
            this.forms.set(formDef.name, expressForm);
        }

        
        
    }

}