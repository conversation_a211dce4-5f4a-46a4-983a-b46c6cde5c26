{"version": 3, "sources": ["../../../../../../node_modules/@ngrx/signals/fesm2022/ngrx-signals.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { untracked, isSignal, computed, assertInInjectionContext, inject, Injector, effect, DestroyRef, signal, Injectable } from '@angular/core';\nfunction toDeepSignal(signal) {\n  const value = untracked(() => signal());\n  if (!isRecord(value)) {\n    return signal;\n  }\n  return new Proxy(signal, {\n    get(target, prop) {\n      if (!(prop in value)) {\n        return target[prop];\n      }\n      if (!isSignal(target[prop])) {\n        Object.defineProperty(target, prop, {\n          value: computed(() => target()[prop]),\n          configurable: true\n        });\n      }\n      return toDeepSignal(target[prop]);\n    }\n  });\n}\nconst nonRecords = [WeakSet, WeakMap, Promise, Date, Error, RegExp, ArrayBuffer, DataView, Function];\nfunction isRecord(value) {\n  if (value === null || typeof value !== 'object' || isIterable(value)) {\n    return false;\n  }\n  let proto = Object.getPrototypeOf(value);\n  if (proto === Object.prototype) {\n    return true;\n  }\n  while (proto && proto !== Object.prototype) {\n    if (nonRecords.includes(proto.constructor)) {\n      return false;\n    }\n    proto = Object.getPrototypeOf(proto);\n  }\n  return proto === Object.prototype;\n}\nfunction isIterable(value) {\n  return typeof value?.[Symbol.iterator] === 'function';\n}\nfunction deepComputed(computation) {\n  return toDeepSignal(computed(computation));\n}\nfunction signalMethod(processingFn, config) {\n  if (!config?.injector) {\n    assertInInjectionContext(signalMethod);\n  }\n  const watchers = [];\n  const sourceInjector = config?.injector ?? inject(Injector);\n  const signalMethodFn = (input, config) => {\n    if (isSignal(input)) {\n      const callerInjector = getCallerInjector();\n      if (typeof ngDevMode !== 'undefined' && ngDevMode && config?.injector === undefined && callerInjector === undefined) {\n        console.warn('@ngrx/signals: The function returned by signalMethod was called', 'outside the injection context with a signal. This may lead to', 'a memory leak. Make sure to call it within the injection context', '(e.g. in a constructor or field initializer) or pass an injector', 'explicitly via the config parameter.\\n\\nFor more information, see:', 'https://ngrx.io/guide/signals/signal-method#automatic-cleanup');\n      }\n      const instanceInjector = config?.injector ?? callerInjector ?? sourceInjector;\n      const watcher = effect(() => {\n        const value = input();\n        untracked(() => processingFn(value));\n      }, {\n        injector: instanceInjector\n      });\n      watchers.push(watcher);\n      instanceInjector.get(DestroyRef).onDestroy(() => {\n        const ix = watchers.indexOf(watcher);\n        if (ix !== -1) {\n          watchers.splice(ix, 1);\n        }\n      });\n      return watcher;\n    } else {\n      processingFn(input);\n      return {\n        destroy: () => void true\n      };\n    }\n  };\n  signalMethodFn.destroy = () => watchers.forEach(watcher => watcher.destroy());\n  return signalMethodFn;\n}\nfunction getCallerInjector() {\n  try {\n    return inject(Injector);\n  } catch {\n    return undefined;\n  }\n}\nconst STATE_WATCHERS = new WeakMap();\nconst STATE_SOURCE = Symbol('STATE_SOURCE');\nfunction isWritableStateSource(stateSource) {\n  return 'set' in stateSource[STATE_SOURCE] && 'update' in stateSource[STATE_SOURCE] && typeof stateSource[STATE_SOURCE].set === 'function' && typeof stateSource[STATE_SOURCE].update === 'function';\n}\nfunction patchState(stateSource, ...updaters) {\n  stateSource[STATE_SOURCE].update(currentState => updaters.reduce((nextState, updater) => ({\n    ...nextState,\n    ...(typeof updater === 'function' ? updater(nextState) : updater)\n  }), currentState));\n  notifyWatchers(stateSource);\n}\nfunction getState(stateSource) {\n  return stateSource[STATE_SOURCE]();\n}\nfunction watchState(stateSource, watcher, config) {\n  if (!config?.injector) {\n    assertInInjectionContext(watchState);\n  }\n  const injector = config?.injector ?? inject(Injector);\n  const destroyRef = injector.get(DestroyRef);\n  addWatcher(stateSource, watcher);\n  watcher(getState(stateSource));\n  const destroy = () => removeWatcher(stateSource, watcher);\n  destroyRef.onDestroy(destroy);\n  return {\n    destroy\n  };\n}\nfunction getWatchers(stateSource) {\n  return STATE_WATCHERS.get(stateSource[STATE_SOURCE]) || [];\n}\nfunction notifyWatchers(stateSource) {\n  const watchers = getWatchers(stateSource);\n  for (const watcher of watchers) {\n    const state = untracked(() => getState(stateSource));\n    watcher(state);\n  }\n}\nfunction addWatcher(stateSource, watcher) {\n  const watchers = getWatchers(stateSource);\n  STATE_WATCHERS.set(stateSource[STATE_SOURCE], [...watchers, watcher]);\n}\nfunction removeWatcher(stateSource, watcher) {\n  const watchers = getWatchers(stateSource);\n  STATE_WATCHERS.set(stateSource[STATE_SOURCE], watchers.filter(w => w !== watcher));\n}\nfunction signalState(initialState) {\n  const stateSource = signal(initialState);\n  const signalState = toDeepSignal(stateSource.asReadonly());\n  Object.defineProperty(signalState, STATE_SOURCE, {\n    value: stateSource\n  });\n  return signalState;\n}\nfunction signalStore(...args) {\n  const signalStoreArgs = [...args];\n  const config = typeof signalStoreArgs[0] === 'function' ? {} : signalStoreArgs.shift();\n  const features = signalStoreArgs;\n  class SignalStore {\n    constructor() {\n      const innerStore = features.reduce((store, feature) => feature(store), getInitialInnerStore());\n      const {\n        stateSignals,\n        props,\n        methods,\n        hooks\n      } = innerStore;\n      const storeMembers = {\n        ...stateSignals,\n        ...props,\n        ...methods\n      };\n      this[STATE_SOURCE] = innerStore[STATE_SOURCE];\n      for (const key of Reflect.ownKeys(storeMembers)) {\n        this[key] = storeMembers[key];\n      }\n      const {\n        onInit,\n        onDestroy\n      } = hooks;\n      if (onInit) {\n        onInit();\n      }\n      if (onDestroy) {\n        inject(DestroyRef).onDestroy(onDestroy);\n      }\n    }\n    /** @nocollapse */\n    static ɵfac = function SignalStore_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SignalStore)();\n    };\n    /** @nocollapse */\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SignalStore,\n      factory: SignalStore.ɵfac,\n      providedIn: config.providedIn || null\n    });\n  }\n  (() => {\n    (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SignalStore, [{\n      type: Injectable,\n      args: [{\n        providedIn: config.providedIn || null\n      }]\n    }], () => [], null);\n  })();\n  return SignalStore;\n}\nfunction getInitialInnerStore() {\n  return {\n    [STATE_SOURCE]: signal({}),\n    stateSignals: {},\n    props: {},\n    methods: {},\n    hooks: {}\n  };\n}\nfunction signalStoreFeature(featureOrInput, ...restFeatures) {\n  const features = typeof featureOrInput === 'function' ? [featureOrInput, ...restFeatures] : restFeatures;\n  return inputStore => features.reduce((store, feature) => feature(store), inputStore);\n}\nfunction type() {\n  return undefined;\n}\nfunction assertUniqueStoreMembers(store, newMemberKeys) {\n  if (typeof ngDevMode === 'undefined' || !ngDevMode) {\n    return;\n  }\n  const storeMembers = {\n    ...store.stateSignals,\n    ...store.props,\n    ...store.methods\n  };\n  const overriddenKeys = Reflect.ownKeys(storeMembers).filter(memberKey => newMemberKeys.includes(memberKey));\n  if (overriddenKeys.length > 0) {\n    console.warn('@ngrx/signals: SignalStore members cannot be overridden.', 'Trying to override:', overriddenKeys.map(key => String(key)).join(', '));\n  }\n}\nfunction withProps(propsFactory) {\n  return store => {\n    const props = propsFactory({\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store.stateSignals,\n      ...store.props,\n      ...store.methods\n    });\n    assertUniqueStoreMembers(store, Reflect.ownKeys(props));\n    return {\n      ...store,\n      props: {\n        ...store.props,\n        ...props\n      }\n    };\n  };\n}\nfunction withComputed(signalsFactory) {\n  return withProps(signalsFactory);\n}\n\n/**\n * @description\n * Allows passing properties, methods, or signals from a SignalStore\n * to a feature.\n *\n * @usageNotes\n * ```typescript\n * signalStore(\n *   withMethods((store) => ({\n *     load(id: number): Observable<Entity> {\n *       return of({ id, name: 'John' });\n *     },\n *   })),\n *   withFeature(\n *     // 👇 has full access to the store\n *     (store) => withEntityLoader((id) => firstValueFrom(store.load(id)))\n *   )\n * );\n * ```\n *\n * @param featureFactory function returning the actual feature\n */\nfunction withFeature(featureFactory) {\n  return store => {\n    const storeForFactory = {\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store['stateSignals'],\n      ...store['props'],\n      ...store['methods']\n    };\n    return featureFactory(storeForFactory)(store);\n  };\n}\nfunction withHooks(hooksOrFactory) {\n  return store => {\n    const storeMembers = {\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store.stateSignals,\n      ...store.props,\n      ...store.methods\n    };\n    const hooks = typeof hooksOrFactory === 'function' ? hooksOrFactory(storeMembers) : hooksOrFactory;\n    const createHook = name => {\n      const hook = hooks[name];\n      const currentHook = store.hooks[name];\n      return hook ? () => {\n        if (currentHook) {\n          currentHook();\n        }\n        hook(storeMembers);\n      } : currentHook;\n    };\n    return {\n      ...store,\n      hooks: {\n        onInit: createHook('onInit'),\n        onDestroy: createHook('onDestroy')\n      }\n    };\n  };\n}\nfunction withMethods(methodsFactory) {\n  return store => {\n    const methods = methodsFactory({\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store.stateSignals,\n      ...store.props,\n      ...store.methods\n    });\n    assertUniqueStoreMembers(store, Reflect.ownKeys(methods));\n    return {\n      ...store,\n      methods: {\n        ...store.methods,\n        ...methods\n      }\n    };\n  };\n}\nfunction withState(stateOrFactory) {\n  return store => {\n    const state = typeof stateOrFactory === 'function' ? stateOrFactory() : stateOrFactory;\n    const stateKeys = Reflect.ownKeys(state);\n    assertUniqueStoreMembers(store, stateKeys);\n    store[STATE_SOURCE].update(currentState => ({\n      ...currentState,\n      ...state\n    }));\n    const stateSignals = stateKeys.reduce((acc, key) => {\n      const sliceSignal = computed(() => store[STATE_SOURCE]()[key]);\n      return {\n        ...acc,\n        [key]: toDeepSignal(sliceSignal)\n      };\n    }, {});\n    return {\n      ...store,\n      stateSignals: {\n        ...store.stateSignals,\n        ...stateSignals\n      }\n    };\n  };\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { deepComputed, getState, isWritableStateSource, patchState, signalMethod, signalState, signalStore, signalStoreFeature, type, watchState, withComputed, withFeature, withHooks, withMethods, withProps, withState };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,aAAaA,SAAQ;AAC5B,QAAM,QAAQ,UAAU,MAAMA,QAAO,CAAC;AACtC,MAAI,CAAC,SAAS,KAAK,GAAG;AACpB,WAAOA;AAAA,EACT;AACA,SAAO,IAAI,MAAMA,SAAQ;AAAA,IACvB,IAAI,QAAQ,MAAM;AAChB,UAAI,EAAE,QAAQ,QAAQ;AACpB,eAAO,OAAO,IAAI;AAAA,MACpB;AACA,UAAI,CAAC,SAAS,OAAO,IAAI,CAAC,GAAG;AAC3B,eAAO,eAAe,QAAQ,MAAM;AAAA,UAClC,OAAO,SAAS,MAAM,OAAO,EAAE,IAAI,CAAC;AAAA,UACpC,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AACA,aAAO,aAAa,OAAO,IAAI,CAAC;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AACA,IAAM,aAAa,CAAC,SAAS,SAAS,SAAS,MAAM,OAAO,QAAQ,aAAa,UAAU,QAAQ;AACnG,SAAS,SAAS,OAAO;AACvB,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,WAAW,KAAK,GAAG;AACpE,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,OAAO,eAAe,KAAK;AACvC,MAAI,UAAU,OAAO,WAAW;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,UAAU,OAAO,WAAW;AAC1C,QAAI,WAAW,SAAS,MAAM,WAAW,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,YAAQ,OAAO,eAAe,KAAK;AAAA,EACrC;AACA,SAAO,UAAU,OAAO;AAC1B;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,QAAQ,OAAO,QAAQ,MAAM;AAC7C;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,aAAa,SAAS,WAAW,CAAC;AAC3C;AACA,SAAS,aAAa,cAAc,QAAQ;AAC1C,MAAI,CAAC,QAAQ,UAAU;AACrB,6BAAyB,YAAY;AAAA,EACvC;AACA,QAAM,WAAW,CAAC;AAClB,QAAM,iBAAiB,QAAQ,YAAY,OAAO,QAAQ;AAC1D,QAAM,iBAAiB,CAAC,OAAOC,YAAW;AACxC,QAAI,SAAS,KAAK,GAAG;AACnB,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,OAAO,cAAc,eAAe,aAAaA,SAAQ,aAAa,UAAa,mBAAmB,QAAW;AACnH,gBAAQ,KAAK,mEAAmE,iEAAiE,oEAAoE,oEAAoE,sEAAsE,+DAA+D;AAAA,MACha;AACA,YAAM,mBAAmBA,SAAQ,YAAY,kBAAkB;AAC/D,YAAM,UAAU,OAAO,MAAM;AAC3B,cAAM,QAAQ,MAAM;AACpB,kBAAU,MAAM,aAAa,KAAK,CAAC;AAAA,MACrC,GAAG;AAAA,QACD,UAAU;AAAA,MACZ,CAAC;AACD,eAAS,KAAK,OAAO;AACrB,uBAAiB,IAAI,UAAU,EAAE,UAAU,MAAM;AAC/C,cAAM,KAAK,SAAS,QAAQ,OAAO;AACnC,YAAI,OAAO,IAAI;AACb,mBAAS,OAAO,IAAI,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,OAAO;AACL,mBAAa,KAAK;AAClB,aAAO;AAAA,QACL,SAAS,MAAM;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,iBAAe,UAAU,MAAM,SAAS,QAAQ,aAAW,QAAQ,QAAQ,CAAC;AAC5E,SAAO;AACT;AACA,SAAS,oBAAoB;AAC3B,MAAI;AACF,WAAO,OAAO,QAAQ;AAAA,EACxB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAM,eAAe,OAAO,cAAc;AAC1C,SAAS,sBAAsB,aAAa;AAC1C,SAAO,SAAS,YAAY,YAAY,KAAK,YAAY,YAAY,YAAY,KAAK,OAAO,YAAY,YAAY,EAAE,QAAQ,cAAc,OAAO,YAAY,YAAY,EAAE,WAAW;AAC3L;AACA,SAAS,WAAW,gBAAgB,UAAU;AAC5C,cAAY,YAAY,EAAE,OAAO,kBAAgB,SAAS,OAAO,CAAC,WAAW,YAAa,kCACrF,YACC,OAAO,YAAY,aAAa,QAAQ,SAAS,IAAI,UACvD,YAAY,CAAC;AACjB,iBAAe,WAAW;AAC5B;AACA,SAAS,SAAS,aAAa;AAC7B,SAAO,YAAY,YAAY,EAAE;AACnC;AACA,SAAS,WAAW,aAAa,SAAS,QAAQ;AAChD,MAAI,CAAC,QAAQ,UAAU;AACrB,6BAAyB,UAAU;AAAA,EACrC;AACA,QAAM,WAAW,QAAQ,YAAY,OAAO,QAAQ;AACpD,QAAM,aAAa,SAAS,IAAI,UAAU;AAC1C,aAAW,aAAa,OAAO;AAC/B,UAAQ,SAAS,WAAW,CAAC;AAC7B,QAAM,UAAU,MAAM,cAAc,aAAa,OAAO;AACxD,aAAW,UAAU,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,EACF;AACF;AACA,SAAS,YAAY,aAAa;AAChC,SAAO,eAAe,IAAI,YAAY,YAAY,CAAC,KAAK,CAAC;AAC3D;AACA,SAAS,eAAe,aAAa;AACnC,QAAM,WAAW,YAAY,WAAW;AACxC,aAAW,WAAW,UAAU;AAC9B,UAAM,QAAQ,UAAU,MAAM,SAAS,WAAW,CAAC;AACnD,YAAQ,KAAK;AAAA,EACf;AACF;AACA,SAAS,WAAW,aAAa,SAAS;AACxC,QAAM,WAAW,YAAY,WAAW;AACxC,iBAAe,IAAI,YAAY,YAAY,GAAG,CAAC,GAAG,UAAU,OAAO,CAAC;AACtE;AACA,SAAS,cAAc,aAAa,SAAS;AAC3C,QAAM,WAAW,YAAY,WAAW;AACxC,iBAAe,IAAI,YAAY,YAAY,GAAG,SAAS,OAAO,OAAK,MAAM,OAAO,CAAC;AACnF;AACA,SAAS,YAAY,cAAc;AACjC,QAAM,cAAc,OAAO,YAAY;AACvC,QAAMC,eAAc,aAAa,YAAY,WAAW,CAAC;AACzD,SAAO,eAAeA,cAAa,cAAc;AAAA,IAC/C,OAAO;AAAA,EACT,CAAC;AACD,SAAOA;AACT;AACA,SAAS,eAAe,MAAM;AAC5B,QAAM,kBAAkB,CAAC,GAAG,IAAI;AAChC,QAAM,SAAS,OAAO,gBAAgB,CAAC,MAAM,aAAa,CAAC,IAAI,gBAAgB,MAAM;AACrF,QAAM,WAAW;AAAA,EACjB,MAAM,YAAY;AAAA,IAChB,cAAc;AACZ,YAAM,aAAa,SAAS,OAAO,CAAC,OAAO,YAAY,QAAQ,KAAK,GAAG,qBAAqB,CAAC;AAC7F,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,eAAe,iDAChB,eACA,QACA;AAEL,WAAK,YAAY,IAAI,WAAW,YAAY;AAC5C,iBAAW,OAAO,QAAQ,QAAQ,YAAY,GAAG;AAC/C,aAAK,GAAG,IAAI,aAAa,GAAG;AAAA,MAC9B;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AACA,UAAI,WAAW;AACb,eAAO,UAAU,EAAE,UAAU,SAAS;AAAA,MACxC;AAAA,IACF;AAAA;AAAA,IAEA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,aAAa;AAAA,IAChD;AAAA;AAAA,IAEA,OAAO,QAA0B,mBAAmB;AAAA,MAClD,OAAO;AAAA,MACP,SAAS,YAAY;AAAA,MACrB,YAAY,OAAO,cAAc;AAAA,IACnC,CAAC;AAAA,EACH;AACA,GAAC,MAAM;AACL,KAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,MACpF,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,YAAY,OAAO,cAAc;AAAA,MACnC,CAAC;AAAA,IACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AAAA,EACpB,GAAG;AACH,SAAO;AACT;AACA,SAAS,uBAAuB;AAC9B,SAAO;AAAA,IACL,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC;AAAA,IACzB,cAAc,CAAC;AAAA,IACf,OAAO,CAAC;AAAA,IACR,SAAS,CAAC;AAAA,IACV,OAAO,CAAC;AAAA,EACV;AACF;AACA,SAAS,mBAAmB,mBAAmB,cAAc;AAC3D,QAAM,WAAW,OAAO,mBAAmB,aAAa,CAAC,gBAAgB,GAAG,YAAY,IAAI;AAC5F,SAAO,gBAAc,SAAS,OAAO,CAAC,OAAO,YAAY,QAAQ,KAAK,GAAG,UAAU;AACrF;AACA,SAAS,OAAO;AACd,SAAO;AACT;AACA,SAAS,yBAAyB,OAAO,eAAe;AACtD,MAAI,OAAO,cAAc,eAAe,CAAC,WAAW;AAClD;AAAA,EACF;AACA,QAAM,eAAe,iDAChB,MAAM,eACN,MAAM,QACN,MAAM;AAEX,QAAM,iBAAiB,QAAQ,QAAQ,YAAY,EAAE,OAAO,eAAa,cAAc,SAAS,SAAS,CAAC;AAC1G,MAAI,eAAe,SAAS,GAAG;AAC7B,YAAQ,KAAK,4DAA4D,uBAAuB,eAAe,IAAI,SAAO,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,EACnJ;AACF;AACA,SAAS,UAAU,cAAc;AAC/B,SAAO,WAAS;AACd,UAAM,QAAQ,aAAa;AAAA,MACzB,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,eACN,MAAM,QACN,MAAM,QACV;AACD,6BAAyB,OAAO,QAAQ,QAAQ,KAAK,CAAC;AACtD,WAAO,iCACF,QADE;AAAA,MAEL,OAAO,kCACF,MAAM,QACN;AAAA,IAEP;AAAA,EACF;AACF;AACA,SAAS,aAAa,gBAAgB;AACpC,SAAO,UAAU,cAAc;AACjC;AAwBA,SAAS,YAAY,gBAAgB;AACnC,SAAO,WAAS;AACd,UAAM,kBAAkB;AAAA,MACtB,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,cAAc,IACpB,MAAM,OAAO,IACb,MAAM,SAAS;AAEpB,WAAO,eAAe,eAAe,EAAE,KAAK;AAAA,EAC9C;AACF;AACA,SAAS,UAAU,gBAAgB;AACjC,SAAO,WAAS;AACd,UAAM,eAAe;AAAA,MACnB,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,eACN,MAAM,QACN,MAAM;AAEX,UAAM,QAAQ,OAAO,mBAAmB,aAAa,eAAe,YAAY,IAAI;AACpF,UAAM,aAAa,UAAQ;AACzB,YAAM,OAAO,MAAM,IAAI;AACvB,YAAM,cAAc,MAAM,MAAM,IAAI;AACpC,aAAO,OAAO,MAAM;AAClB,YAAI,aAAa;AACf,sBAAY;AAAA,QACd;AACA,aAAK,YAAY;AAAA,MACnB,IAAI;AAAA,IACN;AACA,WAAO,iCACF,QADE;AAAA,MAEL,OAAO;AAAA,QACL,QAAQ,WAAW,QAAQ;AAAA,QAC3B,WAAW,WAAW,WAAW;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,YAAY,gBAAgB;AACnC,SAAO,WAAS;AACd,UAAM,UAAU,eAAe;AAAA,MAC7B,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,eACN,MAAM,QACN,MAAM,QACV;AACD,6BAAyB,OAAO,QAAQ,QAAQ,OAAO,CAAC;AACxD,WAAO,iCACF,QADE;AAAA,MAEL,SAAS,kCACJ,MAAM,UACN;AAAA,IAEP;AAAA,EACF;AACF;AACA,SAAS,UAAU,gBAAgB;AACjC,SAAO,WAAS;AACd,UAAM,QAAQ,OAAO,mBAAmB,aAAa,eAAe,IAAI;AACxE,UAAM,YAAY,QAAQ,QAAQ,KAAK;AACvC,6BAAyB,OAAO,SAAS;AACzC,UAAM,YAAY,EAAE,OAAO,kBAAiB,kCACvC,eACA,MACH;AACF,UAAM,eAAe,UAAU,OAAO,CAAC,KAAK,QAAQ;AAClD,YAAM,cAAc,SAAS,MAAM,MAAM,YAAY,EAAE,EAAE,GAAG,CAAC;AAC7D,aAAO,iCACF,MADE;AAAA,QAEL,CAAC,GAAG,GAAG,aAAa,WAAW;AAAA,MACjC;AAAA,IACF,GAAG,CAAC,CAAC;AACL,WAAO,iCACF,QADE;AAAA,MAEL,cAAc,kCACT,MAAM,eACN;AAAA,IAEP;AAAA,EACF;AACF;", "names": ["signal", "config", "signalState"]}