{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-inputtext.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, input, booleanAttribute, computed, HostListener, Input, Directive, NgModule } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { BaseModelHolder } from 'primeng/basemodelholder';\nimport { Fluid } from 'primeng/fluid';\nimport { style } from '@primeuix/styles/inputtext';\nimport { BaseStyle } from 'primeng/base';\nconst theme = /*css*/`\n    ${style}\n\n    /* For PrimeNG */\n   .p-inputtext.ng-invalid.ng-dirty {\n        border-color: dt('inputtext.invalid.border.color');\n    }\n\n    .p-inputtext.ng-invalid.ng-dirty::placeholder {\n        color: dt('inputtext.invalid.placeholder.color');\n    }\n`;\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-inputtext p-component', {\n    'p-filled': instance.$filled(),\n    'p-inputtext-sm': instance.pSize === 'small',\n    'p-inputtext-lg': instance.pSize === 'large',\n    'p-invalid': instance.invalid(),\n    'p-variant-filled': instance.$variant() === 'filled',\n    'p-inputtext-fluid': instance.hasFluid\n  }]\n};\nclass InputTextStyle extends BaseStyle {\n  name = 'inputtext';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputTextStyle_BaseFactory;\n    return function InputTextStyle_Factory(__ngFactoryType__) {\n      return (ɵInputTextStyle_BaseFactory || (ɵInputTextStyle_BaseFactory = i0.ɵɵgetInheritedFactory(InputTextStyle)))(__ngFactoryType__ || InputTextStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputTextStyle,\n    factory: InputTextStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * InputText renders a text field to enter data.\n *\n * [Live Demo](https://www.primeng.org/inputtext/)\n *\n * @module inputtextstyle\n *\n */\nvar InputTextClasses;\n(function (InputTextClasses) {\n  /**\n   * The class of root element\n   */\n  InputTextClasses[\"root\"] = \"p-inputtext\";\n})(InputTextClasses || (InputTextClasses = {}));\n\n/**\n * InputText directive is an extension to standard input element with theming.\n * @group Components\n */\nclass InputText extends BaseModelHolder {\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  pcFluid = inject(Fluid, {\n    optional: true,\n    host: true,\n    skipSelf: true\n  });\n  /**\n   * Defines the size of the component.\n   * @group Props\n   */\n  pSize;\n  /**\n   * Specifies the input variant of the component.\n   * @defaultValue undefined\n   * @group Props\n   */\n  variant = input();\n  /**\n   * Spans 100% width of the container when enabled.\n   * @defaultValue undefined\n   * @group Props\n   */\n  fluid = input(undefined, {\n    transform: booleanAttribute\n  });\n  /**\n   * When present, it specifies that the component should have invalid state style.\n   * @defaultValue false\n   * @group Props\n   */\n  invalid = input(undefined, {\n    transform: booleanAttribute\n  });\n  $variant = computed(() => this.variant() || this.config.inputStyle() || this.config.inputVariant());\n  _componentStyle = inject(InputTextStyle);\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    this.writeModelValue(this.ngControl?.value ?? this.el.nativeElement.value);\n    this.cd.detectChanges();\n  }\n  ngDoCheck() {\n    this.writeModelValue(this.ngControl?.value ?? this.el.nativeElement.value);\n  }\n  onInput() {\n    this.writeModelValue(this.ngControl?.value ?? this.el.nativeElement.value);\n  }\n  get hasFluid() {\n    return this.fluid() ?? !!this.pcFluid;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputText_BaseFactory;\n    return function InputText_Factory(__ngFactoryType__) {\n      return (ɵInputText_BaseFactory || (ɵInputText_BaseFactory = i0.ɵɵgetInheritedFactory(InputText)))(__ngFactoryType__ || InputText);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: InputText,\n    selectors: [[\"\", \"pInputText\", \"\"]],\n    hostVars: 2,\n    hostBindings: function InputText_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function InputText_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.cx(\"root\"));\n      }\n    },\n    inputs: {\n      pSize: \"pSize\",\n      variant: [1, \"variant\"],\n      fluid: [1, \"fluid\"],\n      invalid: [1, \"invalid\"]\n    },\n    features: [i0.ɵɵProvidersFeature([InputTextStyle]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputText, [{\n    type: Directive,\n    args: [{\n      selector: '[pInputText]',\n      standalone: true,\n      host: {\n        '[class]': \"cx('root')\"\n      },\n      providers: [InputTextStyle]\n    }]\n  }], null, {\n    pSize: [{\n      type: Input,\n      args: ['pSize']\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass InputTextModule {\n  static ɵfac = function InputTextModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputTextModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputTextModule,\n    imports: [InputText],\n    exports: [InputText]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextModule, [{\n    type: NgModule,\n    args: [{\n      imports: [InputText],\n      exports: [InputText]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputText, InputTextClasses, InputTextModule, InputTextStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM;AAAA;AAAA,EAAe;AAAA,MACf,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWX,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,2BAA2B;AAAA,IAChC,YAAY,SAAS,QAAQ;AAAA,IAC7B,kBAAkB,SAAS,UAAU;AAAA,IACrC,kBAAkB,SAAS,UAAU;AAAA,IACrC,aAAa,SAAS,QAAQ;AAAA,IAC9B,oBAAoB,SAAS,SAAS,MAAM;AAAA,IAC5C,qBAAqB,SAAS;AAAA,EAChC,CAAC;AACH;AACA,IAAM,iBAAN,MAAM,wBAAuB,UAAU;AAAA,EACrC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,mBAAkB;AAI3B,EAAAA,kBAAiB,MAAM,IAAI;AAC7B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAM9C,IAAM,YAAN,MAAM,mBAAkB,gBAAgB;AAAA,EACtC,YAAY,OAAO,WAAW;AAAA,IAC5B,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,UAAU,OAAO,OAAO;AAAA,IACtB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,QAAQ,MAAM,QAAW;AAAA,IACvB,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,MAAM,QAAW;AAAA,IACzB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,WAAW,SAAS,MAAM,KAAK,QAAQ,KAAK,KAAK,OAAO,WAAW,KAAK,KAAK,OAAO,aAAa,CAAC;AAAA,EAClG,kBAAkB,OAAO,cAAc;AAAA,EACvC,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,SAAK,gBAAgB,KAAK,WAAW,SAAS,KAAK,GAAG,cAAc,KAAK;AACzE,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB,KAAK,WAAW,SAAS,KAAK,GAAG,cAAc,KAAK;AAAA,EAC3E;AAAA,EACA,UAAU;AACR,SAAK,gBAAgB,KAAK,WAAW,SAAS,KAAK,GAAG,cAAc,KAAK;AAAA,EAC3E;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AAAA,EAChC;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,mCAAmC,QAAQ;AACzE,iBAAO,IAAI,QAAQ,MAAM;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,SAAS,CAAC,GAAG,SAAS;AAAA,IACxB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,cAAc,CAAC,GAAM,0BAA0B;AAAA,EACnF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS;AAAA,IACnB,SAAS,CAAC,SAAS;AAAA,EACrB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS;AAAA,MACnB,SAAS,CAAC,SAAS;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["InputTextClasses"]}