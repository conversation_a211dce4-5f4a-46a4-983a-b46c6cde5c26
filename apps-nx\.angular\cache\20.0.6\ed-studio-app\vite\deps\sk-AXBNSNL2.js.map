{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/sk.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    if (i === Math.floor(i) && (i >= 2 && i <= 4) && v === 0)\n        return 3;\n    if (!(v === 0))\n        return 4;\n    return 5;\n}\nexport default [\"sk\", [[\"AM\", \"PM\"], u, u], u, [[\"n\", \"p\", \"u\", \"s\", \"š\", \"p\", \"s\"], [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"], [\"nedeľa\", \"pondelok\", \"utorok\", \"streda\", \"štvrtok\", \"piatok\", \"sobota\"], [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"]], u, [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"], [\"januára\", \"februára\", \"marca\", \"apríla\", \"mája\", \"júna\", \"júla\", \"augusta\", \"septembra\", \"októbra\", \"novembra\", \"decembra\"]], [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"], [\"január\", \"február\", \"marec\", \"apríl\", \"máj\", \"jún\", \"júl\", \"august\", \"september\", \"október\", \"november\", \"december\"]], [[\"pred Kr.\", \"po Kr.\"], u, [\"pred Kristom\", \"po Kristovi\"]], 1, [6, 0], [\"d. M. y\", u, \"d. MMMM y\", \"EEEE d. MMMM y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1} {0}\", \"{1}, {0}\", u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"e\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"ILS\": [\"NIS\", \"₪\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"RUR\": [u, \"р.\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"], \"XXX\": [] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC1F,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,MAAI,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM;AACnD,WAAO;AACX,MAAI,EAAE,MAAM;AACR,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU,YAAY,UAAU,UAAU,WAAW,UAAU,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,YAAY,SAAS,UAAU,QAAQ,QAAQ,QAAQ,WAAW,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,WAAW,SAAS,SAAS,OAAO,OAAO,OAAO,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,YAAY,QAAQ,GAAG,GAAG,CAAC,gBAAgB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,aAAa,gBAAgB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,YAAY,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,MAAM;", "names": []}