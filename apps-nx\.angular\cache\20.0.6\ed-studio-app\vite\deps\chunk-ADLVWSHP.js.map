{"version": 3, "sources": ["../../../../../../node_modules/@ngx-formly/core/fesm2022/ngx-formly-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Type, TemplateRef, ComponentRef, ChangeDetectorRef, ɵNoopNgZone as _NoopNgZone, VERSION, Input, ViewChildren, Directive, ChangeDetectionStrategy, Component, Injectable, ViewContainerRef, ViewChild, Optional, InjectionToken, Inject, EventEmitter, ContentChildren, Output, NgModule } from '@angular/core';\nimport { isObservable, Observable, Subject, of, merge } from 'rxjs';\nimport * as i2 from '@angular/forms';\nimport { AbstractControl, UntypedFormArray, UntypedFormGroup, FormControl, UntypedFormControl, Validators, NgControl } from '@angular/forms';\nimport { tap, map, distinctUntilChanged, startWith, debounceTime, filter, switchMap, take } from 'rxjs/operators';\nimport * as i1 from '@angular/platform-browser';\nimport * as i2$1 from '@angular/common';\nimport { DOCUMENT, AsyncPipe, CommonModule } from '@angular/common';\nconst _c0 = [\"container\"];\nfunction FormlyField_ng_template_0_Template(rf, ctx) {}\nfunction LegacyFormlyField_ng_template_0_Template(rf, ctx) {}\nconst _c1 = [\"*\"];\nfunction FormlyGroup_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"formly-field\", 0);\n  }\n  if (rf & 2) {\n    const f_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"field\", f_r1);\n  }\n}\nconst _c2 = [\"fieldComponent\"];\nfunction disableTreeValidityCall(form, callback) {\n  const _updateTreeValidity = form._updateTreeValidity.bind(form);\n  form._updateTreeValidity = () => {};\n  callback();\n  form._updateTreeValidity = _updateTreeValidity;\n}\nfunction getFieldId(formId, field, index) {\n  if (field.id) {\n    return field.id;\n  }\n  let type = field.type;\n  if (!type && field.template) {\n    type = 'template';\n  }\n  if (type instanceof Type) {\n    type = type.prototype.constructor.name;\n  }\n  return [formId, type, field.key, index].join('_');\n}\nfunction hasKey(field) {\n  return !isNil(field.key) && field.key !== '' && (!Array.isArray(field.key) || field.key.length > 0);\n}\nfunction getKeyPath(field) {\n  if (!hasKey(field)) {\n    return [];\n  }\n  /* We store the keyPath in the field for performance reasons. This function will be called frequently. */\n  if (field._keyPath?.key !== field.key) {\n    let path = [];\n    if (typeof field.key === 'string') {\n      const key = field.key.indexOf('[') === -1 ? field.key : field.key.replace(/\\[(\\w+)\\]/g, '.$1');\n      path = key.indexOf('.') !== -1 ? key.split('.') : [key];\n    } else if (Array.isArray(field.key)) {\n      path = field.key.slice(0);\n    } else {\n      path = [`${field.key}`];\n    }\n    defineHiddenProp(field, '_keyPath', {\n      key: field.key,\n      path\n    });\n  }\n  return field._keyPath.path.slice(0);\n}\nconst FORMLY_VALIDATORS = ['required', 'pattern', 'minLength', 'maxLength', 'min', 'max'];\nfunction assignFieldValue(field, value) {\n  let paths = getKeyPath(field);\n  if (paths.length === 0) {\n    return;\n  }\n  let root = field;\n  while (root.parent) {\n    root = root.parent;\n    paths = [...getKeyPath(root), ...paths];\n  }\n  if (value === undefined && field.resetOnHide) {\n    const k = paths.pop();\n    const m = paths.reduce((model, path) => model[path] || {}, root.model);\n    delete m[k];\n    return;\n  }\n  assignModelValue(root.model, paths, value);\n}\nfunction assignModelValue(model, paths, value) {\n  for (let i = 0; i < paths.length - 1; i++) {\n    const path = paths[i];\n    if (!model[path] || !isObject(model[path])) {\n      model[path] = /^\\d+$/.test(paths[i + 1]) ? [] : {};\n    }\n    model = model[path];\n  }\n  model[paths[paths.length - 1]] = clone(value);\n}\nfunction getFieldValue(field) {\n  let model = field.parent ? field.parent.model : field.model;\n  for (const path of getKeyPath(field)) {\n    if (!model) {\n      return model;\n    }\n    model = model[path];\n  }\n  return model;\n}\nfunction reverseDeepMerge(dest, ...args) {\n  args.forEach(src => {\n    for (const srcArg in src) {\n      if (isNil(dest[srcArg]) || isBlankString(dest[srcArg])) {\n        dest[srcArg] = clone(src[srcArg]);\n      } else if (objAndSameType(dest[srcArg], src[srcArg])) {\n        reverseDeepMerge(dest[srcArg], src[srcArg]);\n      }\n    }\n  });\n  return dest;\n}\n// check a value is null or undefined\nfunction isNil(value) {\n  return value == null;\n}\nfunction isUndefined(value) {\n  return value === undefined;\n}\nfunction isBlankString(value) {\n  return value === '';\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nfunction objAndSameType(obj1, obj2) {\n  return isObject(obj1) && isObject(obj2) && Object.getPrototypeOf(obj1) === Object.getPrototypeOf(obj2) && !(Array.isArray(obj1) || Array.isArray(obj2));\n}\nfunction isObject(x) {\n  return x != null && typeof x === 'object';\n}\nfunction isPromise(obj) {\n  return !!obj && typeof obj.then === 'function';\n}\nfunction clone(value) {\n  if (!isObject(value) || isObservable(value) || value instanceof TemplateRef || /* instanceof SafeHtmlImpl */value.changingThisBreaksApplicationSecurity || ['RegExp', 'FileList', 'File', 'Blob'].indexOf(value.constructor?.name) !== -1) {\n    return value;\n  }\n  if (value instanceof Set) {\n    return new Set(value);\n  }\n  if (value instanceof Map) {\n    return new Map(value);\n  }\n  if (value instanceof Uint8Array) {\n    return new Uint8Array(value);\n  }\n  if (value instanceof Uint16Array) {\n    return new Uint16Array(value);\n  }\n  if (value instanceof Uint32Array) {\n    return new Uint32Array(value);\n  }\n  // https://github.com/moment/moment/blob/master/moment.js#L252\n  if (value._isAMomentObject && isFunction(value.clone)) {\n    return value.clone();\n  }\n  if (value instanceof AbstractControl) {\n    return null;\n  }\n  if (value instanceof Date) {\n    return new Date(value.getTime());\n  }\n  if (Array.isArray(value)) {\n    return value.slice(0).map(v => clone(v));\n  }\n  // best way to clone a js object maybe\n  // https://stackoverflow.com/questions/41474986/how-to-clone-a-javascript-es6-class-instance\n  const proto = Object.getPrototypeOf(value);\n  let c = Object.create(proto);\n  c = Object.setPrototypeOf(c, proto);\n  // need to make a deep copy so we dont use Object.assign\n  // also Object.assign wont copy property descriptor exactly\n  return Object.keys(value).reduce((newVal, prop) => {\n    const propDesc = Object.getOwnPropertyDescriptor(value, prop);\n    if (propDesc.get) {\n      Object.defineProperty(newVal, prop, propDesc);\n    } else {\n      newVal[prop] = clone(value[prop]);\n    }\n    return newVal;\n  }, c);\n}\nfunction defineHiddenProp(field, prop, defaultValue) {\n  Object.defineProperty(field, prop, {\n    enumerable: false,\n    writable: true,\n    configurable: true\n  });\n  field[prop] = defaultValue;\n}\nfunction observeDeep(source, paths, setFn) {\n  let observers = [];\n  const unsubscribe = () => {\n    observers.forEach(observer => observer());\n    observers = [];\n  };\n  const observer = observe(source, paths, ({\n    firstChange,\n    currentValue\n  }) => {\n    !firstChange && setFn();\n    unsubscribe();\n    if (isObject(currentValue) && currentValue.constructor.name === 'Object') {\n      Object.keys(currentValue).forEach(prop => {\n        observers.push(observeDeep(source, [...paths, prop], setFn));\n      });\n    }\n  });\n  return () => {\n    observer.unsubscribe();\n    unsubscribe();\n  };\n}\nfunction observe(o, paths, setFn) {\n  if (!o._observers) {\n    defineHiddenProp(o, '_observers', {});\n  }\n  let target = o;\n  for (let i = 0; i < paths.length - 1; i++) {\n    if (!target[paths[i]] || !isObject(target[paths[i]])) {\n      target[paths[i]] = /^\\d+$/.test(paths[i + 1]) ? [] : {};\n    }\n    target = target[paths[i]];\n  }\n  const key = paths[paths.length - 1];\n  const prop = paths.join('.');\n  if (!o._observers[prop]) {\n    o._observers[prop] = {\n      value: target[key],\n      onChange: []\n    };\n  }\n  const state = o._observers[prop];\n  if (target[key] !== state.value) {\n    state.value = target[key];\n  }\n  if (setFn && state.onChange.indexOf(setFn) === -1) {\n    state.onChange.push(setFn);\n    setFn({\n      currentValue: state.value,\n      firstChange: true\n    });\n    if (state.onChange.length >= 1 && isObject(target)) {\n      const {\n        enumerable\n      } = Object.getOwnPropertyDescriptor(target, key) || {\n        enumerable: true\n      };\n      Object.defineProperty(target, key, {\n        enumerable,\n        configurable: true,\n        get: () => state.value,\n        set: currentValue => {\n          if (currentValue !== state.value) {\n            const previousValue = state.value;\n            state.value = currentValue;\n            state.onChange.forEach(changeFn => changeFn({\n              previousValue,\n              currentValue,\n              firstChange: false\n            }));\n          }\n        }\n      });\n    }\n  }\n  return {\n    setValue(currentValue, emitEvent = true) {\n      if (currentValue === state.value) {\n        return;\n      }\n      const previousValue = state.value;\n      state.value = currentValue;\n      state.onChange.forEach(changeFn => {\n        if (changeFn !== setFn && emitEvent) {\n          changeFn({\n            previousValue,\n            currentValue,\n            firstChange: false\n          });\n        }\n      });\n    },\n    unsubscribe() {\n      state.onChange = state.onChange.filter(changeFn => changeFn !== setFn);\n      if (state.onChange.length === 0) {\n        delete o._observers[prop];\n      }\n    }\n  };\n}\nfunction getField(f, key) {\n  key = Array.isArray(key) ? key.join('.') : key;\n  if (!f.fieldGroup) {\n    return undefined;\n  }\n  for (let i = 0, len = f.fieldGroup.length; i < len; i++) {\n    const c = f.fieldGroup[i];\n    const k = Array.isArray(c.key) ? c.key.join('.') : c.key;\n    if (k === key) {\n      return c;\n    }\n    if (c.fieldGroup && (isNil(k) || key.indexOf(`${k}.`) === 0)) {\n      const field = getField(c, isNil(k) ? key : key.slice(k.length + 1));\n      if (field) {\n        return field;\n      }\n    }\n  }\n  return undefined;\n}\nfunction markFieldForCheck(field) {\n  field._componentRefs?.forEach(ref => {\n    // NOTE: we cannot use ref.changeDetectorRef, see https://github.com/ngx-formly/ngx-formly/issues/2191\n    if (ref instanceof ComponentRef) {\n      const changeDetectorRef = ref.injector.get(ChangeDetectorRef);\n      changeDetectorRef.markForCheck();\n    } else {\n      ref.markForCheck();\n    }\n  });\n}\nfunction isNoopNgZone(ngZone) {\n  return ngZone instanceof _NoopNgZone;\n}\nfunction isHiddenField(field) {\n  const isHidden = f => f.hide || f.expressions?.hide || f.hideExpression;\n  let setDefaultValue = !field.resetOnHide || !isHidden(field);\n  if (!isHidden(field) && field.resetOnHide) {\n    let parent = field.parent;\n    while (parent && !isHidden(parent)) {\n      parent = parent.parent;\n    }\n    setDefaultValue = !parent || !isHidden(parent);\n  }\n  return !setDefaultValue;\n}\nfunction isSignalRequired() {\n  return +VERSION.major > 18 || +VERSION.major >= 18 && +VERSION.minor >= 1;\n}\nfunction evalStringExpression(expression, argNames) {\n  try {\n    return Function(...argNames, `return ${expression};`);\n  } catch (error) {\n    console.error(error);\n  }\n}\nfunction evalExpression(expression, thisArg, argVal) {\n  if (typeof expression === 'function') {\n    return expression.apply(thisArg, argVal);\n  } else {\n    return expression ? true : false;\n  }\n}\nfunction unregisterControl(field, emitEvent = false) {\n  const control = field.formControl;\n  const fieldIndex = control._fields ? control._fields.indexOf(field) : -1;\n  if (fieldIndex !== -1) {\n    control._fields.splice(fieldIndex, 1);\n  }\n  const form = control.parent;\n  if (!form) {\n    return;\n  }\n  const opts = {\n    emitEvent\n  };\n  if (form instanceof UntypedFormArray) {\n    const key = form.controls.findIndex(c => c === control);\n    if (key !== -1) {\n      form.removeAt(key, opts);\n    }\n  } else if (form instanceof UntypedFormGroup) {\n    const paths = getKeyPath(field);\n    const key = paths[paths.length - 1];\n    if (form.get([key]) === control) {\n      form.removeControl(key, opts);\n    }\n  }\n  control.setParent(null);\n}\nfunction findControl(field) {\n  if (field.formControl) {\n    return field.formControl;\n  }\n  if (field.shareFormControl === false) {\n    return null;\n  }\n  return field.form?.get(getKeyPath(field));\n}\nfunction registerControl(field, control, emitEvent = false) {\n  control = control || field.formControl;\n  if (!control._fields) {\n    defineHiddenProp(control, '_fields', []);\n  }\n  if (control._fields.indexOf(field) === -1) {\n    control._fields.push(field);\n  }\n  if (!field.formControl && control) {\n    defineHiddenProp(field, 'formControl', control);\n    control.setValidators(null);\n    control.setAsyncValidators(null);\n    field.props.disabled = !!field.props.disabled;\n    const disabledObserver = observe(field, ['props', 'disabled'], ({\n      firstChange,\n      currentValue\n    }) => {\n      if (!firstChange) {\n        currentValue ? field.formControl.disable() : field.formControl.enable();\n      }\n    });\n    if (control instanceof FormControl) {\n      control.registerOnDisabledChange(disabledObserver.setValue);\n    }\n  }\n  if (!field.form || !hasKey(field)) {\n    return;\n  }\n  let form = field.form;\n  const paths = getKeyPath(field);\n  const value = getFieldValue(field);\n  if (!(isNil(control.value) && isNil(value)) && control.value !== value && control instanceof FormControl) {\n    control.patchValue(value);\n  }\n  for (let i = 0; i < paths.length - 1; i++) {\n    const path = paths[i];\n    if (!form.get([path])) {\n      form.setControl(path, new UntypedFormGroup({}), {\n        emitEvent\n      });\n    }\n    form = form.get([path]);\n  }\n  const key = paths[paths.length - 1];\n  if (!field._hide && form.get([key]) !== control) {\n    form.setControl(key, control, {\n      emitEvent\n    });\n  }\n}\nfunction updateValidity(c, onlySelf = false) {\n  const status = c.status;\n  const value = c.value;\n  c.updateValueAndValidity({\n    emitEvent: false,\n    onlySelf\n  });\n  if (status !== c.status) {\n    c.statusChanges.emit(c.status);\n  }\n  if (value !== c.value) {\n    c.valueChanges.emit(c.value);\n  }\n}\nfunction clearControl(form) {\n  delete form?._fields;\n  form.setValidators(null);\n  form.setAsyncValidators(null);\n  if (form instanceof UntypedFormGroup || form instanceof UntypedFormArray) {\n    Object.values(form.controls).forEach(c => clearControl(c));\n  }\n}\nclass FieldExpressionExtension {\n  onPopulate(field) {\n    if (field._expressions) {\n      return;\n    }\n    // cache built expression\n    defineHiddenProp(field, '_expressions', {});\n    observe(field, ['hide'], ({\n      currentValue,\n      firstChange\n    }) => {\n      defineHiddenProp(field, '_hide', !!currentValue);\n      if (!firstChange || firstChange && currentValue === true) {\n        field.props.hidden = currentValue;\n        field.options._hiddenFieldsForCheck.push({\n          field\n        });\n      }\n    });\n    if (field.hideExpression) {\n      observe(field, ['hideExpression'], ({\n        currentValue: expr\n      }) => {\n        field._expressions.hide = this.parseExpressions(field, 'hide', typeof expr === 'boolean' ? () => expr : expr);\n      });\n    }\n    const evalExpr = (key, expr) => {\n      if (typeof expr === 'string' || isFunction(expr)) {\n        field._expressions[key] = this.parseExpressions(field, key, expr);\n      } else if (expr instanceof Observable) {\n        field._expressions[key] = {\n          value$: expr.pipe(tap(v => {\n            this.evalExpr(field, key, v);\n            field.options._detectChanges(field);\n          }))\n        };\n      }\n    };\n    field.expressions = field.expressions || {};\n    for (const key of Object.keys(field.expressions)) {\n      observe(field, ['expressions', key], ({\n        currentValue: expr\n      }) => {\n        evalExpr(key, isFunction(expr) ? (...args) => expr(field, args[3]) : expr);\n      });\n    }\n    field.expressionProperties = field.expressionProperties || {};\n    for (const key of Object.keys(field.expressionProperties)) {\n      observe(field, ['expressionProperties', key], ({\n        currentValue\n      }) => evalExpr(key, currentValue));\n    }\n  }\n  postPopulate(field) {\n    if (field.parent) {\n      return;\n    }\n    if (!field.options.checkExpressions) {\n      let checkLocked = false;\n      field.options.checkExpressions = (f, ignoreCache) => {\n        if (checkLocked) {\n          return;\n        }\n        checkLocked = true;\n        const fieldChanged = this.checkExpressions(f, ignoreCache);\n        const options = field.options;\n        options._hiddenFieldsForCheck.sort(f => f.field.hide ? -1 : 1).forEach(f => this.changeHideState(f.field, f.field.hide ?? f.default, !ignoreCache));\n        options._hiddenFieldsForCheck = [];\n        if (fieldChanged) {\n          this.checkExpressions(field);\n        }\n        checkLocked = false;\n      };\n    }\n  }\n  parseExpressions(field, path, expr) {\n    let parentExpression;\n    if (field.parent && ['hide', 'props.disabled'].includes(path)) {\n      const rootValue = f => {\n        return path === 'hide' ? f.hide : f.props.disabled;\n      };\n      parentExpression = () => {\n        let root = field.parent;\n        while (root.parent && !rootValue(root)) {\n          root = root.parent;\n        }\n        return rootValue(root);\n      };\n    }\n    expr = expr || (() => false);\n    if (typeof expr === 'string') {\n      expr = evalStringExpression(expr, ['model', 'formState', 'field']);\n    }\n    let currentValue;\n    return {\n      callback: ignoreCache => {\n        try {\n          const exprValue = evalExpression(parentExpression ? (...args) => parentExpression(field) || expr(...args) : expr, {\n            field\n          }, [field.model, field.options.formState, field, ignoreCache]);\n          if (ignoreCache || currentValue !== exprValue && (!isObject(exprValue) || isObservable(exprValue) || JSON.stringify(exprValue) !== JSON.stringify(currentValue))) {\n            currentValue = exprValue;\n            this.evalExpr(field, path, exprValue);\n            return true;\n          }\n          return false;\n        } catch (error) {\n          error.message = `[Formly Error] [Expression \"${path}\"] ${error.message}`;\n          throw error;\n        }\n      }\n    };\n  }\n  checkExpressions(field, ignoreCache = false) {\n    if (!field) {\n      return false;\n    }\n    let fieldChanged = false;\n    if (field._expressions) {\n      for (const key of Object.keys(field._expressions)) {\n        field._expressions[key].callback?.(ignoreCache) && (fieldChanged = true);\n      }\n    }\n    field.fieldGroup?.forEach(f => this.checkExpressions(f, ignoreCache) && (fieldChanged = true));\n    return fieldChanged;\n  }\n  changeDisabledState(field, value) {\n    if (field.fieldGroup) {\n      field.fieldGroup.filter(f => !f._expressions.hasOwnProperty('props.disabled')).forEach(f => this.changeDisabledState(f, value));\n    }\n    if (hasKey(field) && field.props.disabled !== value) {\n      field.props.disabled = value;\n    }\n  }\n  changeHideState(field, hide, resetOnHide) {\n    if (field.fieldGroup) {\n      field.fieldGroup.filter(f => f && !f._expressions.hide).forEach(f => this.changeHideState(f, hide, resetOnHide));\n    }\n    if (field.formControl && hasKey(field)) {\n      defineHiddenProp(field, '_hide', !!(hide || field.hide));\n      const c = field.formControl;\n      if (c._fields?.length > 1) {\n        updateValidity(c);\n      }\n      if (hide === true && (!c._fields || c._fields.every(f => !!f._hide))) {\n        unregisterControl(field, true);\n        if (resetOnHide && field.resetOnHide) {\n          assignFieldValue(field, undefined);\n          field.formControl.reset({\n            value: undefined,\n            disabled: field.formControl.disabled\n          });\n          field.options.fieldChanges.next({\n            value: undefined,\n            field,\n            type: 'valueChanges'\n          });\n          if (field.fieldGroup && field.formControl instanceof UntypedFormArray) {\n            field.fieldGroup.length = 0;\n          }\n        }\n      } else if (hide === false) {\n        if (field.resetOnHide && !isUndefined(field.defaultValue) && isUndefined(getFieldValue(field))) {\n          assignFieldValue(field, field.defaultValue);\n        }\n        registerControl(field, undefined, true);\n        if (field.resetOnHide && field.fieldArray && field.fieldGroup?.length !== field.model?.length) {\n          field.options.build(field);\n        }\n      }\n    }\n    if (field.options.fieldChanges) {\n      field.options.fieldChanges.next({\n        field,\n        type: 'hidden',\n        value: hide\n      });\n    }\n  }\n  evalExpr(field, prop, value) {\n    if (prop.indexOf('model.') === 0) {\n      const key = prop.replace(/^model\\./, ''),\n        parent = field.fieldGroup ? field : field.parent;\n      let control = field?.key === key ? field.formControl : field.form.get(key);\n      if (!control && field.get(key)) {\n        control = field.get(key).formControl;\n      }\n      assignFieldValue({\n        key,\n        parent,\n        model: field.model\n      }, value);\n      if (control && !(isNil(control.value) && isNil(value)) && control.value !== value) {\n        control.patchValue(value);\n      }\n    } else {\n      try {\n        let target = field;\n        const paths = this._evalExpressionPath(field, prop);\n        const lastIndex = paths.length - 1;\n        for (let i = 0; i < lastIndex; i++) {\n          target = target[paths[i]];\n        }\n        target[paths[lastIndex]] = value;\n      } catch (error) {\n        error.message = `[Formly Error] [Expression \"${prop}\"] ${error.message}`;\n        throw error;\n      }\n      if (['templateOptions.disabled', 'props.disabled'].includes(prop) && hasKey(field)) {\n        this.changeDisabledState(field, value);\n      }\n    }\n    this.emitExpressionChanges(field, prop, value);\n  }\n  emitExpressionChanges(field, property, value) {\n    if (!field.options.fieldChanges) {\n      return;\n    }\n    field.options.fieldChanges.next({\n      field,\n      type: 'expressionChanges',\n      property,\n      value\n    });\n  }\n  _evalExpressionPath(field, prop) {\n    if (field._expressions[prop] && field._expressions[prop].paths) {\n      return field._expressions[prop].paths;\n    }\n    let paths = [];\n    if (prop.indexOf('[') === -1) {\n      paths = prop.split('.');\n    } else {\n      prop.split(/[[\\]]{1,2}/) // https://stackoverflow.com/a/20198206\n      .filter(p => p).forEach(path => {\n        const arrayPath = path.match(/['|\"](.*?)['|\"]/);\n        if (arrayPath) {\n          paths.push(arrayPath[1]);\n        } else {\n          paths.push(...path.split('.').filter(p => p));\n        }\n      });\n    }\n    if (field._expressions[prop]) {\n      field._expressions[prop].paths = paths;\n    }\n    return paths;\n  }\n}\nclass CoreExtension {\n  constructor(config) {\n    this.config = config;\n    this.formId = 0;\n  }\n  prePopulate(field) {\n    const root = field.parent;\n    this.initRootOptions(field);\n    this.initFieldProps(field);\n    if (root) {\n      Object.defineProperty(field, 'options', {\n        get: () => root.options,\n        configurable: true\n      });\n      Object.defineProperty(field, 'model', {\n        get: () => hasKey(field) && field.fieldGroup ? getFieldValue(field) : root.model,\n        configurable: true\n      });\n    }\n    Object.defineProperty(field, 'get', {\n      value: key => getField(field, key),\n      configurable: true\n    });\n    this.getFieldComponentInstance(field).prePopulate?.(field);\n  }\n  onPopulate(field) {\n    this.initFieldOptions(field);\n    this.getFieldComponentInstance(field).onPopulate?.(field);\n    if (field.fieldGroup) {\n      field.fieldGroup.forEach((f, index) => {\n        if (f) {\n          Object.defineProperty(f, 'parent', {\n            get: () => field,\n            configurable: true\n          });\n          Object.defineProperty(f, 'index', {\n            get: () => index,\n            configurable: true\n          });\n        }\n        this.formId++;\n      });\n    }\n  }\n  postPopulate(field) {\n    this.getFieldComponentInstance(field).postPopulate?.(field);\n  }\n  initFieldProps(field) {\n    field.props ??= field.templateOptions;\n    Object.defineProperty(field, 'templateOptions', {\n      get: () => field.props,\n      set: props => field.props = props,\n      configurable: true\n    });\n  }\n  initRootOptions(field) {\n    if (field.parent) {\n      return;\n    }\n    const options = field.options;\n    field.options.formState = field.options.formState || {};\n    if (!options.showError) {\n      options.showError = this.config.extras.showError;\n    }\n    if (!options.fieldChanges) {\n      defineHiddenProp(options, 'fieldChanges', new Subject());\n    }\n    if (!options._hiddenFieldsForCheck) {\n      options._hiddenFieldsForCheck = [];\n    }\n    options._detectChanges = f => {\n      if (f._componentRefs) {\n        markFieldForCheck(f);\n      }\n      f.fieldGroup?.forEach(f => f && options._detectChanges(f));\n    };\n    options.detectChanges = f => {\n      f.options.checkExpressions?.(f);\n      options._detectChanges(f);\n    };\n    options.resetModel = model => {\n      model = clone(model ?? options._initialModel);\n      if (field.model) {\n        Object.keys(field.model).forEach(k => delete field.model[k]);\n        Object.assign(field.model, model || {});\n      }\n      if (!isSignalRequired()) {\n        observe(options, ['parentForm', 'submitted']).setValue(false, false);\n      }\n      options.build(field);\n      field.form.reset(field.model);\n    };\n    options.updateInitialValue = model => options._initialModel = clone(model ?? field.model);\n    field.options.updateInitialValue();\n  }\n  initFieldOptions(field) {\n    reverseDeepMerge(field, {\n      id: getFieldId(`formly_${this.formId}`, field, field.index),\n      hooks: {},\n      modelOptions: {},\n      validation: {\n        messages: {}\n      },\n      props: !field.type || !hasKey(field) ? {} : {\n        label: '',\n        placeholder: '',\n        disabled: false\n      }\n    });\n    if (this.config.extras.resetFieldOnHide && field.resetOnHide !== false) {\n      field.resetOnHide = true;\n    }\n    if (field.type !== 'formly-template' && (field.template || field.expressions?.template || field.expressionProperties?.template)) {\n      field.type = 'formly-template';\n    }\n    if (!field.type && field.fieldGroup) {\n      field.type = 'formly-group';\n    }\n    if (field.type) {\n      this.config.getMergedField(field);\n    }\n    if (hasKey(field) && !isUndefined(field.defaultValue) && isUndefined(getFieldValue(field)) && !isHiddenField(field)) {\n      assignFieldValue(field, field.defaultValue);\n    }\n    field.wrappers = field.wrappers || [];\n  }\n  getFieldComponentInstance(field) {\n    const componentRefInstance = () => {\n      let componentRef = this.config.resolveFieldTypeRef(field);\n      const fieldComponentRef = field._componentRefs?.slice(-1)[0];\n      if (fieldComponentRef instanceof ComponentRef && fieldComponentRef?.componentType === componentRef?.componentType) {\n        componentRef = fieldComponentRef;\n      }\n      return componentRef?.instance;\n    };\n    if (!field._proxyInstance) {\n      defineHiddenProp(field, '_proxyInstance', new Proxy({}, {\n        get: (_, prop) => componentRefInstance()?.[prop],\n        set: (_, prop, value) => componentRefInstance()[prop] = value\n      }));\n    }\n    return field._proxyInstance;\n  }\n}\nclass FieldFormExtension {\n  prePopulate(field) {\n    if (!this.root) {\n      this.root = field;\n    }\n    if (field.parent) {\n      Object.defineProperty(field, 'form', {\n        get: () => field.parent.formControl,\n        configurable: true\n      });\n    }\n  }\n  onPopulate(field) {\n    if (field.hasOwnProperty('fieldGroup') && !hasKey(field)) {\n      defineHiddenProp(field, 'formControl', field.form);\n    } else {\n      this.addFormControl(field);\n    }\n  }\n  postPopulate(field) {\n    if (this.root !== field) {\n      return;\n    }\n    this.root = null;\n    const markForCheck = this.setValidators(field);\n    if (markForCheck && field.parent) {\n      let parent = field.parent;\n      while (parent) {\n        if (hasKey(parent) || !parent.parent) {\n          updateValidity(parent.formControl, true);\n        }\n        parent = parent.parent;\n      }\n    }\n  }\n  addFormControl(field) {\n    let control = findControl(field);\n    if (field.fieldArray) {\n      return;\n    }\n    if (!control) {\n      const controlOptions = {\n        updateOn: field.modelOptions.updateOn\n      };\n      if (field.fieldGroup) {\n        control = new UntypedFormGroup({}, controlOptions);\n      } else {\n        const value = hasKey(field) ? getFieldValue(field) : field.defaultValue;\n        control = new UntypedFormControl({\n          value,\n          disabled: !!field.props.disabled\n        }, {\n          ...controlOptions,\n          initialValueIsDefault: true\n        });\n      }\n    } else {\n      if (control instanceof FormControl) {\n        const value = hasKey(field) ? getFieldValue(field) : field.defaultValue;\n        control.defaultValue = value;\n      }\n    }\n    registerControl(field, control);\n  }\n  setValidators(field, disabled = false) {\n    if (disabled === false && hasKey(field) && field.props?.disabled) {\n      disabled = true;\n    }\n    let markForCheck = false;\n    field.fieldGroup?.forEach(f => f && this.setValidators(f, disabled) && (markForCheck = true));\n    if (hasKey(field) || !field.parent || !hasKey(field) && !field.fieldGroup) {\n      const {\n        formControl: c\n      } = field;\n      if (c) {\n        if (hasKey(field) && c instanceof FormControl) {\n          if (disabled && c.enabled) {\n            c.disable({\n              emitEvent: false,\n              onlySelf: true\n            });\n            markForCheck = true;\n          }\n          if (!disabled && c.disabled) {\n            c.enable({\n              emitEvent: false,\n              onlySelf: true\n            });\n            markForCheck = true;\n          }\n        }\n        if (null === c.validator && this.hasValidators(field, '_validators')) {\n          c.setValidators(() => {\n            const v = Validators.compose(this.mergeValidators(field, '_validators'));\n            return v ? v(c) : null;\n          });\n          markForCheck = true;\n        }\n        if (null === c.asyncValidator && this.hasValidators(field, '_asyncValidators')) {\n          c.setAsyncValidators(() => {\n            const v = Validators.composeAsync(this.mergeValidators(field, '_asyncValidators'));\n            return v ? v(c) : of(null);\n          });\n          markForCheck = true;\n        }\n        if (markForCheck) {\n          updateValidity(c, true);\n          // update validity of `FormGroup` instance created by field with nested key.\n          let parent = c.parent;\n          for (let i = 1; i < getKeyPath(field).length; i++) {\n            if (parent) {\n              updateValidity(parent, true);\n              parent = parent.parent;\n            }\n          }\n        }\n      }\n    }\n    return markForCheck;\n  }\n  hasValidators(field, type) {\n    const c = field.formControl;\n    if (c?._fields?.length > 1 && c._fields.some(f => f[type].length > 0)) {\n      return true;\n    } else if (field[type].length > 0) {\n      return true;\n    }\n    return field.fieldGroup?.some(f => f?.fieldGroup && !hasKey(f) && this.hasValidators(f, type));\n  }\n  mergeValidators(field, type) {\n    const validators = [];\n    const c = field.formControl;\n    if (c?._fields?.length > 1) {\n      c._fields.filter(f => !f._hide).forEach(f => validators.push(...f[type]));\n    } else if (field[type]) {\n      validators.push(...field[type]);\n    }\n    if (field.fieldGroup) {\n      field.fieldGroup.filter(f => f?.fieldGroup && !hasKey(f)).forEach(f => validators.push(...this.mergeValidators(f, type)));\n    }\n    return validators;\n  }\n}\nclass FieldValidationExtension {\n  constructor(config) {\n    this.config = config;\n  }\n  onPopulate(field) {\n    this.initFieldValidation(field, 'validators');\n    this.initFieldValidation(field, 'asyncValidators');\n  }\n  initFieldValidation(field, type) {\n    const validators = [];\n    if (type === 'validators' && !(field.hasOwnProperty('fieldGroup') && !hasKey(field))) {\n      validators.push(this.getPredefinedFieldValidation(field));\n    }\n    if (field[type]) {\n      for (const validatorName of Object.keys(field[type])) {\n        validatorName === 'validation' ? validators.push(...field[type].validation.map(v => this.wrapNgValidatorFn(field, v))) : validators.push(this.wrapNgValidatorFn(field, field[type][validatorName], validatorName));\n      }\n    }\n    defineHiddenProp(field, '_' + type, validators);\n  }\n  getPredefinedFieldValidation(field) {\n    let VALIDATORS = [];\n    FORMLY_VALIDATORS.forEach(opt => observe(field, ['props', opt], ({\n      currentValue,\n      firstChange\n    }) => {\n      VALIDATORS = VALIDATORS.filter(o => o !== opt);\n      if (opt === 'required' && currentValue != null && typeof currentValue !== 'boolean') {\n        console.warn(`Formly: Invalid prop 'required' of type '${typeof currentValue}', expected 'boolean' (Field:${field.key}).`);\n      }\n      if (currentValue != null && currentValue !== false) {\n        VALIDATORS.push(opt);\n      }\n      if (!firstChange && field.formControl) {\n        updateValidity(field.formControl);\n      }\n    }));\n    return control => {\n      if (VALIDATORS.length === 0) {\n        return null;\n      }\n      return Validators.compose(VALIDATORS.map(opt => () => {\n        const value = field.props[opt];\n        switch (opt) {\n          case 'required':\n            return Validators.required(control);\n          case 'pattern':\n            return Validators.pattern(value)(control);\n          case 'minLength':\n            const minLengthResult = Validators.minLength(value)(control);\n            const minLengthKey = this.config.getValidatorMessage('minlength') || field.validation?.messages?.minlength ? 'minlength' : 'minLength';\n            return minLengthResult ? {\n              [minLengthKey]: minLengthResult.minlength\n            } : null;\n          case 'maxLength':\n            const maxLengthResult = Validators.maxLength(value)(control);\n            const maxLengthKey = this.config.getValidatorMessage('maxlength') || field.validation?.messages?.maxlength ? 'maxlength' : 'maxLength';\n            return maxLengthResult ? {\n              [maxLengthKey]: maxLengthResult.maxlength\n            } : null;\n          case 'min':\n            return Validators.min(value)(control);\n          case 'max':\n            return Validators.max(value)(control);\n          default:\n            return null;\n        }\n      }))(control);\n    };\n  }\n  wrapNgValidatorFn(field, validator, validatorName) {\n    let validatorOption;\n    if (typeof validator === 'string') {\n      validatorOption = clone(this.config.getValidator(validator));\n    }\n    if (typeof validator === 'object' && validator.name) {\n      validatorOption = clone(this.config.getValidator(validator.name));\n      if (validator.options) {\n        validatorOption.options = validator.options;\n      }\n    }\n    if (typeof validator === 'object' && validator.expression) {\n      const {\n        expression,\n        ...options\n      } = validator;\n      validatorOption = {\n        name: validatorName,\n        validation: expression,\n        options: Object.keys(options).length > 0 ? options : null\n      };\n    }\n    if (typeof validator === 'function') {\n      validatorOption = {\n        name: validatorName,\n        validation: validator\n      };\n    }\n    return control => {\n      const errors = validatorOption.validation(control, field, validatorOption.options);\n      if (isPromise(errors)) {\n        return errors.then(v => this.handleResult(field, validatorName ? !!v : v, validatorOption));\n      }\n      if (isObservable(errors)) {\n        return errors.pipe(map(v => this.handleResult(field, validatorName ? !!v : v, validatorOption)));\n      }\n      return this.handleResult(field, validatorName ? !!errors : errors, validatorOption);\n    };\n  }\n  handleResult(field, errors, {\n    name,\n    options\n  }) {\n    if (typeof errors === 'boolean') {\n      errors = errors ? null : {\n        [name]: options ? options : true\n      };\n    }\n    const ctrl = field.formControl;\n    ctrl?._childrenErrors?.[name]?.();\n    if (isObject(errors)) {\n      Object.keys(errors).forEach(name => {\n        const errorPath = errors[name].errorPath ? errors[name].errorPath : options?.errorPath;\n        const childCtrl = errorPath ? field.formControl.get(errorPath) : null;\n        if (childCtrl) {\n          const {\n            errorPath: _errorPath,\n            ...opts\n          } = errors[name];\n          childCtrl.setErrors({\n            ...(childCtrl.errors || {}),\n            [name]: opts\n          });\n          !ctrl._childrenErrors && defineHiddenProp(ctrl, '_childrenErrors', {});\n          ctrl._childrenErrors[name] = () => {\n            const {\n              [name]: _toDelete,\n              ...childErrors\n            } = childCtrl.errors || {};\n            childCtrl.setErrors(Object.keys(childErrors).length === 0 ? null : childErrors);\n          };\n        }\n      });\n    }\n    return errors;\n  }\n}\nclass FieldType {\n  constructor() {\n    this.field = {};\n  }\n  set _formlyControls(controls) {\n    const f = this.field;\n    f._localFields = controls.map(c => c.control._fields || []).flat().filter(f => f.formControl !== this.field.formControl);\n  }\n  get model() {\n    return this.field.model;\n  }\n  get form() {\n    return this.field.form;\n  }\n  get options() {\n    return this.field.options;\n  }\n  get key() {\n    return this.field.key;\n  }\n  get formControl() {\n    return this.field.formControl;\n  }\n  get props() {\n    return this.field.props || {};\n  }\n  /** @deprecated Use `props` instead. */\n  get to() {\n    return this.props;\n  }\n  get showError() {\n    return this.options.showError(this);\n  }\n  get id() {\n    return this.field.id;\n  }\n  get formState() {\n    return this.options?.formState || {};\n  }\n  static {\n    this.ɵfac = function FieldType_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FieldType)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FieldType,\n      viewQuery: function FieldType_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NgControl, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._formlyControls = _t);\n        }\n      },\n      inputs: {\n        field: \"field\"\n      },\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldType, [{\n    type: Directive\n  }], null, {\n    _formlyControls: [{\n      type: ViewChildren,\n      args: [NgControl]\n    }],\n    field: [{\n      type: Input\n    }]\n  });\n})();\n\n/** @ignore */\nclass FormlyTemplateType extends FieldType {\n  get template() {\n    if (this.field && this.field.template !== this.innerHtml.template) {\n      this.innerHtml = {\n        template: this.field.template,\n        content: this.props.safeHtml ? this.sanitizer.bypassSecurityTrustHtml(this.field.template) : this.field.template\n      };\n    }\n    return this.innerHtml.content;\n  }\n  constructor(sanitizer) {\n    super();\n    this.sanitizer = sanitizer;\n    this.innerHtml = {};\n  }\n  static {\n    this.ɵfac = function FormlyTemplateType_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyTemplateType)(i0.ɵɵdirectiveInject(i1.DomSanitizer));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FormlyTemplateType,\n      selectors: [[\"formly-template\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"innerHtml\"]],\n      template: function FormlyTemplateType_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"innerHtml\", ctx.template, i0.ɵɵsanitizeHtml);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyTemplateType, [{\n    type: Component,\n    args: [{\n      selector: 'formly-template',\n      template: `<div [innerHtml]=\"template\"></div>`,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: i1.DomSanitizer\n  }], null);\n})();\n\n/**\n * Maintains list of formly config options. This can be used to register new field type.\n */\nclass FormlyConfig {\n  constructor() {\n    this.types = {};\n    this.validators = {};\n    this.wrappers = {};\n    this.messages = {};\n    this.extras = {\n      checkExpressionOn: 'modelChange',\n      lazyRender: true,\n      resetFieldOnHide: true,\n      renderFormlyFieldElement: true,\n      showError(field) {\n        return field.formControl?.invalid && (field.formControl?.touched || field.options.parentForm?.submitted || !!field.field.validation?.show);\n      }\n    };\n    this.extensions = {};\n    this.presets = {};\n    this.extensionsByPriority = {};\n    this.componentRefs = {};\n  }\n  addConfig(config) {\n    if (Array.isArray(config)) {\n      config.forEach(c => this.addConfig(c));\n      return;\n    }\n    if (config.types) {\n      config.types.forEach(type => this.setType(type));\n    }\n    if (config.validators) {\n      config.validators.forEach(validator => this.setValidator(validator));\n    }\n    if (config.wrappers) {\n      config.wrappers.forEach(wrapper => this.setWrapper(wrapper));\n    }\n    if (config.validationMessages) {\n      config.validationMessages.forEach(validation => this.addValidatorMessage(validation.name, validation.message));\n    }\n    if (config.extensions) {\n      this.setSortedExtensions(config.extensions);\n    }\n    if (config.extras) {\n      this.extras = {\n        ...this.extras,\n        ...config.extras\n      };\n    }\n    if (config.presets) {\n      this.presets = {\n        ...this.presets,\n        ...config.presets.reduce((acc, curr) => ({\n          ...acc,\n          [curr.name]: curr.config\n        }), {})\n      };\n    }\n  }\n  /**\n   * Allows you to specify a custom type which you can use in your field configuration.\n   * You can pass an object of options, or an array of objects of options.\n   */\n  setType(options) {\n    if (Array.isArray(options)) {\n      options.forEach(option => this.setType(option));\n    } else {\n      if (!this.types[options.name]) {\n        this.types[options.name] = {\n          name: options.name\n        };\n      }\n      ['component', 'extends', 'defaultOptions', 'wrappers'].forEach(prop => {\n        if (options.hasOwnProperty(prop)) {\n          this.types[options.name][prop] = options[prop];\n        }\n      });\n    }\n  }\n  getType(name, throwIfNotFound = false) {\n    if (name instanceof Type) {\n      return {\n        component: name,\n        name: name.prototype.constructor.name\n      };\n    }\n    if (!this.types[name]) {\n      if (throwIfNotFound) {\n        throw new Error(`[Formly Error] The type \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n      }\n      return null;\n    }\n    this.mergeExtendedType(name);\n    return this.types[name];\n  }\n  /** @ignore */\n  getMergedField(field = {}) {\n    const type = this.getType(field.type);\n    if (!type) {\n      return;\n    }\n    if (type.defaultOptions) {\n      reverseDeepMerge(field, type.defaultOptions);\n    }\n    const extendDefaults = type.extends && this.getType(type.extends).defaultOptions;\n    if (extendDefaults) {\n      reverseDeepMerge(field, extendDefaults);\n    }\n    if (field?.optionsTypes) {\n      field.optionsTypes.forEach(option => {\n        const defaultOptions = this.getType(option).defaultOptions;\n        if (defaultOptions) {\n          reverseDeepMerge(field, defaultOptions);\n        }\n      });\n    }\n    const componentRef = this.resolveFieldTypeRef(field);\n    if (componentRef?.instance?.defaultOptions) {\n      reverseDeepMerge(field, componentRef.instance.defaultOptions);\n    }\n    if (!field.wrappers && type.wrappers) {\n      field.wrappers = [...type.wrappers];\n    }\n  }\n  /** @ignore @internal */\n  resolveFieldTypeRef(field = {}) {\n    const type = this.getType(field.type);\n    if (!type) {\n      return null;\n    }\n    if (!type.component) {\n      return null;\n    }\n    if (!this.componentRefs[type.name]) {\n      const {\n        _viewContainerRef,\n        _injector\n      } = field.options;\n      if (!_viewContainerRef || !_injector) {\n        return null;\n      }\n      const componentRef = _viewContainerRef.createComponent(type.component, {\n        injector: _injector\n      });\n      this.componentRefs[type.name] = componentRef;\n      try {\n        componentRef.destroy();\n      } catch (e) {\n        console.error(`An error occurred while destroying the Formly component type \"${field.type}\"`, e);\n      }\n    }\n    return this.componentRefs[type.name];\n  }\n  /** @ignore @internal */\n  clearRefs() {\n    this.componentRefs = {};\n  }\n  setWrapper(options) {\n    this.wrappers[options.name] = options;\n    if (options.types) {\n      options.types.forEach(type => {\n        this.setTypeWrapper(type, options.name);\n      });\n    }\n  }\n  getWrapper(name) {\n    if (name instanceof Type) {\n      return {\n        component: name,\n        name: name.prototype.constructor.name\n      };\n    }\n    if (!this.wrappers[name]) {\n      throw new Error(`[Formly Error] The wrapper \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n    }\n    return this.wrappers[name];\n  }\n  /** @ignore */\n  setTypeWrapper(type, name) {\n    if (!this.types[type]) {\n      this.types[type] = {};\n    }\n    if (!this.types[type].wrappers) {\n      this.types[type].wrappers = [];\n    }\n    if (this.types[type].wrappers.indexOf(name) === -1) {\n      this.types[type].wrappers.push(name);\n    }\n  }\n  setValidator(options) {\n    this.validators[options.name] = options;\n  }\n  getValidator(name) {\n    if (!this.validators[name]) {\n      throw new Error(`[Formly Error] The validator \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n    }\n    return this.validators[name];\n  }\n  addValidatorMessage(name, message) {\n    this.messages[name] = message;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const deprecated = {\n        minlength: 'minLength',\n        maxlength: 'maxLength'\n      };\n      if (deprecated[name]) {\n        console.warn(`Formly deprecation: passing validation messages key '${name}' is deprecated since v6.0, use '${deprecated[name]}' instead.`);\n        this.messages[deprecated[name]] = message;\n      }\n    }\n  }\n  getValidatorMessage(name) {\n    return this.messages[name];\n  }\n  setSortedExtensions(extensionOptions) {\n    // insert new extensions, grouped by priority\n    extensionOptions.forEach(extensionOption => {\n      const priority = extensionOption.priority ?? 1;\n      this.extensionsByPriority[priority] = {\n        ...this.extensionsByPriority[priority],\n        [extensionOption.name]: extensionOption.extension\n      };\n    });\n    // flatten extensions object with sorted keys\n    this.extensions = Object.keys(this.extensionsByPriority).map(Number).sort((a, b) => a - b).reduce((acc, prio) => ({\n      ...acc,\n      ...this.extensionsByPriority[prio]\n    }), {});\n  }\n  mergeExtendedType(name) {\n    if (!this.types[name].extends) {\n      return;\n    }\n    const extendedType = this.getType(this.types[name].extends);\n    if (!this.types[name].component) {\n      this.types[name].component = extendedType.component;\n    }\n    if (!this.types[name].wrappers) {\n      this.types[name].wrappers = extendedType.wrappers;\n    }\n  }\n  static {\n    this.ɵfac = function FormlyConfig_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FormlyConfig,\n      factory: FormlyConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass FormlyTemplate {\n  constructor(ref) {\n    this.ref = ref;\n  }\n  ngOnChanges() {\n    this.name = this.name || 'formly-group';\n  }\n  static {\n    this.ɵfac = function FormlyTemplate_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyTemplate)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FormlyTemplate,\n      selectors: [[\"\", \"formlyTemplate\", \"\"]],\n      inputs: {\n        name: [0, \"formlyTemplate\", \"name\"]\n      },\n      standalone: false,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyTemplate, [{\n    type: Directive,\n    args: [{\n      selector: '[formlyTemplate]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    name: [{\n      type: Input,\n      args: ['formlyTemplate']\n    }]\n  });\n})();\n// workarround for https://github.com/angular/angular/issues/43227#issuecomment-904173738\nclass FormlyFieldTemplates {\n  static {\n    this.ɵfac = function FormlyFieldTemplates_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyFieldTemplates)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FormlyFieldTemplates,\n      factory: FormlyFieldTemplates.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyFieldTemplates, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The `<formly-field>` component is used to render the UI widget (layout + type) of a given `field`.\n */\nclass FormlyField {\n  get containerRef() {\n    return this.config.extras.renderFormlyFieldElement ? this.viewContainerRef : this.hostContainerRef;\n  }\n  get elementRef() {\n    if (this.config.extras.renderFormlyFieldElement) {\n      return this._elementRef;\n    }\n    if (this.componentRefs?.[0] instanceof ComponentRef) {\n      return this.componentRefs[0].location;\n    }\n    return null;\n  }\n  constructor(config, renderer, _elementRef, hostContainerRef, form) {\n    this.config = config;\n    this.renderer = renderer;\n    this._elementRef = _elementRef;\n    this.hostContainerRef = hostContainerRef;\n    this.form = form;\n    this.hostObservers = [];\n    this.componentRefs = [];\n    this.hooksObservers = [];\n    this.detectFieldBuild = false;\n    this.valueChangesUnsubscribe = () => {};\n  }\n  ngAfterContentInit() {\n    this.triggerHook('afterContentInit');\n  }\n  ngAfterViewInit() {\n    this.triggerHook('afterViewInit');\n  }\n  ngDoCheck() {\n    if (this.detectFieldBuild && this.field && this.field.options) {\n      this.render();\n    }\n  }\n  ngOnInit() {\n    this.triggerHook('onInit');\n  }\n  ngOnChanges(changes) {\n    this.triggerHook('onChanges', changes);\n  }\n  ngOnDestroy() {\n    this.resetRefs(this.field);\n    this.hostObservers.forEach(hostObserver => hostObserver.unsubscribe());\n    this.hooksObservers.forEach(unsubscribe => unsubscribe());\n    this.valueChangesUnsubscribe();\n    this.triggerHook('onDestroy');\n  }\n  renderField(containerRef, f, wrappers = []) {\n    if (this.containerRef === containerRef) {\n      this.resetRefs(this.field);\n      this.containerRef.clear();\n      wrappers = this.field?.wrappers;\n    }\n    if (wrappers?.length > 0) {\n      const [wrapper, ...wps] = wrappers;\n      const {\n        component\n      } = this.config.getWrapper(wrapper);\n      const ref = containerRef.createComponent(component);\n      this.attachComponentRef(ref, f);\n      observe(ref.instance, ['fieldComponent'], ({\n        currentValue,\n        previousValue,\n        firstChange\n      }) => {\n        if (currentValue) {\n          if (previousValue && previousValue._lContainer === currentValue._lContainer) {\n            return;\n          }\n          const viewRef = previousValue ? previousValue.detach() : null;\n          if (viewRef && !viewRef.destroyed) {\n            currentValue.insert(viewRef);\n          } else {\n            this.renderField(currentValue, f, wps);\n          }\n          !firstChange && ref.changeDetectorRef.detectChanges();\n        }\n      });\n    } else if (f?.type) {\n      const inlineType = this.form?.templates?.find(ref => ref.name === f.type);\n      let ref;\n      if (inlineType) {\n        ref = containerRef.createEmbeddedView(inlineType.ref, {\n          $implicit: f\n        });\n      } else {\n        const {\n          component\n        } = this.config.getType(f.type, true);\n        ref = containerRef.createComponent(component);\n      }\n      this.attachComponentRef(ref, f);\n    }\n  }\n  triggerHook(name, changes) {\n    if (name === 'onInit' || name === 'onChanges' && changes.field && !changes.field.firstChange) {\n      this.valueChangesUnsubscribe();\n      this.valueChangesUnsubscribe = this.fieldChanges(this.field);\n    }\n    if (this.field?.hooks?.[name]) {\n      if (!changes || changes.field) {\n        const r = this.field.hooks[name](this.field);\n        if (isObservable(r) && ['onInit', 'afterContentInit', 'afterViewInit'].indexOf(name) !== -1) {\n          const sub = r.subscribe();\n          this.hooksObservers.push(() => sub.unsubscribe());\n        }\n      }\n    }\n    if (name === 'onChanges' && changes.field) {\n      this.resetRefs(changes.field.previousValue);\n      this.render();\n    }\n  }\n  attachComponentRef(ref, field) {\n    this.componentRefs.push(ref);\n    field._componentRefs.push(ref);\n    if (ref instanceof ComponentRef) {\n      Object.assign(ref.instance, {\n        field\n      });\n    }\n  }\n  render() {\n    if (!this.field) {\n      return;\n    }\n    // require Formly build\n    if (!this.field.options) {\n      this.detectFieldBuild = true;\n      return;\n    }\n    this.detectFieldBuild = false;\n    this.hostObservers.forEach(hostObserver => hostObserver.unsubscribe());\n    this.hostObservers = [observe(this.field, ['hide'], ({\n      firstChange,\n      currentValue\n    }) => {\n      const containerRef = this.containerRef;\n      if (this.config.extras.lazyRender === false) {\n        firstChange && this.renderField(containerRef, this.field);\n        if (!firstChange || firstChange && currentValue) {\n          this.elementRef && this.renderer.setStyle(this.elementRef.nativeElement, 'display', currentValue ? 'none' : '');\n        }\n      } else {\n        if (currentValue) {\n          containerRef.clear();\n          if (this.field.className) {\n            this.renderer.removeAttribute(this.elementRef.nativeElement, 'class');\n          }\n        } else {\n          this.renderField(containerRef, this.field);\n          if (this.field.className) {\n            this.renderer.setAttribute(this.elementRef.nativeElement, 'class', this.field.className);\n          }\n        }\n      }\n      !firstChange && this.field.options.detectChanges(this.field);\n    }), observe(this.field, ['className'], ({\n      firstChange,\n      currentValue\n    }) => {\n      if ((!firstChange || firstChange && currentValue) && (!this.config.extras.lazyRender || this.field.hide !== true)) {\n        this.elementRef && this.renderer.setAttribute(this.elementRef.nativeElement, 'class', currentValue);\n      }\n    })];\n    if (!isSignalRequired()) {\n      ['touched', 'pristine', 'status'].forEach(prop => this.hostObservers.push(observe(this.field, ['formControl', prop], ({\n        firstChange\n      }) => !firstChange && markFieldForCheck(this.field))));\n    } else if (this.field.formControl) {\n      const events = this.field.formControl.events.subscribe(() => markFieldForCheck(this.field));\n      this.hostObservers.push(events);\n    }\n  }\n  resetRefs(field) {\n    if (field) {\n      if (field._localFields) {\n        field._localFields = [];\n      } else {\n        defineHiddenProp(this.field, '_localFields', []);\n      }\n      if (field._componentRefs) {\n        field._componentRefs = field._componentRefs.filter(ref => this.componentRefs.indexOf(ref) === -1);\n      } else {\n        defineHiddenProp(this.field, '_componentRefs', []);\n      }\n    }\n    this.componentRefs = [];\n  }\n  fieldChanges(field) {\n    if (!field) {\n      return () => {};\n    }\n    const propsObserver = observeDeep(field, ['props'], () => field.options.detectChanges(field));\n    const subscribes = [() => {\n      propsObserver();\n    }];\n    for (const key of Object.keys(field._expressions || {})) {\n      const expressionObserver = observe(field, ['_expressions', key], ({\n        currentValue,\n        previousValue\n      }) => {\n        if (previousValue?.subscription) {\n          previousValue.subscription.unsubscribe();\n          previousValue.subscription = null;\n        }\n        if (isObservable(currentValue.value$)) {\n          currentValue.subscription = currentValue.value$.subscribe();\n        }\n      });\n      subscribes.push(() => {\n        if (field._expressions[key]?.subscription) {\n          field._expressions[key].subscription.unsubscribe();\n        }\n        expressionObserver.unsubscribe();\n      });\n    }\n    for (const path of [['focus'], ['template'], ['fieldGroupClassName'], ['validation', 'show']]) {\n      const fieldObserver = observe(field, path, ({\n        firstChange\n      }) => !firstChange && field.options.detectChanges(field));\n      subscribes.push(() => fieldObserver.unsubscribe());\n    }\n    if (field.formControl && !field.fieldGroup) {\n      const control = field.formControl;\n      let valueChanges = control.valueChanges.pipe(map(value => {\n        field.parsers?.map(parserFn => value = parserFn(value, field));\n        if (!Object.is(value, field.formControl.value)) {\n          field.formControl.setValue(value);\n        }\n        return value;\n      }), distinctUntilChanged((x, y) => {\n        if (x !== y || Array.isArray(x) || isObject(x)) {\n          return false;\n        }\n        return true;\n      }));\n      if (control.value !== getFieldValue(field)) {\n        valueChanges = valueChanges.pipe(startWith(control.value));\n      }\n      const {\n        updateOn,\n        debounce\n      } = field.modelOptions;\n      if ((!updateOn || updateOn === 'change') && debounce?.default > 0) {\n        valueChanges = valueChanges.pipe(debounceTime(debounce.default));\n      }\n      const sub = valueChanges.subscribe(value => {\n        // workaround for https://github.com/angular/angular/issues/13792\n        if (control._fields?.length > 1 && control instanceof FormControl) {\n          control.patchValue(value, {\n            emitEvent: false,\n            onlySelf: true\n          });\n        }\n        if (hasKey(field)) {\n          assignFieldValue(field, value);\n        }\n        field.options.fieldChanges.next({\n          value,\n          field,\n          type: 'valueChanges'\n        });\n      });\n      subscribes.push(() => sub.unsubscribe());\n    }\n    let templateFieldsSubs = [];\n    observe(field, ['_localFields'], ({\n      currentValue\n    }) => {\n      templateFieldsSubs.forEach(unsubscribe => unsubscribe());\n      templateFieldsSubs = (currentValue || []).map(f => this.fieldChanges(f));\n    });\n    return () => {\n      subscribes.forEach(unsubscribe => unsubscribe());\n      templateFieldsSubs.forEach(unsubscribe => unsubscribe());\n    };\n  }\n  static {\n    this.ɵfac = function FormlyField_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyField)(i0.ɵɵdirectiveInject(FormlyConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(FormlyFieldTemplates, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FormlyField,\n      selectors: [[\"formly-field\"]],\n      viewQuery: function FormlyField_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.viewContainerRef = _t.first);\n        }\n      },\n      inputs: {\n        field: \"field\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"container\", \"\"]],\n      template: function FormlyField_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, FormlyField_ng_template_0_Template, 0, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]:empty{display:none}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyField, [{\n    type: Component,\n    args: [{\n      selector: 'formly-field',\n      template: '<ng-template #container></ng-template>',\n      standalone: true,\n      styles: [\":host:empty{display:none}\\n\"]\n    }]\n  }], () => [{\n    type: FormlyConfig\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: FormlyFieldTemplates,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    field: [{\n      type: Input\n    }],\n    viewContainerRef: [{\n      type: ViewChild,\n      args: ['container', {\n        read: ViewContainerRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass LegacyFormlyField extends FormlyField {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵLegacyFormlyField_BaseFactory;\n      return function LegacyFormlyField_Factory(__ngFactoryType__) {\n        return (ɵLegacyFormlyField_BaseFactory || (ɵLegacyFormlyField_BaseFactory = i0.ɵɵgetInheritedFactory(LegacyFormlyField)))(__ngFactoryType__ || LegacyFormlyField);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LegacyFormlyField,\n      selectors: [[\"formly-field\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"container\", \"\"]],\n      template: function LegacyFormlyField_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LegacyFormlyField_ng_template_0_Template, 0, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]:empty{display:none}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LegacyFormlyField, [{\n    type: Component,\n    args: [{\n      selector: 'formly-field',\n      template: '<ng-template #container></ng-template>',\n      standalone: false,\n      styles: [\":host:empty{display:none}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/** @ignore */\nclass FormlyGroup extends FieldType {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵFormlyGroup_BaseFactory;\n      return function FormlyGroup_Factory(__ngFactoryType__) {\n        return (ɵFormlyGroup_BaseFactory || (ɵFormlyGroup_BaseFactory = i0.ɵɵgetInheritedFactory(FormlyGroup)))(__ngFactoryType__ || FormlyGroup);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FormlyGroup,\n      selectors: [[\"formly-group\"]],\n      hostVars: 2,\n      hostBindings: function FormlyGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.field.fieldGroupClassName || \"\");\n        }\n      },\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 0,\n      consts: [[3, \"field\"]],\n      template: function FormlyGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵrepeaterCreate(0, FormlyGroup_For_1_Template, 1, 1, \"formly-field\", 0, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx.field.fieldGroup);\n        }\n      },\n      dependencies: [LegacyFormlyField],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyGroup, [{\n    type: Component,\n    args: [{\n      selector: 'formly-group',\n      template: `\n    @for (f of field.fieldGroup; track $index) {\n      <formly-field [field]=\"f\"></formly-field>\n    }\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class]': 'field.fieldGroupClassName || \"\"'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n\n/**\n * An InjectionToken for registering additional formly config options (types, wrappers ...).\n */\nconst FORMLY_CONFIG = new InjectionToken('FORMLY_CONFIG');\nfunction withDefaultConfig(config) {\n  return {\n    types: [{\n      name: 'formly-group',\n      component: FormlyGroup\n    }, {\n      name: 'formly-template',\n      component: FormlyTemplateType\n    }],\n    extensions: [{\n      name: 'core',\n      extension: new CoreExtension(config),\n      priority: -250\n    }, {\n      name: 'field-validation',\n      extension: new FieldValidationExtension(config),\n      priority: -200\n    }, {\n      name: 'field-form',\n      extension: new FieldFormExtension(),\n      priority: -150\n    }, {\n      name: 'field-expression',\n      extension: new FieldExpressionExtension(),\n      priority: -100\n    }]\n  };\n}\nconst provideFormlyCore = (configs = []) => {\n  return [{\n    provide: FORMLY_CONFIG,\n    multi: true,\n    useFactory: withDefaultConfig,\n    deps: [FormlyConfig]\n  }, provideFormlyConfig(configs)];\n};\nconst provideFormlyConfig = (configs = []) => {\n  return [{\n    provide: FORMLY_CONFIG,\n    multi: true,\n    useValue: configs\n  }];\n};\nclass FormlyFormBuilder {\n  constructor(config, injector, viewContainerRef, parentForm, configs = []) {\n    this.config = config;\n    this.injector = injector;\n    this.viewContainerRef = viewContainerRef;\n    this.parentForm = parentForm;\n    if (configs) {\n      configs.forEach(c => config.addConfig(c));\n    }\n  }\n  buildForm(form, fieldGroup = [], model, options) {\n    this.build({\n      fieldGroup,\n      model,\n      form,\n      options\n    });\n  }\n  build(field) {\n    if (!this.config.extensions.core) {\n      throw new Error('NgxFormly: missing `forRoot()` call. use `forRoot()` when registering the `FormlyModule`.');\n    }\n    if (!field.parent) {\n      this._setOptions(field);\n    }\n    disableTreeValidityCall(field.form, () => {\n      this._build(field);\n      // TODO: add test for https://github.com/ngx-formly/ngx-formly/issues/3910\n      if (!field.parent || field.fieldArray) {\n        // detect changes early to avoid reset value by hidden fields\n        const options = field.options;\n        if (field.parent && isHiddenField(field)) {\n          // when hide is used in expression set defaul value will not be set until detect hide changes\n          // which causes default value not set on new item is added\n          options._hiddenFieldsForCheck?.push({\n            field,\n            default: false\n          });\n        }\n        options.checkExpressions?.(field, true);\n        options._detectChanges?.(field);\n      }\n    });\n  }\n  _build(field) {\n    if (!field) {\n      return;\n    }\n    const extensions = Object.values(this.config.extensions);\n    extensions.forEach(extension => extension.prePopulate?.(field));\n    extensions.forEach(extension => extension.onPopulate?.(field));\n    field.fieldGroup?.forEach(f => this._build(f));\n    extensions.forEach(extension => extension.postPopulate?.(field));\n  }\n  _setOptions(field) {\n    field.form = field.form || new UntypedFormGroup({});\n    field.model = field.model || {};\n    field.options = field.options || {};\n    const options = field.options;\n    if (!options._viewContainerRef) {\n      defineHiddenProp(options, '_viewContainerRef', this.viewContainerRef);\n    }\n    if (!options._injector) {\n      defineHiddenProp(options, '_injector', this.injector);\n    }\n    if (!options.build) {\n      options.build = (f = field) => {\n        this.build(f);\n        return f;\n      };\n    }\n    if (!options.parentForm && this.parentForm) {\n      defineHiddenProp(options, 'parentForm', this.parentForm);\n      if (!isSignalRequired()) {\n        observe(options, ['parentForm', 'submitted'], ({\n          firstChange\n        }) => {\n          if (!firstChange) {\n            options.detectChanges(field);\n          }\n        });\n      }\n    }\n  }\n  static {\n    this.ɵfac = function FormlyFormBuilder_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyFormBuilder)(i0.ɵɵinject(FormlyConfig), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.ViewContainerRef, 8), i0.ɵɵinject(i2.FormGroupDirective, 8), i0.ɵɵinject(FORMLY_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FormlyFormBuilder,\n      factory: FormlyFormBuilder.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyFormBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: FormlyConfig\n  }, {\n    type: i0.Injector\n  }, {\n    type: i0.ViewContainerRef,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.FormGroupDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FORMLY_CONFIG]\n    }]\n  }], null);\n})();\n\n/**\n * The `<form-form>` component is the main container of the form,\n * which takes care of managing the form state\n * and delegates the rendering of each field to `<formly-field>` component.\n */\nclass FormlyForm {\n  /** The form instance which allow to track model value and validation status. */\n  set form(form) {\n    this.field.form = form;\n  }\n  get form() {\n    return this.field.form;\n  }\n  /** The model to be represented by the form. */\n  set model(model) {\n    if (this.config.extras.immutable && this._modelChangeValue === model) {\n      return;\n    }\n    this.setField({\n      model\n    });\n  }\n  get model() {\n    return this.field.model;\n  }\n  /** The field configurations for building the form. */\n  set fields(fieldGroup) {\n    this.setField({\n      fieldGroup\n    });\n  }\n  get fields() {\n    return this.field.fieldGroup;\n  }\n  /** Options for the form. */\n  set options(options) {\n    this.setField({\n      options\n    });\n  }\n  get options() {\n    return this.field.options;\n  }\n  set templates(templates) {\n    this.fieldTemplates.templates = templates;\n  }\n  constructor(builder, config, ngZone, fieldTemplates) {\n    this.builder = builder;\n    this.config = config;\n    this.ngZone = ngZone;\n    this.fieldTemplates = fieldTemplates;\n    /** Event that is emitted when the model value is changed */\n    this.modelChange = new EventEmitter();\n    this.field = {\n      type: 'formly-group'\n    };\n    this._modelChangeValue = {};\n    this.valueChangesUnsubscribe = () => {};\n  }\n  ngDoCheck() {\n    if (this.config.extras.checkExpressionOn === 'changeDetectionCheck') {\n      this.checkExpressionChange();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.fields && this.form) {\n      clearControl(this.form);\n    }\n    if (changes.fields || changes.form || changes.model && this._modelChangeValue !== changes.model.currentValue) {\n      this.valueChangesUnsubscribe();\n      this.builder.build(this.field);\n      this.valueChangesUnsubscribe = this.valueChanges();\n    }\n  }\n  ngOnDestroy() {\n    this.valueChangesUnsubscribe();\n    this.config.clearRefs();\n  }\n  checkExpressionChange() {\n    this.field.options.checkExpressions?.(this.field);\n  }\n  valueChanges() {\n    this.valueChangesUnsubscribe();\n    let formEvents = null;\n    if (isSignalRequired()) {\n      let submitted = this.options?.parentForm?.submitted;\n      formEvents = this.form.events.subscribe(() => {\n        if (submitted !== this.options?.parentForm?.submitted) {\n          this.options.detectChanges(this.field);\n          submitted = this.options?.parentForm?.submitted;\n        }\n      });\n    }\n    const fieldChangesDetection = [observeDeep(this.field.options, ['formState'], () => this.field.options.detectChanges(this.field))];\n    const valueChanges = this.field.options.fieldChanges.pipe(filter(({\n      field,\n      type\n    }) => hasKey(field) && type === 'valueChanges'), switchMap(() => isNoopNgZone(this.ngZone) ? of(null) : this.ngZone.onStable.asObservable().pipe(take(1)))).subscribe(() => this.ngZone.runGuarded(() => {\n      // runGuarded is used to keep in sync the expression changes\n      // https://github.com/ngx-formly/ngx-formly/issues/2095\n      this.checkExpressionChange();\n      this.modelChange.emit(this._modelChangeValue = clone(this.model));\n    }));\n    return () => {\n      fieldChangesDetection.forEach(fnc => fnc());\n      formEvents?.unsubscribe();\n      valueChanges.unsubscribe();\n    };\n  }\n  setField(field) {\n    if (this.config.extras.immutable) {\n      this.field = {\n        ...this.field,\n        ...clone(field)\n      };\n    } else {\n      Object.keys(field).forEach(p => this.field[p] = field[p]);\n    }\n  }\n  static {\n    this.ɵfac = function FormlyForm_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyForm)(i0.ɵɵdirectiveInject(FormlyFormBuilder), i0.ɵɵdirectiveInject(FormlyConfig), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(FormlyFieldTemplates));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FormlyForm,\n      selectors: [[\"formly-form\"]],\n      contentQueries: function FormlyForm_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, FormlyTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      inputs: {\n        form: \"form\",\n        model: \"model\",\n        fields: \"fields\",\n        options: \"options\"\n      },\n      outputs: {\n        modelChange: \"modelChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([FormlyFormBuilder, FormlyFieldTemplates]), i0.ɵɵNgOnChangesFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"field\"]],\n      template: function FormlyForm_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"formly-field\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"field\", ctx.field);\n        }\n      },\n      dependencies: [FormlyField],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyForm, [{\n    type: Component,\n    args: [{\n      selector: 'formly-form',\n      template: '<formly-field [field]=\"field\"></formly-field>',\n      providers: [FormlyFormBuilder, FormlyFieldTemplates],\n      imports: [FormlyField],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true\n    }]\n  }], () => [{\n    type: FormlyFormBuilder\n  }, {\n    type: FormlyConfig\n  }, {\n    type: i0.NgZone\n  }, {\n    type: FormlyFieldTemplates\n  }], {\n    form: [{\n      type: Input\n    }],\n    model: [{\n      type: Input\n    }],\n    fields: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    modelChange: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [FormlyTemplate]\n    }]\n  });\n})();\nclass LegacyFormlyForm extends FormlyForm {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵLegacyFormlyForm_BaseFactory;\n      return function LegacyFormlyForm_Factory(__ngFactoryType__) {\n        return (ɵLegacyFormlyForm_BaseFactory || (ɵLegacyFormlyForm_BaseFactory = i0.ɵɵgetInheritedFactory(LegacyFormlyForm)))(__ngFactoryType__ || LegacyFormlyForm);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LegacyFormlyForm,\n      selectors: [[\"formly-form\"]],\n      standalone: false,\n      features: [i0.ɵɵProvidersFeature([FormlyFormBuilder, FormlyFieldTemplates]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"field\"]],\n      template: function LegacyFormlyForm_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"formly-field\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"field\", ctx.field);\n        }\n      },\n      dependencies: [LegacyFormlyField],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LegacyFormlyForm, [{\n    type: Component,\n    args: [{\n      selector: 'formly-form',\n      template: '<formly-field [field]=\"field\"></formly-field>',\n      providers: [FormlyFormBuilder, FormlyFieldTemplates],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false\n    }]\n  }], null, null);\n})();\n\n/**\n * Allow to link the `field` HTML attributes (`id`, `name` ...) and Event attributes (`focus`, `blur` ...) to an element in the DOM.\n */\nclass FormlyAttributes {\n  get props() {\n    return this.field.props || {};\n  }\n  get fieldAttrElements() {\n    return this.field?.['_elementRefs'] || [];\n  }\n  constructor(renderer, elementRef, _document) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.uiAttributesCache = {};\n    /**\n     * HostBinding doesn't register listeners conditionally which may produce some perf issues.\n     *\n     * Formly issue: https://github.com/ngx-formly/ngx-formly/issues/1991\n     */\n    this.uiEvents = {\n      listeners: [],\n      events: ['click', 'keyup', 'keydown', 'keypress', 'focus', 'blur', 'change', 'wheel'],\n      callback: (eventName, $event) => {\n        switch (eventName) {\n          case 'focus':\n            return this.onFocus($event);\n          case 'blur':\n            return this.onBlur($event);\n          case 'change':\n            return this.onChange($event);\n          default:\n            return this.props[eventName](this.field, $event);\n        }\n      }\n    };\n    this.document = _document;\n  }\n  ngOnChanges(changes) {\n    if (changes.field) {\n      this.field.name && this.setAttribute('name', this.field.name);\n      this.uiEvents.listeners.forEach(listener => listener());\n      this.uiEvents.events.forEach(eventName => {\n        if (this.props?.[eventName] || ['focus', 'blur', 'change'].indexOf(eventName) !== -1) {\n          this.uiEvents.listeners.push(this.renderer.listen(this.elementRef.nativeElement, eventName, e => this.uiEvents.callback(eventName, e)));\n        }\n      });\n      if (this.props?.attributes) {\n        observe(this.field, ['props', 'attributes'], ({\n          currentValue,\n          previousValue\n        }) => {\n          if (previousValue) {\n            Object.keys(previousValue).forEach(attr => this.removeAttribute(attr));\n          }\n          if (currentValue) {\n            Object.keys(currentValue).forEach(attr => {\n              if (currentValue[attr] != null) {\n                this.setAttribute(attr, currentValue[attr]);\n              }\n            });\n          }\n        });\n      }\n      this.detachElementRef(changes.field.previousValue);\n      this.attachElementRef(changes.field.currentValue);\n      if (this.fieldAttrElements.length === 1) {\n        !this.id && this.field.id && this.setAttribute('id', this.field.id);\n        this.focusObserver = observe(this.field, ['focus'], ({\n          currentValue\n        }) => {\n          this.toggleFocus(currentValue);\n        });\n      }\n    }\n    if (changes.id) {\n      this.setAttribute('id', this.id);\n    }\n  }\n  /**\n   * We need to re-evaluate all the attributes on every change detection cycle, because\n   * by using a HostBinding we run into certain edge cases. This means that whatever logic\n   * is in here has to be super lean or we risk seriously damaging or destroying the performance.\n   *\n   * Formly issue: https://github.com/ngx-formly/ngx-formly/issues/1317\n   * Material issue: https://github.com/angular/components/issues/14024\n   */\n  ngDoCheck() {\n    if (!this.uiAttributes) {\n      const element = this.elementRef.nativeElement;\n      this.uiAttributes = [...FORMLY_VALIDATORS, 'tabindex', 'placeholder', 'readonly', 'disabled', 'step'].filter(attr => !element.hasAttribute || !element.hasAttribute(attr));\n    }\n    for (let i = 0; i < this.uiAttributes.length; i++) {\n      const attr = this.uiAttributes[i];\n      const value = this.props[attr];\n      if (this.uiAttributesCache[attr] !== value && (!this.props.attributes || !this.props.attributes.hasOwnProperty(attr.toLowerCase()))) {\n        this.uiAttributesCache[attr] = value;\n        if (value || value === 0) {\n          this.setAttribute(attr, value === true ? attr : `${value}`);\n        } else {\n          this.removeAttribute(attr);\n        }\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.uiEvents.listeners.forEach(listener => listener());\n    this.detachElementRef(this.field);\n    this.focusObserver?.unsubscribe();\n  }\n  toggleFocus(value) {\n    const element = this.fieldAttrElements ? this.fieldAttrElements[0] : null;\n    if (!element || !element.nativeElement.focus) {\n      return;\n    }\n    const isFocused = !!this.document.activeElement && this.fieldAttrElements.some(({\n      nativeElement\n    }) => this.document.activeElement === nativeElement || nativeElement.contains(this.document.activeElement));\n    if (value && !isFocused) {\n      Promise.resolve().then(() => element.nativeElement.focus());\n    } else if (!value && isFocused) {\n      Promise.resolve().then(() => element.nativeElement.blur());\n    }\n  }\n  onFocus($event) {\n    this.focusObserver?.setValue(true);\n    this.props.focus?.(this.field, $event);\n  }\n  onBlur($event) {\n    this.focusObserver?.setValue(false);\n    this.props.blur?.(this.field, $event);\n  }\n  // handle custom `change` event, for regular ones rely on DOM listener\n  onHostChange($event) {\n    if ($event instanceof Event) {\n      return;\n    }\n    this.onChange($event);\n  }\n  onChange($event) {\n    this.props.change?.(this.field, $event);\n    this.field.formControl?.markAsDirty();\n  }\n  attachElementRef(f) {\n    if (!f) {\n      return;\n    }\n    if (f['_elementRefs']?.indexOf(this.elementRef) === -1) {\n      f['_elementRefs'].push(this.elementRef);\n    } else {\n      defineHiddenProp(f, '_elementRefs', [this.elementRef]);\n    }\n  }\n  detachElementRef(f) {\n    const index = f?.['_elementRefs'] ? this.fieldAttrElements.indexOf(this.elementRef) : -1;\n    if (index !== -1) {\n      f['_elementRefs'].splice(index, 1);\n    }\n  }\n  setAttribute(attr, value) {\n    this.renderer.setAttribute(this.elementRef.nativeElement, attr, value);\n  }\n  removeAttribute(attr) {\n    this.renderer.removeAttribute(this.elementRef.nativeElement, attr);\n  }\n  static {\n    this.ɵfac = function FormlyAttributes_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyAttributes)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FormlyAttributes,\n      selectors: [[\"\", \"formlyAttributes\", \"\"]],\n      hostBindings: function FormlyAttributes_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"change\", function FormlyAttributes_change_HostBindingHandler($event) {\n            return ctx.onHostChange($event);\n          });\n        }\n      },\n      inputs: {\n        field: [0, \"formlyAttributes\", \"field\"],\n        id: \"id\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyAttributes, [{\n    type: Directive,\n    args: [{\n      selector: '[formlyAttributes]',\n      standalone: true,\n      host: {\n        '(change)': 'onHostChange($event)'\n      }\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    field: [{\n      type: Input,\n      args: ['formlyAttributes']\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass LegacyFormlyAttributes extends FormlyAttributes {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵLegacyFormlyAttributes_BaseFactory;\n      return function LegacyFormlyAttributes_Factory(__ngFactoryType__) {\n        return (ɵLegacyFormlyAttributes_BaseFactory || (ɵLegacyFormlyAttributes_BaseFactory = i0.ɵɵgetInheritedFactory(LegacyFormlyAttributes)))(__ngFactoryType__ || LegacyFormlyAttributes);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LegacyFormlyAttributes,\n      selectors: [[\"\", \"formlyAttributes\", \"\"]],\n      hostBindings: function LegacyFormlyAttributes_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"change\", function LegacyFormlyAttributes_change_HostBindingHandler($event) {\n            return ctx.onHostChange($event);\n          });\n        }\n      },\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LegacyFormlyAttributes, [{\n    type: Directive,\n    args: [{\n      selector: '[formlyAttributes]',\n      host: {\n        '(change)': 'onHostChange($event)'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * The `<formly-validation-message>` component renders the error message of a given `field`.\n */\nclass FormlyValidationMessage {\n  constructor(config) {\n    this.config = config;\n  }\n  ngOnChanges() {\n    const EXPR_VALIDATORS = FORMLY_VALIDATORS.map(v => `templateOptions.${v}`);\n    this.errorMessage$ = merge(this.field.formControl.statusChanges, !this.field.options ? of(null) : this.field.options.fieldChanges.pipe(filter(({\n      field,\n      type,\n      property\n    }) => {\n      return field === this.field && type === 'expressionChanges' && (property.indexOf('validation') !== -1 || EXPR_VALIDATORS.indexOf(property) !== -1);\n    }))).pipe(startWith(null), switchMap(() => isObservable(this.errorMessage) ? this.errorMessage : of(this.errorMessage)));\n  }\n  get errorMessage() {\n    const fieldForm = this.field.formControl;\n    for (const error in fieldForm.errors) {\n      if (fieldForm.errors.hasOwnProperty(error)) {\n        let message = this.config.getValidatorMessage(error);\n        if (isObject(fieldForm.errors[error])) {\n          if (fieldForm.errors[error].errorPath) {\n            return undefined;\n          }\n          if (fieldForm.errors[error].message) {\n            message = fieldForm.errors[error].message;\n          }\n        }\n        if (this.field.validation?.messages?.[error]) {\n          message = this.field.validation.messages[error];\n        }\n        if (this.field.validators?.[error]?.message) {\n          message = this.field.validators[error].message;\n        }\n        if (this.field.asyncValidators?.[error]?.message) {\n          message = this.field.asyncValidators[error].message;\n        }\n        if (typeof message === 'function') {\n          return message(fieldForm.errors[error], this.field);\n        }\n        return message;\n      }\n    }\n    return undefined;\n  }\n  static {\n    this.ɵfac = function FormlyValidationMessage_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyValidationMessage)(i0.ɵɵdirectiveInject(FormlyConfig));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FormlyValidationMessage,\n      selectors: [[\"formly-validation-message\"]],\n      inputs: {\n        field: \"field\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 2,\n      vars: 3,\n      template: function FormlyValidationMessage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtext(0);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(1, 1, ctx.errorMessage$));\n        }\n      },\n      dependencies: [AsyncPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyValidationMessage, [{\n    type: Component,\n    args: [{\n      selector: 'formly-validation-message',\n      template: '{{ errorMessage$ | async }}',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [AsyncPipe]\n    }]\n  }], () => [{\n    type: FormlyConfig\n  }], {\n    field: [{\n      type: Input\n    }]\n  });\n})();\nclass LegacyFormlyValidationMessage extends FormlyValidationMessage {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵLegacyFormlyValidationMessage_BaseFactory;\n      return function LegacyFormlyValidationMessage_Factory(__ngFactoryType__) {\n        return (ɵLegacyFormlyValidationMessage_BaseFactory || (ɵLegacyFormlyValidationMessage_BaseFactory = i0.ɵɵgetInheritedFactory(LegacyFormlyValidationMessage)))(__ngFactoryType__ || LegacyFormlyValidationMessage);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LegacyFormlyValidationMessage,\n      selectors: [[\"formly-validation-message\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 3,\n      template: function LegacyFormlyValidationMessage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtext(0);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(1, 1, ctx.errorMessage$));\n        }\n      },\n      dependencies: [i2$1.AsyncPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LegacyFormlyValidationMessage, [{\n    type: Component,\n    args: [{\n      selector: 'formly-validation-message',\n      template: '{{ errorMessage$ | async }}',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\nclass FieldArrayType extends FieldType {\n  onPopulate(field) {\n    if (hasKey(field)) {\n      const control = findControl(field);\n      registerControl(field, control ? control : new UntypedFormArray([], {\n        updateOn: field.modelOptions.updateOn\n      }));\n    }\n    field.fieldGroup = field.fieldGroup || [];\n    const length = Array.isArray(field.model) ? field.model.length : 0;\n    if (field.fieldGroup.length > length) {\n      for (let i = field.fieldGroup.length - 1; i >= length; --i) {\n        unregisterControl(field.fieldGroup[i], true);\n        field.fieldGroup.splice(i, 1);\n      }\n    }\n    for (let i = field.fieldGroup.length; i < length; i++) {\n      const f = {\n        ...clone(typeof field.fieldArray === 'function' ? field.fieldArray(field) : field.fieldArray)\n      };\n      if (f.key !== null) {\n        f.key = `${i}`;\n      }\n      field.fieldGroup.push(f);\n    }\n  }\n  add(i, initialModel, {\n    markAsDirty\n  } = {\n    markAsDirty: true\n  }) {\n    markAsDirty && this.formControl.markAsDirty();\n    i = i == null ? this.field.fieldGroup.length : i;\n    if (!this.model) {\n      assignFieldValue(this.field, []);\n    }\n    this.model.splice(i, 0, initialModel ? clone(initialModel) : undefined);\n    this.markFieldForCheck(this.field.fieldGroup[i]);\n    this._build();\n  }\n  remove(i, {\n    markAsDirty\n  } = {\n    markAsDirty: true\n  }) {\n    markAsDirty && this.formControl.markAsDirty();\n    this.model.splice(i, 1);\n    const field = this.field.fieldGroup[i];\n    this.field.fieldGroup.splice(i, 1);\n    this.field.fieldGroup.forEach((f, key) => this.updateArrayElementKey(f, `${key}`));\n    unregisterControl(field, true);\n    this._build();\n  }\n  _build() {\n    const fields = this.field.formControl._fields ?? [this.field];\n    fields.forEach(f => this.options.build(f));\n    this.options.fieldChanges.next({\n      field: this.field,\n      value: getFieldValue(this.field),\n      type: 'valueChanges'\n    });\n  }\n  updateArrayElementKey(f, newKey) {\n    if (hasKey(f)) {\n      f.key = newKey;\n      return;\n    }\n    if (!f.fieldGroup?.length) {\n      return;\n    }\n    for (let i = 0; i < f.fieldGroup.length; i++) {\n      this.updateArrayElementKey(f.fieldGroup[i], newKey);\n    }\n  }\n  markFieldForCheck(f) {\n    if (!f) {\n      return;\n    }\n    f.fieldGroup?.forEach(c => this.markFieldForCheck(c));\n    if (f.hide === false) {\n      this.options._hiddenFieldsForCheck.push({\n        field: f\n      });\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵFieldArrayType_BaseFactory;\n      return function FieldArrayType_Factory(__ngFactoryType__) {\n        return (ɵFieldArrayType_BaseFactory || (ɵFieldArrayType_BaseFactory = i0.ɵɵgetInheritedFactory(FieldArrayType)))(__ngFactoryType__ || FieldArrayType);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FieldArrayType,\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldArrayType, [{\n    type: Directive\n  }], null, null);\n})();\nclass FieldWrapper extends FieldType {\n  set _formlyControls(_) {}\n  set _staticContent(content) {\n    this.fieldComponent = content;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵFieldWrapper_BaseFactory;\n      return function FieldWrapper_Factory(__ngFactoryType__) {\n        return (ɵFieldWrapper_BaseFactory || (ɵFieldWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(FieldWrapper)))(__ngFactoryType__ || FieldWrapper);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FieldWrapper,\n      viewQuery: function FieldWrapper_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 5, ViewContainerRef);\n          i0.ɵɵviewQuery(_c2, 7, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fieldComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._staticContent = _t.first);\n        }\n      },\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldWrapper, [{\n    type: Directive\n  }], null, {\n    fieldComponent: [{\n      type: ViewChild,\n      args: ['fieldComponent', {\n        read: ViewContainerRef\n      }]\n    }],\n    _staticContent: [{\n      type: ViewChild,\n      args: ['fieldComponent', {\n        read: ViewContainerRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass FormlyModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: FormlyModule,\n      providers: [{\n        provide: FORMLY_CONFIG,\n        multi: true,\n        useFactory: withDefaultConfig,\n        deps: [FormlyConfig]\n      }, {\n        provide: FORMLY_CONFIG,\n        useValue: config,\n        multi: true\n      }, FormlyConfig, FormlyFormBuilder]\n    };\n  }\n  static forChild(config = {}) {\n    return {\n      ngModule: FormlyModule,\n      providers: [{\n        provide: FORMLY_CONFIG,\n        multi: true,\n        useFactory: withDefaultConfig,\n        deps: [FormlyConfig]\n      }, {\n        provide: FORMLY_CONFIG,\n        useValue: config,\n        multi: true\n      }, FormlyFormBuilder]\n    };\n  }\n  static {\n    this.ɵfac = function FormlyModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlyModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FormlyModule,\n      declarations: [FormlyTemplate, LegacyFormlyForm, LegacyFormlyField, LegacyFormlyAttributes, LegacyFormlyValidationMessage, FormlyGroup, FormlyTemplateType],\n      imports: [CommonModule],\n      exports: [FormlyTemplate, LegacyFormlyForm, LegacyFormlyField, LegacyFormlyAttributes, LegacyFormlyValidationMessage, FormlyGroup]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [FormlyTemplate, LegacyFormlyForm, LegacyFormlyField, LegacyFormlyAttributes, LegacyFormlyValidationMessage, FormlyGroup, FormlyTemplateType],\n      exports: [FormlyTemplate, LegacyFormlyForm, LegacyFormlyField, LegacyFormlyAttributes, LegacyFormlyValidationMessage, FormlyGroup],\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of core\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FORMLY_CONFIG, FieldArrayType, FieldType, FieldWrapper, FormlyAttributes, FormlyConfig, FormlyField, FormlyForm, FormlyFormBuilder, FormlyModule, FormlyValidationMessage, LegacyFormlyAttributes, LegacyFormlyField, LegacyFormlyForm, LegacyFormlyValidationMessage, provideFormlyConfig, provideFormlyCore, FormlyGroup as ɵFormlyGroup, FormlyTemplate as ɵFormlyTemplate, clone as ɵclone, defineHiddenProp as ɵdefineHiddenProp, getFieldValue as ɵgetFieldValue, hasKey as ɵhasKey, observe as ɵobserve, reverseDeepMerge as ɵreverseDeepMerge };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,WAAW;AACxB,SAAS,mCAAmC,IAAI,KAAK;AAAC;AACtD,SAAS,yCAAyC,IAAI,KAAK;AAAC;AAC5D,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,2BAA2B,IAAI,KAAK;AAC3C,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB,CAAC;AAAA,EACnC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,IAAG,WAAW,SAAS,IAAI;AAAA,EAC7B;AACF;AACA,IAAM,MAAM,CAAC,gBAAgB;AAC7B,SAAS,wBAAwB,MAAM,UAAU;AAC/C,QAAM,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC9D,OAAK,sBAAsB,MAAM;AAAA,EAAC;AAClC,WAAS;AACT,OAAK,sBAAsB;AAC7B;AACA,SAAS,WAAW,QAAQ,OAAO,OAAO;AACxC,MAAI,MAAM,IAAI;AACZ,WAAO,MAAM;AAAA,EACf;AACA,MAAI,OAAO,MAAM;AACjB,MAAI,CAAC,QAAQ,MAAM,UAAU;AAC3B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,MAAM;AACxB,WAAO,KAAK,UAAU,YAAY;AAAA,EACpC;AACA,SAAO,CAAC,QAAQ,MAAM,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;AAClD;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,CAAC,MAAM,MAAM,GAAG,KAAK,MAAM,QAAQ,OAAO,CAAC,MAAM,QAAQ,MAAM,GAAG,KAAK,MAAM,IAAI,SAAS;AACnG;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,CAAC,OAAO,KAAK,GAAG;AAClB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,MAAM,UAAU,QAAQ,MAAM,KAAK;AACrC,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,MAAM,QAAQ,UAAU;AACjC,YAAM,MAAM,MAAM,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,MAAM,MAAM,IAAI,QAAQ,cAAc,KAAK;AAC7F,aAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG;AAAA,IACxD,WAAW,MAAM,QAAQ,MAAM,GAAG,GAAG;AACnC,aAAO,MAAM,IAAI,MAAM,CAAC;AAAA,IAC1B,OAAO;AACL,aAAO,CAAC,GAAG,MAAM,GAAG,EAAE;AAAA,IACxB;AACA,qBAAiB,OAAO,YAAY;AAAA,MAClC,KAAK,MAAM;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,MAAM,SAAS,KAAK,MAAM,CAAC;AACpC;AACA,IAAM,oBAAoB,CAAC,YAAY,WAAW,aAAa,aAAa,OAAO,KAAK;AACxF,SAAS,iBAAiB,OAAO,OAAO;AACtC,MAAI,QAAQ,WAAW,KAAK;AAC5B,MAAI,MAAM,WAAW,GAAG;AACtB;AAAA,EACF;AACA,MAAI,OAAO;AACX,SAAO,KAAK,QAAQ;AAClB,WAAO,KAAK;AACZ,YAAQ,CAAC,GAAG,WAAW,IAAI,GAAG,GAAG,KAAK;AAAA,EACxC;AACA,MAAI,UAAU,UAAa,MAAM,aAAa;AAC5C,UAAM,IAAI,MAAM,IAAI;AACpB,UAAM,IAAI,MAAM,OAAO,CAAC,OAAO,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK;AACrE,WAAO,EAAE,CAAC;AACV;AAAA,EACF;AACA,mBAAiB,KAAK,OAAO,OAAO,KAAK;AAC3C;AACA,SAAS,iBAAiB,OAAO,OAAO,OAAO;AAC7C,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACzC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,IAAI,CAAC,GAAG;AAC1C,YAAM,IAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACnD;AACA,YAAQ,MAAM,IAAI;AAAA,EACpB;AACA,QAAM,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI,MAAM,KAAK;AAC9C;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,MAAM,SAAS,MAAM,OAAO,QAAQ,MAAM;AACtD,aAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,IAAI;AAAA,EACpB;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS,MAAM;AACvC,OAAK,QAAQ,SAAO;AAClB,eAAW,UAAU,KAAK;AACxB,UAAI,MAAM,KAAK,MAAM,CAAC,KAAK,cAAc,KAAK,MAAM,CAAC,GAAG;AACtD,aAAK,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,MAClC,WAAW,eAAe,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG;AACpD,yBAAiB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,MAAM,OAAO;AACpB,SAAO,SAAS;AAClB;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,UAAU;AACnB;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,UAAU;AACnB;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,eAAe,MAAM,MAAM;AAClC,SAAO,SAAS,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,eAAe,IAAI,MAAM,OAAO,eAAe,IAAI,KAAK,EAAE,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI;AACvJ;AACA,SAAS,SAAS,GAAG;AACnB,SAAO,KAAK,QAAQ,OAAO,MAAM;AACnC;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,CAAC,CAAC,OAAO,OAAO,IAAI,SAAS;AACtC;AACA,SAAS,MAAM,OAAO;AACpB,MAAI,CAAC,SAAS,KAAK,KAAK,aAAa,KAAK,KAAK,iBAAiB;AAAA,EAA4C,MAAM,yCAAyC,CAAC,UAAU,YAAY,QAAQ,MAAM,EAAE,QAAQ,MAAM,aAAa,IAAI,MAAM,IAAI;AACzO,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,KAAK;AACxB,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB;AACA,MAAI,iBAAiB,KAAK;AACxB,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB;AACA,MAAI,iBAAiB,YAAY;AAC/B,WAAO,IAAI,WAAW,KAAK;AAAA,EAC7B;AACA,MAAI,iBAAiB,aAAa;AAChC,WAAO,IAAI,YAAY,KAAK;AAAA,EAC9B;AACA,MAAI,iBAAiB,aAAa;AAChC,WAAO,IAAI,YAAY,KAAK;AAAA,EAC9B;AAEA,MAAI,MAAM,oBAAoB,WAAW,MAAM,KAAK,GAAG;AACrD,WAAO,MAAM,MAAM;AAAA,EACrB;AACA,MAAI,iBAAiB,iBAAiB;AACpC,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,MAAM;AACzB,WAAO,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,EACjC;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,MAAM,CAAC,EAAE,IAAI,OAAK,MAAM,CAAC,CAAC;AAAA,EACzC;AAGA,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,MAAI,IAAI,OAAO,OAAO,KAAK;AAC3B,MAAI,OAAO,eAAe,GAAG,KAAK;AAGlC,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,QAAQ,SAAS;AACjD,UAAM,WAAW,OAAO,yBAAyB,OAAO,IAAI;AAC5D,QAAI,SAAS,KAAK;AAChB,aAAO,eAAe,QAAQ,MAAM,QAAQ;AAAA,IAC9C,OAAO;AACL,aAAO,IAAI,IAAI,MAAM,MAAM,IAAI,CAAC;AAAA,IAClC;AACA,WAAO;AAAA,EACT,GAAG,CAAC;AACN;AACA,SAAS,iBAAiB,OAAO,MAAM,cAAc;AACnD,SAAO,eAAe,OAAO,MAAM;AAAA,IACjC,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,IAAI,IAAI;AAChB;AACA,SAAS,YAAY,QAAQ,OAAO,OAAO;AACzC,MAAI,YAAY,CAAC;AACjB,QAAM,cAAc,MAAM;AACxB,cAAU,QAAQ,CAAAA,cAAYA,UAAS,CAAC;AACxC,gBAAY,CAAC;AAAA,EACf;AACA,QAAM,WAAW,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACvC;AAAA,IACA;AAAA,EACF,MAAM;AACJ,KAAC,eAAe,MAAM;AACtB,gBAAY;AACZ,QAAI,SAAS,YAAY,KAAK,aAAa,YAAY,SAAS,UAAU;AACxE,aAAO,KAAK,YAAY,EAAE,QAAQ,UAAQ;AACxC,kBAAU,KAAK,YAAY,QAAQ,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAAA,MAC7D,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO,MAAM;AACX,aAAS,YAAY;AACrB,gBAAY;AAAA,EACd;AACF;AACA,SAAS,QAAQ,GAAG,OAAO,OAAO;AAChC,MAAI,CAAC,EAAE,YAAY;AACjB,qBAAiB,GAAG,cAAc,CAAC,CAAC;AAAA,EACtC;AACA,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACzC,QAAI,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG;AACpD,aAAO,MAAM,CAAC,CAAC,IAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACxD;AACA,aAAS,OAAO,MAAM,CAAC,CAAC;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM,SAAS,CAAC;AAClC,QAAM,OAAO,MAAM,KAAK,GAAG;AAC3B,MAAI,CAAC,EAAE,WAAW,IAAI,GAAG;AACvB,MAAE,WAAW,IAAI,IAAI;AAAA,MACnB,OAAO,OAAO,GAAG;AAAA,MACjB,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AACA,QAAM,QAAQ,EAAE,WAAW,IAAI;AAC/B,MAAI,OAAO,GAAG,MAAM,MAAM,OAAO;AAC/B,UAAM,QAAQ,OAAO,GAAG;AAAA,EAC1B;AACA,MAAI,SAAS,MAAM,SAAS,QAAQ,KAAK,MAAM,IAAI;AACjD,UAAM,SAAS,KAAK,KAAK;AACzB,UAAM;AAAA,MACJ,cAAc,MAAM;AAAA,MACpB,aAAa;AAAA,IACf,CAAC;AACD,QAAI,MAAM,SAAS,UAAU,KAAK,SAAS,MAAM,GAAG;AAClD,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,OAAO,yBAAyB,QAAQ,GAAG,KAAK;AAAA,QAClD,YAAY;AAAA,MACd;AACA,aAAO,eAAe,QAAQ,KAAK;AAAA,QACjC;AAAA,QACA,cAAc;AAAA,QACd,KAAK,MAAM,MAAM;AAAA,QACjB,KAAK,kBAAgB;AACnB,cAAI,iBAAiB,MAAM,OAAO;AAChC,kBAAM,gBAAgB,MAAM;AAC5B,kBAAM,QAAQ;AACd,kBAAM,SAAS,QAAQ,cAAY,SAAS;AAAA,cAC1C;AAAA,cACA;AAAA,cACA,aAAa;AAAA,YACf,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,SAAS,cAAc,YAAY,MAAM;AACvC,UAAI,iBAAiB,MAAM,OAAO;AAChC;AAAA,MACF;AACA,YAAM,gBAAgB,MAAM;AAC5B,YAAM,QAAQ;AACd,YAAM,SAAS,QAAQ,cAAY;AACjC,YAAI,aAAa,SAAS,WAAW;AACnC,mBAAS;AAAA,YACP;AAAA,YACA;AAAA,YACA,aAAa;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,cAAc;AACZ,YAAM,WAAW,MAAM,SAAS,OAAO,cAAY,aAAa,KAAK;AACrE,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,EAAE,WAAW,IAAI;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,SAAS,GAAG,KAAK;AACxB,QAAM,MAAM,QAAQ,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI;AAC3C,MAAI,CAAC,EAAE,YAAY;AACjB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,MAAM,EAAE,WAAW,QAAQ,IAAI,KAAK,KAAK;AACvD,UAAM,IAAI,EAAE,WAAW,CAAC;AACxB,UAAM,IAAI,MAAM,QAAQ,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,GAAG,IAAI,EAAE;AACrD,QAAI,MAAM,KAAK;AACb,aAAO;AAAA,IACT;AACA,QAAI,EAAE,eAAe,MAAM,CAAC,KAAK,IAAI,QAAQ,GAAG,CAAC,GAAG,MAAM,IAAI;AAC5D,YAAM,QAAQ,SAAS,GAAG,MAAM,CAAC,IAAI,MAAM,IAAI,MAAM,EAAE,SAAS,CAAC,CAAC;AAClE,UAAI,OAAO;AACT,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,OAAO;AAChC,QAAM,gBAAgB,QAAQ,SAAO;AAEnC,QAAI,eAAe,gBAAc;AAC/B,YAAM,oBAAoB,IAAI,SAAS,IAAI,iBAAiB;AAC5D,wBAAkB,aAAa;AAAA,IACjC,OAAO;AACL,UAAI,aAAa;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,QAAQ;AAC5B,SAAO,kBAAkB;AAC3B;AACA,SAAS,cAAc,OAAO;AAC5B,QAAM,WAAW,OAAK,EAAE,QAAQ,EAAE,aAAa,QAAQ,EAAE;AACzD,MAAI,kBAAkB,CAAC,MAAM,eAAe,CAAC,SAAS,KAAK;AAC3D,MAAI,CAAC,SAAS,KAAK,KAAK,MAAM,aAAa;AACzC,QAAI,SAAS,MAAM;AACnB,WAAO,UAAU,CAAC,SAAS,MAAM,GAAG;AAClC,eAAS,OAAO;AAAA,IAClB;AACA,sBAAkB,CAAC,UAAU,CAAC,SAAS,MAAM;AAAA,EAC/C;AACA,SAAO,CAAC;AACV;AACA,SAAS,mBAAmB;AAC1B,SAAO,CAAC,QAAQ,QAAQ,MAAM,CAAC,QAAQ,SAAS,MAAM,CAAC,QAAQ,SAAS;AAC1E;AACA,SAAS,qBAAqB,YAAY,UAAU;AAClD,MAAI;AACF,WAAO,SAAS,GAAG,UAAU,UAAU,UAAU,GAAG;AAAA,EACtD,SAAS,OAAO;AACd,YAAQ,MAAM,KAAK;AAAA,EACrB;AACF;AACA,SAAS,eAAe,YAAY,SAAS,QAAQ;AACnD,MAAI,OAAO,eAAe,YAAY;AACpC,WAAO,WAAW,MAAM,SAAS,MAAM;AAAA,EACzC,OAAO;AACL,WAAO,aAAa,OAAO;AAAA,EAC7B;AACF;AACA,SAAS,kBAAkB,OAAO,YAAY,OAAO;AACnD,QAAM,UAAU,MAAM;AACtB,QAAM,aAAa,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,KAAK,IAAI;AACtE,MAAI,eAAe,IAAI;AACrB,YAAQ,QAAQ,OAAO,YAAY,CAAC;AAAA,EACtC;AACA,QAAM,OAAO,QAAQ;AACrB,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX;AAAA,EACF;AACA,MAAI,gBAAgB,kBAAkB;AACpC,UAAM,MAAM,KAAK,SAAS,UAAU,OAAK,MAAM,OAAO;AACtD,QAAI,QAAQ,IAAI;AACd,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB;AAAA,EACF,WAAW,gBAAgB,kBAAkB;AAC3C,UAAM,QAAQ,WAAW,KAAK;AAC9B,UAAM,MAAM,MAAM,MAAM,SAAS,CAAC;AAClC,QAAI,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,SAAS;AAC/B,WAAK,cAAc,KAAK,IAAI;AAAA,IAC9B;AAAA,EACF;AACA,UAAQ,UAAU,IAAI;AACxB;AACA,SAAS,YAAY,OAAO;AAC1B,MAAI,MAAM,aAAa;AACrB,WAAO,MAAM;AAAA,EACf;AACA,MAAI,MAAM,qBAAqB,OAAO;AACpC,WAAO;AAAA,EACT;AACA,SAAO,MAAM,MAAM,IAAI,WAAW,KAAK,CAAC;AAC1C;AACA,SAAS,gBAAgB,OAAO,SAAS,YAAY,OAAO;AAC1D,YAAU,WAAW,MAAM;AAC3B,MAAI,CAAC,QAAQ,SAAS;AACpB,qBAAiB,SAAS,WAAW,CAAC,CAAC;AAAA,EACzC;AACA,MAAI,QAAQ,QAAQ,QAAQ,KAAK,MAAM,IAAI;AACzC,YAAQ,QAAQ,KAAK,KAAK;AAAA,EAC5B;AACA,MAAI,CAAC,MAAM,eAAe,SAAS;AACjC,qBAAiB,OAAO,eAAe,OAAO;AAC9C,YAAQ,cAAc,IAAI;AAC1B,YAAQ,mBAAmB,IAAI;AAC/B,UAAM,MAAM,WAAW,CAAC,CAAC,MAAM,MAAM;AACrC,UAAM,mBAAmB,QAAQ,OAAO,CAAC,SAAS,UAAU,GAAG,CAAC;AAAA,MAC9D;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,CAAC,aAAa;AAChB,uBAAe,MAAM,YAAY,QAAQ,IAAI,MAAM,YAAY,OAAO;AAAA,MACxE;AAAA,IACF,CAAC;AACD,QAAI,mBAAmB,aAAa;AAClC,cAAQ,yBAAyB,iBAAiB,QAAQ;AAAA,IAC5D;AAAA,EACF;AACA,MAAI,CAAC,MAAM,QAAQ,CAAC,OAAO,KAAK,GAAG;AACjC;AAAA,EACF;AACA,MAAI,OAAO,MAAM;AACjB,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,QAAQ,cAAc,KAAK;AACjC,MAAI,EAAE,MAAM,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,QAAQ,UAAU,SAAS,mBAAmB,aAAa;AACxG,YAAQ,WAAW,KAAK;AAAA,EAC1B;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACzC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG;AACrB,WAAK,WAAW,MAAM,IAAI,iBAAiB,CAAC,CAAC,GAAG;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK,IAAI,CAAC,IAAI,CAAC;AAAA,EACxB;AACA,QAAM,MAAM,MAAM,MAAM,SAAS,CAAC;AAClC,MAAI,CAAC,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,SAAS;AAC/C,SAAK,WAAW,KAAK,SAAS;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,eAAe,GAAG,WAAW,OAAO;AAC3C,QAAM,SAAS,EAAE;AACjB,QAAM,QAAQ,EAAE;AAChB,IAAE,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX;AAAA,EACF,CAAC;AACD,MAAI,WAAW,EAAE,QAAQ;AACvB,MAAE,cAAc,KAAK,EAAE,MAAM;AAAA,EAC/B;AACA,MAAI,UAAU,EAAE,OAAO;AACrB,MAAE,aAAa,KAAK,EAAE,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,MAAM;AACb,OAAK,cAAc,IAAI;AACvB,OAAK,mBAAmB,IAAI;AAC5B,MAAI,gBAAgB,oBAAoB,gBAAgB,kBAAkB;AACxE,WAAO,OAAO,KAAK,QAAQ,EAAE,QAAQ,OAAK,aAAa,CAAC,CAAC;AAAA,EAC3D;AACF;AACA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,WAAW,OAAO;AAChB,QAAI,MAAM,cAAc;AACtB;AAAA,IACF;AAEA,qBAAiB,OAAO,gBAAgB,CAAC,CAAC;AAC1C,YAAQ,OAAO,CAAC,MAAM,GAAG,CAAC;AAAA,MACxB;AAAA,MACA;AAAA,IACF,MAAM;AACJ,uBAAiB,OAAO,SAAS,CAAC,CAAC,YAAY;AAC/C,UAAI,CAAC,eAAe,eAAe,iBAAiB,MAAM;AACxD,cAAM,MAAM,SAAS;AACrB,cAAM,QAAQ,sBAAsB,KAAK;AAAA,UACvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,MAAM,gBAAgB;AACxB,cAAQ,OAAO,CAAC,gBAAgB,GAAG,CAAC;AAAA,QAClC,cAAc;AAAA,MAChB,MAAM;AACJ,cAAM,aAAa,OAAO,KAAK,iBAAiB,OAAO,QAAQ,OAAO,SAAS,YAAY,MAAM,OAAO,IAAI;AAAA,MAC9G,CAAC;AAAA,IACH;AACA,UAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,UAAI,OAAO,SAAS,YAAY,WAAW,IAAI,GAAG;AAChD,cAAM,aAAa,GAAG,IAAI,KAAK,iBAAiB,OAAO,KAAK,IAAI;AAAA,MAClE,WAAW,gBAAgB,YAAY;AACrC,cAAM,aAAa,GAAG,IAAI;AAAA,UACxB,QAAQ,KAAK,KAAK,IAAI,OAAK;AACzB,iBAAK,SAAS,OAAO,KAAK,CAAC;AAC3B,kBAAM,QAAQ,eAAe,KAAK;AAAA,UACpC,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,UAAM,cAAc,MAAM,eAAe,CAAC;AAC1C,eAAW,OAAO,OAAO,KAAK,MAAM,WAAW,GAAG;AAChD,cAAQ,OAAO,CAAC,eAAe,GAAG,GAAG,CAAC;AAAA,QACpC,cAAc;AAAA,MAChB,MAAM;AACJ,iBAAS,KAAK,WAAW,IAAI,IAAI,IAAI,SAAS,KAAK,OAAO,KAAK,CAAC,CAAC,IAAI,IAAI;AAAA,MAC3E,CAAC;AAAA,IACH;AACA,UAAM,uBAAuB,MAAM,wBAAwB,CAAC;AAC5D,eAAW,OAAO,OAAO,KAAK,MAAM,oBAAoB,GAAG;AACzD,cAAQ,OAAO,CAAC,wBAAwB,GAAG,GAAG,CAAC;AAAA,QAC7C;AAAA,MACF,MAAM,SAAS,KAAK,YAAY,CAAC;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,QAAQ;AAChB;AAAA,IACF;AACA,QAAI,CAAC,MAAM,QAAQ,kBAAkB;AACnC,UAAI,cAAc;AAClB,YAAM,QAAQ,mBAAmB,CAAC,GAAG,gBAAgB;AACnD,YAAI,aAAa;AACf;AAAA,QACF;AACA,sBAAc;AACd,cAAM,eAAe,KAAK,iBAAiB,GAAG,WAAW;AACzD,cAAM,UAAU,MAAM;AACtB,gBAAQ,sBAAsB,KAAK,CAAAC,OAAKA,GAAE,MAAM,OAAO,KAAK,CAAC,EAAE,QAAQ,CAAAA,OAAK,KAAK,gBAAgBA,GAAE,OAAOA,GAAE,MAAM,QAAQA,GAAE,SAAS,CAAC,WAAW,CAAC;AAClJ,gBAAQ,wBAAwB,CAAC;AACjC,YAAI,cAAc;AAChB,eAAK,iBAAiB,KAAK;AAAA,QAC7B;AACA,sBAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,MAAM,MAAM;AAClC,QAAI;AACJ,QAAI,MAAM,UAAU,CAAC,QAAQ,gBAAgB,EAAE,SAAS,IAAI,GAAG;AAC7D,YAAM,YAAY,OAAK;AACrB,eAAO,SAAS,SAAS,EAAE,OAAO,EAAE,MAAM;AAAA,MAC5C;AACA,yBAAmB,MAAM;AACvB,YAAI,OAAO,MAAM;AACjB,eAAO,KAAK,UAAU,CAAC,UAAU,IAAI,GAAG;AACtC,iBAAO,KAAK;AAAA,QACd;AACA,eAAO,UAAU,IAAI;AAAA,MACvB;AAAA,IACF;AACA,WAAO,SAAS,MAAM;AACtB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,qBAAqB,MAAM,CAAC,SAAS,aAAa,OAAO,CAAC;AAAA,IACnE;AACA,QAAI;AACJ,WAAO;AAAA,MACL,UAAU,iBAAe;AACvB,YAAI;AACF,gBAAM,YAAY,eAAe,mBAAmB,IAAI,SAAS,iBAAiB,KAAK,KAAK,KAAK,GAAG,IAAI,IAAI,MAAM;AAAA,YAChH;AAAA,UACF,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,WAAW,OAAO,WAAW,CAAC;AAC7D,cAAI,eAAe,iBAAiB,cAAc,CAAC,SAAS,SAAS,KAAK,aAAa,SAAS,KAAK,KAAK,UAAU,SAAS,MAAM,KAAK,UAAU,YAAY,IAAI;AAChK,2BAAe;AACf,iBAAK,SAAS,OAAO,MAAM,SAAS;AACpC,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,SAAS,OAAO;AACd,gBAAM,UAAU,+BAA+B,IAAI,MAAM,MAAM,OAAO;AACtE,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,cAAc,OAAO;AAC3C,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,eAAe;AACnB,QAAI,MAAM,cAAc;AACtB,iBAAW,OAAO,OAAO,KAAK,MAAM,YAAY,GAAG;AACjD,cAAM,aAAa,GAAG,EAAE,WAAW,WAAW,MAAM,eAAe;AAAA,MACrE;AAAA,IACF;AACA,UAAM,YAAY,QAAQ,OAAK,KAAK,iBAAiB,GAAG,WAAW,MAAM,eAAe,KAAK;AAC7F,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,OAAO,OAAO;AAChC,QAAI,MAAM,YAAY;AACpB,YAAM,WAAW,OAAO,OAAK,CAAC,EAAE,aAAa,eAAe,gBAAgB,CAAC,EAAE,QAAQ,OAAK,KAAK,oBAAoB,GAAG,KAAK,CAAC;AAAA,IAChI;AACA,QAAI,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,OAAO;AACnD,YAAM,MAAM,WAAW;AAAA,IACzB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO,MAAM,aAAa;AACxC,QAAI,MAAM,YAAY;AACpB,YAAM,WAAW,OAAO,OAAK,KAAK,CAAC,EAAE,aAAa,IAAI,EAAE,QAAQ,OAAK,KAAK,gBAAgB,GAAG,MAAM,WAAW,CAAC;AAAA,IACjH;AACA,QAAI,MAAM,eAAe,OAAO,KAAK,GAAG;AACtC,uBAAiB,OAAO,SAAS,CAAC,EAAE,QAAQ,MAAM,KAAK;AACvD,YAAM,IAAI,MAAM;AAChB,UAAI,EAAE,SAAS,SAAS,GAAG;AACzB,uBAAe,CAAC;AAAA,MAClB;AACA,UAAI,SAAS,SAAS,CAAC,EAAE,WAAW,EAAE,QAAQ,MAAM,OAAK,CAAC,CAAC,EAAE,KAAK,IAAI;AACpE,0BAAkB,OAAO,IAAI;AAC7B,YAAI,eAAe,MAAM,aAAa;AACpC,2BAAiB,OAAO,MAAS;AACjC,gBAAM,YAAY,MAAM;AAAA,YACtB,OAAO;AAAA,YACP,UAAU,MAAM,YAAY;AAAA,UAC9B,CAAC;AACD,gBAAM,QAAQ,aAAa,KAAK;AAAA,YAC9B,OAAO;AAAA,YACP;AAAA,YACA,MAAM;AAAA,UACR,CAAC;AACD,cAAI,MAAM,cAAc,MAAM,uBAAuB,kBAAkB;AACrE,kBAAM,WAAW,SAAS;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,WAAW,SAAS,OAAO;AACzB,YAAI,MAAM,eAAe,CAAC,YAAY,MAAM,YAAY,KAAK,YAAY,cAAc,KAAK,CAAC,GAAG;AAC9F,2BAAiB,OAAO,MAAM,YAAY;AAAA,QAC5C;AACA,wBAAgB,OAAO,QAAW,IAAI;AACtC,YAAI,MAAM,eAAe,MAAM,cAAc,MAAM,YAAY,WAAW,MAAM,OAAO,QAAQ;AAC7F,gBAAM,QAAQ,MAAM,KAAK;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,cAAc;AAC9B,YAAM,QAAQ,aAAa,KAAK;AAAA,QAC9B;AAAA,QACA,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS,OAAO,MAAM,OAAO;AAC3B,QAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAChC,YAAM,MAAM,KAAK,QAAQ,YAAY,EAAE,GACrC,SAAS,MAAM,aAAa,QAAQ,MAAM;AAC5C,UAAI,UAAU,OAAO,QAAQ,MAAM,MAAM,cAAc,MAAM,KAAK,IAAI,GAAG;AACzE,UAAI,CAAC,WAAW,MAAM,IAAI,GAAG,GAAG;AAC9B,kBAAU,MAAM,IAAI,GAAG,EAAE;AAAA,MAC3B;AACA,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA,OAAO,MAAM;AAAA,MACf,GAAG,KAAK;AACR,UAAI,WAAW,EAAE,MAAM,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,QAAQ,UAAU,OAAO;AACjF,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,UAAI;AACF,YAAI,SAAS;AACb,cAAM,QAAQ,KAAK,oBAAoB,OAAO,IAAI;AAClD,cAAM,YAAY,MAAM,SAAS;AACjC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,mBAAS,OAAO,MAAM,CAAC,CAAC;AAAA,QAC1B;AACA,eAAO,MAAM,SAAS,CAAC,IAAI;AAAA,MAC7B,SAAS,OAAO;AACd,cAAM,UAAU,+BAA+B,IAAI,MAAM,MAAM,OAAO;AACtE,cAAM;AAAA,MACR;AACA,UAAI,CAAC,4BAA4B,gBAAgB,EAAE,SAAS,IAAI,KAAK,OAAO,KAAK,GAAG;AAClF,aAAK,oBAAoB,OAAO,KAAK;AAAA,MACvC;AAAA,IACF;AACA,SAAK,sBAAsB,OAAO,MAAM,KAAK;AAAA,EAC/C;AAAA,EACA,sBAAsB,OAAO,UAAU,OAAO;AAC5C,QAAI,CAAC,MAAM,QAAQ,cAAc;AAC/B;AAAA,IACF;AACA,UAAM,QAAQ,aAAa,KAAK;AAAA,MAC9B;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,OAAO,MAAM;AAC/B,QAAI,MAAM,aAAa,IAAI,KAAK,MAAM,aAAa,IAAI,EAAE,OAAO;AAC9D,aAAO,MAAM,aAAa,IAAI,EAAE;AAAA,IAClC;AACA,QAAI,QAAQ,CAAC;AACb,QAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,cAAQ,KAAK,MAAM,GAAG;AAAA,IACxB,OAAO;AACL,WAAK,MAAM,YAAY,EACtB,OAAO,OAAK,CAAC,EAAE,QAAQ,UAAQ;AAC9B,cAAM,YAAY,KAAK,MAAM,iBAAiB;AAC9C,YAAI,WAAW;AACb,gBAAM,KAAK,UAAU,CAAC,CAAC;AAAA,QACzB,OAAO;AACL,gBAAM,KAAK,GAAG,KAAK,MAAM,GAAG,EAAE,OAAO,OAAK,CAAC,CAAC;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,aAAa,IAAI,GAAG;AAC5B,YAAM,aAAa,IAAI,EAAE,QAAQ;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,OAAO,MAAM;AACnB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,eAAe,KAAK;AACzB,QAAI,MAAM;AACR,aAAO,eAAe,OAAO,WAAW;AAAA,QACtC,KAAK,MAAM,KAAK;AAAA,QAChB,cAAc;AAAA,MAChB,CAAC;AACD,aAAO,eAAe,OAAO,SAAS;AAAA,QACpC,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,aAAa,cAAc,KAAK,IAAI,KAAK;AAAA,QAC3E,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,WAAO,eAAe,OAAO,OAAO;AAAA,MAClC,OAAO,SAAO,SAAS,OAAO,GAAG;AAAA,MACjC,cAAc;AAAA,IAChB,CAAC;AACD,SAAK,0BAA0B,KAAK,EAAE,cAAc,KAAK;AAAA,EAC3D;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,0BAA0B,KAAK,EAAE,aAAa,KAAK;AACxD,QAAI,MAAM,YAAY;AACpB,YAAM,WAAW,QAAQ,CAAC,GAAG,UAAU;AACrC,YAAI,GAAG;AACL,iBAAO,eAAe,GAAG,UAAU;AAAA,YACjC,KAAK,MAAM;AAAA,YACX,cAAc;AAAA,UAChB,CAAC;AACD,iBAAO,eAAe,GAAG,SAAS;AAAA,YAChC,KAAK,MAAM;AAAA,YACX,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AACA,aAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,0BAA0B,KAAK,EAAE,eAAe,KAAK;AAAA,EAC5D;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,UAAU,MAAM;AACtB,WAAO,eAAe,OAAO,mBAAmB;AAAA,MAC9C,KAAK,MAAM,MAAM;AAAA,MACjB,KAAK,WAAS,MAAM,QAAQ;AAAA,MAC5B,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,MAAM,QAAQ;AAChB;AAAA,IACF;AACA,UAAM,UAAU,MAAM;AACtB,UAAM,QAAQ,YAAY,MAAM,QAAQ,aAAa,CAAC;AACtD,QAAI,CAAC,QAAQ,WAAW;AACtB,cAAQ,YAAY,KAAK,OAAO,OAAO;AAAA,IACzC;AACA,QAAI,CAAC,QAAQ,cAAc;AACzB,uBAAiB,SAAS,gBAAgB,IAAI,QAAQ,CAAC;AAAA,IACzD;AACA,QAAI,CAAC,QAAQ,uBAAuB;AAClC,cAAQ,wBAAwB,CAAC;AAAA,IACnC;AACA,YAAQ,iBAAiB,OAAK;AAC5B,UAAI,EAAE,gBAAgB;AACpB,0BAAkB,CAAC;AAAA,MACrB;AACA,QAAE,YAAY,QAAQ,CAAAA,OAAKA,MAAK,QAAQ,eAAeA,EAAC,CAAC;AAAA,IAC3D;AACA,YAAQ,gBAAgB,OAAK;AAC3B,QAAE,QAAQ,mBAAmB,CAAC;AAC9B,cAAQ,eAAe,CAAC;AAAA,IAC1B;AACA,YAAQ,aAAa,WAAS;AAC5B,cAAQ,MAAM,SAAS,QAAQ,aAAa;AAC5C,UAAI,MAAM,OAAO;AACf,eAAO,KAAK,MAAM,KAAK,EAAE,QAAQ,OAAK,OAAO,MAAM,MAAM,CAAC,CAAC;AAC3D,eAAO,OAAO,MAAM,OAAO,SAAS,CAAC,CAAC;AAAA,MACxC;AACA,UAAI,CAAC,iBAAiB,GAAG;AACvB,gBAAQ,SAAS,CAAC,cAAc,WAAW,CAAC,EAAE,SAAS,OAAO,KAAK;AAAA,MACrE;AACA,cAAQ,MAAM,KAAK;AACnB,YAAM,KAAK,MAAM,MAAM,KAAK;AAAA,IAC9B;AACA,YAAQ,qBAAqB,WAAS,QAAQ,gBAAgB,MAAM,SAAS,MAAM,KAAK;AACxF,UAAM,QAAQ,mBAAmB;AAAA,EACnC;AAAA,EACA,iBAAiB,OAAO;AACtB,qBAAiB,OAAO;AAAA,MACtB,IAAI,WAAW,UAAU,KAAK,MAAM,IAAI,OAAO,MAAM,KAAK;AAAA,MAC1D,OAAO,CAAC;AAAA,MACR,cAAc,CAAC;AAAA,MACf,YAAY;AAAA,QACV,UAAU,CAAC;AAAA,MACb;AAAA,MACA,OAAO,CAAC,MAAM,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI;AAAA,QAC1C,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AACD,QAAI,KAAK,OAAO,OAAO,oBAAoB,MAAM,gBAAgB,OAAO;AACtE,YAAM,cAAc;AAAA,IACtB;AACA,QAAI,MAAM,SAAS,sBAAsB,MAAM,YAAY,MAAM,aAAa,YAAY,MAAM,sBAAsB,WAAW;AAC/H,YAAM,OAAO;AAAA,IACf;AACA,QAAI,CAAC,MAAM,QAAQ,MAAM,YAAY;AACnC,YAAM,OAAO;AAAA,IACf;AACA,QAAI,MAAM,MAAM;AACd,WAAK,OAAO,eAAe,KAAK;AAAA,IAClC;AACA,QAAI,OAAO,KAAK,KAAK,CAAC,YAAY,MAAM,YAAY,KAAK,YAAY,cAAc,KAAK,CAAC,KAAK,CAAC,cAAc,KAAK,GAAG;AACnH,uBAAiB,OAAO,MAAM,YAAY;AAAA,IAC5C;AACA,UAAM,WAAW,MAAM,YAAY,CAAC;AAAA,EACtC;AAAA,EACA,0BAA0B,OAAO;AAC/B,UAAM,uBAAuB,MAAM;AACjC,UAAI,eAAe,KAAK,OAAO,oBAAoB,KAAK;AACxD,YAAM,oBAAoB,MAAM,gBAAgB,MAAM,EAAE,EAAE,CAAC;AAC3D,UAAI,6BAA6B,kBAAgB,mBAAmB,kBAAkB,cAAc,eAAe;AACjH,uBAAe;AAAA,MACjB;AACA,aAAO,cAAc;AAAA,IACvB;AACA,QAAI,CAAC,MAAM,gBAAgB;AACzB,uBAAiB,OAAO,kBAAkB,IAAI,MAAM,CAAC,GAAG;AAAA,QACtD,KAAK,CAAC,GAAG,SAAS,qBAAqB,IAAI,IAAI;AAAA,QAC/C,KAAK,CAAC,GAAG,MAAM,UAAU,qBAAqB,EAAE,IAAI,IAAI;AAAA,MAC1D,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,MAAM;AAAA,EACf;AACF;AACA,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,OAAO;AACjB,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,OAAO;AAAA,IACd;AACA,QAAI,MAAM,QAAQ;AAChB,aAAO,eAAe,OAAO,QAAQ;AAAA,QACnC,KAAK,MAAM,MAAM,OAAO;AAAA,QACxB,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,MAAM,eAAe,YAAY,KAAK,CAAC,OAAO,KAAK,GAAG;AACxD,uBAAiB,OAAO,eAAe,MAAM,IAAI;AAAA,IACnD,OAAO;AACL,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,KAAK,SAAS,OAAO;AACvB;AAAA,IACF;AACA,SAAK,OAAO;AACZ,UAAM,eAAe,KAAK,cAAc,KAAK;AAC7C,QAAI,gBAAgB,MAAM,QAAQ;AAChC,UAAI,SAAS,MAAM;AACnB,aAAO,QAAQ;AACb,YAAI,OAAO,MAAM,KAAK,CAAC,OAAO,QAAQ;AACpC,yBAAe,OAAO,aAAa,IAAI;AAAA,QACzC;AACA,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,UAAU,YAAY,KAAK;AAC/B,QAAI,MAAM,YAAY;AACpB;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,YAAM,iBAAiB;AAAA,QACrB,UAAU,MAAM,aAAa;AAAA,MAC/B;AACA,UAAI,MAAM,YAAY;AACpB,kBAAU,IAAI,iBAAiB,CAAC,GAAG,cAAc;AAAA,MACnD,OAAO;AACL,cAAM,QAAQ,OAAO,KAAK,IAAI,cAAc,KAAK,IAAI,MAAM;AAC3D,kBAAU,IAAI,mBAAmB;AAAA,UAC/B;AAAA,UACA,UAAU,CAAC,CAAC,MAAM,MAAM;AAAA,QAC1B,GAAG,iCACE,iBADF;AAAA,UAED,uBAAuB;AAAA,QACzB,EAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,mBAAmB,aAAa;AAClC,cAAM,QAAQ,OAAO,KAAK,IAAI,cAAc,KAAK,IAAI,MAAM;AAC3D,gBAAQ,eAAe;AAAA,MACzB;AAAA,IACF;AACA,oBAAgB,OAAO,OAAO;AAAA,EAChC;AAAA,EACA,cAAc,OAAO,WAAW,OAAO;AACrC,QAAI,aAAa,SAAS,OAAO,KAAK,KAAK,MAAM,OAAO,UAAU;AAChE,iBAAW;AAAA,IACb;AACA,QAAI,eAAe;AACnB,UAAM,YAAY,QAAQ,OAAK,KAAK,KAAK,cAAc,GAAG,QAAQ,MAAM,eAAe,KAAK;AAC5F,QAAI,OAAO,KAAK,KAAK,CAAC,MAAM,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC,MAAM,YAAY;AACzE,YAAM;AAAA,QACJ,aAAa;AAAA,MACf,IAAI;AACJ,UAAI,GAAG;AACL,YAAI,OAAO,KAAK,KAAK,aAAa,aAAa;AAC7C,cAAI,YAAY,EAAE,SAAS;AACzB,cAAE,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,UAAU;AAAA,YACZ,CAAC;AACD,2BAAe;AAAA,UACjB;AACA,cAAI,CAAC,YAAY,EAAE,UAAU;AAC3B,cAAE,OAAO;AAAA,cACP,WAAW;AAAA,cACX,UAAU;AAAA,YACZ,CAAC;AACD,2BAAe;AAAA,UACjB;AAAA,QACF;AACA,YAAI,SAAS,EAAE,aAAa,KAAK,cAAc,OAAO,aAAa,GAAG;AACpE,YAAE,cAAc,MAAM;AACpB,kBAAM,IAAI,WAAW,QAAQ,KAAK,gBAAgB,OAAO,aAAa,CAAC;AACvE,mBAAO,IAAI,EAAE,CAAC,IAAI;AAAA,UACpB,CAAC;AACD,yBAAe;AAAA,QACjB;AACA,YAAI,SAAS,EAAE,kBAAkB,KAAK,cAAc,OAAO,kBAAkB,GAAG;AAC9E,YAAE,mBAAmB,MAAM;AACzB,kBAAM,IAAI,WAAW,aAAa,KAAK,gBAAgB,OAAO,kBAAkB,CAAC;AACjF,mBAAO,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI;AAAA,UAC3B,CAAC;AACD,yBAAe;AAAA,QACjB;AACA,YAAI,cAAc;AAChB,yBAAe,GAAG,IAAI;AAEtB,cAAI,SAAS,EAAE;AACf,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK,EAAE,QAAQ,KAAK;AACjD,gBAAI,QAAQ;AACV,6BAAe,QAAQ,IAAI;AAC3B,uBAAS,OAAO;AAAA,YAClB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,OAAO,MAAM;AACzB,UAAM,IAAI,MAAM;AAChB,QAAI,GAAG,SAAS,SAAS,KAAK,EAAE,QAAQ,KAAK,OAAK,EAAE,IAAI,EAAE,SAAS,CAAC,GAAG;AACrE,aAAO;AAAA,IACT,WAAW,MAAM,IAAI,EAAE,SAAS,GAAG;AACjC,aAAO;AAAA,IACT;AACA,WAAO,MAAM,YAAY,KAAK,OAAK,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,KAAK,cAAc,GAAG,IAAI,CAAC;AAAA,EAC/F;AAAA,EACA,gBAAgB,OAAO,MAAM;AAC3B,UAAM,aAAa,CAAC;AACpB,UAAM,IAAI,MAAM;AAChB,QAAI,GAAG,SAAS,SAAS,GAAG;AAC1B,QAAE,QAAQ,OAAO,OAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,OAAK,WAAW,KAAK,GAAG,EAAE,IAAI,CAAC,CAAC;AAAA,IAC1E,WAAW,MAAM,IAAI,GAAG;AACtB,iBAAW,KAAK,GAAG,MAAM,IAAI,CAAC;AAAA,IAChC;AACA,QAAI,MAAM,YAAY;AACpB,YAAM,WAAW,OAAO,OAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,OAAK,WAAW,KAAK,GAAG,KAAK,gBAAgB,GAAG,IAAI,CAAC,CAAC;AAAA,IAC1H;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,oBAAoB,OAAO,YAAY;AAC5C,SAAK,oBAAoB,OAAO,iBAAiB;AAAA,EACnD;AAAA,EACA,oBAAoB,OAAO,MAAM;AAC/B,UAAM,aAAa,CAAC;AACpB,QAAI,SAAS,gBAAgB,EAAE,MAAM,eAAe,YAAY,KAAK,CAAC,OAAO,KAAK,IAAI;AACpF,iBAAW,KAAK,KAAK,6BAA6B,KAAK,CAAC;AAAA,IAC1D;AACA,QAAI,MAAM,IAAI,GAAG;AACf,iBAAW,iBAAiB,OAAO,KAAK,MAAM,IAAI,CAAC,GAAG;AACpD,0BAAkB,eAAe,WAAW,KAAK,GAAG,MAAM,IAAI,EAAE,WAAW,IAAI,OAAK,KAAK,kBAAkB,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,KAAK,KAAK,kBAAkB,OAAO,MAAM,IAAI,EAAE,aAAa,GAAG,aAAa,CAAC;AAAA,MACnN;AAAA,IACF;AACA,qBAAiB,OAAO,MAAM,MAAM,UAAU;AAAA,EAChD;AAAA,EACA,6BAA6B,OAAO;AAClC,QAAI,aAAa,CAAC;AAClB,sBAAkB,QAAQ,SAAO,QAAQ,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC;AAAA,MAC/D;AAAA,MACA;AAAA,IACF,MAAM;AACJ,mBAAa,WAAW,OAAO,OAAK,MAAM,GAAG;AAC7C,UAAI,QAAQ,cAAc,gBAAgB,QAAQ,OAAO,iBAAiB,WAAW;AACnF,gBAAQ,KAAK,4CAA4C,OAAO,YAAY,gCAAgC,MAAM,GAAG,IAAI;AAAA,MAC3H;AACA,UAAI,gBAAgB,QAAQ,iBAAiB,OAAO;AAClD,mBAAW,KAAK,GAAG;AAAA,MACrB;AACA,UAAI,CAAC,eAAe,MAAM,aAAa;AACrC,uBAAe,MAAM,WAAW;AAAA,MAClC;AAAA,IACF,CAAC,CAAC;AACF,WAAO,aAAW;AAChB,UAAI,WAAW,WAAW,GAAG;AAC3B,eAAO;AAAA,MACT;AACA,aAAO,WAAW,QAAQ,WAAW,IAAI,SAAO,MAAM;AACpD,cAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,gBAAQ,KAAK;AAAA,UACX,KAAK;AACH,mBAAO,WAAW,SAAS,OAAO;AAAA,UACpC,KAAK;AACH,mBAAO,WAAW,QAAQ,KAAK,EAAE,OAAO;AAAA,UAC1C,KAAK;AACH,kBAAM,kBAAkB,WAAW,UAAU,KAAK,EAAE,OAAO;AAC3D,kBAAM,eAAe,KAAK,OAAO,oBAAoB,WAAW,KAAK,MAAM,YAAY,UAAU,YAAY,cAAc;AAC3H,mBAAO,kBAAkB;AAAA,cACvB,CAAC,YAAY,GAAG,gBAAgB;AAAA,YAClC,IAAI;AAAA,UACN,KAAK;AACH,kBAAM,kBAAkB,WAAW,UAAU,KAAK,EAAE,OAAO;AAC3D,kBAAM,eAAe,KAAK,OAAO,oBAAoB,WAAW,KAAK,MAAM,YAAY,UAAU,YAAY,cAAc;AAC3H,mBAAO,kBAAkB;AAAA,cACvB,CAAC,YAAY,GAAG,gBAAgB;AAAA,YAClC,IAAI;AAAA,UACN,KAAK;AACH,mBAAO,WAAW,IAAI,KAAK,EAAE,OAAO;AAAA,UACtC,KAAK;AACH,mBAAO,WAAW,IAAI,KAAK,EAAE,OAAO;AAAA,UACtC;AACE,mBAAO;AAAA,QACX;AAAA,MACF,CAAC,CAAC,EAAE,OAAO;AAAA,IACb;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO,WAAW,eAAe;AACjD,QAAI;AACJ,QAAI,OAAO,cAAc,UAAU;AACjC,wBAAkB,MAAM,KAAK,OAAO,aAAa,SAAS,CAAC;AAAA,IAC7D;AACA,QAAI,OAAO,cAAc,YAAY,UAAU,MAAM;AACnD,wBAAkB,MAAM,KAAK,OAAO,aAAa,UAAU,IAAI,CAAC;AAChE,UAAI,UAAU,SAAS;AACrB,wBAAgB,UAAU,UAAU;AAAA,MACtC;AAAA,IACF;AACA,QAAI,OAAO,cAAc,YAAY,UAAU,YAAY;AACzD,YAGI,gBAFF;AAAA;AAAA,MAhkCR,IAkkCU,IADC,oBACD,IADC;AAAA,QADH;AAAA;AAGF,wBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,SAAS,OAAO,KAAK,OAAO,EAAE,SAAS,IAAI,UAAU;AAAA,MACvD;AAAA,IACF;AACA,QAAI,OAAO,cAAc,YAAY;AACnC,wBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,YAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,aAAW;AAChB,YAAM,SAAS,gBAAgB,WAAW,SAAS,OAAO,gBAAgB,OAAO;AACjF,UAAI,UAAU,MAAM,GAAG;AACrB,eAAO,OAAO,KAAK,OAAK,KAAK,aAAa,OAAO,gBAAgB,CAAC,CAAC,IAAI,GAAG,eAAe,CAAC;AAAA,MAC5F;AACA,UAAI,aAAa,MAAM,GAAG;AACxB,eAAO,OAAO,KAAK,IAAI,OAAK,KAAK,aAAa,OAAO,gBAAgB,CAAC,CAAC,IAAI,GAAG,eAAe,CAAC,CAAC;AAAA,MACjG;AACA,aAAO,KAAK,aAAa,OAAO,gBAAgB,CAAC,CAAC,SAAS,QAAQ,eAAe;AAAA,IACpF;AAAA,EACF;AAAA,EACA,aAAa,OAAO,QAAQ;AAAA,IAC1B;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,OAAO,WAAW,WAAW;AAC/B,eAAS,SAAS,OAAO;AAAA,QACvB,CAAC,IAAI,GAAG,UAAU,UAAU;AAAA,MAC9B;AAAA,IACF;AACA,UAAM,OAAO,MAAM;AACnB,UAAM,kBAAkB,IAAI,IAAI;AAChC,QAAI,SAAS,MAAM,GAAG;AACpB,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAAC,UAAQ;AAClC,cAAM,YAAY,OAAOA,KAAI,EAAE,YAAY,OAAOA,KAAI,EAAE,YAAY,SAAS;AAC7E,cAAM,YAAY,YAAY,MAAM,YAAY,IAAI,SAAS,IAAI;AACjE,YAAI,WAAW;AACb,gBAGI,YAAOA,KAAI,GAFb;AAAA,uBAAW;AAAA,UA3mCvB,IA6mCc,IADC,iBACD,IADC;AAAA,YADH;AAAA;AAGF,oBAAU,UAAU,iCACd,UAAU,UAAU,CAAC,IADP;AAAA,YAElB,CAACA,KAAI,GAAG;AAAA,UACV,EAAC;AACD,WAAC,KAAK,mBAAmB,iBAAiB,MAAM,mBAAmB,CAAC,CAAC;AACrE,eAAK,gBAAgBA,KAAI,IAAI,MAAM;AACjC,kBAGIC,MAAA,UAAU,UAAU,CAAC,GAFtB;AAAA,cArnCf,CAqnCeD,QAAO;AAAA,YArnCtB,IAunCgBC,KADC,wBACDA,KADC;AAAA,cADF,UAAAD;AAAA;AAGH,sBAAU,UAAU,OAAO,KAAK,WAAW,EAAE,WAAW,IAAI,OAAO,WAAW;AAAA,UAChF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAN,MAAM,WAAU;AAAA,EACd,cAAc;AACZ,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB,UAAU;AAC5B,UAAM,IAAI,KAAK;AACf,MAAE,eAAe,SAAS,IAAI,OAAK,EAAE,QAAQ,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAAD,OAAKA,GAAE,gBAAgB,KAAK,MAAM,WAAW;AAAA,EACzH;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,MAAM,SAAS,CAAC;AAAA,EAC9B;AAAA;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,QAAQ,UAAU,IAAI;AAAA,EACpC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS,aAAa,CAAC;AAAA,EACrC;AAwBF;AAtBI,WAAK,OAAO,SAAS,kBAAkB,mBAAmB;AACxD,SAAO,KAAK,qBAAqB,YAAW;AAC9C;AAGA,WAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,WAAW,CAAC;AAAA,IAC7B;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AAAA,IACrE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AACd,CAAC;AA5DL,IAAM,YAAN;AAAA,CA+DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,sBAAN,MAAM,4BAA2B,UAAU;AAAA,EACzC,IAAI,WAAW;AACb,QAAI,KAAK,SAAS,KAAK,MAAM,aAAa,KAAK,UAAU,UAAU;AACjE,WAAK,YAAY;AAAA,QACf,UAAU,KAAK,MAAM;AAAA,QACrB,SAAS,KAAK,MAAM,WAAW,KAAK,UAAU,wBAAwB,KAAK,MAAM,QAAQ,IAAI,KAAK,MAAM;AAAA,MAC1G;AAAA,IACF;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,YAAY,WAAW;AACrB,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,YAAY,CAAC;AAAA,EACpB;AA2BF;AAzBI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,YAAY,CAAC;AAC5F;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,EAC/B,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC;AAAA,EACzB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,GAAG,OAAO,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,aAAa,IAAI,UAAa,cAAc;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAvCL,IAAM,qBAAN;AAAA,CA0CC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,QAAQ,CAAC;AACd,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,CAAC;AACjB,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS;AAAA,MACZ,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,0BAA0B;AAAA,MAC1B,UAAU,OAAO;AACf,eAAO,MAAM,aAAa,YAAY,MAAM,aAAa,WAAW,MAAM,QAAQ,YAAY,aAAa,CAAC,CAAC,MAAM,MAAM,YAAY;AAAA,MACvI;AAAA,IACF;AACA,SAAK,aAAa,CAAC;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,uBAAuB,CAAC;AAC7B,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA,EACA,UAAU,QAAQ;AAChB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO,QAAQ,OAAK,KAAK,UAAU,CAAC,CAAC;AACrC;AAAA,IACF;AACA,QAAI,OAAO,OAAO;AAChB,aAAO,MAAM,QAAQ,UAAQ,KAAK,QAAQ,IAAI,CAAC;AAAA,IACjD;AACA,QAAI,OAAO,YAAY;AACrB,aAAO,WAAW,QAAQ,eAAa,KAAK,aAAa,SAAS,CAAC;AAAA,IACrE;AACA,QAAI,OAAO,UAAU;AACnB,aAAO,SAAS,QAAQ,aAAW,KAAK,WAAW,OAAO,CAAC;AAAA,IAC7D;AACA,QAAI,OAAO,oBAAoB;AAC7B,aAAO,mBAAmB,QAAQ,gBAAc,KAAK,oBAAoB,WAAW,MAAM,WAAW,OAAO,CAAC;AAAA,IAC/G;AACA,QAAI,OAAO,YAAY;AACrB,WAAK,oBAAoB,OAAO,UAAU;AAAA,IAC5C;AACA,QAAI,OAAO,QAAQ;AACjB,WAAK,SAAS,kCACT,KAAK,SACL,OAAO;AAAA,IAEd;AACA,QAAI,OAAO,SAAS;AAClB,WAAK,UAAU,kCACV,KAAK,UACL,OAAO,QAAQ,OAAO,CAAC,KAAK,SAAU,iCACpC,MADoC;AAAA,QAEvC,CAAC,KAAK,IAAI,GAAG,KAAK;AAAA,MACpB,IAAI,CAAC,CAAC;AAAA,IAEV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAS;AACf,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,QAAQ,YAAU,KAAK,QAAQ,MAAM,CAAC;AAAA,IAChD,OAAO;AACL,UAAI,CAAC,KAAK,MAAM,QAAQ,IAAI,GAAG;AAC7B,aAAK,MAAM,QAAQ,IAAI,IAAI;AAAA,UACzB,MAAM,QAAQ;AAAA,QAChB;AAAA,MACF;AACA,OAAC,aAAa,WAAW,kBAAkB,UAAU,EAAE,QAAQ,UAAQ;AACrE,YAAI,QAAQ,eAAe,IAAI,GAAG;AAChC,eAAK,MAAM,QAAQ,IAAI,EAAE,IAAI,IAAI,QAAQ,IAAI;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,MAAM,kBAAkB,OAAO;AACrC,QAAI,gBAAgB,MAAM;AACxB,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,KAAK,UAAU,YAAY;AAAA,MACnC;AAAA,IACF;AACA,QAAI,CAAC,KAAK,MAAM,IAAI,GAAG;AACrB,UAAI,iBAAiB;AACnB,cAAM,IAAI,MAAM,4BAA4B,IAAI,iGAAiG;AAAA,MACnJ;AACA,aAAO;AAAA,IACT;AACA,SAAK,kBAAkB,IAAI;AAC3B,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB;AAAA;AAAA,EAEA,eAAe,QAAQ,CAAC,GAAG;AACzB,UAAM,OAAO,KAAK,QAAQ,MAAM,IAAI;AACpC,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,uBAAiB,OAAO,KAAK,cAAc;AAAA,IAC7C;AACA,UAAM,iBAAiB,KAAK,WAAW,KAAK,QAAQ,KAAK,OAAO,EAAE;AAClE,QAAI,gBAAgB;AAClB,uBAAiB,OAAO,cAAc;AAAA,IACxC;AACA,QAAI,OAAO,cAAc;AACvB,YAAM,aAAa,QAAQ,YAAU;AACnC,cAAM,iBAAiB,KAAK,QAAQ,MAAM,EAAE;AAC5C,YAAI,gBAAgB;AAClB,2BAAiB,OAAO,cAAc;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,eAAe,KAAK,oBAAoB,KAAK;AACnD,QAAI,cAAc,UAAU,gBAAgB;AAC1C,uBAAiB,OAAO,aAAa,SAAS,cAAc;AAAA,IAC9D;AACA,QAAI,CAAC,MAAM,YAAY,KAAK,UAAU;AACpC,YAAM,WAAW,CAAC,GAAG,KAAK,QAAQ;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,QAAQ,CAAC,GAAG;AAC9B,UAAM,OAAO,KAAK,QAAQ,MAAM,IAAI;AACpC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,cAAc,KAAK,IAAI,GAAG;AAClC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM;AACV,UAAI,CAAC,qBAAqB,CAAC,WAAW;AACpC,eAAO;AAAA,MACT;AACA,YAAM,eAAe,kBAAkB,gBAAgB,KAAK,WAAW;AAAA,QACrE,UAAU;AAAA,MACZ,CAAC;AACD,WAAK,cAAc,KAAK,IAAI,IAAI;AAChC,UAAI;AACF,qBAAa,QAAQ;AAAA,MACvB,SAAS,GAAG;AACV,gBAAQ,MAAM,iEAAiE,MAAM,IAAI,KAAK,CAAC;AAAA,MACjG;AAAA,IACF;AACA,WAAO,KAAK,cAAc,KAAK,IAAI;AAAA,EACrC;AAAA;AAAA,EAEA,YAAY;AACV,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,SAAS,QAAQ,IAAI,IAAI;AAC9B,QAAI,QAAQ,OAAO;AACjB,cAAQ,MAAM,QAAQ,UAAQ;AAC5B,aAAK,eAAe,MAAM,QAAQ,IAAI;AAAA,MACxC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,MAAM;AACf,QAAI,gBAAgB,MAAM;AACxB,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,KAAK,UAAU,YAAY;AAAA,MACnC;AAAA,IACF;AACA,QAAI,CAAC,KAAK,SAAS,IAAI,GAAG;AACxB,YAAM,IAAI,MAAM,+BAA+B,IAAI,iGAAiG;AAAA,IACtJ;AACA,WAAO,KAAK,SAAS,IAAI;AAAA,EAC3B;AAAA;AAAA,EAEA,eAAe,MAAM,MAAM;AACzB,QAAI,CAAC,KAAK,MAAM,IAAI,GAAG;AACrB,WAAK,MAAM,IAAI,IAAI,CAAC;AAAA,IACtB;AACA,QAAI,CAAC,KAAK,MAAM,IAAI,EAAE,UAAU;AAC9B,WAAK,MAAM,IAAI,EAAE,WAAW,CAAC;AAAA,IAC/B;AACA,QAAI,KAAK,MAAM,IAAI,EAAE,SAAS,QAAQ,IAAI,MAAM,IAAI;AAClD,WAAK,MAAM,IAAI,EAAE,SAAS,KAAK,IAAI;AAAA,IACrC;AAAA,EACF;AAAA,EACA,aAAa,SAAS;AACpB,SAAK,WAAW,QAAQ,IAAI,IAAI;AAAA,EAClC;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,CAAC,KAAK,WAAW,IAAI,GAAG;AAC1B,YAAM,IAAI,MAAM,iCAAiC,IAAI,iGAAiG;AAAA,IACxJ;AACA,WAAO,KAAK,WAAW,IAAI;AAAA,EAC7B;AAAA,EACA,oBAAoB,MAAM,SAAS;AACjC,SAAK,SAAS,IAAI,IAAI;AACtB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AACA,UAAI,WAAW,IAAI,GAAG;AACpB,gBAAQ,KAAK,wDAAwD,IAAI,oCAAoC,WAAW,IAAI,CAAC,YAAY;AACzI,aAAK,SAAS,WAAW,IAAI,CAAC,IAAI;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB,MAAM;AACxB,WAAO,KAAK,SAAS,IAAI;AAAA,EAC3B;AAAA,EACA,oBAAoB,kBAAkB;AAEpC,qBAAiB,QAAQ,qBAAmB;AAC1C,YAAM,WAAW,gBAAgB,YAAY;AAC7C,WAAK,qBAAqB,QAAQ,IAAI,iCACjC,KAAK,qBAAqB,QAAQ,IADD;AAAA,QAEpC,CAAC,gBAAgB,IAAI,GAAG,gBAAgB;AAAA,MAC1C;AAAA,IACF,CAAC;AAED,SAAK,aAAa,OAAO,KAAK,KAAK,oBAAoB,EAAE,IAAI,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,SAAU,kCAC7G,MACA,KAAK,qBAAqB,IAAI,IAC/B,CAAC,CAAC;AAAA,EACR;AAAA,EACA,kBAAkB,MAAM;AACtB,QAAI,CAAC,KAAK,MAAM,IAAI,EAAE,SAAS;AAC7B;AAAA,IACF;AACA,UAAM,eAAe,KAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,OAAO;AAC1D,QAAI,CAAC,KAAK,MAAM,IAAI,EAAE,WAAW;AAC/B,WAAK,MAAM,IAAI,EAAE,YAAY,aAAa;AAAA,IAC5C;AACA,QAAI,CAAC,KAAK,MAAM,IAAI,EAAE,UAAU;AAC9B,WAAK,MAAM,IAAI,EAAE,WAAW,aAAa;AAAA,IAC3C;AAAA,EACF;AAaF;AAXI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AAGA,cAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,cAAa;AAAA,EACtB,YAAY;AACd,CAAC;AAxPL,IAAM,eAAN;AAAA,CA2PC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,KAAK,QAAQ;AAAA,EAC3B;AAiBF;AAfI,gBAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,WAAW,CAAC;AACvF;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,EACtC,QAAQ;AAAA,IACN,MAAM,CAAC,GAAG,kBAAkB,MAAM;AAAA,EACpC;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AACpC,CAAC;AArBL,IAAM,iBAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,wBAAN,MAAM,sBAAqB;AAY3B;AAVI,sBAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,uBAAsB;AACzD;AAGA,sBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,sBAAqB;AAChC,CAAC;AAVL,IAAM,uBAAN;AAAA,CAaC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,eAAN,MAAM,aAAY;AAAA,EAChB,IAAI,eAAe;AACjB,WAAO,KAAK,OAAO,OAAO,2BAA2B,KAAK,mBAAmB,KAAK;AAAA,EACpF;AAAA,EACA,IAAI,aAAa;AACf,QAAI,KAAK,OAAO,OAAO,0BAA0B;AAC/C,aAAO,KAAK;AAAA,IACd;AACA,QAAI,KAAK,gBAAgB,CAAC,aAAa,gBAAc;AACnD,aAAO,KAAK,cAAc,CAAC,EAAE;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,QAAQ,UAAU,aAAa,kBAAkB,MAAM;AACjE,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AACZ,SAAK,gBAAgB,CAAC;AACtB,SAAK,gBAAgB,CAAC;AACtB,SAAK,iBAAiB,CAAC;AACvB,SAAK,mBAAmB;AACxB,SAAK,0BAA0B,MAAM;AAAA,IAAC;AAAA,EACxC;AAAA,EACA,qBAAqB;AACnB,SAAK,YAAY,kBAAkB;AAAA,EACrC;AAAA,EACA,kBAAkB;AAChB,SAAK,YAAY,eAAe;AAAA,EAClC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,oBAAoB,KAAK,SAAS,KAAK,MAAM,SAAS;AAC7D,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,YAAY,aAAa,OAAO;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,KAAK,KAAK;AACzB,SAAK,cAAc,QAAQ,kBAAgB,aAAa,YAAY,CAAC;AACrE,SAAK,eAAe,QAAQ,iBAAe,YAAY,CAAC;AACxD,SAAK,wBAAwB;AAC7B,SAAK,YAAY,WAAW;AAAA,EAC9B;AAAA,EACA,YAAY,cAAc,GAAG,WAAW,CAAC,GAAG;AAC1C,QAAI,KAAK,iBAAiB,cAAc;AACtC,WAAK,UAAU,KAAK,KAAK;AACzB,WAAK,aAAa,MAAM;AACxB,iBAAW,KAAK,OAAO;AAAA,IACzB;AACA,QAAI,UAAU,SAAS,GAAG;AACxB,YAAM,CAAC,SAAS,GAAG,GAAG,IAAI;AAC1B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK,OAAO,WAAW,OAAO;AAClC,YAAM,MAAM,aAAa,gBAAgB,SAAS;AAClD,WAAK,mBAAmB,KAAK,CAAC;AAC9B,cAAQ,IAAI,UAAU,CAAC,gBAAgB,GAAG,CAAC;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACF,MAAM;AACJ,YAAI,cAAc;AAChB,cAAI,iBAAiB,cAAc,gBAAgB,aAAa,aAAa;AAC3E;AAAA,UACF;AACA,gBAAM,UAAU,gBAAgB,cAAc,OAAO,IAAI;AACzD,cAAI,WAAW,CAAC,QAAQ,WAAW;AACjC,yBAAa,OAAO,OAAO;AAAA,UAC7B,OAAO;AACL,iBAAK,YAAY,cAAc,GAAG,GAAG;AAAA,UACvC;AACA,WAAC,eAAe,IAAI,kBAAkB,cAAc;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH,WAAW,GAAG,MAAM;AAClB,YAAM,aAAa,KAAK,MAAM,WAAW,KAAK,CAAAG,SAAOA,KAAI,SAAS,EAAE,IAAI;AACxE,UAAI;AACJ,UAAI,YAAY;AACd,cAAM,aAAa,mBAAmB,WAAW,KAAK;AAAA,UACpD,WAAW;AAAA,QACb,CAAC;AAAA,MACH,OAAO;AACL,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,KAAK,OAAO,QAAQ,EAAE,MAAM,IAAI;AACpC,cAAM,aAAa,gBAAgB,SAAS;AAAA,MAC9C;AACA,WAAK,mBAAmB,KAAK,CAAC;AAAA,IAChC;AAAA,EACF;AAAA,EACA,YAAY,MAAM,SAAS;AACzB,QAAI,SAAS,YAAY,SAAS,eAAe,QAAQ,SAAS,CAAC,QAAQ,MAAM,aAAa;AAC5F,WAAK,wBAAwB;AAC7B,WAAK,0BAA0B,KAAK,aAAa,KAAK,KAAK;AAAA,IAC7D;AACA,QAAI,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7B,UAAI,CAAC,WAAW,QAAQ,OAAO;AAC7B,cAAM,IAAI,KAAK,MAAM,MAAM,IAAI,EAAE,KAAK,KAAK;AAC3C,YAAI,aAAa,CAAC,KAAK,CAAC,UAAU,oBAAoB,eAAe,EAAE,QAAQ,IAAI,MAAM,IAAI;AAC3F,gBAAM,MAAM,EAAE,UAAU;AACxB,eAAK,eAAe,KAAK,MAAM,IAAI,YAAY,CAAC;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,eAAe,QAAQ,OAAO;AACzC,WAAK,UAAU,QAAQ,MAAM,aAAa;AAC1C,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,mBAAmB,KAAK,OAAO;AAC7B,SAAK,cAAc,KAAK,GAAG;AAC3B,UAAM,eAAe,KAAK,GAAG;AAC7B,QAAI,eAAe,gBAAc;AAC/B,aAAO,OAAO,IAAI,UAAU;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,MAAM,SAAS;AACvB,WAAK,mBAAmB;AACxB;AAAA,IACF;AACA,SAAK,mBAAmB;AACxB,SAAK,cAAc,QAAQ,kBAAgB,aAAa,YAAY,CAAC;AACrE,SAAK,gBAAgB,CAAC,QAAQ,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC;AAAA,MACnD;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,eAAe,KAAK;AAC1B,UAAI,KAAK,OAAO,OAAO,eAAe,OAAO;AAC3C,uBAAe,KAAK,YAAY,cAAc,KAAK,KAAK;AACxD,YAAI,CAAC,eAAe,eAAe,cAAc;AAC/C,eAAK,cAAc,KAAK,SAAS,SAAS,KAAK,WAAW,eAAe,WAAW,eAAe,SAAS,EAAE;AAAA,QAChH;AAAA,MACF,OAAO;AACL,YAAI,cAAc;AAChB,uBAAa,MAAM;AACnB,cAAI,KAAK,MAAM,WAAW;AACxB,iBAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,OAAO;AAAA,UACtE;AAAA,QACF,OAAO;AACL,eAAK,YAAY,cAAc,KAAK,KAAK;AACzC,cAAI,KAAK,MAAM,WAAW;AACxB,iBAAK,SAAS,aAAa,KAAK,WAAW,eAAe,SAAS,KAAK,MAAM,SAAS;AAAA,UACzF;AAAA,QACF;AAAA,MACF;AACA,OAAC,eAAe,KAAK,MAAM,QAAQ,cAAc,KAAK,KAAK;AAAA,IAC7D,CAAC,GAAG,QAAQ,KAAK,OAAO,CAAC,WAAW,GAAG,CAAC;AAAA,MACtC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,CAAC,eAAe,eAAe,kBAAkB,CAAC,KAAK,OAAO,OAAO,cAAc,KAAK,MAAM,SAAS,OAAO;AACjH,aAAK,cAAc,KAAK,SAAS,aAAa,KAAK,WAAW,eAAe,SAAS,YAAY;AAAA,MACpG;AAAA,IACF,CAAC,CAAC;AACF,QAAI,CAAC,iBAAiB,GAAG;AACvB,OAAC,WAAW,YAAY,QAAQ,EAAE,QAAQ,UAAQ,KAAK,cAAc,KAAK,QAAQ,KAAK,OAAO,CAAC,eAAe,IAAI,GAAG,CAAC;AAAA,QACpH;AAAA,MACF,MAAM,CAAC,eAAe,kBAAkB,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,IACvD,WAAW,KAAK,MAAM,aAAa;AACjC,YAAM,SAAS,KAAK,MAAM,YAAY,OAAO,UAAU,MAAM,kBAAkB,KAAK,KAAK,CAAC;AAC1F,WAAK,cAAc,KAAK,MAAM;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,QAAI,OAAO;AACT,UAAI,MAAM,cAAc;AACtB,cAAM,eAAe,CAAC;AAAA,MACxB,OAAO;AACL,yBAAiB,KAAK,OAAO,gBAAgB,CAAC,CAAC;AAAA,MACjD;AACA,UAAI,MAAM,gBAAgB;AACxB,cAAM,iBAAiB,MAAM,eAAe,OAAO,SAAO,KAAK,cAAc,QAAQ,GAAG,MAAM,EAAE;AAAA,MAClG,OAAO;AACL,yBAAiB,KAAK,OAAO,kBAAkB,CAAC,CAAC;AAAA,MACnD;AAAA,IACF;AACA,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,OAAO;AACV,aAAO,MAAM;AAAA,MAAC;AAAA,IAChB;AACA,UAAM,gBAAgB,YAAY,OAAO,CAAC,OAAO,GAAG,MAAM,MAAM,QAAQ,cAAc,KAAK,CAAC;AAC5F,UAAM,aAAa,CAAC,MAAM;AACxB,oBAAc;AAAA,IAChB,CAAC;AACD,eAAW,OAAO,OAAO,KAAK,MAAM,gBAAgB,CAAC,CAAC,GAAG;AACvD,YAAM,qBAAqB,QAAQ,OAAO,CAAC,gBAAgB,GAAG,GAAG,CAAC;AAAA,QAChE;AAAA,QACA;AAAA,MACF,MAAM;AACJ,YAAI,eAAe,cAAc;AAC/B,wBAAc,aAAa,YAAY;AACvC,wBAAc,eAAe;AAAA,QAC/B;AACA,YAAI,aAAa,aAAa,MAAM,GAAG;AACrC,uBAAa,eAAe,aAAa,OAAO,UAAU;AAAA,QAC5D;AAAA,MACF,CAAC;AACD,iBAAW,KAAK,MAAM;AACpB,YAAI,MAAM,aAAa,GAAG,GAAG,cAAc;AACzC,gBAAM,aAAa,GAAG,EAAE,aAAa,YAAY;AAAA,QACnD;AACA,2BAAmB,YAAY;AAAA,MACjC,CAAC;AAAA,IACH;AACA,eAAW,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,UAAU,GAAG,CAAC,qBAAqB,GAAG,CAAC,cAAc,MAAM,CAAC,GAAG;AAC7F,YAAM,gBAAgB,QAAQ,OAAO,MAAM,CAAC;AAAA,QAC1C;AAAA,MACF,MAAM,CAAC,eAAe,MAAM,QAAQ,cAAc,KAAK,CAAC;AACxD,iBAAW,KAAK,MAAM,cAAc,YAAY,CAAC;AAAA,IACnD;AACA,QAAI,MAAM,eAAe,CAAC,MAAM,YAAY;AAC1C,YAAM,UAAU,MAAM;AACtB,UAAI,eAAe,QAAQ,aAAa,KAAK,IAAI,WAAS;AACxD,cAAM,SAAS,IAAI,cAAY,QAAQ,SAAS,OAAO,KAAK,CAAC;AAC7D,YAAI,CAAC,OAAO,GAAG,OAAO,MAAM,YAAY,KAAK,GAAG;AAC9C,gBAAM,YAAY,SAAS,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT,CAAC,GAAG,qBAAqB,CAAC,GAAG,MAAM;AACjC,YAAI,MAAM,KAAK,MAAM,QAAQ,CAAC,KAAK,SAAS,CAAC,GAAG;AAC9C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,CAAC;AACF,UAAI,QAAQ,UAAU,cAAc,KAAK,GAAG;AAC1C,uBAAe,aAAa,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,MAC3D;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM;AACV,WAAK,CAAC,YAAY,aAAa,aAAa,UAAU,UAAU,GAAG;AACjE,uBAAe,aAAa,KAAK,aAAa,SAAS,OAAO,CAAC;AAAA,MACjE;AACA,YAAM,MAAM,aAAa,UAAU,WAAS;AAE1C,YAAI,QAAQ,SAAS,SAAS,KAAK,mBAAmB,aAAa;AACjE,kBAAQ,WAAW,OAAO;AAAA,YACxB,WAAW;AAAA,YACX,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AACA,YAAI,OAAO,KAAK,GAAG;AACjB,2BAAiB,OAAO,KAAK;AAAA,QAC/B;AACA,cAAM,QAAQ,aAAa,KAAK;AAAA,UAC9B;AAAA,UACA;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AACD,iBAAW,KAAK,MAAM,IAAI,YAAY,CAAC;AAAA,IACzC;AACA,QAAI,qBAAqB,CAAC;AAC1B,YAAQ,OAAO,CAAC,cAAc,GAAG,CAAC;AAAA,MAChC;AAAA,IACF,MAAM;AACJ,yBAAmB,QAAQ,iBAAe,YAAY,CAAC;AACvD,4BAAsB,gBAAgB,CAAC,GAAG,IAAI,OAAK,KAAK,aAAa,CAAC,CAAC;AAAA,IACzE,CAAC;AACD,WAAO,MAAM;AACX,iBAAW,QAAQ,iBAAe,YAAY,CAAC;AAC/C,yBAAmB,QAAQ,iBAAe,YAAY,CAAC;AAAA,IACzD;AAAA,EACF;AAkCF;AAhCI,aAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,SAAO,KAAK,qBAAqB,cAAgB,kBAAkB,YAAY,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,sBAAsB,CAAC,CAAC;AACrP;AAGA,aAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,GAAG,gBAAgB;AAAA,IACzC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,IACzE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;AAAA,EAC1B,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,IAC9G;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,sCAAsC;AACjD,CAAC;AAvTL,IAAM,cAAN;AAAA,CA0TC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ,CAAC,6BAA6B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,2BAA0B,YAAY;AA0B5C;AAxBI,mBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,0BAA0B,mBAAmB;AAC3D,YAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,EAClK;AACF,GAAG;AAGH,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;AAAA,EAC1B,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,IACpH;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,sCAAsC;AACjD,CAAC;AAxBL,IAAM,oBAAN;AAAA,CA2BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ,CAAC,6BAA6B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,eAAN,MAAM,qBAAoB,UAAU;AAwCpC;AAtCI,aAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,oBAAoB,mBAAmB;AACrD,YAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,EAC1I;AACF,GAAG;AAGH,aAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,UAAU;AAAA,EACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,IAAI,MAAM,uBAAuB,EAAE;AAAA,IACnD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC;AAAA,EACrB,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,iBAAiB,GAAG,4BAA4B,GAAG,GAAG,gBAAgB,GAAM,sBAAsB;AACrG,MAAG,aAAa,CAAC;AAAA,IACnB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,IAAI,MAAM,UAAU;AAAA,IACpC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,iBAAiB;AAAA,EAChC,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAtCL,IAAM,cAAN;AAAA,CAyCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,MACA,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,gBAAgB,IAAI,eAAe,eAAe;AACxD,SAAS,kBAAkB,QAAQ;AACjC,SAAO;AAAA,IACL,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,IACb,GAAG;AAAA,MACD,MAAM;AAAA,MACN,WAAW;AAAA,IACb,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,WAAW,IAAI,cAAc,MAAM;AAAA,MACnC,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,MAAM;AAAA,MACN,WAAW,IAAI,yBAAyB,MAAM;AAAA,MAC9C,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,MAAM;AAAA,MACN,WAAW,IAAI,mBAAmB;AAAA,MAClC,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,MAAM;AAAA,MACN,WAAW,IAAI,yBAAyB;AAAA,MACxC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACF;AACA,IAAM,oBAAoB,CAAC,UAAU,CAAC,MAAM;AAC1C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,MAAM,CAAC,YAAY;AAAA,EACrB,GAAG,oBAAoB,OAAO,CAAC;AACjC;AACA,IAAM,sBAAsB,CAAC,UAAU,CAAC,MAAM;AAC5C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,CAAC;AACH;AACA,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,QAAQ,UAAU,kBAAkB,YAAY,UAAU,CAAC,GAAG;AACxE,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,QAAI,SAAS;AACX,cAAQ,QAAQ,OAAK,OAAO,UAAU,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,UAAU,MAAM,aAAa,CAAC,GAAG,OAAO,SAAS;AAC/C,SAAK,MAAM;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,OAAO;AACX,QAAI,CAAC,KAAK,OAAO,WAAW,MAAM;AAChC,YAAM,IAAI,MAAM,2FAA2F;AAAA,IAC7G;AACA,QAAI,CAAC,MAAM,QAAQ;AACjB,WAAK,YAAY,KAAK;AAAA,IACxB;AACA,4BAAwB,MAAM,MAAM,MAAM;AACxC,WAAK,OAAO,KAAK;AAEjB,UAAI,CAAC,MAAM,UAAU,MAAM,YAAY;AAErC,cAAM,UAAU,MAAM;AACtB,YAAI,MAAM,UAAU,cAAc,KAAK,GAAG;AAGxC,kBAAQ,uBAAuB,KAAK;AAAA,YAClC;AAAA,YACA,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AACA,gBAAQ,mBAAmB,OAAO,IAAI;AACtC,gBAAQ,iBAAiB,KAAK;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,aAAa,OAAO,OAAO,KAAK,OAAO,UAAU;AACvD,eAAW,QAAQ,eAAa,UAAU,cAAc,KAAK,CAAC;AAC9D,eAAW,QAAQ,eAAa,UAAU,aAAa,KAAK,CAAC;AAC7D,UAAM,YAAY,QAAQ,OAAK,KAAK,OAAO,CAAC,CAAC;AAC7C,eAAW,QAAQ,eAAa,UAAU,eAAe,KAAK,CAAC;AAAA,EACjE;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,OAAO,MAAM,QAAQ,IAAI,iBAAiB,CAAC,CAAC;AAClD,UAAM,QAAQ,MAAM,SAAS,CAAC;AAC9B,UAAM,UAAU,MAAM,WAAW,CAAC;AAClC,UAAM,UAAU,MAAM;AACtB,QAAI,CAAC,QAAQ,mBAAmB;AAC9B,uBAAiB,SAAS,qBAAqB,KAAK,gBAAgB;AAAA,IACtE;AACA,QAAI,CAAC,QAAQ,WAAW;AACtB,uBAAiB,SAAS,aAAa,KAAK,QAAQ;AAAA,IACtD;AACA,QAAI,CAAC,QAAQ,OAAO;AAClB,cAAQ,QAAQ,CAAC,IAAI,UAAU;AAC7B,aAAK,MAAM,CAAC;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,cAAc,KAAK,YAAY;AAC1C,uBAAiB,SAAS,cAAc,KAAK,UAAU;AACvD,UAAI,CAAC,iBAAiB,GAAG;AACvB,gBAAQ,SAAS,CAAC,cAAc,WAAW,GAAG,CAAC;AAAA,UAC7C;AAAA,QACF,MAAM;AACJ,cAAI,CAAC,aAAa;AAChB,oBAAQ,cAAc,KAAK;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAaF;AAXI,mBAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,oBAAsB,SAAS,YAAY,GAAM,SAAY,QAAQ,GAAM,SAAY,kBAAkB,CAAC,GAAM,SAAY,oBAAoB,CAAC,GAAM,SAAS,eAAe,CAAC,CAAC;AACpN;AAGA,mBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,mBAAkB;AAAA,EAC3B,YAAY;AACd,CAAC;AA9FL,IAAM,oBAAN;AAAA,CAiGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAM,cAAN,MAAM,YAAW;AAAA;AAAA,EAEf,IAAI,KAAK,MAAM;AACb,SAAK,MAAM,OAAO;AAAA,EACpB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,IAAI,MAAM,OAAO;AACf,QAAI,KAAK,OAAO,OAAO,aAAa,KAAK,sBAAsB,OAAO;AACpE;AAAA,IACF;AACA,SAAK,SAAS;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,IAAI,OAAO,YAAY;AACrB,SAAK,SAAS;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,UAAU,WAAW;AACvB,SAAK,eAAe,YAAY;AAAA,EAClC;AAAA,EACA,YAAY,SAAS,QAAQ,QAAQ,gBAAgB;AACnD,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB;AAEtB,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,QAAQ;AAAA,MACX,MAAM;AAAA,IACR;AACA,SAAK,oBAAoB,CAAC;AAC1B,SAAK,0BAA0B,MAAM;AAAA,IAAC;AAAA,EACxC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,OAAO,OAAO,sBAAsB,wBAAwB;AACnE,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,KAAK,MAAM;AAC/B,mBAAa,KAAK,IAAI;AAAA,IACxB;AACA,QAAI,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,SAAS,KAAK,sBAAsB,QAAQ,MAAM,cAAc;AAC5G,WAAK,wBAAwB;AAC7B,WAAK,QAAQ,MAAM,KAAK,KAAK;AAC7B,WAAK,0BAA0B,KAAK,aAAa;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,wBAAwB;AAC7B,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,wBAAwB;AACtB,SAAK,MAAM,QAAQ,mBAAmB,KAAK,KAAK;AAAA,EAClD;AAAA,EACA,eAAe;AACb,SAAK,wBAAwB;AAC7B,QAAI,aAAa;AACjB,QAAI,iBAAiB,GAAG;AACtB,UAAI,YAAY,KAAK,SAAS,YAAY;AAC1C,mBAAa,KAAK,KAAK,OAAO,UAAU,MAAM;AAC5C,YAAI,cAAc,KAAK,SAAS,YAAY,WAAW;AACrD,eAAK,QAAQ,cAAc,KAAK,KAAK;AACrC,sBAAY,KAAK,SAAS,YAAY;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,wBAAwB,CAAC,YAAY,KAAK,MAAM,SAAS,CAAC,WAAW,GAAG,MAAM,KAAK,MAAM,QAAQ,cAAc,KAAK,KAAK,CAAC,CAAC;AACjI,UAAM,eAAe,KAAK,MAAM,QAAQ,aAAa,KAAK,OAAO,CAAC;AAAA,MAChE;AAAA,MACA;AAAA,IACF,MAAM,OAAO,KAAK,KAAK,SAAS,cAAc,GAAG,UAAU,MAAM,aAAa,KAAK,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,OAAO,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,OAAO,WAAW,MAAM;AAGvM,WAAK,sBAAsB;AAC3B,WAAK,YAAY,KAAK,KAAK,oBAAoB,MAAM,KAAK,KAAK,CAAC;AAAA,IAClE,CAAC,CAAC;AACF,WAAO,MAAM;AACX,4BAAsB,QAAQ,SAAO,IAAI,CAAC;AAC1C,kBAAY,YAAY;AACxB,mBAAa,YAAY;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,OAAO,OAAO,WAAW;AAChC,WAAK,QAAQ,kCACR,KAAK,QACL,MAAM,KAAK;AAAA,IAElB,OAAO;AACL,aAAO,KAAK,KAAK,EAAE,QAAQ,OAAK,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF;AA6CF;AA3CI,YAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,aAAe,kBAAkB,iBAAiB,GAAM,kBAAkB,YAAY,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,oBAAoB,CAAC;AACvM;AAGA,YAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,EAC3B,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,IAC/C;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,oBAAoB,CAAC,GAAM,oBAAoB;AAAA,EACpG,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC;AAAA,EACrB,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,GAAG,gBAAgB,CAAC;AAAA,IACnC;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,IAAI,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,WAAW;AAAA,EAC1B,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA5JL,IAAM,aAAN;AAAA,CA+JC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB,oBAAoB;AAAA,MACnD,SAAS,CAAC,WAAW;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,0BAAyB,WAAW;AA+B1C;AA7BI,kBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,yBAAyB,mBAAmB;AAC1D,YAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,EAC9J;AACF,GAAG;AAGH,kBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,EAC3B,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,EAC1G,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC;AAAA,EACrB,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,GAAG,gBAAgB,CAAC;AAAA,IACnC;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,IAAI,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,iBAAiB;AAAA,EAChC,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA7BL,IAAM,mBAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB,oBAAoB;AAAA,MACnD,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,oBAAN,MAAM,kBAAiB;AAAA,EACrB,IAAI,QAAQ;AACV,WAAO,KAAK,MAAM,SAAS,CAAC;AAAA,EAC9B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,QAAQ,cAAc,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,YAAY,UAAU,YAAY,WAAW;AAC3C,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,oBAAoB,CAAC;AAM1B,SAAK,WAAW;AAAA,MACd,WAAW,CAAC;AAAA,MACZ,QAAQ,CAAC,SAAS,SAAS,WAAW,YAAY,SAAS,QAAQ,UAAU,OAAO;AAAA,MACpF,UAAU,CAAC,WAAW,WAAW;AAC/B,gBAAQ,WAAW;AAAA,UACjB,KAAK;AACH,mBAAO,KAAK,QAAQ,MAAM;AAAA,UAC5B,KAAK;AACH,mBAAO,KAAK,OAAO,MAAM;AAAA,UAC3B,KAAK;AACH,mBAAO,KAAK,SAAS,MAAM;AAAA,UAC7B;AACE,mBAAO,KAAK,MAAM,SAAS,EAAE,KAAK,OAAO,MAAM;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,OAAO;AACjB,WAAK,MAAM,QAAQ,KAAK,aAAa,QAAQ,KAAK,MAAM,IAAI;AAC5D,WAAK,SAAS,UAAU,QAAQ,cAAY,SAAS,CAAC;AACtD,WAAK,SAAS,OAAO,QAAQ,eAAa;AACxC,YAAI,KAAK,QAAQ,SAAS,KAAK,CAAC,SAAS,QAAQ,QAAQ,EAAE,QAAQ,SAAS,MAAM,IAAI;AACpF,eAAK,SAAS,UAAU,KAAK,KAAK,SAAS,OAAO,KAAK,WAAW,eAAe,WAAW,OAAK,KAAK,SAAS,SAAS,WAAW,CAAC,CAAC,CAAC;AAAA,QACxI;AAAA,MACF,CAAC;AACD,UAAI,KAAK,OAAO,YAAY;AAC1B,gBAAQ,KAAK,OAAO,CAAC,SAAS,YAAY,GAAG,CAAC;AAAA,UAC5C;AAAA,UACA;AAAA,QACF,MAAM;AACJ,cAAI,eAAe;AACjB,mBAAO,KAAK,aAAa,EAAE,QAAQ,UAAQ,KAAK,gBAAgB,IAAI,CAAC;AAAA,UACvE;AACA,cAAI,cAAc;AAChB,mBAAO,KAAK,YAAY,EAAE,QAAQ,UAAQ;AACxC,kBAAI,aAAa,IAAI,KAAK,MAAM;AAC9B,qBAAK,aAAa,MAAM,aAAa,IAAI,CAAC;AAAA,cAC5C;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,iBAAiB,QAAQ,MAAM,aAAa;AACjD,WAAK,iBAAiB,QAAQ,MAAM,YAAY;AAChD,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,SAAC,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,aAAa,MAAM,KAAK,MAAM,EAAE;AAClE,aAAK,gBAAgB,QAAQ,KAAK,OAAO,CAAC,OAAO,GAAG,CAAC;AAAA,UACnD;AAAA,QACF,MAAM;AACJ,eAAK,YAAY,YAAY;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,QAAQ,IAAI;AACd,WAAK,aAAa,MAAM,KAAK,EAAE;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY;AACV,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,UAAU,KAAK,WAAW;AAChC,WAAK,eAAe,CAAC,GAAG,mBAAmB,YAAY,eAAe,YAAY,YAAY,MAAM,EAAE,OAAO,UAAQ,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,aAAa,IAAI,CAAC;AAAA,IAC3K;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,YAAM,OAAO,KAAK,aAAa,CAAC;AAChC,YAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,UAAI,KAAK,kBAAkB,IAAI,MAAM,UAAU,CAAC,KAAK,MAAM,cAAc,CAAC,KAAK,MAAM,WAAW,eAAe,KAAK,YAAY,CAAC,IAAI;AACnI,aAAK,kBAAkB,IAAI,IAAI;AAC/B,YAAI,SAAS,UAAU,GAAG;AACxB,eAAK,aAAa,MAAM,UAAU,OAAO,OAAO,GAAG,KAAK,EAAE;AAAA,QAC5D,OAAO;AACL,eAAK,gBAAgB,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,UAAU,QAAQ,cAAY,SAAS,CAAC;AACtD,SAAK,iBAAiB,KAAK,KAAK;AAChC,SAAK,eAAe,YAAY;AAAA,EAClC;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,UAAU,KAAK,oBAAoB,KAAK,kBAAkB,CAAC,IAAI;AACrE,QAAI,CAAC,WAAW,CAAC,QAAQ,cAAc,OAAO;AAC5C;AAAA,IACF;AACA,UAAM,YAAY,CAAC,CAAC,KAAK,SAAS,iBAAiB,KAAK,kBAAkB,KAAK,CAAC;AAAA,MAC9E;AAAA,IACF,MAAM,KAAK,SAAS,kBAAkB,iBAAiB,cAAc,SAAS,KAAK,SAAS,aAAa,CAAC;AAC1G,QAAI,SAAS,CAAC,WAAW;AACvB,cAAQ,QAAQ,EAAE,KAAK,MAAM,QAAQ,cAAc,MAAM,CAAC;AAAA,IAC5D,WAAW,CAAC,SAAS,WAAW;AAC9B,cAAQ,QAAQ,EAAE,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,SAAK,eAAe,SAAS,IAAI;AACjC,SAAK,MAAM,QAAQ,KAAK,OAAO,MAAM;AAAA,EACvC;AAAA,EACA,OAAO,QAAQ;AACb,SAAK,eAAe,SAAS,KAAK;AAClC,SAAK,MAAM,OAAO,KAAK,OAAO,MAAM;AAAA,EACtC;AAAA;AAAA,EAEA,aAAa,QAAQ;AACnB,QAAI,kBAAkB,OAAO;AAC3B;AAAA,IACF;AACA,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA,EACA,SAAS,QAAQ;AACf,SAAK,MAAM,SAAS,KAAK,OAAO,MAAM;AACtC,SAAK,MAAM,aAAa,YAAY;AAAA,EACtC;AAAA,EACA,iBAAiB,GAAG;AAClB,QAAI,CAAC,GAAG;AACN;AAAA,IACF;AACA,QAAI,EAAE,cAAc,GAAG,QAAQ,KAAK,UAAU,MAAM,IAAI;AACtD,QAAE,cAAc,EAAE,KAAK,KAAK,UAAU;AAAA,IACxC,OAAO;AACL,uBAAiB,GAAG,gBAAgB,CAAC,KAAK,UAAU,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,iBAAiB,GAAG;AAClB,UAAM,QAAQ,IAAI,cAAc,IAAI,KAAK,kBAAkB,QAAQ,KAAK,UAAU,IAAI;AACtF,QAAI,UAAU,IAAI;AAChB,QAAE,cAAc,EAAE,OAAO,OAAO,CAAC;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa,MAAM,OAAO;AACxB,SAAK,SAAS,aAAa,KAAK,WAAW,eAAe,MAAM,KAAK;AAAA,EACvE;AAAA,EACA,gBAAgB,MAAM;AACpB,SAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,IAAI;AAAA,EACnE;AAwBF;AAtBI,kBAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,CAAC;AAC5J;AAGA,kBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,EACxC,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,UAAU,SAAS,2CAA2C,QAAQ;AAClF,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO,CAAC,GAAG,oBAAoB,OAAO;AAAA,IACtC,IAAI;AAAA,EACN;AAAA,EACA,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAtLL,IAAM,mBAAN;AAAA,CAyLC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,gCAA+B,iBAAiB;AAwBtD;AAtBI,wBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,+BAA+B,mBAAmB;AAChE,YAAQ,wCAAwC,sCAAyC,sBAAsB,uBAAsB,IAAI,qBAAqB,uBAAsB;AAAA,EACtL;AACF,GAAG;AAGH,wBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,EACxC,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,UAAU,SAAS,iDAAiD,QAAQ;AACxF,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AAtBL,IAAM,yBAAN;AAAA,CAyBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,UAAM,kBAAkB,kBAAkB,IAAI,OAAK,mBAAmB,CAAC,EAAE;AACzE,SAAK,gBAAgB,MAAM,KAAK,MAAM,YAAY,eAAe,CAAC,KAAK,MAAM,UAAU,GAAG,IAAI,IAAI,KAAK,MAAM,QAAQ,aAAa,KAAK,OAAO,CAAC;AAAA,MAC7I;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,UAAU,KAAK,SAAS,SAAS,wBAAwB,SAAS,QAAQ,YAAY,MAAM,MAAM,gBAAgB,QAAQ,QAAQ,MAAM;AAAA,IACjJ,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,IAAI,GAAG,UAAU,MAAM,aAAa,KAAK,YAAY,IAAI,KAAK,eAAe,GAAG,KAAK,YAAY,CAAC,CAAC;AAAA,EACzH;AAAA,EACA,IAAI,eAAe;AACjB,UAAM,YAAY,KAAK,MAAM;AAC7B,eAAW,SAAS,UAAU,QAAQ;AACpC,UAAI,UAAU,OAAO,eAAe,KAAK,GAAG;AAC1C,YAAI,UAAU,KAAK,OAAO,oBAAoB,KAAK;AACnD,YAAI,SAAS,UAAU,OAAO,KAAK,CAAC,GAAG;AACrC,cAAI,UAAU,OAAO,KAAK,EAAE,WAAW;AACrC,mBAAO;AAAA,UACT;AACA,cAAI,UAAU,OAAO,KAAK,EAAE,SAAS;AACnC,sBAAU,UAAU,OAAO,KAAK,EAAE;AAAA,UACpC;AAAA,QACF;AACA,YAAI,KAAK,MAAM,YAAY,WAAW,KAAK,GAAG;AAC5C,oBAAU,KAAK,MAAM,WAAW,SAAS,KAAK;AAAA,QAChD;AACA,YAAI,KAAK,MAAM,aAAa,KAAK,GAAG,SAAS;AAC3C,oBAAU,KAAK,MAAM,WAAW,KAAK,EAAE;AAAA,QACzC;AACA,YAAI,KAAK,MAAM,kBAAkB,KAAK,GAAG,SAAS;AAChD,oBAAU,KAAK,MAAM,gBAAgB,KAAK,EAAE;AAAA,QAC9C;AACA,YAAI,OAAO,YAAY,YAAY;AACjC,iBAAO,QAAQ,UAAU,OAAO,KAAK,GAAG,KAAK,KAAK;AAAA,QACpD;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AA8BF;AA5BI,yBAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,SAAO,KAAK,qBAAqB,0BAA4B,kBAAkB,YAAY,CAAC;AAC9F;AAGA,yBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,2BAA2B,CAAC;AAAA,EACzC,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,QAAI,KAAK,GAAG;AACV,MAAG,OAAO,CAAC;AACX,MAAG,OAAO,GAAG,OAAO;AAAA,IACtB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,kBAAqB,YAAY,GAAG,GAAG,IAAI,aAAa,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,cAAc,CAAC,SAAS;AAAA,EACxB,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAvEL,IAAM,0BAAN;AAAA,CA0EC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAM,uCAAsC,wBAAwB;AA+BpE;AA7BI,+BAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,sCAAsC,mBAAmB;AACvE,YAAQ,+CAA+C,6CAAgD,sBAAsB,8BAA6B,IAAI,qBAAqB,8BAA6B;AAAA,EAClN;AACF,GAAG;AAGH,+BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,2BAA2B,CAAC;AAAA,EACzC,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,QAAI,KAAK,GAAG;AACV,MAAG,OAAO,CAAC;AACX,MAAG,OAAO,GAAG,OAAO;AAAA,IACtB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,kBAAqB,YAAY,GAAG,GAAG,IAAI,aAAa,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,cAAc,CAAM,SAAS;AAAA,EAC7B,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA7BL,IAAM,gCAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,wBAAuB,UAAU;AAAA,EACrC,WAAW,OAAO;AAChB,QAAI,OAAO,KAAK,GAAG;AACjB,YAAM,UAAU,YAAY,KAAK;AACjC,sBAAgB,OAAO,UAAU,UAAU,IAAI,iBAAiB,CAAC,GAAG;AAAA,QAClE,UAAU,MAAM,aAAa;AAAA,MAC/B,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,aAAa,MAAM,cAAc,CAAC;AACxC,UAAM,SAAS,MAAM,QAAQ,MAAM,KAAK,IAAI,MAAM,MAAM,SAAS;AACjE,QAAI,MAAM,WAAW,SAAS,QAAQ;AACpC,eAAS,IAAI,MAAM,WAAW,SAAS,GAAG,KAAK,QAAQ,EAAE,GAAG;AAC1D,0BAAkB,MAAM,WAAW,CAAC,GAAG,IAAI;AAC3C,cAAM,WAAW,OAAO,GAAG,CAAC;AAAA,MAC9B;AAAA,IACF;AACA,aAAS,IAAI,MAAM,WAAW,QAAQ,IAAI,QAAQ,KAAK;AACrD,YAAM,IAAI,mBACL,MAAM,OAAO,MAAM,eAAe,aAAa,MAAM,WAAW,KAAK,IAAI,MAAM,UAAU;AAE9F,UAAI,EAAE,QAAQ,MAAM;AAClB,UAAE,MAAM,GAAG,CAAC;AAAA,MACd;AACA,YAAM,WAAW,KAAK,CAAC;AAAA,IACzB;AAAA,EACF;AAAA,EACA,IAAI,GAAG,cAAc;AAAA,IACnB;AAAA,EACF,IAAI;AAAA,IACF,aAAa;AAAA,EACf,GAAG;AACD,mBAAe,KAAK,YAAY,YAAY;AAC5C,QAAI,KAAK,OAAO,KAAK,MAAM,WAAW,SAAS;AAC/C,QAAI,CAAC,KAAK,OAAO;AACf,uBAAiB,KAAK,OAAO,CAAC,CAAC;AAAA,IACjC;AACA,SAAK,MAAM,OAAO,GAAG,GAAG,eAAe,MAAM,YAAY,IAAI,MAAS;AACtE,SAAK,kBAAkB,KAAK,MAAM,WAAW,CAAC,CAAC;AAC/C,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,GAAG;AAAA,IACR;AAAA,EACF,IAAI;AAAA,IACF,aAAa;AAAA,EACf,GAAG;AACD,mBAAe,KAAK,YAAY,YAAY;AAC5C,SAAK,MAAM,OAAO,GAAG,CAAC;AACtB,UAAM,QAAQ,KAAK,MAAM,WAAW,CAAC;AACrC,SAAK,MAAM,WAAW,OAAO,GAAG,CAAC;AACjC,SAAK,MAAM,WAAW,QAAQ,CAAC,GAAG,QAAQ,KAAK,sBAAsB,GAAG,GAAG,GAAG,EAAE,CAAC;AACjF,sBAAkB,OAAO,IAAI;AAC7B,SAAK,OAAO;AAAA,EACd;AAAA,EACA,SAAS;AACP,UAAM,SAAS,KAAK,MAAM,YAAY,WAAW,CAAC,KAAK,KAAK;AAC5D,WAAO,QAAQ,OAAK,KAAK,QAAQ,MAAM,CAAC,CAAC;AACzC,SAAK,QAAQ,aAAa,KAAK;AAAA,MAC7B,OAAO,KAAK;AAAA,MACZ,OAAO,cAAc,KAAK,KAAK;AAAA,MAC/B,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,GAAG,QAAQ;AAC/B,QAAI,OAAO,CAAC,GAAG;AACb,QAAE,MAAM;AACR;AAAA,IACF;AACA,QAAI,CAAC,EAAE,YAAY,QAAQ;AACzB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,EAAE,WAAW,QAAQ,KAAK;AAC5C,WAAK,sBAAsB,EAAE,WAAW,CAAC,GAAG,MAAM;AAAA,IACpD;AAAA,EACF;AAAA,EACA,kBAAkB,GAAG;AACnB,QAAI,CAAC,GAAG;AACN;AAAA,IACF;AACA,MAAE,YAAY,QAAQ,OAAK,KAAK,kBAAkB,CAAC,CAAC;AACpD,QAAI,EAAE,SAAS,OAAO;AACpB,WAAK,QAAQ,sBAAsB,KAAK;AAAA,QACtC,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAgBF;AAdI,gBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,uBAAuB,mBAAmB;AACxD,YAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,EACtJ;AACF,GAAG;AAGH,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AAlGL,IAAM,iBAAN;AAAA,CAqGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,sBAAqB,UAAU;AAAA,EACnC,IAAI,gBAAgB,GAAG;AAAA,EAAC;AAAA,EACxB,IAAI,eAAe,SAAS;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AA2BF;AAzBI,cAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,qBAAqB,mBAAmB;AACtD,YAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,EAC9I;AACF,GAAG;AAGH,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,GAAG,gBAAgB;AACvC,MAAG,YAAY,KAAK,GAAG,gBAAgB;AAAA,IACzC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AA7BL,IAAM,eAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,OAAO,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,MAAM,CAAC,YAAY;AAAA,MACrB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,GAAG,cAAc,iBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO,SAAS,SAAS,CAAC,GAAG;AAC3B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,MAAM,CAAC,YAAY;AAAA,MACrB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,GAAG,iBAAiB;AAAA,IACtB;AAAA,EACF;AAmBF;AAjBI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AAGA,cAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,cAAc,CAAC,gBAAgB,kBAAkB,mBAAmB,wBAAwB,+BAA+B,aAAa,kBAAkB;AAAA,EAC1J,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,gBAAgB,kBAAkB,mBAAmB,wBAAwB,+BAA+B,WAAW;AACnI,CAAC;AAGD,cAAK,OAAyB,iBAAiB;AAAA,EAC7C,SAAS,CAAC,YAAY;AACxB,CAAC;AA/CL,IAAM,eAAN;AAAA,CAkDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,gBAAgB,kBAAkB,mBAAmB,wBAAwB,+BAA+B,aAAa,kBAAkB;AAAA,MAC1J,SAAS,CAAC,gBAAgB,kBAAkB,mBAAmB,wBAAwB,+BAA+B,WAAW;AAAA,MACjI,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["observer", "f", "name", "_a", "ref"]}