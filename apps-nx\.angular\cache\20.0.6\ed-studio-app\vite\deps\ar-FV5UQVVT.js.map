{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/ar.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 0)\n        return 0;\n    if (n === 1)\n        return 1;\n    if (n === 2)\n        return 2;\n    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 3 && n % 100 <= 10))\n        return 3;\n    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 99))\n        return 4;\n    return 5;\n}\nexport default [\"ar\", [[\"ص\", \"م\"], u, u], [[\"ص\", \"م\"], u, [\"صباحًا\", \"مساءً\"]], [[\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"], [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"], u, [\"أحد\", \"إثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"]], u, [[\"ي\", \"ف\", \"م\", \"أ\", \"و\", \"ن\", \"ل\", \"غ\", \"س\", \"ك\", \"ب\", \"د\"], [\"يناير\", \"فبراير\", \"مارس\", \"أبريل\", \"مايو\", \"يونيو\", \"يوليو\", \"أغسطس\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"], u], u, [[\"ق.م\", \"م\"], u, [\"قبل الميلاد\", \"ميلادي\"]], 6, [5, 6], [\"d‏/M‏/y\", \"dd‏/MM‏/y\", \"d MMMM y\", \"EEEE، d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} في {0}\", u], [\".\", \",\", \";\", \"‎%‎\", \"‎+\", \"‎-\", \"E\", \"×\", \"‰\", \"∞\", \"ليس رقمًا\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"EGP\", \"ج.م.‏\", \"جنيه مصري\", { \"AED\": [\"د.إ.‏\"], \"ARS\": [u, \"AR$\"], \"AUD\": [\"AU$\"], \"BBD\": [u, \"BB$\"], \"BHD\": [\"د.ب.‏\"], \"BMD\": [u, \"BM$\"], \"BND\": [u, \"BN$\"], \"BSD\": [u, \"BS$\"], \"BYN\": [u, \"р.\"], \"BZD\": [u, \"BZ$\"], \"CAD\": [\"CA$\"], \"CLP\": [u, \"CL$\"], \"CNY\": [\"CN¥\"], \"COP\": [u, \"CO$\"], \"CUP\": [u, \"CU$\"], \"DOP\": [u, \"DO$\"], \"DZD\": [\"د.ج.‏\"], \"EGP\": [\"ج.م.‏\", \"E£\"], \"FJD\": [u, \"FJ$\"], \"GBP\": [\"UK£\"], \"GYD\": [u, \"GY$\"], \"HKD\": [\"HK$\"], \"IQD\": [\"د.ع.‏\"], \"IRR\": [\"ر.إ.\"], \"JMD\": [u, \"JM$\"], \"JOD\": [\"د.أ.‏\"], \"JPY\": [\"JP¥\"], \"KWD\": [\"د.ك.‏\"], \"KYD\": [u, \"KY$\"], \"LBP\": [\"ل.ل.‏\", \"L£\"], \"LRD\": [u, \"$LR\"], \"LYD\": [\"د.ل.‏\"], \"MAD\": [\"د.م.‏\"], \"MRU\": [\"أ.م.\"], \"MXN\": [\"MX$\"], \"NZD\": [\"NZ$\"], \"OMR\": [\"ر.ع.‏\"], \"PHP\": [u, \"₱\"], \"QAR\": [\"ر.ق.‏\"], \"SAR\": [\"ر.س.‏\"], \"SBD\": [u, \"SB$\"], \"SDD\": [\"د.س.‏\"], \"SDG\": [\"ج.س.\"], \"SRD\": [u, \"SR$\"], \"SYP\": [\"ل.س.‏\", \"£\"], \"THB\": [\"฿\"], \"TND\": [\"د.ت.‏\"], \"TTD\": [u, \"TT$\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\"], \"UYU\": [u, \"UY$\"], \"YER\": [\"ر.ي.‏\"] }, \"rtl\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI;AACV,MAAI,MAAM;AACN,WAAO;AACX,MAAI,MAAM;AACN,WAAO;AACX,MAAI,MAAM;AACN,WAAO;AACX,MAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO;AAC/D,WAAO;AACX,MAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO;AAChE,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,WAAW,YAAY,YAAY,UAAU,UAAU,OAAO,GAAG,GAAG,CAAC,OAAO,SAAS,UAAU,UAAU,QAAQ,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,UAAU,QAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS,UAAU,UAAU,UAAU,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC,eAAe,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,aAAa,YAAY,gBAAgB,GAAG,CAAC,UAAU,aAAa,eAAe,gBAAgB,GAAG,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,OAAO,MAAM,MAAM,KAAK,KAAK,KAAK,KAAK,aAAa,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,SAAS,aAAa,EAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,IAAI,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,SAAS,IAAI,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,MAAM;", "names": []}