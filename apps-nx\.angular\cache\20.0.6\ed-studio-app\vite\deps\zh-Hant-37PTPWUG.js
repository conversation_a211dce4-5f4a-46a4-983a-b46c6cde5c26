import "./chunk-QDB2FYN3.js";

// node_modules/@angular/common/locales/zh-Hant.js
var u = void 0;
function plural(val) {
  const n = val;
  return 5;
}
var zh_Hant_default = ["zh-Hant", [["上午", "下午"], u, u], u, [["日", "一", "二", "三", "四", "五", "六"], ["週日", "週一", "週二", "週三", "週四", "週五", "週六"], ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"], ["日", "一", "二", "三", "四", "五", "六"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"], u], u, [["西元前", "西元"], u, u], 0, [6, 0], ["y/M/d", "y年M月d日", u, "y年M月d日 EEEE"], ["Bh:mm", "Bh:mm:ss", "Bh:mm:ss [z]", "Bh:mm:ss [zzzz]"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "非數值", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "TWD", "$", "新台幣", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "KRW": ["￦", "₩"], "PHP": [u, "₱"], "RON": [u, "L"], "RUR": [u, "р."], "TWD": ["$"], "USD": ["US$", "$"], "XXX": [] }, "ltr", plural];
export {
  zh_Hant_default as default
};
/*! Bundled license information:

@angular/common/locales/zh-Hant.js:
  (**
   * @license
   * Copyright Google LLC All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.dev/license
   *)
*/
//# sourceMappingURL=zh-Hant-37PTPWUG.js.map
