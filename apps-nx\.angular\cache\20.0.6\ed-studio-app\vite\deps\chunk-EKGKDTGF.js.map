{"version": 3, "sources": ["../../../../../../node_modules/angular-oauth2-oidc/fesm2022/angular-oauth2-oidc.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, makeEnvironmentProviders, NgModule, InjectionToken } from '@angular/core';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1 from '@angular/common/http';\nimport { HttpHeaders, HttpParams, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, of, from, race, throwError, combineLatest, merge } from 'rxjs';\nimport { filter, tap, debounceTime, delay, map, switchMap, first, catchError, take, mergeMap, timeout } from 'rxjs/operators';\n\n/**\n * A validation handler that isn't validating nothing.\n * Can be used to skip validation (at your own risk).\n */\nclass NullValidationHandler {\n  validateSignature(validationParams) {\n    return Promise.resolve(null);\n  }\n  validateAtHash(validationParams) {\n    return Promise.resolve(true);\n  }\n}\nclass OAuthModuleConfig {}\nclass OAuthResourceServerConfig {}\nclass DateTimeProvider {}\nclass SystemDateTimeProvider extends DateTimeProvider {\n  now() {\n    return Date.now();\n  }\n  new() {\n    return new Date();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵSystemDateTimeProvider_BaseFactory;\n      return function SystemDateTimeProvider_Factory(__ngFactoryType__) {\n        return (ɵSystemDateTimeProvider_BaseFactory || (ɵSystemDateTimeProvider_BaseFactory = i0.ɵɵgetInheritedFactory(SystemDateTimeProvider)))(__ngFactoryType__ || SystemDateTimeProvider);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SystemDateTimeProvider,\n      factory: SystemDateTimeProvider.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SystemDateTimeProvider, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Additional options that can be passed to tryLogin.\n */\nclass LoginOptions {\n  constructor() {\n    /**\n     * Set this to true to disable the nonce\n     * check which is used to avoid\n     * replay attacks.\n     * This flag should never be true in\n     * production environments.\n     */\n    this.disableNonceCheck = false;\n    /**\n     * Normally, you want to clear your hash fragment after\n     * the lib read the token(s) so that they are not displayed\n     * anymore in the url. If not, set this to true. For code flow\n     * this controls removing query string values.\n     */\n    this.preventClearHashAfterLogin = false;\n  }\n}\n/**\n * Defines the logging interface the OAuthService uses\n * internally. Is compatible with the `console` object,\n * but you can provide your own implementation as well\n * through dependency injection.\n */\nclass OAuthLogger {}\n/**\n * Defines a simple storage that can be used for\n * storing the tokens at client side.\n * Is compatible to localStorage and sessionStorage,\n * but you can also create your own implementations.\n */\nclass OAuthStorage {}\nclass MemoryStorage {\n  constructor() {\n    this.data = new Map();\n  }\n  getItem(key) {\n    return this.data.get(key);\n  }\n  removeItem(key) {\n    this.data.delete(key);\n  }\n  setItem(key, data) {\n    this.data.set(key, data);\n  }\n  static {\n    this.ɵfac = function MemoryStorage_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MemoryStorage)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MemoryStorage,\n      factory: MemoryStorage.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MemoryStorage, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Represents the received tokens, the received state\n * and the parsed claims from the id-token.\n */\nclass ReceivedTokens {}\nclass OAuthEvent {\n  constructor(type) {\n    this.type = type;\n  }\n}\nclass OAuthSuccessEvent extends OAuthEvent {\n  constructor(type, info = null) {\n    super(type);\n    this.info = info;\n  }\n}\nclass OAuthInfoEvent extends OAuthEvent {\n  constructor(type, info = null) {\n    super(type);\n    this.info = info;\n  }\n}\nclass OAuthErrorEvent extends OAuthEvent {\n  constructor(type, reason, params = null) {\n    super(type);\n    this.reason = reason;\n    this.params = params;\n  }\n}\n\n// see: https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#The_.22Unicode_Problem.22\nfunction b64DecodeUnicode(str) {\n  const base64 = str.replace(/-/g, '+').replace(/_/g, '/');\n  return decodeURIComponent(atob(base64).split('').map(function (c) {\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n  }).join(''));\n}\nfunction base64UrlEncode(str) {\n  const base64 = btoa(str);\n  return base64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\nclass AuthConfig {\n  constructor(json) {\n    /**\n     * The client's id as registered with the auth server\n     */\n    this.clientId = '';\n    /**\n     * The client's redirectUri as registered with the auth server\n     */\n    this.redirectUri = '';\n    /**\n     * An optional second redirectUri where the auth server\n     * redirects the user to after logging out.\n     */\n    this.postLogoutRedirectUri = '';\n    /**\n     * Defines whether to use 'redirectUri' as a replacement\n     * of 'postLogoutRedirectUri' if the latter is not set.\n     */\n    this.redirectUriAsPostLogoutRedirectUriFallback = true;\n    /**\n     * The auth server's endpoint that allows to log\n     * the user in when using implicit flow.\n     */\n    this.loginUrl = '';\n    /**\n     * The requested scopes\n     */\n    this.scope = 'openid profile';\n    this.resource = '';\n    this.rngUrl = '';\n    /**\n     * Defines whether to use OpenId Connect during\n     * implicit flow.\n     */\n    this.oidc = true;\n    /**\n     * Defines whether to request an access token during\n     * implicit flow.\n     */\n    this.requestAccessToken = true;\n    this.options = null;\n    /**\n     * The issuer's uri.\n     */\n    this.issuer = '';\n    /**\n     * The logout url.\n     */\n    this.logoutUrl = '';\n    /**\n     * Defines whether to clear the hash fragment after logging in.\n     */\n    this.clearHashAfterLogin = true;\n    /**\n     * Url of the token endpoint as defined by OpenId Connect and OAuth 2.\n     */\n    this.tokenEndpoint = null;\n    /**\n     * Url of the revocation endpoint as defined by OpenId Connect and OAuth 2.\n     */\n    this.revocationEndpoint = null;\n    /**\n     * Names of known parameters sent out in the TokenResponse. https://tools.ietf.org/html/rfc6749#section-5.1\n     */\n    this.customTokenParameters = [];\n    /**\n     * Url of the userinfo endpoint as defined by OpenId Connect.\n     */\n    this.userinfoEndpoint = null;\n    this.responseType = '';\n    /**\n     * Defines whether additional debug information should\n     * be shown at the console. Note that in certain browsers\n     * the verbosity of the console needs to be explicitly set\n     * to include Debug level messages.\n     */\n    this.showDebugInformation = false;\n    /**\n     * The redirect uri used when doing silent refresh.\n     */\n    this.silentRefreshRedirectUri = '';\n    this.silentRefreshMessagePrefix = '';\n    /**\n     * Set this to true to display the iframe used for\n     * silent refresh for debugging.\n     */\n    this.silentRefreshShowIFrame = false;\n    /**\n     * Timeout for silent refresh.\n     * @internal\n     * @deprecated use silentRefreshTimeout\n     */\n    this.siletRefreshTimeout = 1000 * 20;\n    /**\n     * Timeout for silent refresh.\n     */\n    this.silentRefreshTimeout = 1000 * 20;\n    /**\n     * Some auth servers don't allow using password flow\n     * w/o a client secret while the standards do not\n     * demand for it. In this case, you can set a password\n     * here. As this password is exposed to the public\n     * it does not bring additional security and is therefore\n     * as good as using no password.\n     */\n    this.dummyClientSecret = '';\n    /**\n     * Defines whether https is required.\n     * The default value is remoteOnly which only allows\n     * http for localhost, while every other domains need\n     * to be used with https.\n     */\n    this.requireHttps = 'remoteOnly';\n    /**\n     * Defines whether every url provided by the discovery\n     * document has to start with the issuer's url.\n     */\n    this.strictDiscoveryDocumentValidation = true;\n    /**\n     * JSON Web Key Set (https://tools.ietf.org/html/rfc7517)\n     * with keys used to validate received id_tokens.\n     * This is taken out of the disovery document. Can be set manually too.\n     */\n    this.jwks = null;\n    /**\n     * Map with additional query parameter that are appended to\n     * the request when initializing implicit flow.\n     */\n    this.customQueryParams = null;\n    this.silentRefreshIFrameName = 'angular-oauth-oidc-silent-refresh-iframe';\n    /**\n     * Defines when the token_timeout event should be raised.\n     * If you set this to the default value 0.75, the event\n     * is triggered after 75% of the token's life time.\n     */\n    this.timeoutFactor = 0.75;\n    /**\n     * If true, the lib will try to check whether the user\n     * is still logged in on a regular basis as described\n     * in http://openid.net/specs/openid-connect-session-1_0.html#ChangeNotification\n     */\n    this.sessionChecksEnabled = false;\n    /**\n     * Interval in msec for checking the session\n     * according to http://openid.net/specs/openid-connect-session-1_0.html#ChangeNotification\n     */\n    this.sessionCheckIntervall = 3 * 1000;\n    /**\n     * Url for the iframe used for session checks\n     */\n    this.sessionCheckIFrameUrl = null;\n    /**\n     * Name of the iframe to use for session checks\n     */\n    this.sessionCheckIFrameName = 'angular-oauth-oidc-check-session-iframe';\n    /**\n     * This property has been introduced to disable at_hash checks\n     * and is indented for Identity Provider that does not deliver\n     * an at_hash EVEN THOUGH its recommended by the OIDC specs.\n     * Of course, when disabling these checks then we are bypassing\n     * a security check which means we are more vulnerable.\n     */\n    this.disableAtHashCheck = false;\n    /**\n     * Defines wether to check the subject of a refreshed token after silent refresh.\n     * Normally, it should be the same as before.\n     */\n    this.skipSubjectCheck = false;\n    this.useIdTokenHintForSilentRefresh = false;\n    /**\n     * Defined whether to skip the validation of the issuer in the discovery document.\n     * Normally, the discovey document's url starts with the url of the issuer.\n     */\n    this.skipIssuerCheck = false;\n    /**\n     * final state sent to issuer is built as follows:\n     * state = nonce + nonceStateSeparator + additional state\n     * Default separator is ';' (encoded %3B).\n     * In rare cases, this character might be forbidden or inconvenient to use by the issuer so it can be customized.\n     */\n    this.nonceStateSeparator = ';';\n    /**\n     * Set this to true to use HTTP BASIC auth for AJAX calls\n     */\n    this.useHttpBasicAuth = false;\n    /**\n     * Decreases the Expiration time of tokens by this number of seconds\n     */\n    this.decreaseExpirationBySec = 0;\n    /**\n     * The interceptors waits this time span if there is no token\n     */\n    this.waitForTokenInMsec = 0;\n    /**\n     * Code Flow is by defauld used together with PKCI which is also higly recommented.\n     * You can disbale it here by setting this flag to true.\n     * https://tools.ietf.org/html/rfc7636#section-1.1\n     */\n    this.disablePKCE = false;\n    /**\n     * Set this to true to preserve the requested route including query parameters after code flow login.\n     * This setting enables deep linking for the code flow.\n     */\n    this.preserveRequestedRoute = false;\n    /**\n     * Allows to disable the timer for the id_token used\n     * for token refresh\n     */\n    this.disableIdTokenTimer = false;\n    /**\n     * Blocks other origins requesting a silent refresh\n     */\n    this.checkOrigin = false;\n    /**\n     * This property allows you to override the method that is used to open the login url,\n     * allowing a way for implementations to specify their own method of routing to new\n     * urls.\n     */\n    this.openUri = uri => {\n      location.href = uri;\n    };\n    if (json) {\n      Object.assign(this, json);\n    }\n  }\n}\n\n/**\n * This custom encoder allows charactes like +, % and / to be used in passwords\n */\nclass WebHttpUrlEncodingCodec {\n  encodeKey(k) {\n    return encodeURIComponent(k);\n  }\n  encodeValue(v) {\n    return encodeURIComponent(v);\n  }\n  decodeKey(k) {\n    return decodeURIComponent(k);\n  }\n  decodeValue(v) {\n    return decodeURIComponent(v);\n  }\n}\n\n/**\n * Interface for Handlers that are hooked in to\n * validate tokens.\n */\nclass ValidationHandler {}\n/**\n * This abstract implementation of ValidationHandler already implements\n * the method validateAtHash. However, to make use of it,\n * you have to override the method calcHash.\n */\nclass AbstractValidationHandler {\n  /**\n   * Validates the at_hash in an id_token against the received access_token.\n   */\n  async validateAtHash(params) {\n    const hashAlg = this.inferHashAlgorithm(params.idTokenHeader);\n    const tokenHash = await this.calcHash(params.accessToken, hashAlg); // sha256(accessToken, { asString: true });\n    const leftMostHalf = tokenHash.substr(0, tokenHash.length / 2);\n    const atHash = base64UrlEncode(leftMostHalf);\n    const claimsAtHash = params.idTokenClaims['at_hash'].replace(/=/g, '');\n    if (atHash !== claimsAtHash) {\n      console.error('exptected at_hash: ' + atHash);\n      console.error('actual at_hash: ' + claimsAtHash);\n    }\n    return atHash === claimsAtHash;\n  }\n  /**\n   * Infers the name of the hash algorithm to use\n   * from the alg field of an id_token.\n   *\n   * @param jwtHeader the id_token's parsed header\n   */\n  inferHashAlgorithm(jwtHeader) {\n    const alg = jwtHeader['alg'];\n    if (!alg.match(/^.S[0-9]{3}$/)) {\n      throw new Error('Algorithm not supported: ' + alg);\n    }\n    return 'sha-' + alg.substr(2);\n  }\n}\nclass UrlHelperService {\n  getHashFragmentParams(customHashFragment) {\n    let hash = customHashFragment || window.location.hash;\n    hash = decodeURIComponent(hash);\n    if (hash.indexOf('#') !== 0) {\n      return {};\n    }\n    const questionMarkPosition = hash.indexOf('?');\n    if (questionMarkPosition > -1) {\n      hash = hash.substr(questionMarkPosition + 1);\n    } else {\n      hash = hash.substr(1);\n    }\n    return this.parseQueryString(hash);\n  }\n  parseQueryString(queryString) {\n    const data = {};\n    let pair, separatorIndex, escapedKey, escapedValue, key, value;\n    if (queryString === null) {\n      return data;\n    }\n    const pairs = queryString.split('&');\n    for (let i = 0; i < pairs.length; i++) {\n      pair = pairs[i];\n      separatorIndex = pair.indexOf('=');\n      if (separatorIndex === -1) {\n        escapedKey = pair;\n        escapedValue = null;\n      } else {\n        escapedKey = pair.substr(0, separatorIndex);\n        escapedValue = pair.substr(separatorIndex + 1);\n      }\n      key = decodeURIComponent(escapedKey);\n      value = decodeURIComponent(escapedValue);\n      if (key.substr(0, 1) === '/') {\n        key = key.substr(1);\n      }\n      data[key] = value;\n    }\n    return data;\n  }\n  static {\n    this.ɵfac = function UrlHelperService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UrlHelperService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UrlHelperService,\n      factory: UrlHelperService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UrlHelperService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n// Credits: https://github.com/dchest/fast-sha256-js/tree/master/src\n// We add this lib directly b/c the published version of fast-sha256-js\n// is commonjs and hence leads to a warning about tree-shakability emitted\n// by the Angular CLI\n// SHA-256 (+ HMAC and PBKDF2) for JavaScript.\n//\n// Written in 2014-2016 by Dmitry Chestnykh.\n// Public domain, no warranty.\n//\n// Functions (accept and return Uint8Arrays):\n//\n//   sha256(message) -> hash\n//   sha256.hmac(key, message) -> mac\n//   sha256.pbkdf2(password, salt, rounds, dkLen) -> dk\n//\n//  Classes:\n//\n//   new sha256.Hash()\n//   new sha256.HMAC(key)\n//\nconst digestLength = 32;\nconst blockSize = 64;\n// SHA-256 constants\nconst K = new Uint32Array([0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2]);\nfunction hashBlocks(w, v, p, pos, len) {\n  let a, b, c, d, e, f, g, h, u, i, j, t1, t2;\n  while (len >= 64) {\n    a = v[0];\n    b = v[1];\n    c = v[2];\n    d = v[3];\n    e = v[4];\n    f = v[5];\n    g = v[6];\n    h = v[7];\n    for (i = 0; i < 16; i++) {\n      j = pos + i * 4;\n      w[i] = (p[j] & 0xff) << 24 | (p[j + 1] & 0xff) << 16 | (p[j + 2] & 0xff) << 8 | p[j + 3] & 0xff;\n    }\n    for (i = 16; i < 64; i++) {\n      u = w[i - 2];\n      t1 = (u >>> 17 | u << 32 - 17) ^ (u >>> 19 | u << 32 - 19) ^ u >>> 10;\n      u = w[i - 15];\n      t2 = (u >>> 7 | u << 32 - 7) ^ (u >>> 18 | u << 32 - 18) ^ u >>> 3;\n      w[i] = (t1 + w[i - 7] | 0) + (t2 + w[i - 16] | 0);\n    }\n    for (i = 0; i < 64; i++) {\n      t1 = (((e >>> 6 | e << 32 - 6) ^ (e >>> 11 | e << 32 - 11) ^ (e >>> 25 | e << 32 - 25)) + (e & f ^ ~e & g) | 0) + (h + (K[i] + w[i] | 0) | 0) | 0;\n      t2 = ((a >>> 2 | a << 32 - 2) ^ (a >>> 13 | a << 32 - 13) ^ (a >>> 22 | a << 32 - 22)) + (a & b ^ a & c ^ b & c) | 0;\n      h = g;\n      g = f;\n      f = e;\n      e = d + t1 | 0;\n      d = c;\n      c = b;\n      b = a;\n      a = t1 + t2 | 0;\n    }\n    v[0] += a;\n    v[1] += b;\n    v[2] += c;\n    v[3] += d;\n    v[4] += e;\n    v[5] += f;\n    v[6] += g;\n    v[7] += h;\n    pos += 64;\n    len -= 64;\n  }\n  return pos;\n}\n// Hash implements SHA256 hash algorithm.\nclass Hash {\n  constructor() {\n    this.digestLength = digestLength;\n    this.blockSize = blockSize;\n    // Note: Int32Array is used instead of Uint32Array for performance reasons.\n    this.state = new Int32Array(8); // hash state\n    this.temp = new Int32Array(64); // temporary state\n    this.buffer = new Uint8Array(128); // buffer for data to hash\n    this.bufferLength = 0; // number of bytes in buffer\n    this.bytesHashed = 0; // number of total bytes hashed\n    this.finished = false; // indicates whether the hash was finalized\n    this.reset();\n  }\n  // Resets hash state making it possible\n  // to re-use this instance to hash other data.\n  reset() {\n    this.state[0] = 0x6a09e667;\n    this.state[1] = 0xbb67ae85;\n    this.state[2] = 0x3c6ef372;\n    this.state[3] = 0xa54ff53a;\n    this.state[4] = 0x510e527f;\n    this.state[5] = 0x9b05688c;\n    this.state[6] = 0x1f83d9ab;\n    this.state[7] = 0x5be0cd19;\n    this.bufferLength = 0;\n    this.bytesHashed = 0;\n    this.finished = false;\n    return this;\n  }\n  // Cleans internal buffers and re-initializes hash state.\n  clean() {\n    for (let i = 0; i < this.buffer.length; i++) {\n      this.buffer[i] = 0;\n    }\n    for (let i = 0; i < this.temp.length; i++) {\n      this.temp[i] = 0;\n    }\n    this.reset();\n  }\n  // Updates hash state with the given data.\n  //\n  // Optionally, length of the data can be specified to hash\n  // fewer bytes than data.length.\n  //\n  // Throws error when trying to update already finalized hash:\n  // instance must be reset to use it again.\n  update(data, dataLength = data.length) {\n    if (this.finished) {\n      throw new Error(\"SHA256: can't update because hash was finished.\");\n    }\n    let dataPos = 0;\n    this.bytesHashed += dataLength;\n    if (this.bufferLength > 0) {\n      while (this.bufferLength < 64 && dataLength > 0) {\n        this.buffer[this.bufferLength++] = data[dataPos++];\n        dataLength--;\n      }\n      if (this.bufferLength === 64) {\n        hashBlocks(this.temp, this.state, this.buffer, 0, 64);\n        this.bufferLength = 0;\n      }\n    }\n    if (dataLength >= 64) {\n      dataPos = hashBlocks(this.temp, this.state, data, dataPos, dataLength);\n      dataLength %= 64;\n    }\n    while (dataLength > 0) {\n      this.buffer[this.bufferLength++] = data[dataPos++];\n      dataLength--;\n    }\n    return this;\n  }\n  // Finalizes hash state and puts hash into out.\n  //\n  // If hash was already finalized, puts the same value.\n  finish(out) {\n    if (!this.finished) {\n      const bytesHashed = this.bytesHashed;\n      const left = this.bufferLength;\n      const bitLenHi = bytesHashed / 0x20000000 | 0;\n      const bitLenLo = bytesHashed << 3;\n      const padLength = bytesHashed % 64 < 56 ? 64 : 128;\n      this.buffer[left] = 0x80;\n      for (let i = left + 1; i < padLength - 8; i++) {\n        this.buffer[i] = 0;\n      }\n      this.buffer[padLength - 8] = bitLenHi >>> 24 & 0xff;\n      this.buffer[padLength - 7] = bitLenHi >>> 16 & 0xff;\n      this.buffer[padLength - 6] = bitLenHi >>> 8 & 0xff;\n      this.buffer[padLength - 5] = bitLenHi >>> 0 & 0xff;\n      this.buffer[padLength - 4] = bitLenLo >>> 24 & 0xff;\n      this.buffer[padLength - 3] = bitLenLo >>> 16 & 0xff;\n      this.buffer[padLength - 2] = bitLenLo >>> 8 & 0xff;\n      this.buffer[padLength - 1] = bitLenLo >>> 0 & 0xff;\n      hashBlocks(this.temp, this.state, this.buffer, 0, padLength);\n      this.finished = true;\n    }\n    for (let i = 0; i < 8; i++) {\n      out[i * 4 + 0] = this.state[i] >>> 24 & 0xff;\n      out[i * 4 + 1] = this.state[i] >>> 16 & 0xff;\n      out[i * 4 + 2] = this.state[i] >>> 8 & 0xff;\n      out[i * 4 + 3] = this.state[i] >>> 0 & 0xff;\n    }\n    return this;\n  }\n  // Returns the final hash digest.\n  digest() {\n    const out = new Uint8Array(this.digestLength);\n    this.finish(out);\n    return out;\n  }\n  // Internal function for use in HMAC for optimization.\n  _saveState(out) {\n    for (let i = 0; i < this.state.length; i++) {\n      out[i] = this.state[i];\n    }\n  }\n  // Internal function for use in HMAC for optimization.\n  _restoreState(from, bytesHashed) {\n    for (let i = 0; i < this.state.length; i++) {\n      this.state[i] = from[i];\n    }\n    this.bytesHashed = bytesHashed;\n    this.finished = false;\n    this.bufferLength = 0;\n  }\n}\n// HMAC implements HMAC-SHA256 message authentication algorithm.\nclass HMAC {\n  constructor(key) {\n    this.inner = new Hash();\n    this.outer = new Hash();\n    this.blockSize = this.inner.blockSize;\n    this.digestLength = this.inner.digestLength;\n    const pad = new Uint8Array(this.blockSize);\n    if (key.length > this.blockSize) {\n      new Hash().update(key).finish(pad).clean();\n    } else {\n      for (let i = 0; i < key.length; i++) {\n        pad[i] = key[i];\n      }\n    }\n    for (let i = 0; i < pad.length; i++) {\n      pad[i] ^= 0x36;\n    }\n    this.inner.update(pad);\n    for (let i = 0; i < pad.length; i++) {\n      pad[i] ^= 0x36 ^ 0x5c;\n    }\n    this.outer.update(pad);\n    this.istate = new Uint32Array(8);\n    this.ostate = new Uint32Array(8);\n    this.inner._saveState(this.istate);\n    this.outer._saveState(this.ostate);\n    for (let i = 0; i < pad.length; i++) {\n      pad[i] = 0;\n    }\n  }\n  // Returns HMAC state to the state initialized with key\n  // to make it possible to run HMAC over the other data with the same\n  // key without creating a new instance.\n  reset() {\n    this.inner._restoreState(this.istate, this.inner.blockSize);\n    this.outer._restoreState(this.ostate, this.outer.blockSize);\n    return this;\n  }\n  // Cleans HMAC state.\n  clean() {\n    for (let i = 0; i < this.istate.length; i++) {\n      this.ostate[i] = this.istate[i] = 0;\n    }\n    this.inner.clean();\n    this.outer.clean();\n  }\n  // Updates state with provided data.\n  update(data) {\n    this.inner.update(data);\n    return this;\n  }\n  // Finalizes HMAC and puts the result in out.\n  finish(out) {\n    if (this.outer.finished) {\n      this.outer.finish(out);\n    } else {\n      this.inner.finish(out);\n      this.outer.update(out, this.digestLength).finish(out);\n    }\n    return this;\n  }\n  // Returns message authentication code.\n  digest() {\n    const out = new Uint8Array(this.digestLength);\n    this.finish(out);\n    return out;\n  }\n}\n// Returns SHA256 hash of data.\nfunction hash(data) {\n  const h = new Hash().update(data);\n  const digest = h.digest();\n  h.clean();\n  return digest;\n}\n// Returns HMAC-SHA256 of data under the key.\nfunction hmac(key, data) {\n  const h = new HMAC(key).update(data);\n  const digest = h.digest();\n  h.clean();\n  return digest;\n}\n// Fills hkdf buffer like this:\n// T(1) = HMAC-Hash(PRK, T(0) | info | 0x01)\nfunction fillBuffer(buffer, hmac, info, counter) {\n  // Counter is a byte value: check if it overflowed.\n  const num = counter[0];\n  if (num === 0) {\n    throw new Error('hkdf: cannot expand more');\n  }\n  // Prepare HMAC instance for new data with old key.\n  hmac.reset();\n  // Hash in previous output if it was generated\n  // (i.e. counter is greater than 1).\n  if (num > 1) {\n    hmac.update(buffer);\n  }\n  // Hash in info if it exists.\n  if (info) {\n    hmac.update(info);\n  }\n  // Hash in the counter.\n  hmac.update(counter);\n  // Output result to buffer and clean HMAC instance.\n  hmac.finish(buffer);\n  // Increment counter inside typed array, this works properly.\n  counter[0]++;\n}\nconst hkdfSalt = new Uint8Array(digestLength); // Filled with zeroes.\nfunction hkdf(key, salt = hkdfSalt, info, length = 32) {\n  const counter = new Uint8Array([1]);\n  // HKDF-Extract uses salt as HMAC key, and key as data.\n  const okm = hmac(salt, key);\n  // Initialize HMAC for expanding with extracted key.\n  // Ensure no collisions with `hmac` function.\n  const hmac_ = new HMAC(okm);\n  // Allocate buffer.\n  const buffer = new Uint8Array(hmac_.digestLength);\n  let bufpos = buffer.length;\n  const out = new Uint8Array(length);\n  for (let i = 0; i < length; i++) {\n    if (bufpos === buffer.length) {\n      fillBuffer(buffer, hmac_, info, counter);\n      bufpos = 0;\n    }\n    out[i] = buffer[bufpos++];\n  }\n  hmac_.clean();\n  buffer.fill(0);\n  counter.fill(0);\n  return out;\n}\n// Derives a key from password and salt using PBKDF2-HMAC-SHA256\n// with the given number of iterations.\n//\n// The number of bytes returned is equal to dkLen.\n//\n// (For better security, avoid dkLen greater than hash length - 32 bytes).\nfunction pbkdf2(password, salt, iterations, dkLen) {\n  const prf = new HMAC(password);\n  const len = prf.digestLength;\n  const ctr = new Uint8Array(4);\n  const t = new Uint8Array(len);\n  const u = new Uint8Array(len);\n  const dk = new Uint8Array(dkLen);\n  for (let i = 0; i * len < dkLen; i++) {\n    const c = i + 1;\n    ctr[0] = c >>> 24 & 0xff;\n    ctr[1] = c >>> 16 & 0xff;\n    ctr[2] = c >>> 8 & 0xff;\n    ctr[3] = c >>> 0 & 0xff;\n    prf.reset();\n    prf.update(salt);\n    prf.update(ctr);\n    prf.finish(u);\n    for (let j = 0; j < len; j++) {\n      t[j] = u[j];\n    }\n    for (let j = 2; j <= iterations; j++) {\n      prf.reset();\n      prf.update(u).finish(u);\n      for (let k = 0; k < len; k++) {\n        t[k] ^= u[k];\n      }\n    }\n    for (let j = 0; j < len && i * len + j < dkLen; j++) {\n      dk[i * len + j] = t[j];\n    }\n  }\n  for (let i = 0; i < len; i++) {\n    t[i] = u[i] = 0;\n  }\n  for (let i = 0; i < 4; i++) {\n    ctr[i] = 0;\n  }\n  prf.clean();\n  return dk;\n}\n\n/**\n * Abstraction for crypto algorithms\n */\nclass HashHandler {}\nfunction decodeUTF8(s) {\n  if (typeof s !== 'string') throw new TypeError('expected string');\n  const d = s,\n    b = new Uint8Array(d.length);\n  for (let i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n  return b;\n}\nfunction encodeUTF8(arr) {\n  const s = [];\n  for (let i = 0; i < arr.length; i++) s.push(String.fromCharCode(arr[i]));\n  return s.join('');\n}\nclass DefaultHashHandler {\n  async calcHash(valueToHash, algorithm) {\n    // const encoder = new TextEncoder();\n    // const hashArray = await window.crypto.subtle.digest(algorithm, data);\n    // const data = encoder.encode(valueToHash);\n    // const fhash = fsha256(valueToHash);\n    const candHash = encodeUTF8(hash(decodeUTF8(valueToHash)));\n    // const hashArray = (sha256 as any).array(valueToHash);\n    // // const hashString = this.toHashString(hashArray);\n    // const hashString = this.toHashString2(hashArray);\n    // console.debug('hash orig - cand', candHash, hashString);\n    // alert(1);\n    return candHash;\n  }\n  toHashString2(byteArray) {\n    let result = '';\n    for (const e of byteArray) {\n      result += String.fromCharCode(e);\n    }\n    return result;\n  }\n  toHashString(buffer) {\n    const byteArray = new Uint8Array(buffer);\n    let result = '';\n    for (const e of byteArray) {\n      result += String.fromCharCode(e);\n    }\n    return result;\n  }\n  static {\n    this.ɵfac = function DefaultHashHandler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DefaultHashHandler)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DefaultHashHandler,\n      factory: DefaultHashHandler.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultHashHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Service for logging in and logging out with\n * OIDC and OAuth2. Supports implicit flow and\n * password flow.\n */\nclass OAuthService extends AuthConfig {\n  constructor(ngZone, http, storage, tokenValidationHandler, config, urlHelper, logger, crypto, document, dateTimeService) {\n    super();\n    this.ngZone = ngZone;\n    this.http = http;\n    this.config = config;\n    this.urlHelper = urlHelper;\n    this.logger = logger;\n    this.crypto = crypto;\n    this.dateTimeService = dateTimeService;\n    /**\n     * @internal\n     * Deprecated:  use property events instead\n     */\n    this.discoveryDocumentLoaded = false;\n    /**\n     * The received (passed around) state, when logging\n     * in with implicit flow.\n     */\n    this.state = '';\n    this.eventsSubject = new Subject();\n    this.discoveryDocumentLoadedSubject = new Subject();\n    this.grantTypesSupported = [];\n    this.inImplicitFlow = false;\n    this.saveNoncesInLocalStorage = false;\n    this.debug('angular-oauth2-oidc v10');\n    // See https://github.com/manfredsteyer/angular-oauth2-oidc/issues/773 for why this is needed\n    this.document = document;\n    if (!config) {\n      config = {};\n    }\n    this.discoveryDocumentLoaded$ = this.discoveryDocumentLoadedSubject.asObservable();\n    this.events = this.eventsSubject.asObservable();\n    if (tokenValidationHandler) {\n      this.tokenValidationHandler = tokenValidationHandler;\n    }\n    if (config) {\n      this.configure(config);\n    }\n    try {\n      if (storage) {\n        this.setStorage(storage);\n      } else if (typeof sessionStorage !== 'undefined') {\n        this.setStorage(sessionStorage);\n      }\n    } catch (e) {\n      console.error('No OAuthStorage provided and cannot access default (sessionStorage).' + 'Consider providing a custom OAuthStorage implementation in your module.', e);\n    }\n    // in IE, sessionStorage does not always survive a redirect\n    if (this.checkLocalStorageAccessable()) {\n      const ua = window?.navigator?.userAgent;\n      const msie = ua?.includes('MSIE ') || ua?.includes('Trident');\n      if (msie) {\n        this.saveNoncesInLocalStorage = true;\n      }\n    }\n    this.setupRefreshTimer();\n  }\n  checkLocalStorageAccessable() {\n    if (typeof window === 'undefined') return false;\n    const test = 'test';\n    try {\n      if (typeof window['localStorage'] === 'undefined') return false;\n      localStorage.setItem(test, test);\n      localStorage.removeItem(test);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n  /**\n   * Use this method to configure the service\n   * @param config the configuration\n   */\n  configure(config) {\n    // For the sake of downward compatibility with\n    // original configuration API\n    Object.assign(this, new AuthConfig(), config);\n    this.config = Object.assign({}, new AuthConfig(), config);\n    if (this.sessionChecksEnabled) {\n      this.setupSessionCheck();\n    }\n    this.configChanged();\n  }\n  configChanged() {\n    this.setupRefreshTimer();\n  }\n  restartSessionChecksIfStillLoggedIn() {\n    if (this.hasValidIdToken()) {\n      this.initSessionCheck();\n    }\n  }\n  restartRefreshTimerIfStillLoggedIn() {\n    this.setupExpirationTimers();\n  }\n  setupSessionCheck() {\n    this.events.pipe(filter(e => e.type === 'token_received')).subscribe(() => {\n      this.initSessionCheck();\n    });\n  }\n  /**\n   * Will setup up silent refreshing for when the token is\n   * about to expire. When the user is logged out via this.logOut method, the\n   * silent refreshing will pause and not refresh the tokens until the user is\n   * logged back in via receiving a new token.\n   * @param params Additional parameter to pass\n   * @param listenTo Setup automatic refresh of a specific token type\n   */\n  setupAutomaticSilentRefresh(params = {}, listenTo, noPrompt = true) {\n    let shouldRunSilentRefresh = true;\n    this.clearAutomaticRefreshTimer();\n    this.automaticRefreshSubscription = this.events.pipe(tap(e => {\n      if (e.type === 'token_received') {\n        shouldRunSilentRefresh = true;\n      } else if (e.type === 'logout') {\n        shouldRunSilentRefresh = false;\n      }\n    }), filter(e => e.type === 'token_expires' && (listenTo == null || listenTo === 'any' || e.info === listenTo)), debounceTime(1000)).subscribe(() => {\n      if (shouldRunSilentRefresh) {\n        // this.silentRefresh(params, noPrompt).catch(_ => {\n        this.refreshInternal(params, noPrompt).catch(() => {\n          this.debug('Automatic silent refresh did not work');\n        });\n      }\n    });\n    this.restartRefreshTimerIfStillLoggedIn();\n  }\n  refreshInternal(params, noPrompt) {\n    if (!this.useSilentRefresh && this.responseType === 'code') {\n      return this.refreshToken();\n    } else {\n      return this.silentRefresh(params, noPrompt);\n    }\n  }\n  /**\n   * Convenience method that first calls `loadDiscoveryDocument(...)` and\n   * directly chains using the `then(...)` part of the promise to call\n   * the `tryLogin(...)` method.\n   *\n   * @param options LoginOptions to pass through to `tryLogin(...)`\n   */\n  loadDiscoveryDocumentAndTryLogin(options = null) {\n    return this.loadDiscoveryDocument().then(() => {\n      return this.tryLogin(options);\n    });\n  }\n  /**\n   * Convenience method that first calls `loadDiscoveryDocumentAndTryLogin(...)`\n   * and if then chains to `initLoginFlow()`, but only if there is no valid\n   * IdToken or no valid AccessToken.\n   *\n   * @param options LoginOptions to pass through to `tryLogin(...)`\n   */\n  loadDiscoveryDocumentAndLogin(options = null) {\n    options = options || {};\n    return this.loadDiscoveryDocumentAndTryLogin(options).then(() => {\n      if (!this.hasValidIdToken() || !this.hasValidAccessToken()) {\n        const state = typeof options.state === 'string' ? options.state : '';\n        this.initLoginFlow(state);\n        return false;\n      } else {\n        return true;\n      }\n    });\n  }\n  debug(...args) {\n    if (this.showDebugInformation) {\n      this.logger.debug(...args);\n    }\n  }\n  validateUrlFromDiscoveryDocument(url) {\n    const errors = [];\n    const httpsCheck = this.validateUrlForHttps(url);\n    const issuerCheck = this.validateUrlAgainstIssuer(url);\n    if (!httpsCheck) {\n      errors.push('https for all urls required. Also for urls received by discovery.');\n    }\n    if (!issuerCheck) {\n      errors.push('Every url in discovery document has to start with the issuer url.' + 'Also see property strictDiscoveryDocumentValidation.');\n    }\n    return errors;\n  }\n  validateUrlForHttps(url) {\n    if (!url) {\n      return true;\n    }\n    const lcUrl = url.toLowerCase();\n    if (this.requireHttps === false) {\n      return true;\n    }\n    if ((lcUrl.match(/^http:\\/\\/localhost($|[:/])/) || lcUrl.match(/^http:\\/\\/localhost($|[:/])/)) && this.requireHttps === 'remoteOnly') {\n      return true;\n    }\n    return lcUrl.startsWith('https://');\n  }\n  assertUrlNotNullAndCorrectProtocol(url, description) {\n    if (!url) {\n      throw new Error(`'${description}' should not be null`);\n    }\n    if (!this.validateUrlForHttps(url)) {\n      throw new Error(`'${description}' must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).`);\n    }\n  }\n  validateUrlAgainstIssuer(url) {\n    if (!this.strictDiscoveryDocumentValidation) {\n      return true;\n    }\n    if (!url) {\n      return true;\n    }\n    return url.toLowerCase().startsWith(this.issuer.toLowerCase());\n  }\n  setupRefreshTimer() {\n    if (typeof window === 'undefined') {\n      this.debug('timer not supported on this plattform');\n      return;\n    }\n    if (this.hasValidIdToken() || this.hasValidAccessToken()) {\n      this.clearAccessTokenTimer();\n      this.clearIdTokenTimer();\n      this.setupExpirationTimers();\n    }\n    if (this.tokenReceivedSubscription) this.tokenReceivedSubscription.unsubscribe();\n    this.tokenReceivedSubscription = this.events.pipe(filter(e => e.type === 'token_received')).subscribe(() => {\n      this.clearAccessTokenTimer();\n      this.clearIdTokenTimer();\n      this.setupExpirationTimers();\n    });\n  }\n  setupExpirationTimers() {\n    if (this.hasValidAccessToken()) {\n      this.setupAccessTokenTimer();\n    }\n    if (!this.disableIdTokenTimer && this.hasValidIdToken()) {\n      this.setupIdTokenTimer();\n    }\n  }\n  setupAccessTokenTimer() {\n    const expiration = this.getAccessTokenExpiration();\n    const storedAt = this.getAccessTokenStoredAt();\n    const timeout = this.calcTimeout(storedAt, expiration);\n    this.ngZone.runOutsideAngular(() => {\n      this.accessTokenTimeoutSubscription = of(new OAuthInfoEvent('token_expires', 'access_token')).pipe(delay(timeout)).subscribe(e => {\n        this.ngZone.run(() => {\n          this.eventsSubject.next(e);\n        });\n      });\n    });\n  }\n  setupIdTokenTimer() {\n    const expiration = this.getIdTokenExpiration();\n    const storedAt = this.getIdTokenStoredAt();\n    const timeout = this.calcTimeout(storedAt, expiration);\n    this.ngZone.runOutsideAngular(() => {\n      this.idTokenTimeoutSubscription = of(new OAuthInfoEvent('token_expires', 'id_token')).pipe(delay(timeout)).subscribe(e => {\n        this.ngZone.run(() => {\n          this.eventsSubject.next(e);\n        });\n      });\n    });\n  }\n  /**\n   * Stops timers for automatic refresh.\n   * To restart it, call setupAutomaticSilentRefresh again.\n   */\n  stopAutomaticRefresh() {\n    this.clearAccessTokenTimer();\n    this.clearIdTokenTimer();\n    this.clearAutomaticRefreshTimer();\n  }\n  clearAccessTokenTimer() {\n    if (this.accessTokenTimeoutSubscription) {\n      this.accessTokenTimeoutSubscription.unsubscribe();\n    }\n  }\n  clearIdTokenTimer() {\n    if (this.idTokenTimeoutSubscription) {\n      this.idTokenTimeoutSubscription.unsubscribe();\n    }\n  }\n  clearAutomaticRefreshTimer() {\n    if (this.automaticRefreshSubscription) {\n      this.automaticRefreshSubscription.unsubscribe();\n    }\n  }\n  calcTimeout(storedAt, expiration) {\n    const now = this.dateTimeService.now();\n    const delta = (expiration - storedAt) * this.timeoutFactor - (now - storedAt);\n    const duration = Math.max(0, delta);\n    const maxTimeoutValue = **********;\n    return duration > maxTimeoutValue ? maxTimeoutValue : duration;\n  }\n  /**\n   * DEPRECATED. Use a provider for OAuthStorage instead:\n   *\n   * { provide: OAuthStorage, useFactory: oAuthStorageFactory }\n   * export function oAuthStorageFactory(): OAuthStorage { return localStorage; }\n   * Sets a custom storage used to store the received\n   * tokens on client side. By default, the browser's\n   * sessionStorage is used.\n   * @ignore\n   *\n   * @param storage\n   */\n  setStorage(storage) {\n    this._storage = storage;\n    this.configChanged();\n  }\n  /**\n   * Loads the discovery document to configure most\n   * properties of this service. The url of the discovery\n   * document is infered from the issuer's url according\n   * to the OpenId Connect spec. To use another url you\n   * can pass it to to optional parameter fullUrl.\n   *\n   * @param fullUrl\n   */\n  loadDiscoveryDocument(fullUrl = null) {\n    return new Promise((resolve, reject) => {\n      if (!fullUrl) {\n        fullUrl = this.issuer || '';\n        if (!fullUrl.endsWith('/')) {\n          fullUrl += '/';\n        }\n        fullUrl += '.well-known/openid-configuration';\n      }\n      if (!this.validateUrlForHttps(fullUrl)) {\n        reject(\"issuer  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).\");\n        return;\n      }\n      this.http.get(fullUrl).subscribe(doc => {\n        if (!this.validateDiscoveryDocument(doc)) {\n          this.eventsSubject.next(new OAuthErrorEvent('discovery_document_validation_error', null));\n          reject('discovery_document_validation_error');\n          return;\n        }\n        this.loginUrl = doc.authorization_endpoint;\n        this.logoutUrl = doc.end_session_endpoint || this.logoutUrl;\n        this.grantTypesSupported = doc.grant_types_supported;\n        this.issuer = doc.issuer;\n        this.tokenEndpoint = doc.token_endpoint;\n        this.userinfoEndpoint = doc.userinfo_endpoint || this.userinfoEndpoint;\n        this.jwksUri = doc.jwks_uri;\n        this.sessionCheckIFrameUrl = doc.check_session_iframe || this.sessionCheckIFrameUrl;\n        this.discoveryDocumentLoaded = true;\n        this.discoveryDocumentLoadedSubject.next(doc);\n        this.revocationEndpoint = doc.revocation_endpoint || this.revocationEndpoint;\n        if (this.sessionChecksEnabled) {\n          this.restartSessionChecksIfStillLoggedIn();\n        }\n        this.loadJwks().then(jwks => {\n          const result = {\n            discoveryDocument: doc,\n            jwks: jwks\n          };\n          const event = new OAuthSuccessEvent('discovery_document_loaded', result);\n          this.eventsSubject.next(event);\n          resolve(event);\n          return;\n        }).catch(err => {\n          this.eventsSubject.next(new OAuthErrorEvent('discovery_document_load_error', err));\n          reject(err);\n          return;\n        });\n      }, err => {\n        this.logger.error('error loading discovery document', err);\n        this.eventsSubject.next(new OAuthErrorEvent('discovery_document_load_error', err));\n        reject(err);\n      });\n    });\n  }\n  loadJwks() {\n    return new Promise((resolve, reject) => {\n      if (this.jwksUri) {\n        this.http.get(this.jwksUri).subscribe(jwks => {\n          this.jwks = jwks;\n          // this.eventsSubject.next(\n          //   new OAuthSuccessEvent('discovery_document_loaded')\n          // );\n          resolve(jwks);\n        }, err => {\n          this.logger.error('error loading jwks', err);\n          this.eventsSubject.next(new OAuthErrorEvent('jwks_load_error', err));\n          reject(err);\n        });\n      } else {\n        resolve(null);\n      }\n    });\n  }\n  validateDiscoveryDocument(doc) {\n    let errors;\n    if (!this.skipIssuerCheck && doc.issuer !== this.issuer) {\n      this.logger.error('invalid issuer in discovery document', 'expected: ' + this.issuer, 'current: ' + doc.issuer);\n      return false;\n    }\n    errors = this.validateUrlFromDiscoveryDocument(doc.authorization_endpoint);\n    if (errors.length > 0) {\n      this.logger.error('error validating authorization_endpoint in discovery document', errors);\n      return false;\n    }\n    errors = this.validateUrlFromDiscoveryDocument(doc.end_session_endpoint);\n    if (errors.length > 0) {\n      this.logger.error('error validating end_session_endpoint in discovery document', errors);\n      return false;\n    }\n    errors = this.validateUrlFromDiscoveryDocument(doc.token_endpoint);\n    if (errors.length > 0) {\n      this.logger.error('error validating token_endpoint in discovery document', errors);\n    }\n    errors = this.validateUrlFromDiscoveryDocument(doc.revocation_endpoint);\n    if (errors.length > 0) {\n      this.logger.error('error validating revocation_endpoint in discovery document', errors);\n    }\n    errors = this.validateUrlFromDiscoveryDocument(doc.userinfo_endpoint);\n    if (errors.length > 0) {\n      this.logger.error('error validating userinfo_endpoint in discovery document', errors);\n      return false;\n    }\n    errors = this.validateUrlFromDiscoveryDocument(doc.jwks_uri);\n    if (errors.length > 0) {\n      this.logger.error('error validating jwks_uri in discovery document', errors);\n      return false;\n    }\n    if (this.sessionChecksEnabled && !doc.check_session_iframe) {\n      this.logger.warn('sessionChecksEnabled is activated but discovery document' + ' does not contain a check_session_iframe field');\n    }\n    return true;\n  }\n  /**\n   * Uses password flow to exchange userName and password for an\n   * access_token. After receiving the access_token, this method\n   * uses it to query the userinfo endpoint in order to get information\n   * about the user in question.\n   *\n   * When using this, make sure that the property oidc is set to false.\n   * Otherwise stricter validations take place that make this operation\n   * fail.\n   *\n   * @param userName\n   * @param password\n   * @param headers Optional additional http-headers.\n   */\n  fetchTokenUsingPasswordFlowAndLoadUserProfile(userName, password, headers = new HttpHeaders()) {\n    return this.fetchTokenUsingPasswordFlow(userName, password, headers).then(() => this.loadUserProfile());\n  }\n  /**\n   * Loads the user profile by accessing the user info endpoint defined by OpenId Connect.\n   *\n   * When using this with OAuth2 password flow, make sure that the property oidc is set to false.\n   * Otherwise stricter validations take place that make this operation fail.\n   */\n  loadUserProfile() {\n    if (!this.hasValidAccessToken()) {\n      throw new Error('Can not load User Profile without access_token');\n    }\n    if (!this.validateUrlForHttps(this.userinfoEndpoint)) {\n      throw new Error(\"userinfoEndpoint must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).\");\n    }\n    return new Promise((resolve, reject) => {\n      const headers = new HttpHeaders().set('Authorization', 'Bearer ' + this.getAccessToken());\n      this.http.get(this.userinfoEndpoint, {\n        headers,\n        observe: 'response',\n        responseType: 'text'\n      }).subscribe(response => {\n        this.debug('userinfo received', JSON.stringify(response));\n        if (response.headers.get('content-type').startsWith('application/json')) {\n          let info = JSON.parse(response.body);\n          const existingClaims = this.getIdentityClaims() || {};\n          if (!this.skipSubjectCheck) {\n            if (this.oidc && (!existingClaims['sub'] || info.sub !== existingClaims['sub'])) {\n              const err = 'if property oidc is true, the received user-id (sub) has to be the user-id ' + 'of the user that has logged in with oidc.\\n' + 'if you are not using oidc but just oauth2 password flow set oidc to false';\n              reject(err);\n              return;\n            }\n          }\n          info = Object.assign({}, existingClaims, info);\n          this._storage.setItem('id_token_claims_obj', JSON.stringify(info));\n          this.eventsSubject.next(new OAuthSuccessEvent('user_profile_loaded'));\n          resolve({\n            info\n          });\n        } else {\n          this.debug('userinfo is not JSON, treating it as JWE/JWS');\n          this.eventsSubject.next(new OAuthSuccessEvent('user_profile_loaded'));\n          resolve(JSON.parse(response.body));\n        }\n      }, err => {\n        this.logger.error('error loading user info', err);\n        this.eventsSubject.next(new OAuthErrorEvent('user_profile_load_error', err));\n        reject(err);\n      });\n    });\n  }\n  /**\n   * Uses password flow to exchange userName and password for an access_token.\n   * @param userName\n   * @param password\n   * @param headers Optional additional http-headers.\n   */\n  fetchTokenUsingPasswordFlow(userName, password, headers = new HttpHeaders()) {\n    const parameters = {\n      username: userName,\n      password: password\n    };\n    return this.fetchTokenUsingGrant('password', parameters, headers);\n  }\n  /**\n   * Uses a custom grant type to retrieve tokens.\n   * @param grantType Grant type.\n   * @param parameters Parameters to pass.\n   * @param headers Optional additional HTTP headers.\n   */\n  fetchTokenUsingGrant(grantType, parameters, headers = new HttpHeaders()) {\n    this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint, 'tokenEndpoint');\n    /**\n     * A `HttpParameterCodec` that uses `encodeURIComponent` and `decodeURIComponent` to\n     * serialize and parse URL parameter keys and values.\n     *\n     * @stable\n     */\n    let params = new HttpParams({\n      encoder: new WebHttpUrlEncodingCodec()\n    }).set('grant_type', grantType).set('scope', this.scope);\n    if (this.useHttpBasicAuth) {\n      const header = btoa(`${this.clientId}:${this.dummyClientSecret}`);\n      headers = headers.set('Authorization', 'Basic ' + header);\n    }\n    if (!this.useHttpBasicAuth) {\n      params = params.set('client_id', this.clientId);\n    }\n    if (!this.useHttpBasicAuth && this.dummyClientSecret) {\n      params = params.set('client_secret', this.dummyClientSecret);\n    }\n    if (this.customQueryParams) {\n      for (const key of Object.getOwnPropertyNames(this.customQueryParams)) {\n        params = params.set(key, this.customQueryParams[key]);\n      }\n    }\n    // set explicit parameters last, to allow overwriting\n    for (const key of Object.keys(parameters)) {\n      params = params.set(key, parameters[key]);\n    }\n    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n    return new Promise((resolve, reject) => {\n      this.http.post(this.tokenEndpoint, params, {\n        headers\n      }).subscribe(tokenResponse => {\n        this.debug('tokenResponse', tokenResponse);\n        this.storeAccessTokenResponse(tokenResponse.access_token, tokenResponse.refresh_token, tokenResponse.expires_in || this.fallbackAccessTokenExpirationTimeInSec, tokenResponse.scope, this.extractRecognizedCustomParameters(tokenResponse));\n        if (this.oidc && tokenResponse.id_token) {\n          this.processIdToken(tokenResponse.id_token, tokenResponse.access_token).then(result => {\n            this.storeIdToken(result);\n            resolve(tokenResponse);\n          });\n        }\n        this.eventsSubject.next(new OAuthSuccessEvent('token_received'));\n        resolve(tokenResponse);\n      }, err => {\n        this.logger.error('Error performing ${grantType} flow', err);\n        this.eventsSubject.next(new OAuthErrorEvent('token_error', err));\n        reject(err);\n      });\n    });\n  }\n  /**\n   * Refreshes the token using a refresh_token.\n   * This does not work for implicit flow, b/c\n   * there is no refresh_token in this flow.\n   * A solution for this is provided by the\n   * method silentRefresh.\n   */\n  refreshToken() {\n    this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint, 'tokenEndpoint');\n    return new Promise((resolve, reject) => {\n      let params = new HttpParams({\n        encoder: new WebHttpUrlEncodingCodec()\n      }).set('grant_type', 'refresh_token').set('scope', this.scope).set('refresh_token', this._storage.getItem('refresh_token'));\n      let headers = new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded');\n      if (this.useHttpBasicAuth) {\n        const header = btoa(`${this.clientId}:${this.dummyClientSecret}`);\n        headers = headers.set('Authorization', 'Basic ' + header);\n      }\n      if (!this.useHttpBasicAuth) {\n        params = params.set('client_id', this.clientId);\n      }\n      if (!this.useHttpBasicAuth && this.dummyClientSecret) {\n        params = params.set('client_secret', this.dummyClientSecret);\n      }\n      if (this.customQueryParams) {\n        for (const key of Object.getOwnPropertyNames(this.customQueryParams)) {\n          params = params.set(key, this.customQueryParams[key]);\n        }\n      }\n      this.http.post(this.tokenEndpoint, params, {\n        headers\n      }).pipe(switchMap(tokenResponse => {\n        if (this.oidc && tokenResponse.id_token) {\n          return from(this.processIdToken(tokenResponse.id_token, tokenResponse.access_token, true)).pipe(tap(result => this.storeIdToken(result)), map(() => tokenResponse));\n        } else {\n          return of(tokenResponse);\n        }\n      })).subscribe(tokenResponse => {\n        this.debug('refresh tokenResponse', tokenResponse);\n        this.storeAccessTokenResponse(tokenResponse.access_token, tokenResponse.refresh_token, tokenResponse.expires_in || this.fallbackAccessTokenExpirationTimeInSec, tokenResponse.scope, this.extractRecognizedCustomParameters(tokenResponse));\n        this.eventsSubject.next(new OAuthSuccessEvent('token_received'));\n        this.eventsSubject.next(new OAuthSuccessEvent('token_refreshed'));\n        resolve(tokenResponse);\n      }, err => {\n        this.logger.error('Error refreshing token', err);\n        this.eventsSubject.next(new OAuthErrorEvent('token_refresh_error', err));\n        reject(err);\n      });\n    });\n  }\n  removeSilentRefreshEventListener() {\n    if (this.silentRefreshPostMessageEventListener) {\n      window.removeEventListener('message', this.silentRefreshPostMessageEventListener);\n      this.silentRefreshPostMessageEventListener = null;\n    }\n  }\n  setupSilentRefreshEventListener() {\n    this.removeSilentRefreshEventListener();\n    this.silentRefreshPostMessageEventListener = e => {\n      const message = this.processMessageEventMessage(e);\n      if (this.checkOrigin && e.origin !== location.origin) {\n        console.error('wrong origin requested silent refresh!');\n      }\n      this.tryLogin({\n        customHashFragment: message,\n        preventClearHashAfterLogin: true,\n        customRedirectUri: this.silentRefreshRedirectUri || this.redirectUri\n      }).catch(err => this.debug('tryLogin during silent refresh failed', err));\n    };\n    window.addEventListener('message', this.silentRefreshPostMessageEventListener);\n  }\n  /**\n   * Performs a silent refresh for implicit flow.\n   * Use this method to get new tokens when/before\n   * the existing tokens expire.\n   */\n  silentRefresh(params = {}, noPrompt = true) {\n    const claims = this.getIdentityClaims() || {};\n    if (this.useIdTokenHintForSilentRefresh && this.hasValidIdToken()) {\n      params['id_token_hint'] = this.getIdToken();\n    }\n    if (!this.validateUrlForHttps(this.loginUrl)) {\n      throw new Error(\"loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).\");\n    }\n    if (typeof this.document === 'undefined') {\n      throw new Error('silent refresh is not supported on this platform');\n    }\n    const existingIframe = this.document.getElementById(this.silentRefreshIFrameName);\n    if (existingIframe) {\n      this.document.body.removeChild(existingIframe);\n    }\n    this.silentRefreshSubject = claims['sub'];\n    const iframe = this.document.createElement('iframe');\n    iframe.id = this.silentRefreshIFrameName;\n    this.setupSilentRefreshEventListener();\n    const redirectUri = this.silentRefreshRedirectUri || this.redirectUri;\n    this.createLoginUrl(null, null, redirectUri, noPrompt, params).then(url => {\n      iframe.setAttribute('src', url);\n      if (!this.silentRefreshShowIFrame) {\n        iframe.style['display'] = 'none';\n      }\n      this.document.body.appendChild(iframe);\n    });\n    const errors = this.events.pipe(filter(e => e instanceof OAuthErrorEvent), first());\n    const success = this.events.pipe(filter(e => e.type === 'token_received'), first());\n    const timeout = of(new OAuthErrorEvent('silent_refresh_timeout', null)).pipe(delay(this.silentRefreshTimeout));\n    return race([errors, success, timeout]).pipe(map(e => {\n      if (e instanceof OAuthErrorEvent) {\n        if (e.type === 'silent_refresh_timeout') {\n          this.eventsSubject.next(e);\n        } else {\n          e = new OAuthErrorEvent('silent_refresh_error', e);\n          this.eventsSubject.next(e);\n        }\n        throw e;\n      } else if (e.type === 'token_received') {\n        e = new OAuthSuccessEvent('silently_refreshed');\n        this.eventsSubject.next(e);\n      }\n      return e;\n    })).toPromise();\n  }\n  /**\n   * This method exists for backwards compatibility.\n   * {@link OAuthService#initLoginFlowInPopup} handles both code\n   * and implicit flows.\n   */\n  initImplicitFlowInPopup(options) {\n    return this.initLoginFlowInPopup(options);\n  }\n  initLoginFlowInPopup(options) {\n    options = options || {};\n    return this.createLoginUrl(null, null, this.silentRefreshRedirectUri, false, {\n      display: 'popup'\n    }).then(url => {\n      return new Promise((resolve, reject) => {\n        /**\n         * Error handling section\n         */\n        const checkForPopupClosedInterval = 500;\n        let windowRef = null;\n        // If we got no window reference we open a window\n        // else we are using the window already opened\n        if (!options.windowRef) {\n          windowRef = window.open(url, 'ngx-oauth2-oidc-login', this.calculatePopupFeatures(options));\n        } else if (options.windowRef && !options.windowRef.closed) {\n          windowRef = options.windowRef;\n          windowRef.location.href = url;\n        }\n        let checkForPopupClosedTimer;\n        const tryLogin = hash => {\n          this.tryLogin({\n            customHashFragment: hash,\n            preventClearHashAfterLogin: true,\n            customRedirectUri: this.silentRefreshRedirectUri\n          }).then(() => {\n            cleanup();\n            resolve(true);\n          }, err => {\n            cleanup();\n            reject(err);\n          });\n        };\n        const checkForPopupClosed = () => {\n          if (!windowRef || windowRef.closed) {\n            cleanup();\n            reject(new OAuthErrorEvent('popup_closed', {}));\n          }\n        };\n        if (!windowRef) {\n          reject(new OAuthErrorEvent('popup_blocked', {}));\n        } else {\n          checkForPopupClosedTimer = window.setInterval(checkForPopupClosed, checkForPopupClosedInterval);\n        }\n        const cleanup = () => {\n          window.clearInterval(checkForPopupClosedTimer);\n          window.removeEventListener('storage', storageListener);\n          window.removeEventListener('message', listener);\n          if (windowRef !== null) {\n            windowRef.close();\n          }\n          windowRef = null;\n        };\n        const listener = e => {\n          const message = this.processMessageEventMessage(e);\n          if (message && message !== null) {\n            window.removeEventListener('storage', storageListener);\n            tryLogin(message);\n          } else {\n            console.log('false event firing');\n          }\n        };\n        const storageListener = event => {\n          if (event.key === 'auth_hash') {\n            window.removeEventListener('message', listener);\n            tryLogin(event.newValue);\n          }\n        };\n        window.addEventListener('message', listener);\n        window.addEventListener('storage', storageListener);\n      });\n    });\n  }\n  calculatePopupFeatures(options) {\n    // Specify an static height and width and calculate centered position\n    const height = options.height || 470;\n    const width = options.width || 500;\n    const left = window.screenLeft + (window.outerWidth - width) / 2;\n    const top = window.screenTop + (window.outerHeight - height) / 2;\n    return `location=no,toolbar=no,width=${width},height=${height},top=${top},left=${left}`;\n  }\n  processMessageEventMessage(e) {\n    let expectedPrefix = '#';\n    if (this.silentRefreshMessagePrefix) {\n      expectedPrefix += this.silentRefreshMessagePrefix;\n    }\n    if (!e || !e.data || typeof e.data !== 'string') {\n      return;\n    }\n    const prefixedMessage = e.data;\n    if (!prefixedMessage.startsWith(expectedPrefix)) {\n      return;\n    }\n    return '#' + prefixedMessage.substr(expectedPrefix.length);\n  }\n  canPerformSessionCheck() {\n    if (!this.sessionChecksEnabled) {\n      return false;\n    }\n    if (!this.sessionCheckIFrameUrl) {\n      console.warn('sessionChecksEnabled is activated but there is no sessionCheckIFrameUrl');\n      return false;\n    }\n    const sessionState = this.getSessionState();\n    if (!sessionState) {\n      console.warn('sessionChecksEnabled is activated but there is no session_state');\n      return false;\n    }\n    if (typeof this.document === 'undefined') {\n      return false;\n    }\n    return true;\n  }\n  setupSessionCheckEventListener() {\n    this.removeSessionCheckEventListener();\n    this.sessionCheckEventListener = e => {\n      const origin = e.origin.toLowerCase();\n      const issuer = this.issuer.toLowerCase();\n      this.debug('sessionCheckEventListener');\n      if (!issuer.startsWith(origin)) {\n        this.debug('sessionCheckEventListener', 'wrong origin', origin, 'expected', issuer, 'event', e);\n        return;\n      }\n      // only run in Angular zone if it is 'changed' or 'error'\n      switch (e.data) {\n        case 'unchanged':\n          this.ngZone.run(() => {\n            this.handleSessionUnchanged();\n          });\n          break;\n        case 'changed':\n          this.ngZone.run(() => {\n            this.handleSessionChange();\n          });\n          break;\n        case 'error':\n          this.ngZone.run(() => {\n            this.handleSessionError();\n          });\n          break;\n      }\n      this.debug('got info from session check inframe', e);\n    };\n    // prevent Angular from refreshing the view on every message (runs in intervals)\n    this.ngZone.runOutsideAngular(() => {\n      window.addEventListener('message', this.sessionCheckEventListener);\n    });\n  }\n  handleSessionUnchanged() {\n    this.debug('session check', 'session unchanged');\n    this.eventsSubject.next(new OAuthInfoEvent('session_unchanged'));\n  }\n  handleSessionChange() {\n    this.eventsSubject.next(new OAuthInfoEvent('session_changed'));\n    this.stopSessionCheckTimer();\n    if (!this.useSilentRefresh && this.responseType === 'code') {\n      this.refreshToken().then(() => {\n        this.debug('token refresh after session change worked');\n      }).catch(() => {\n        this.debug('token refresh did not work after session changed');\n        this.eventsSubject.next(new OAuthInfoEvent('session_terminated'));\n        this.logOut(true);\n      });\n    } else if (this.silentRefreshRedirectUri) {\n      this.silentRefresh().catch(() => this.debug('silent refresh failed after session changed'));\n      this.waitForSilentRefreshAfterSessionChange();\n    } else {\n      this.eventsSubject.next(new OAuthInfoEvent('session_terminated'));\n      this.logOut(true);\n    }\n  }\n  waitForSilentRefreshAfterSessionChange() {\n    this.events.pipe(filter(e => e.type === 'silently_refreshed' || e.type === 'silent_refresh_timeout' || e.type === 'silent_refresh_error'), first()).subscribe(e => {\n      if (e.type !== 'silently_refreshed') {\n        this.debug('silent refresh did not work after session changed');\n        this.eventsSubject.next(new OAuthInfoEvent('session_terminated'));\n        this.logOut(true);\n      }\n    });\n  }\n  handleSessionError() {\n    this.stopSessionCheckTimer();\n    this.eventsSubject.next(new OAuthInfoEvent('session_error'));\n  }\n  removeSessionCheckEventListener() {\n    if (this.sessionCheckEventListener) {\n      window.removeEventListener('message', this.sessionCheckEventListener);\n      this.sessionCheckEventListener = null;\n    }\n  }\n  initSessionCheck() {\n    if (!this.canPerformSessionCheck()) {\n      return;\n    }\n    const existingIframe = this.document.getElementById(this.sessionCheckIFrameName);\n    if (existingIframe) {\n      this.document.body.removeChild(existingIframe);\n    }\n    const iframe = this.document.createElement('iframe');\n    iframe.id = this.sessionCheckIFrameName;\n    this.setupSessionCheckEventListener();\n    const url = this.sessionCheckIFrameUrl;\n    iframe.setAttribute('src', url);\n    iframe.style.display = 'none';\n    this.document.body.appendChild(iframe);\n    this.startSessionCheckTimer();\n  }\n  startSessionCheckTimer() {\n    this.stopSessionCheckTimer();\n    this.ngZone.runOutsideAngular(() => {\n      this.sessionCheckTimer = setInterval(this.checkSession.bind(this), this.sessionCheckIntervall);\n    });\n  }\n  stopSessionCheckTimer() {\n    if (this.sessionCheckTimer) {\n      clearInterval(this.sessionCheckTimer);\n      this.sessionCheckTimer = null;\n    }\n  }\n  checkSession() {\n    const iframe = this.document.getElementById(this.sessionCheckIFrameName);\n    if (!iframe) {\n      this.logger.warn('checkSession did not find iframe', this.sessionCheckIFrameName);\n    }\n    const sessionState = this.getSessionState();\n    if (!sessionState) {\n      this.stopSessionCheckTimer();\n    }\n    const message = this.clientId + ' ' + sessionState;\n    iframe.contentWindow.postMessage(message, this.issuer);\n  }\n  async createLoginUrl(state = '', loginHint = '', customRedirectUri = '', noPrompt = false, params = {}) {\n    const that = this; // eslint-disable-line @typescript-eslint/no-this-alias\n    let redirectUri;\n    if (customRedirectUri) {\n      redirectUri = customRedirectUri;\n    } else {\n      redirectUri = this.redirectUri;\n    }\n    const nonce = await this.createAndSaveNonce();\n    if (state) {\n      state = nonce + this.config.nonceStateSeparator + encodeURIComponent(state);\n    } else {\n      state = nonce;\n    }\n    if (!this.requestAccessToken && !this.oidc) {\n      throw new Error('Either requestAccessToken or oidc or both must be true');\n    }\n    if (this.config.responseType) {\n      this.responseType = this.config.responseType;\n    } else {\n      if (this.oidc && this.requestAccessToken) {\n        this.responseType = 'id_token token';\n      } else if (this.oidc && !this.requestAccessToken) {\n        this.responseType = 'id_token';\n      } else {\n        this.responseType = 'token';\n      }\n    }\n    const seperationChar = that.loginUrl.indexOf('?') > -1 ? '&' : '?';\n    let scope = that.scope;\n    if (this.oidc && !scope.match(/(^|\\s)openid($|\\s)/)) {\n      scope = 'openid ' + scope;\n    }\n    let url = that.loginUrl + seperationChar + 'response_type=' + encodeURIComponent(that.responseType) + '&client_id=' + encodeURIComponent(that.clientId) + '&state=' + encodeURIComponent(state) + '&redirect_uri=' + encodeURIComponent(redirectUri) + '&scope=' + encodeURIComponent(scope);\n    if (this.responseType.includes('code') && !this.disablePKCE) {\n      const [challenge, verifier] = await this.createChallangeVerifierPairForPKCE();\n      if (this.saveNoncesInLocalStorage && typeof window['localStorage'] !== 'undefined') {\n        localStorage.setItem('PKCE_verifier', verifier);\n      } else {\n        this._storage.setItem('PKCE_verifier', verifier);\n      }\n      url += '&code_challenge=' + challenge;\n      url += '&code_challenge_method=S256';\n    }\n    if (loginHint) {\n      url += '&login_hint=' + encodeURIComponent(loginHint);\n    }\n    if (that.resource) {\n      url += '&resource=' + encodeURIComponent(that.resource);\n    }\n    if (that.oidc) {\n      url += '&nonce=' + encodeURIComponent(nonce);\n    }\n    if (noPrompt) {\n      url += '&prompt=none';\n    }\n    for (const key of Object.keys(params)) {\n      url += '&' + encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);\n    }\n    if (this.customQueryParams) {\n      for (const key of Object.getOwnPropertyNames(this.customQueryParams)) {\n        url += '&' + key + '=' + encodeURIComponent(this.customQueryParams[key]);\n      }\n    }\n    return url;\n  }\n  initImplicitFlowInternal(additionalState = '', params = '') {\n    if (this.inImplicitFlow) {\n      return;\n    }\n    this.inImplicitFlow = true;\n    if (!this.validateUrlForHttps(this.loginUrl)) {\n      throw new Error(\"loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).\");\n    }\n    let addParams = {};\n    let loginHint = null;\n    if (typeof params === 'string') {\n      loginHint = params;\n    } else if (typeof params === 'object') {\n      addParams = params;\n    }\n    this.createLoginUrl(additionalState, loginHint, null, false, addParams).then(this.config.openUri).catch(error => {\n      console.error('Error in initImplicitFlow', error);\n      this.inImplicitFlow = false;\n    });\n  }\n  /**\n   * Starts the implicit flow and redirects to user to\n   * the auth servers' login url.\n   *\n   * @param additionalState Optional state that is passed around.\n   *  You'll find this state in the property `state` after `tryLogin` logged in the user.\n   * @param params Hash with additional parameter. If it is a string, it is used for the\n   *               parameter loginHint (for the sake of compatibility with former versions)\n   */\n  initImplicitFlow(additionalState = '', params = '') {\n    if (this.loginUrl !== '') {\n      this.initImplicitFlowInternal(additionalState, params);\n    } else {\n      this.events.pipe(filter(e => e.type === 'discovery_document_loaded')).subscribe(() => this.initImplicitFlowInternal(additionalState, params));\n    }\n  }\n  /**\n   * Reset current implicit flow\n   *\n   * @description This method allows resetting the current implict flow in order to be initialized again.\n   */\n  resetImplicitFlow() {\n    this.inImplicitFlow = false;\n  }\n  callOnTokenReceivedIfExists(options) {\n    const that = this; // eslint-disable-line @typescript-eslint/no-this-alias\n    if (options.onTokenReceived) {\n      const tokenParams = {\n        idClaims: that.getIdentityClaims(),\n        idToken: that.getIdToken(),\n        accessToken: that.getAccessToken(),\n        state: that.state\n      };\n      options.onTokenReceived(tokenParams);\n    }\n  }\n  storeAccessTokenResponse(accessToken, refreshToken, expiresIn, grantedScopes, customParameters) {\n    this._storage.setItem('access_token', accessToken);\n    if (grantedScopes && !Array.isArray(grantedScopes)) {\n      this._storage.setItem('granted_scopes', JSON.stringify(grantedScopes.split(' ')));\n    } else if (grantedScopes && Array.isArray(grantedScopes)) {\n      this._storage.setItem('granted_scopes', JSON.stringify(grantedScopes));\n    }\n    this._storage.setItem('access_token_stored_at', '' + this.dateTimeService.now());\n    if (expiresIn) {\n      const expiresInMilliSeconds = expiresIn * 1000;\n      const now = this.dateTimeService.new();\n      const expiresAt = now.getTime() + expiresInMilliSeconds;\n      this._storage.setItem('expires_at', '' + expiresAt);\n    }\n    if (refreshToken) {\n      this._storage.setItem('refresh_token', refreshToken);\n    }\n    if (customParameters) {\n      customParameters.forEach((value, key) => {\n        this._storage.setItem(key, value);\n      });\n    }\n  }\n  /**\n   * Delegates to tryLoginImplicitFlow for the sake of competability\n   * @param options Optional options.\n   */\n  tryLogin(options = null) {\n    if (this.config.responseType === 'code') {\n      return this.tryLoginCodeFlow(options).then(() => true);\n    } else {\n      return this.tryLoginImplicitFlow(options);\n    }\n  }\n  parseQueryString(queryString) {\n    if (!queryString || queryString.length === 0) {\n      return {};\n    }\n    if (queryString.charAt(0) === '?') {\n      queryString = queryString.substr(1);\n    }\n    return this.urlHelper.parseQueryString(queryString);\n  }\n  async tryLoginCodeFlow(options = null) {\n    options = options || {};\n    const querySource = options.customHashFragment ? options.customHashFragment.substring(1) : window.location.search;\n    const parts = this.getCodePartsFromUrl(querySource);\n    const code = parts['code'];\n    const state = parts['state'];\n    const sessionState = parts['session_state'];\n    if (!options.preventClearHashAfterLogin) {\n      const href = location.origin + location.pathname + location.search.replace(/code=[^&$]*/, '').replace(/scope=[^&$]*/, '').replace(/state=[^&$]*/, '').replace(/session_state=[^&$]*/, '').replace(/^\\?&/, '?').replace(/&$/, '').replace(/^\\?$/, '').replace(/&+/g, '&').replace(/\\?&/, '?').replace(/\\?$/, '') + location.hash;\n      history.replaceState(null, window.name, href);\n    }\n    const [nonceInState, userState] = this.parseState(state);\n    this.state = userState;\n    if (parts['error']) {\n      this.debug('error trying to login');\n      this.handleLoginError(options, parts);\n      const err = new OAuthErrorEvent('code_error', {}, parts);\n      this.eventsSubject.next(err);\n      return Promise.reject(err);\n    }\n    if (!options.disableNonceCheck) {\n      if (!nonceInState) {\n        this.saveRequestedRoute();\n        return Promise.resolve();\n      }\n      if (!options.disableOAuth2StateCheck) {\n        const success = this.validateNonce(nonceInState);\n        if (!success) {\n          const event = new OAuthErrorEvent('invalid_nonce_in_state', null);\n          this.eventsSubject.next(event);\n          return Promise.reject(event);\n        }\n      }\n    }\n    this.storeSessionState(sessionState);\n    if (code) {\n      await this.getTokenFromCode(code, options);\n      this.restoreRequestedRoute();\n      return Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  saveRequestedRoute() {\n    if (this.config.preserveRequestedRoute) {\n      this._storage.setItem('requested_route', window.location.pathname + window.location.search);\n    }\n  }\n  restoreRequestedRoute() {\n    const requestedRoute = this._storage.getItem('requested_route');\n    if (requestedRoute) {\n      history.replaceState(null, '', window.location.origin + requestedRoute);\n    }\n  }\n  /**\n   * Retrieve the returned auth code from the redirect uri that has been called.\n   * If required also check hash, as we could use hash location strategy.\n   */\n  getCodePartsFromUrl(queryString) {\n    if (!queryString || queryString.length === 0) {\n      return this.urlHelper.getHashFragmentParams();\n    }\n    // normalize query string\n    if (queryString.charAt(0) === '?') {\n      queryString = queryString.substr(1);\n    }\n    return this.urlHelper.parseQueryString(queryString);\n  }\n  /**\n   * Get token using an intermediate code. Works for the Authorization Code flow.\n   */\n  getTokenFromCode(code, options) {\n    let params = new HttpParams({\n      encoder: new WebHttpUrlEncodingCodec()\n    }).set('grant_type', 'authorization_code').set('code', code).set('redirect_uri', options.customRedirectUri || this.redirectUri);\n    if (!this.disablePKCE) {\n      let PKCEVerifier;\n      if (this.saveNoncesInLocalStorage && typeof window['localStorage'] !== 'undefined') {\n        PKCEVerifier = localStorage.getItem('PKCE_verifier');\n      } else {\n        PKCEVerifier = this._storage.getItem('PKCE_verifier');\n      }\n      if (!PKCEVerifier) {\n        console.warn('No PKCE verifier found in oauth storage!');\n      } else {\n        params = params.set('code_verifier', PKCEVerifier);\n      }\n    }\n    return this.fetchAndProcessToken(params, options);\n  }\n  fetchAndProcessToken(params, options) {\n    options = options || {};\n    this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint, 'tokenEndpoint');\n    let headers = new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded');\n    if (this.useHttpBasicAuth) {\n      const header = btoa(`${this.clientId}:${this.dummyClientSecret}`);\n      headers = headers.set('Authorization', 'Basic ' + header);\n    }\n    if (!this.useHttpBasicAuth) {\n      params = params.set('client_id', this.clientId);\n    }\n    if (!this.useHttpBasicAuth && this.dummyClientSecret) {\n      params = params.set('client_secret', this.dummyClientSecret);\n    }\n    return new Promise((resolve, reject) => {\n      if (this.customQueryParams) {\n        for (const key of Object.getOwnPropertyNames(this.customQueryParams)) {\n          params = params.set(key, this.customQueryParams[key]);\n        }\n      }\n      this.http.post(this.tokenEndpoint, params, {\n        headers\n      }).subscribe(tokenResponse => {\n        this.debug('refresh tokenResponse', tokenResponse);\n        this.storeAccessTokenResponse(tokenResponse.access_token, tokenResponse.refresh_token, tokenResponse.expires_in || this.fallbackAccessTokenExpirationTimeInSec, tokenResponse.scope, this.extractRecognizedCustomParameters(tokenResponse));\n        if (this.oidc && tokenResponse.id_token) {\n          this.processIdToken(tokenResponse.id_token, tokenResponse.access_token, options.disableNonceCheck).then(result => {\n            this.storeIdToken(result);\n            this.eventsSubject.next(new OAuthSuccessEvent('token_received'));\n            this.eventsSubject.next(new OAuthSuccessEvent('token_refreshed'));\n            resolve(tokenResponse);\n          }).catch(reason => {\n            this.eventsSubject.next(new OAuthErrorEvent('token_validation_error', reason));\n            console.error('Error validating tokens');\n            console.error(reason);\n            reject(reason);\n          });\n        } else {\n          this.eventsSubject.next(new OAuthSuccessEvent('token_received'));\n          this.eventsSubject.next(new OAuthSuccessEvent('token_refreshed'));\n          resolve(tokenResponse);\n        }\n      }, err => {\n        console.error('Error getting token', err);\n        this.eventsSubject.next(new OAuthErrorEvent('token_refresh_error', err));\n        reject(err);\n      });\n    });\n  }\n  /**\n   * Checks whether there are tokens in the hash fragment\n   * as a result of the implicit flow. These tokens are\n   * parsed, validated and used to sign the user in to the\n   * current client.\n   *\n   * @param options Optional options.\n   */\n  tryLoginImplicitFlow(options = null) {\n    options = options || {};\n    let parts;\n    if (options.customHashFragment) {\n      parts = this.urlHelper.getHashFragmentParams(options.customHashFragment);\n    } else {\n      parts = this.urlHelper.getHashFragmentParams();\n    }\n    this.debug('parsed url', parts);\n    const state = parts['state'];\n    const [nonceInState, userState] = this.parseState(state);\n    this.state = userState;\n    if (parts['error']) {\n      this.debug('error trying to login');\n      this.handleLoginError(options, parts);\n      const err = new OAuthErrorEvent('token_error', {}, parts);\n      this.eventsSubject.next(err);\n      return Promise.reject(err);\n    }\n    const accessToken = parts['access_token'];\n    const idToken = parts['id_token'];\n    const sessionState = parts['session_state'];\n    const grantedScopes = parts['scope'];\n    if (!this.requestAccessToken && !this.oidc) {\n      return Promise.reject('Either requestAccessToken or oidc (or both) must be true.');\n    }\n    if (this.requestAccessToken && !accessToken) {\n      return Promise.resolve(false);\n    }\n    if (this.requestAccessToken && !options.disableOAuth2StateCheck && !state) {\n      return Promise.resolve(false);\n    }\n    if (this.oidc && !idToken) {\n      return Promise.resolve(false);\n    }\n    if (this.sessionChecksEnabled && !sessionState) {\n      this.logger.warn('session checks (Session Status Change Notification) ' + 'were activated in the configuration but the id_token ' + 'does not contain a session_state claim');\n    }\n    if (this.requestAccessToken && !options.disableNonceCheck) {\n      const success = this.validateNonce(nonceInState);\n      if (!success) {\n        const event = new OAuthErrorEvent('invalid_nonce_in_state', null);\n        this.eventsSubject.next(event);\n        return Promise.reject(event);\n      }\n    }\n    if (this.requestAccessToken) {\n      this.storeAccessTokenResponse(accessToken, null, parts['expires_in'] || this.fallbackAccessTokenExpirationTimeInSec, grantedScopes);\n    }\n    if (!this.oidc) {\n      this.eventsSubject.next(new OAuthSuccessEvent('token_received'));\n      if (this.clearHashAfterLogin && !options.preventClearHashAfterLogin) {\n        this.clearLocationHash();\n      }\n      this.callOnTokenReceivedIfExists(options);\n      return Promise.resolve(true);\n    }\n    return this.processIdToken(idToken, accessToken, options.disableNonceCheck).then(result => {\n      if (options.validationHandler) {\n        return options.validationHandler({\n          accessToken: accessToken,\n          idClaims: result.idTokenClaims,\n          idToken: result.idToken,\n          state: state\n        }).then(() => result);\n      }\n      return result;\n    }).then(result => {\n      this.storeIdToken(result);\n      this.storeSessionState(sessionState);\n      if (this.clearHashAfterLogin && !options.preventClearHashAfterLogin) {\n        this.clearLocationHash();\n      }\n      this.eventsSubject.next(new OAuthSuccessEvent('token_received'));\n      this.callOnTokenReceivedIfExists(options);\n      this.inImplicitFlow = false;\n      return true;\n    }).catch(reason => {\n      this.eventsSubject.next(new OAuthErrorEvent('token_validation_error', reason));\n      this.logger.error('Error validating tokens');\n      this.logger.error(reason);\n      return Promise.reject(reason);\n    });\n  }\n  parseState(state) {\n    let nonce = state;\n    let userState = '';\n    if (state) {\n      const idx = state.indexOf(this.config.nonceStateSeparator);\n      if (idx > -1) {\n        nonce = state.substr(0, idx);\n        userState = state.substr(idx + this.config.nonceStateSeparator.length);\n      }\n    }\n    return [nonce, userState];\n  }\n  validateNonce(nonceInState) {\n    let savedNonce;\n    if (this.saveNoncesInLocalStorage && typeof window['localStorage'] !== 'undefined') {\n      savedNonce = localStorage.getItem('nonce');\n    } else {\n      savedNonce = this._storage.getItem('nonce');\n    }\n    if (savedNonce !== nonceInState) {\n      const err = 'Validating access_token failed, wrong state/nonce.';\n      console.error(err, savedNonce, nonceInState);\n      return false;\n    }\n    return true;\n  }\n  storeIdToken(idToken) {\n    this._storage.setItem('id_token', idToken.idToken);\n    this._storage.setItem('id_token_claims_obj', idToken.idTokenClaimsJson);\n    this._storage.setItem('id_token_expires_at', '' + idToken.idTokenExpiresAt);\n    this._storage.setItem('id_token_stored_at', '' + this.dateTimeService.now());\n  }\n  storeSessionState(sessionState) {\n    this._storage.setItem('session_state', sessionState);\n  }\n  getSessionState() {\n    return this._storage.getItem('session_state');\n  }\n  handleLoginError(options, parts) {\n    if (options.onLoginError) {\n      options.onLoginError(parts);\n    }\n    if (this.clearHashAfterLogin && !options.preventClearHashAfterLogin) {\n      this.clearLocationHash();\n    }\n  }\n  getClockSkewInMsec(defaultSkewMsc = 600000) {\n    if (!this.clockSkewInSec && this.clockSkewInSec !== 0) {\n      return defaultSkewMsc;\n    }\n    return this.clockSkewInSec * 1000;\n  }\n  /**\n   * @ignore\n   */\n  processIdToken(idToken, accessToken, skipNonceCheck = false) {\n    const tokenParts = idToken.split('.');\n    const headerBase64 = this.padBase64(tokenParts[0]);\n    const headerJson = b64DecodeUnicode(headerBase64);\n    const header = JSON.parse(headerJson);\n    const claimsBase64 = this.padBase64(tokenParts[1]);\n    const claimsJson = b64DecodeUnicode(claimsBase64);\n    const claims = JSON.parse(claimsJson);\n    let savedNonce;\n    if (this.saveNoncesInLocalStorage && typeof window['localStorage'] !== 'undefined') {\n      savedNonce = localStorage.getItem('nonce');\n    } else {\n      savedNonce = this._storage.getItem('nonce');\n    }\n    if (Array.isArray(claims.aud)) {\n      if (claims.aud.every(v => v !== this.clientId)) {\n        const err = 'Wrong audience: ' + claims.aud.join(',');\n        this.logger.warn(err);\n        return Promise.reject(err);\n      }\n    } else {\n      if (claims.aud !== this.clientId) {\n        const err = 'Wrong audience: ' + claims.aud;\n        this.logger.warn(err);\n        return Promise.reject(err);\n      }\n    }\n    if (!claims.sub) {\n      const err = 'No sub claim in id_token';\n      this.logger.warn(err);\n      return Promise.reject(err);\n    }\n    /* For now, we only check whether the sub against\n     * silentRefreshSubject when sessionChecksEnabled is on\n     * We will reconsider in a later version to do this\n     * in every other case too.\n     */\n    if (this.sessionChecksEnabled && this.silentRefreshSubject && this.silentRefreshSubject !== claims['sub']) {\n      const err = 'After refreshing, we got an id_token for another user (sub). ' + `Expected sub: ${this.silentRefreshSubject}, received sub: ${claims['sub']}`;\n      this.logger.warn(err);\n      return Promise.reject(err);\n    }\n    if (!claims.iat) {\n      const err = 'No iat claim in id_token';\n      this.logger.warn(err);\n      return Promise.reject(err);\n    }\n    if (!this.skipIssuerCheck && claims.iss !== this.issuer) {\n      const err = 'Wrong issuer: ' + claims.iss;\n      this.logger.warn(err);\n      return Promise.reject(err);\n    }\n    if (!skipNonceCheck && claims.nonce !== savedNonce) {\n      const err = 'Wrong nonce: ' + claims.nonce;\n      this.logger.warn(err);\n      return Promise.reject(err);\n    }\n    // at_hash is not applicable to authorization code flow\n    // addressing https://github.com/manfredsteyer/angular-oauth2-oidc/issues/661\n    // i.e. Based on spec the at_hash check is only true for implicit code flow on Ping Federate\n    // https://www.pingidentity.com/developer/en/resources/openid-connect-developers-guide.html\n    if (Object.prototype.hasOwnProperty.call(this, 'responseType') && (this.responseType === 'code' || this.responseType === 'id_token')) {\n      this.disableAtHashCheck = true;\n    }\n    if (!this.disableAtHashCheck && this.requestAccessToken && !claims['at_hash']) {\n      const err = 'An at_hash is needed!';\n      this.logger.warn(err);\n      return Promise.reject(err);\n    }\n    const now = this.dateTimeService.now();\n    const issuedAtMSec = claims.iat * 1000;\n    const expiresAtMSec = claims.exp * 1000;\n    const clockSkewInMSec = this.getClockSkewInMsec(); // (this.getClockSkewInMsec() || 600) * 1000;\n    if (issuedAtMSec - clockSkewInMSec >= now || expiresAtMSec + clockSkewInMSec - this.decreaseExpirationBySec <= now) {\n      const err = 'Token has expired';\n      console.error(err);\n      console.error({\n        now: now,\n        issuedAtMSec: issuedAtMSec,\n        expiresAtMSec: expiresAtMSec\n      });\n      return Promise.reject(err);\n    }\n    const validationParams = {\n      accessToken: accessToken,\n      idToken: idToken,\n      jwks: this.jwks,\n      idTokenClaims: claims,\n      idTokenHeader: header,\n      loadKeys: () => this.loadJwks()\n    };\n    if (this.disableAtHashCheck) {\n      return this.checkSignature(validationParams).then(() => {\n        const result = {\n          idToken: idToken,\n          idTokenClaims: claims,\n          idTokenClaimsJson: claimsJson,\n          idTokenHeader: header,\n          idTokenHeaderJson: headerJson,\n          idTokenExpiresAt: expiresAtMSec\n        };\n        return result;\n      });\n    }\n    return this.checkAtHash(validationParams).then(atHashValid => {\n      if (!this.disableAtHashCheck && this.requestAccessToken && !atHashValid) {\n        const err = 'Wrong at_hash';\n        this.logger.warn(err);\n        return Promise.reject(err);\n      }\n      return this.checkSignature(validationParams).then(() => {\n        const atHashCheckEnabled = !this.disableAtHashCheck;\n        const result = {\n          idToken: idToken,\n          idTokenClaims: claims,\n          idTokenClaimsJson: claimsJson,\n          idTokenHeader: header,\n          idTokenHeaderJson: headerJson,\n          idTokenExpiresAt: expiresAtMSec\n        };\n        if (atHashCheckEnabled) {\n          return this.checkAtHash(validationParams).then(atHashValid => {\n            if (this.requestAccessToken && !atHashValid) {\n              const err = 'Wrong at_hash';\n              this.logger.warn(err);\n              return Promise.reject(err);\n            } else {\n              return result;\n            }\n          });\n        } else {\n          return result;\n        }\n      });\n    });\n  }\n  /**\n   * Returns the received claims about the user.\n   */\n  getIdentityClaims() {\n    const claims = this._storage.getItem('id_token_claims_obj');\n    if (!claims) {\n      return null;\n    }\n    return JSON.parse(claims);\n  }\n  /**\n   * Returns the granted scopes from the server.\n   */\n  getGrantedScopes() {\n    const scopes = this._storage.getItem('granted_scopes');\n    if (!scopes) {\n      return null;\n    }\n    return JSON.parse(scopes);\n  }\n  /**\n   * Returns the current id_token.\n   */\n  getIdToken() {\n    return this._storage ? this._storage.getItem('id_token') : null;\n  }\n  padBase64(base64data) {\n    while (base64data.length % 4 !== 0) {\n      base64data += '=';\n    }\n    return base64data;\n  }\n  /**\n   * Returns the current access_token.\n   */\n  getAccessToken() {\n    return this._storage ? this._storage.getItem('access_token') : null;\n  }\n  getRefreshToken() {\n    return this._storage ? this._storage.getItem('refresh_token') : null;\n  }\n  /**\n   * Returns the expiration date of the access_token\n   * as milliseconds since 1970.\n   */\n  getAccessTokenExpiration() {\n    if (!this._storage.getItem('expires_at')) {\n      return null;\n    }\n    return parseInt(this._storage.getItem('expires_at'), 10);\n  }\n  getAccessTokenStoredAt() {\n    return parseInt(this._storage.getItem('access_token_stored_at'), 10);\n  }\n  getIdTokenStoredAt() {\n    return parseInt(this._storage.getItem('id_token_stored_at'), 10);\n  }\n  /**\n   * Returns the expiration date of the id_token\n   * as milliseconds since 1970.\n   */\n  getIdTokenExpiration() {\n    if (!this._storage.getItem('id_token_expires_at')) {\n      return null;\n    }\n    return parseInt(this._storage.getItem('id_token_expires_at'), 10);\n  }\n  /**\n   * Checkes, whether there is a valid access_token.\n   */\n  hasValidAccessToken() {\n    if (this.getAccessToken()) {\n      const expiresAt = this._storage.getItem('expires_at');\n      const now = this.dateTimeService.new();\n      if (expiresAt && parseInt(expiresAt, 10) - this.decreaseExpirationBySec < now.getTime() - this.getClockSkewInMsec()) {\n        return false;\n      }\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Checks whether there is a valid id_token.\n   */\n  hasValidIdToken() {\n    if (this.getIdToken()) {\n      const expiresAt = this._storage.getItem('id_token_expires_at');\n      const now = this.dateTimeService.new();\n      if (expiresAt && parseInt(expiresAt, 10) - this.decreaseExpirationBySec < now.getTime() - this.getClockSkewInMsec()) {\n        return false;\n      }\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Retrieve a saved custom property of the TokenReponse object. Only if predefined in authconfig.\n   */\n  getCustomTokenResponseProperty(requestedProperty) {\n    return this._storage && this.config.customTokenParameters && this.config.customTokenParameters.indexOf(requestedProperty) >= 0 && this._storage.getItem(requestedProperty) !== null ? JSON.parse(this._storage.getItem(requestedProperty)) : null;\n  }\n  /**\n   * Returns the auth-header that can be used\n   * to transmit the access_token to a service\n   */\n  authorizationHeader() {\n    return 'Bearer ' + this.getAccessToken();\n  }\n  logOut(customParameters = {}, state = '') {\n    let noRedirectToLogoutUrl = false;\n    if (typeof customParameters === 'boolean') {\n      noRedirectToLogoutUrl = customParameters;\n      customParameters = {};\n    }\n    const id_token = this.getIdToken();\n    this._storage.removeItem('access_token');\n    this._storage.removeItem('id_token');\n    this._storage.removeItem('refresh_token');\n    if (this.saveNoncesInLocalStorage) {\n      localStorage.removeItem('nonce');\n      localStorage.removeItem('PKCE_verifier');\n    } else {\n      this._storage.removeItem('nonce');\n      this._storage.removeItem('PKCE_verifier');\n    }\n    this._storage.removeItem('expires_at');\n    this._storage.removeItem('id_token_claims_obj');\n    this._storage.removeItem('id_token_expires_at');\n    this._storage.removeItem('id_token_stored_at');\n    this._storage.removeItem('access_token_stored_at');\n    this._storage.removeItem('granted_scopes');\n    this._storage.removeItem('session_state');\n    if (this.config.customTokenParameters) {\n      this.config.customTokenParameters.forEach(customParam => this._storage.removeItem(customParam));\n    }\n    this.silentRefreshSubject = null;\n    this.eventsSubject.next(new OAuthInfoEvent('logout'));\n    if (!this.logoutUrl) {\n      return;\n    }\n    if (noRedirectToLogoutUrl) {\n      return;\n    }\n    // if (!id_token && !this.postLogoutRedirectUri) {\n    //   return;\n    // }\n    let logoutUrl;\n    if (!this.validateUrlForHttps(this.logoutUrl)) {\n      throw new Error(\"logoutUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).\");\n    }\n    // For backward compatibility\n    if (this.logoutUrl.indexOf('{{') > -1) {\n      logoutUrl = this.logoutUrl.replace(/\\{\\{id_token\\}\\}/, encodeURIComponent(id_token)).replace(/\\{\\{client_id\\}\\}/, encodeURIComponent(this.clientId));\n    } else {\n      let params = new HttpParams({\n        encoder: new WebHttpUrlEncodingCodec()\n      });\n      if (id_token) {\n        params = params.set('id_token_hint', id_token);\n      }\n      const postLogoutUrl = this.postLogoutRedirectUri || this.redirectUriAsPostLogoutRedirectUriFallback && this.redirectUri || '';\n      if (postLogoutUrl) {\n        params = params.set('post_logout_redirect_uri', postLogoutUrl);\n        if (state) {\n          params = params.set('state', state);\n        }\n      }\n      for (const key in customParameters) {\n        params = params.set(key, customParameters[key]);\n      }\n      logoutUrl = this.logoutUrl + (this.logoutUrl.indexOf('?') > -1 ? '&' : '?') + params.toString();\n    }\n    this.config.openUri(logoutUrl);\n  }\n  /**\n   * @ignore\n   */\n  createAndSaveNonce() {\n    const that = this; // eslint-disable-line @typescript-eslint/no-this-alias\n    return this.createNonce().then(function (nonce) {\n      // Use localStorage for nonce if possible\n      // localStorage is the only storage who survives a\n      // redirect in ALL browsers (also IE)\n      // Otherwiese we'd force teams who have to support\n      // IE into using localStorage for everything\n      if (that.saveNoncesInLocalStorage && typeof window['localStorage'] !== 'undefined') {\n        localStorage.setItem('nonce', nonce);\n      } else {\n        that._storage.setItem('nonce', nonce);\n      }\n      return nonce;\n    });\n  }\n  /**\n   * @ignore\n   */\n  ngOnDestroy() {\n    this.clearAccessTokenTimer();\n    this.clearIdTokenTimer();\n    this.removeSilentRefreshEventListener();\n    const silentRefreshFrame = this.document.getElementById(this.silentRefreshIFrameName);\n    if (silentRefreshFrame) {\n      silentRefreshFrame.remove();\n    }\n    this.stopSessionCheckTimer();\n    this.removeSessionCheckEventListener();\n    const sessionCheckFrame = this.document.getElementById(this.sessionCheckIFrameName);\n    if (sessionCheckFrame) {\n      sessionCheckFrame.remove();\n    }\n  }\n  createNonce() {\n    return new Promise(resolve => {\n      if (this.rngUrl) {\n        throw new Error('createNonce with rng-web-api has not been implemented so far');\n      }\n      /*\n       * This alphabet is from:\n       * https://tools.ietf.org/html/rfc7636#section-4.1\n       *\n       * [A-Z] / [a-z] / [0-9] / \"-\" / \".\" / \"_\" / \"~\"\n       */\n      const unreserved = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n      let size = 45;\n      let id = '';\n      const crypto = typeof self === 'undefined' ? null : self.crypto || self['msCrypto'];\n      if (crypto) {\n        let bytes = new Uint8Array(size);\n        crypto.getRandomValues(bytes);\n        // Needed for IE\n        if (!bytes.map) {\n          bytes.map = Array.prototype.map;\n        }\n        bytes = bytes.map(x => unreserved.charCodeAt(x % unreserved.length));\n        id = String.fromCharCode.apply(null, bytes);\n      } else {\n        while (0 < size--) {\n          id += unreserved[Math.random() * unreserved.length | 0];\n        }\n      }\n      resolve(base64UrlEncode(id));\n    });\n  }\n  async checkAtHash(params) {\n    if (!this.tokenValidationHandler) {\n      this.logger.warn('No tokenValidationHandler configured. Cannot check at_hash.');\n      return true;\n    }\n    return this.tokenValidationHandler.validateAtHash(params);\n  }\n  checkSignature(params) {\n    if (!this.tokenValidationHandler) {\n      this.logger.warn('No tokenValidationHandler configured. Cannot check signature.');\n      return Promise.resolve(null);\n    }\n    return this.tokenValidationHandler.validateSignature(params);\n  }\n  /**\n   * Start the implicit flow or the code flow,\n   * depending on your configuration.\n   */\n  initLoginFlow(additionalState = '', params = {}) {\n    if (this.responseType === 'code') {\n      return this.initCodeFlow(additionalState, params);\n    } else {\n      return this.initImplicitFlow(additionalState, params);\n    }\n  }\n  /**\n   * Starts the authorization code flow and redirects to user to\n   * the auth servers login url.\n   */\n  initCodeFlow(additionalState = '', params = {}) {\n    if (this.loginUrl !== '') {\n      this.initCodeFlowInternal(additionalState, params);\n    } else {\n      this.events.pipe(filter(e => e.type === 'discovery_document_loaded')).subscribe(() => this.initCodeFlowInternal(additionalState, params));\n    }\n  }\n  initCodeFlowInternal(additionalState = '', params = {}) {\n    if (!this.validateUrlForHttps(this.loginUrl)) {\n      throw new Error(\"loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).\");\n    }\n    let addParams = {};\n    let loginHint = null;\n    if (typeof params === 'string') {\n      loginHint = params;\n    } else if (typeof params === 'object') {\n      addParams = params;\n    }\n    this.createLoginUrl(additionalState, loginHint, null, false, addParams).then(this.config.openUri).catch(error => {\n      console.error('Error in initAuthorizationCodeFlow');\n      console.error(error);\n    });\n  }\n  async createChallangeVerifierPairForPKCE() {\n    if (!this.crypto) {\n      throw new Error('PKCE support for code flow needs a CryptoHander. Did you import the OAuthModule using forRoot() ?');\n    }\n    const verifier = await this.createNonce();\n    const challengeRaw = await this.crypto.calcHash(verifier, 'sha-256');\n    const challenge = base64UrlEncode(challengeRaw);\n    return [challenge, verifier];\n  }\n  extractRecognizedCustomParameters(tokenResponse) {\n    const foundParameters = new Map();\n    if (!this.config.customTokenParameters) {\n      return foundParameters;\n    }\n    this.config.customTokenParameters.forEach(recognizedParameter => {\n      if (tokenResponse[recognizedParameter]) {\n        foundParameters.set(recognizedParameter, JSON.stringify(tokenResponse[recognizedParameter]));\n      }\n    });\n    return foundParameters;\n  }\n  /**\n   * Revokes the auth token to secure the vulnarability\n   * of the token issued allowing the authorization server to clean\n   * up any security credentials associated with the authorization\n   */\n  revokeTokenAndLogout(customParameters = {}, ignoreCorsIssues = false) {\n    const revokeEndpoint = this.revocationEndpoint;\n    const accessToken = this.getAccessToken();\n    const refreshToken = this.getRefreshToken();\n    if (!accessToken) {\n      return Promise.resolve();\n    }\n    let params = new HttpParams({\n      encoder: new WebHttpUrlEncodingCodec()\n    });\n    let headers = new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded');\n    if (this.useHttpBasicAuth) {\n      const header = btoa(`${this.clientId}:${this.dummyClientSecret}`);\n      headers = headers.set('Authorization', 'Basic ' + header);\n    }\n    if (!this.useHttpBasicAuth) {\n      params = params.set('client_id', this.clientId);\n    }\n    if (!this.useHttpBasicAuth && this.dummyClientSecret) {\n      params = params.set('client_secret', this.dummyClientSecret);\n    }\n    if (this.customQueryParams) {\n      for (const key of Object.getOwnPropertyNames(this.customQueryParams)) {\n        params = params.set(key, this.customQueryParams[key]);\n      }\n    }\n    return new Promise((resolve, reject) => {\n      let revokeAccessToken;\n      let revokeRefreshToken;\n      if (accessToken) {\n        const revokationParams = params.set('token', accessToken).set('token_type_hint', 'access_token');\n        revokeAccessToken = this.http.post(revokeEndpoint, revokationParams, {\n          headers\n        });\n      } else {\n        revokeAccessToken = of(null);\n      }\n      if (refreshToken) {\n        const revokationParams = params.set('token', refreshToken).set('token_type_hint', 'refresh_token');\n        revokeRefreshToken = this.http.post(revokeEndpoint, revokationParams, {\n          headers\n        });\n      } else {\n        revokeRefreshToken = of(null);\n      }\n      if (ignoreCorsIssues) {\n        revokeAccessToken = revokeAccessToken.pipe(catchError(err => {\n          if (err.status === 0) {\n            return of(null);\n          }\n          return throwError(err);\n        }));\n        revokeRefreshToken = revokeRefreshToken.pipe(catchError(err => {\n          if (err.status === 0) {\n            return of(null);\n          }\n          return throwError(err);\n        }));\n      }\n      combineLatest([revokeAccessToken, revokeRefreshToken]).subscribe(res => {\n        this.logOut(customParameters);\n        resolve(res);\n        this.logger.info('Token successfully revoked');\n      }, err => {\n        this.logger.error('Error revoking token', err);\n        this.eventsSubject.next(new OAuthErrorEvent('token_revoke_error', err));\n        reject(err);\n      });\n    });\n  }\n  /**\n   * Clear location.hash if it's present\n   */\n  clearLocationHash() {\n    // Checking for empty hash is necessary for Firefox\n    // as setting an empty hash to an empty string adds # to the URL\n    if (location.hash != '') {\n      location.hash = '';\n    }\n  }\n  static {\n    this.ɵfac = function OAuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(OAuthStorage, 8), i0.ɵɵinject(ValidationHandler, 8), i0.ɵɵinject(AuthConfig, 8), i0.ɵɵinject(UrlHelperService), i0.ɵɵinject(OAuthLogger), i0.ɵɵinject(HashHandler, 8), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(DateTimeProvider));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OAuthService,\n      factory: OAuthService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OAuthService, [{\n    type: Injectable\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.HttpClient\n  }, {\n    type: OAuthStorage,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: ValidationHandler,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: AuthConfig,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: UrlHelperService\n  }, {\n    type: OAuthLogger\n  }, {\n    type: HashHandler,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: DateTimeProvider\n  }], null);\n})();\nclass OAuthResourceServerErrorHandler {}\nclass OAuthNoopResourceServerErrorHandler {\n  handleError(err) {\n    return throwError(err);\n  }\n}\nclass DefaultOAuthInterceptor {\n  constructor(oAuthService, errorHandler, moduleConfig) {\n    this.oAuthService = oAuthService;\n    this.errorHandler = errorHandler;\n    this.moduleConfig = moduleConfig;\n  }\n  checkUrl(url) {\n    if (this.moduleConfig.resourceServer.customUrlValidation) {\n      return this.moduleConfig.resourceServer.customUrlValidation(url);\n    }\n    if (this.moduleConfig.resourceServer.allowedUrls) {\n      return !!this.moduleConfig.resourceServer.allowedUrls.find(u => url.toLowerCase().startsWith(u.toLowerCase()));\n    }\n    return true;\n  }\n  intercept(req, next) {\n    const url = req.url.toLowerCase();\n    if (!this.moduleConfig || !this.moduleConfig.resourceServer || !this.checkUrl(url)) {\n      return next.handle(req);\n    }\n    const sendAccessToken = this.moduleConfig.resourceServer.sendAccessToken;\n    if (!sendAccessToken) {\n      return next.handle(req).pipe(catchError(err => this.errorHandler.handleError(err)));\n    }\n    return merge(of(this.oAuthService.getAccessToken()).pipe(filter(token => !!token)), this.oAuthService.events.pipe(filter(e => e.type === 'token_received'), timeout(this.oAuthService.waitForTokenInMsec || 0), catchError(() => of(null)),\n    // timeout is not an error\n    map(() => this.oAuthService.getAccessToken()))).pipe(take(1), mergeMap(token => {\n      if (token) {\n        const header = 'Bearer ' + token;\n        const headers = req.headers.set('Authorization', header);\n        req = req.clone({\n          headers\n        });\n      }\n      return next.handle(req).pipe(catchError(err => this.errorHandler.handleError(err)));\n    }));\n  }\n  static {\n    this.ɵfac = function DefaultOAuthInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DefaultOAuthInterceptor)(i0.ɵɵinject(OAuthService), i0.ɵɵinject(OAuthResourceServerErrorHandler), i0.ɵɵinject(OAuthModuleConfig, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DefaultOAuthInterceptor,\n      factory: DefaultOAuthInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultOAuthInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: OAuthService\n  }, {\n    type: OAuthResourceServerErrorHandler\n  }, {\n    type: OAuthModuleConfig,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nfunction createDefaultLogger() {\n  return console;\n}\nfunction createDefaultStorage() {\n  return typeof sessionStorage !== 'undefined' ? sessionStorage : new MemoryStorage();\n}\nfunction provideOAuthClient(config = null, validationHandlerClass = NullValidationHandler) {\n  return makeEnvironmentProviders([OAuthService, UrlHelperService, {\n    provide: OAuthLogger,\n    useFactory: createDefaultLogger\n  }, {\n    provide: OAuthStorage,\n    useFactory: createDefaultStorage\n  }, {\n    provide: ValidationHandler,\n    useClass: validationHandlerClass\n  }, {\n    provide: HashHandler,\n    useClass: DefaultHashHandler\n  }, {\n    provide: OAuthResourceServerErrorHandler,\n    useClass: OAuthNoopResourceServerErrorHandler\n  }, {\n    provide: OAuthModuleConfig,\n    useValue: config\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: DefaultOAuthInterceptor,\n    multi: true\n  }, {\n    provide: DateTimeProvider,\n    useClass: SystemDateTimeProvider\n  }]);\n}\nclass OAuthModule {\n  static forRoot(config = null, validationHandlerClass = NullValidationHandler) {\n    return {\n      ngModule: OAuthModule,\n      providers: [provideOAuthClient(config, validationHandlerClass)]\n    };\n  }\n  static {\n    this.ɵfac = function OAuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OAuthModule,\n      imports: [CommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OAuthModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [],\n      exports: []\n    }]\n  }], null, null);\n})();\nconst err = `PLEASE READ THIS CAREFULLY:\n\nBeginning with angular-oauth2-oidc version 9, the JwksValidationHandler\nhas been moved to an library of its own. If you need it for implementing\nOAuth2/OIDC **implicit flow**, please install it using npm:\n\n  npm i angular-oauth2-oidc-jwks --save\n\nAfter that, you can import it into your application:\n\n  import { JwksValidationHandler } from 'angular-oauth2-oidc-jwks';\n\nPlease note, that this dependency is not needed for the **code flow**,\nwhich is nowadays the **recommented** one for single page applications.\nThis also results in smaller bundle sizes.\n`;\n/**\n * This is just a dummy of the JwksValidationHandler\n * telling the users that the real one has been moved\n * to an library of its own, namely angular-oauth2-oidc-utils\n */\nclass JwksValidationHandler extends NullValidationHandler {\n  constructor() {\n    super();\n    console.error(err);\n  }\n}\nconst AUTH_CONFIG = new InjectionToken('AUTH_CONFIG');\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTH_CONFIG, AbstractValidationHandler, AuthConfig, DateTimeProvider, DefaultHashHandler, DefaultOAuthInterceptor, HashHandler, JwksValidationHandler, LoginOptions, MemoryStorage, NullValidationHandler, OAuthErrorEvent, OAuthEvent, OAuthInfoEvent, OAuthLogger, OAuthModule, OAuthModuleConfig, OAuthNoopResourceServerErrorHandler, OAuthResourceServerConfig, OAuthResourceServerErrorHandler, OAuthService, OAuthStorage, OAuthSuccessEvent, ReceivedTokens, SystemDateTimeProvider, UrlHelperService, ValidationHandler, provideOAuthClient };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,kBAAkB,kBAAkB;AAClC,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAC7B;AAAA,EACA,eAAe,kBAAkB;AAC/B,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAC7B;AACF;AACA,IAAM,oBAAN,MAAwB;AAAC;AACzB,IAAM,4BAAN,MAAgC;AAAC;AACjC,IAAM,mBAAN,MAAuB;AAAC;AACxB,IAAM,0BAAN,MAAM,gCAA+B,iBAAiB;AAAA,EACpD,MAAM;AACJ,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,MAAM;AACJ,WAAO,oBAAI,KAAK;AAAA,EAClB;AAeF;AAbI,wBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,+BAA+B,mBAAmB;AAChE,YAAQ,wCAAwC,sCAAyC,sBAAsB,uBAAsB,IAAI,qBAAqB,uBAAsB;AAAA,EACtL;AACF,GAAG;AAGH,wBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,wBAAuB;AAClC,CAAC;AAnBL,IAAM,yBAAN;AAAA,CAsBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AAQZ,SAAK,oBAAoB;AAOzB,SAAK,6BAA6B;AAAA,EACpC;AACF;AAOA,IAAM,cAAN,MAAkB;AAAC;AAOnB,IAAM,eAAN,MAAmB;AAAC;AACpB,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,SAAK,OAAO,oBAAI,IAAI;AAAA,EACtB;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,KAAK,KAAK,IAAI,GAAG;AAAA,EAC1B;AAAA,EACA,WAAW,KAAK;AACd,SAAK,KAAK,OAAO,GAAG;AAAA,EACtB;AAAA,EACA,QAAQ,KAAK,MAAM;AACjB,SAAK,KAAK,IAAI,KAAK,IAAI;AAAA,EACzB;AAYF;AAVI,eAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,SAAO,KAAK,qBAAqB,gBAAe;AAClD;AAGA,eAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,eAAc;AACzB,CAAC;AAtBL,IAAM,gBAAN;AAAA,CAyBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,iBAAN,MAAqB;AAAC;AACtB,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,oBAAN,cAAgC,WAAW;AAAA,EACzC,YAAY,MAAM,OAAO,MAAM;AAC7B,UAAM,IAAI;AACV,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,iBAAN,cAA6B,WAAW;AAAA,EACtC,YAAY,MAAM,OAAO,MAAM;AAC7B,UAAM,IAAI;AACV,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,kBAAN,cAA8B,WAAW;AAAA,EACvC,YAAY,MAAM,QAAQ,SAAS,MAAM;AACvC,UAAM,IAAI;AACV,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AACF;AAGA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACvD,SAAO,mBAAmB,KAAK,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AAChE,WAAO,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,EAC7D,CAAC,EAAE,KAAK,EAAE,CAAC;AACb;AACA,SAAS,gBAAgB,KAAK;AAC5B,QAAM,SAAS,KAAK,GAAG;AACvB,SAAO,OAAO,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AACxE;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,MAAM;AAIhB,SAAK,WAAW;AAIhB,SAAK,cAAc;AAKnB,SAAK,wBAAwB;AAK7B,SAAK,6CAA6C;AAKlD,SAAK,WAAW;AAIhB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,SAAS;AAKd,SAAK,OAAO;AAKZ,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AAIf,SAAK,SAAS;AAId,SAAK,YAAY;AAIjB,SAAK,sBAAsB;AAI3B,SAAK,gBAAgB;AAIrB,SAAK,qBAAqB;AAI1B,SAAK,wBAAwB,CAAC;AAI9B,SAAK,mBAAmB;AACxB,SAAK,eAAe;AAOpB,SAAK,uBAAuB;AAI5B,SAAK,2BAA2B;AAChC,SAAK,6BAA6B;AAKlC,SAAK,0BAA0B;AAM/B,SAAK,sBAAsB,MAAO;AAIlC,SAAK,uBAAuB,MAAO;AASnC,SAAK,oBAAoB;AAOzB,SAAK,eAAe;AAKpB,SAAK,oCAAoC;AAMzC,SAAK,OAAO;AAKZ,SAAK,oBAAoB;AACzB,SAAK,0BAA0B;AAM/B,SAAK,gBAAgB;AAMrB,SAAK,uBAAuB;AAK5B,SAAK,wBAAwB,IAAI;AAIjC,SAAK,wBAAwB;AAI7B,SAAK,yBAAyB;AAQ9B,SAAK,qBAAqB;AAK1B,SAAK,mBAAmB;AACxB,SAAK,iCAAiC;AAKtC,SAAK,kBAAkB;AAOvB,SAAK,sBAAsB;AAI3B,SAAK,mBAAmB;AAIxB,SAAK,0BAA0B;AAI/B,SAAK,qBAAqB;AAM1B,SAAK,cAAc;AAKnB,SAAK,yBAAyB;AAK9B,SAAK,sBAAsB;AAI3B,SAAK,cAAc;AAMnB,SAAK,UAAU,SAAO;AACpB,eAAS,OAAO;AAAA,IAClB;AACA,QAAI,MAAM;AACR,aAAO,OAAO,MAAM,IAAI;AAAA,IAC1B;AAAA,EACF;AACF;AAKA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,UAAU,GAAG;AACX,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,YAAY,GAAG;AACb,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,UAAU,GAAG;AACX,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,YAAY,GAAG;AACb,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AACF;AAMA,IAAM,oBAAN,MAAwB;AAAC;AAMzB,IAAM,4BAAN,MAAgC;AAAA;AAAA;AAAA;AAAA,EAIxB,eAAe,QAAQ;AAAA;AAC3B,YAAM,UAAU,KAAK,mBAAmB,OAAO,aAAa;AAC5D,YAAM,YAAY,MAAM,KAAK,SAAS,OAAO,aAAa,OAAO;AACjE,YAAM,eAAe,UAAU,OAAO,GAAG,UAAU,SAAS,CAAC;AAC7D,YAAM,SAAS,gBAAgB,YAAY;AAC3C,YAAM,eAAe,OAAO,cAAc,SAAS,EAAE,QAAQ,MAAM,EAAE;AACrE,UAAI,WAAW,cAAc;AAC3B,gBAAQ,MAAM,wBAAwB,MAAM;AAC5C,gBAAQ,MAAM,qBAAqB,YAAY;AAAA,MACjD;AACA,aAAO,WAAW;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,WAAW;AAC5B,UAAM,MAAM,UAAU,KAAK;AAC3B,QAAI,CAAC,IAAI,MAAM,cAAc,GAAG;AAC9B,YAAM,IAAI,MAAM,8BAA8B,GAAG;AAAA,IACnD;AACA,WAAO,SAAS,IAAI,OAAO,CAAC;AAAA,EAC9B;AACF;AACA,IAAM,oBAAN,MAAM,kBAAiB;AAAA,EACrB,sBAAsB,oBAAoB;AACxC,QAAIA,QAAO,sBAAsB,OAAO,SAAS;AACjD,IAAAA,QAAO,mBAAmBA,KAAI;AAC9B,QAAIA,MAAK,QAAQ,GAAG,MAAM,GAAG;AAC3B,aAAO,CAAC;AAAA,IACV;AACA,UAAM,uBAAuBA,MAAK,QAAQ,GAAG;AAC7C,QAAI,uBAAuB,IAAI;AAC7B,MAAAA,QAAOA,MAAK,OAAO,uBAAuB,CAAC;AAAA,IAC7C,OAAO;AACL,MAAAA,QAAOA,MAAK,OAAO,CAAC;AAAA,IACtB;AACA,WAAO,KAAK,iBAAiBA,KAAI;AAAA,EACnC;AAAA,EACA,iBAAiB,aAAa;AAC5B,UAAM,OAAO,CAAC;AACd,QAAI,MAAM,gBAAgB,YAAY,cAAc,KAAK;AACzD,QAAI,gBAAgB,MAAM;AACxB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,YAAY,MAAM,GAAG;AACnC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAO,MAAM,CAAC;AACd,uBAAiB,KAAK,QAAQ,GAAG;AACjC,UAAI,mBAAmB,IAAI;AACzB,qBAAa;AACb,uBAAe;AAAA,MACjB,OAAO;AACL,qBAAa,KAAK,OAAO,GAAG,cAAc;AAC1C,uBAAe,KAAK,OAAO,iBAAiB,CAAC;AAAA,MAC/C;AACA,YAAM,mBAAmB,UAAU;AACnC,cAAQ,mBAAmB,YAAY;AACvC,UAAI,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK;AAC5B,cAAM,IAAI,OAAO,CAAC;AAAA,MACpB;AACA,WAAK,GAAG,IAAI;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAYF;AAVI,kBAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,mBAAkB;AACrD;AAGA,kBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,kBAAiB;AAC5B,CAAC;AAlDL,IAAM,mBAAN;AAAA,CAqDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAsBH,IAAM,eAAe;AACrB,IAAM,YAAY;AAElB,IAAM,IAAI,IAAI,YAAY,CAAC,YAAY,YAAY,YAAY,YAAY,WAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,UAAU,CAAC;AAC1xB,SAAS,WAAW,GAAG,GAAG,GAAG,KAAK,KAAK;AACrC,MAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI;AACzC,SAAO,OAAO,IAAI;AAChB,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,QAAI,EAAE,CAAC;AACP,SAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,UAAI,MAAM,IAAI;AACd,QAAE,CAAC,KAAK,EAAE,CAAC,IAAI,QAAS,MAAM,EAAE,IAAI,CAAC,IAAI,QAAS,MAAM,EAAE,IAAI,CAAC,IAAI,QAAS,IAAI,EAAE,IAAI,CAAC,IAAI;AAAA,IAC7F;AACA,SAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxB,UAAI,EAAE,IAAI,CAAC;AACX,YAAM,MAAM,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM;AACnE,UAAI,EAAE,IAAI,EAAE;AACZ,YAAM,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM;AACjE,QAAE,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,MAAM,KAAK,EAAE,IAAI,EAAE,IAAI;AAAA,IACjD;AACA,SAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,cAAQ,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAChJ,aAAO,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AACnH,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,IAAI,KAAK;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,KAAK;AAAA,IAChB;AACA,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,WAAO;AACP,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,OAAN,MAAW;AAAA,EACT,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,YAAY;AAEjB,SAAK,QAAQ,IAAI,WAAW,CAAC;AAC7B,SAAK,OAAO,IAAI,WAAW,EAAE;AAC7B,SAAK,SAAS,IAAI,WAAW,GAAG;AAChC,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,MAAM;AAAA,EACb;AAAA;AAAA;AAAA,EAGA,QAAQ;AACN,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ;AACN,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,WAAK,OAAO,CAAC,IAAI;AAAA,IACnB;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,WAAK,KAAK,CAAC,IAAI;AAAA,IACjB;AACA,SAAK,MAAM;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM,aAAa,KAAK,QAAQ;AACrC,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AACA,QAAI,UAAU;AACd,SAAK,eAAe;AACpB,QAAI,KAAK,eAAe,GAAG;AACzB,aAAO,KAAK,eAAe,MAAM,aAAa,GAAG;AAC/C,aAAK,OAAO,KAAK,cAAc,IAAI,KAAK,SAAS;AACjD;AAAA,MACF;AACA,UAAI,KAAK,iBAAiB,IAAI;AAC5B,mBAAW,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ,GAAG,EAAE;AACpD,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AACA,QAAI,cAAc,IAAI;AACpB,gBAAU,WAAW,KAAK,MAAM,KAAK,OAAO,MAAM,SAAS,UAAU;AACrE,oBAAc;AAAA,IAChB;AACA,WAAO,aAAa,GAAG;AACrB,WAAK,OAAO,KAAK,cAAc,IAAI,KAAK,SAAS;AACjD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK;AACV,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,cAAc,KAAK;AACzB,YAAM,OAAO,KAAK;AAClB,YAAM,WAAW,cAAc,YAAa;AAC5C,YAAM,WAAW,eAAe;AAChC,YAAM,YAAY,cAAc,KAAK,KAAK,KAAK;AAC/C,WAAK,OAAO,IAAI,IAAI;AACpB,eAAS,IAAI,OAAO,GAAG,IAAI,YAAY,GAAG,KAAK;AAC7C,aAAK,OAAO,CAAC,IAAI;AAAA,MACnB;AACA,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,KAAK;AAC/C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,KAAK;AAC/C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,IAAI;AAC9C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,IAAI;AAC9C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,KAAK;AAC/C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,KAAK;AAC/C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,IAAI;AAC9C,WAAK,OAAO,YAAY,CAAC,IAAI,aAAa,IAAI;AAC9C,iBAAW,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ,GAAG,SAAS;AAC3D,WAAK,WAAW;AAAA,IAClB;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,KAAK;AACxC,UAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,KAAK;AACxC,UAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,IAAI;AACvC,UAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,IAAI;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS;AACP,UAAM,MAAM,IAAI,WAAW,KAAK,YAAY;AAC5C,SAAK,OAAO,GAAG;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW,KAAK;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAI,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,cAAcC,OAAM,aAAa;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,WAAK,MAAM,CAAC,IAAIA,MAAK,CAAC;AAAA,IACxB;AACA,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AACF;AAuEA,SAAS,KAAK,MAAM;AAClB,QAAM,IAAI,IAAI,KAAK,EAAE,OAAO,IAAI;AAChC,QAAM,SAAS,EAAE,OAAO;AACxB,IAAE,MAAM;AACR,SAAO;AACT;AAkCA,IAAM,WAAW,IAAI,WAAW,YAAY;AA0E5C,IAAM,cAAN,MAAkB;AAAC;AACnB,SAAS,WAAW,GAAG;AACrB,MAAI,OAAO,MAAM,SAAU,OAAM,IAAI,UAAU,iBAAiB;AAChE,QAAM,IAAI,GACR,IAAI,IAAI,WAAW,EAAE,MAAM;AAC7B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,CAAC,IAAI,EAAE,WAAW,CAAC;AACxD,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,QAAM,IAAI,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,GAAE,KAAK,OAAO,aAAa,IAAI,CAAC,CAAC,CAAC;AACvE,SAAO,EAAE,KAAK,EAAE;AAClB;AACA,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACjB,SAAS,aAAa,WAAW;AAAA;AAKrC,YAAM,WAAW,WAAW,KAAK,WAAW,WAAW,CAAC,CAAC;AAMzD,aAAO;AAAA,IACT;AAAA;AAAA,EACA,cAAc,WAAW;AACvB,QAAI,SAAS;AACb,eAAW,KAAK,WAAW;AACzB,gBAAU,OAAO,aAAa,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,QAAQ;AACnB,UAAM,YAAY,IAAI,WAAW,MAAM;AACvC,QAAI,SAAS;AACb,eAAW,KAAK,WAAW;AACzB,gBAAU,OAAO,aAAa,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAYF;AAVI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,oBAAmB;AAC9B,CAAC;AAtCL,IAAM,qBAAN;AAAA,CAyCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,gBAAN,MAAM,sBAAqB,WAAW;AAAA,EACpC,YAAY,QAAQ,MAAM,SAAS,wBAAwB,QAAQ,WAAW,QAAQ,QAAQ,UAAU,iBAAiB;AACvH,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,kBAAkB;AAKvB,SAAK,0BAA0B;AAK/B,SAAK,QAAQ;AACb,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,iCAAiC,IAAI,QAAQ;AAClD,SAAK,sBAAsB,CAAC;AAC5B,SAAK,iBAAiB;AACtB,SAAK,2BAA2B;AAChC,SAAK,MAAM,yBAAyB;AAEpC,SAAK,WAAW;AAChB,QAAI,CAAC,QAAQ;AACX,eAAS,CAAC;AAAA,IACZ;AACA,SAAK,2BAA2B,KAAK,+BAA+B,aAAa;AACjF,SAAK,SAAS,KAAK,cAAc,aAAa;AAC9C,QAAI,wBAAwB;AAC1B,WAAK,yBAAyB;AAAA,IAChC;AACA,QAAI,QAAQ;AACV,WAAK,UAAU,MAAM;AAAA,IACvB;AACA,QAAI;AACF,UAAI,SAAS;AACX,aAAK,WAAW,OAAO;AAAA,MACzB,WAAW,OAAO,mBAAmB,aAAa;AAChD,aAAK,WAAW,cAAc;AAAA,MAChC;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,MAAM,+IAAoJ,CAAC;AAAA,IACrK;AAEA,QAAI,KAAK,4BAA4B,GAAG;AACtC,YAAM,KAAK,QAAQ,WAAW;AAC9B,YAAM,OAAO,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,SAAS;AAC5D,UAAI,MAAM;AACR,aAAK,2BAA2B;AAAA,MAClC;AAAA,IACF;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,8BAA8B;AAC5B,QAAI,OAAO,WAAW,YAAa,QAAO;AAC1C,UAAM,OAAO;AACb,QAAI;AACF,UAAI,OAAO,OAAO,cAAc,MAAM,YAAa,QAAO;AAC1D,mBAAa,QAAQ,MAAM,IAAI;AAC/B,mBAAa,WAAW,IAAI;AAC5B,aAAO;AAAA,IACT,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AAGhB,WAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM;AAC5C,SAAK,SAAS,OAAO,OAAO,CAAC,GAAG,IAAI,WAAW,GAAG,MAAM;AACxD,QAAI,KAAK,sBAAsB;AAC7B,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,gBAAgB;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,sCAAsC;AACpC,QAAI,KAAK,gBAAgB,GAAG;AAC1B,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,qCAAqC;AACnC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,oBAAoB;AAClB,SAAK,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,gBAAgB,CAAC,EAAE,UAAU,MAAM;AACzE,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,4BAA4B,SAAS,CAAC,GAAG,UAAU,WAAW,MAAM;AAClE,QAAI,yBAAyB;AAC7B,SAAK,2BAA2B;AAChC,SAAK,+BAA+B,KAAK,OAAO,KAAK,IAAI,OAAK;AAC5D,UAAI,EAAE,SAAS,kBAAkB;AAC/B,iCAAyB;AAAA,MAC3B,WAAW,EAAE,SAAS,UAAU;AAC9B,iCAAyB;AAAA,MAC3B;AAAA,IACF,CAAC,GAAG,OAAO,OAAK,EAAE,SAAS,oBAAoB,YAAY,QAAQ,aAAa,SAAS,EAAE,SAAS,SAAS,GAAG,aAAa,GAAI,CAAC,EAAE,UAAU,MAAM;AAClJ,UAAI,wBAAwB;AAE1B,aAAK,gBAAgB,QAAQ,QAAQ,EAAE,MAAM,MAAM;AACjD,eAAK,MAAM,uCAAuC;AAAA,QACpD,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,mCAAmC;AAAA,EAC1C;AAAA,EACA,gBAAgB,QAAQ,UAAU;AAChC,QAAI,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,QAAQ;AAC1D,aAAO,KAAK,aAAa;AAAA,IAC3B,OAAO;AACL,aAAO,KAAK,cAAc,QAAQ,QAAQ;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,UAAU,MAAM;AAC/C,WAAO,KAAK,sBAAsB,EAAE,KAAK,MAAM;AAC7C,aAAO,KAAK,SAAS,OAAO;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,UAAU,MAAM;AAC5C,cAAU,WAAW,CAAC;AACtB,WAAO,KAAK,iCAAiC,OAAO,EAAE,KAAK,MAAM;AAC/D,UAAI,CAAC,KAAK,gBAAgB,KAAK,CAAC,KAAK,oBAAoB,GAAG;AAC1D,cAAM,QAAQ,OAAO,QAAQ,UAAU,WAAW,QAAQ,QAAQ;AAClE,aAAK,cAAc,KAAK;AACxB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,MAAM;AACb,QAAI,KAAK,sBAAsB;AAC7B,WAAK,OAAO,MAAM,GAAG,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,iCAAiC,KAAK;AACpC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,KAAK,oBAAoB,GAAG;AAC/C,UAAM,cAAc,KAAK,yBAAyB,GAAG;AACrD,QAAI,CAAC,YAAY;AACf,aAAO,KAAK,mEAAmE;AAAA,IACjF;AACA,QAAI,CAAC,aAAa;AAChB,aAAO,KAAK,uHAA4H;AAAA,IAC1I;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,KAAK;AACvB,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,IAAI,YAAY;AAC9B,QAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAO;AAAA,IACT;AACA,SAAK,MAAM,MAAM,6BAA6B,KAAK,MAAM,MAAM,6BAA6B,MAAM,KAAK,iBAAiB,cAAc;AACpI,aAAO;AAAA,IACT;AACA,WAAO,MAAM,WAAW,UAAU;AAAA,EACpC;AAAA,EACA,mCAAmC,KAAK,aAAa;AACnD,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,MAAM,IAAI,WAAW,sBAAsB;AAAA,IACvD;AACA,QAAI,CAAC,KAAK,oBAAoB,GAAG,GAAG;AAClC,YAAM,IAAI,MAAM,IAAI,WAAW,+HAA+H;AAAA,IAChK;AAAA,EACF;AAAA,EACA,yBAAyB,KAAK;AAC5B,QAAI,CAAC,KAAK,mCAAmC;AAC3C,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,WAAO,IAAI,YAAY,EAAE,WAAW,KAAK,OAAO,YAAY,CAAC;AAAA,EAC/D;AAAA,EACA,oBAAoB;AAClB,QAAI,OAAO,WAAW,aAAa;AACjC,WAAK,MAAM,uCAAuC;AAClD;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,GAAG;AACxD,WAAK,sBAAsB;AAC3B,WAAK,kBAAkB;AACvB,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,KAAK,0BAA2B,MAAK,0BAA0B,YAAY;AAC/E,SAAK,4BAA4B,KAAK,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,gBAAgB,CAAC,EAAE,UAAU,MAAM;AAC1G,WAAK,sBAAsB;AAC3B,WAAK,kBAAkB;AACvB,WAAK,sBAAsB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,oBAAoB,GAAG;AAC9B,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,uBAAuB,KAAK,gBAAgB,GAAG;AACvD,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,UAAM,aAAa,KAAK,yBAAyB;AACjD,UAAM,WAAW,KAAK,uBAAuB;AAC7C,UAAMC,WAAU,KAAK,YAAY,UAAU,UAAU;AACrD,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,iCAAiC,GAAG,IAAI,eAAe,iBAAiB,cAAc,CAAC,EAAE,KAAK,MAAMA,QAAO,CAAC,EAAE,UAAU,OAAK;AAChI,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,cAAc,KAAK,CAAC;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,UAAM,aAAa,KAAK,qBAAqB;AAC7C,UAAM,WAAW,KAAK,mBAAmB;AACzC,UAAMA,WAAU,KAAK,YAAY,UAAU,UAAU;AACrD,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,6BAA6B,GAAG,IAAI,eAAe,iBAAiB,UAAU,CAAC,EAAE,KAAK,MAAMA,QAAO,CAAC,EAAE,UAAU,OAAK;AACxH,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,cAAc,KAAK,CAAC;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,gCAAgC;AACvC,WAAK,+BAA+B,YAAY;AAAA,IAClD;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,4BAA4B;AACnC,WAAK,2BAA2B,YAAY;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,8BAA8B;AACrC,WAAK,6BAA6B,YAAY;AAAA,IAChD;AAAA,EACF;AAAA,EACA,YAAY,UAAU,YAAY;AAChC,UAAM,MAAM,KAAK,gBAAgB,IAAI;AACrC,UAAM,SAAS,aAAa,YAAY,KAAK,iBAAiB,MAAM;AACpE,UAAM,WAAW,KAAK,IAAI,GAAG,KAAK;AAClC,UAAM,kBAAkB;AACxB,WAAO,WAAW,kBAAkB,kBAAkB;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,WAAW,SAAS;AAClB,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,UAAU,MAAM;AACpC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,SAAS;AACZ,kBAAU,KAAK,UAAU;AACzB,YAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AAC1B,qBAAW;AAAA,QACb;AACA,mBAAW;AAAA,MACb;AACA,UAAI,CAAC,KAAK,oBAAoB,OAAO,GAAG;AACtC,eAAO,qIAAqI;AAC5I;AAAA,MACF;AACA,WAAK,KAAK,IAAI,OAAO,EAAE,UAAU,SAAO;AACtC,YAAI,CAAC,KAAK,0BAA0B,GAAG,GAAG;AACxC,eAAK,cAAc,KAAK,IAAI,gBAAgB,uCAAuC,IAAI,CAAC;AACxF,iBAAO,qCAAqC;AAC5C;AAAA,QACF;AACA,aAAK,WAAW,IAAI;AACpB,aAAK,YAAY,IAAI,wBAAwB,KAAK;AAClD,aAAK,sBAAsB,IAAI;AAC/B,aAAK,SAAS,IAAI;AAClB,aAAK,gBAAgB,IAAI;AACzB,aAAK,mBAAmB,IAAI,qBAAqB,KAAK;AACtD,aAAK,UAAU,IAAI;AACnB,aAAK,wBAAwB,IAAI,wBAAwB,KAAK;AAC9D,aAAK,0BAA0B;AAC/B,aAAK,+BAA+B,KAAK,GAAG;AAC5C,aAAK,qBAAqB,IAAI,uBAAuB,KAAK;AAC1D,YAAI,KAAK,sBAAsB;AAC7B,eAAK,oCAAoC;AAAA,QAC3C;AACA,aAAK,SAAS,EAAE,KAAK,UAAQ;AAC3B,gBAAM,SAAS;AAAA,YACb,mBAAmB;AAAA,YACnB;AAAA,UACF;AACA,gBAAM,QAAQ,IAAI,kBAAkB,6BAA6B,MAAM;AACvE,eAAK,cAAc,KAAK,KAAK;AAC7B,kBAAQ,KAAK;AACb;AAAA,QACF,CAAC,EAAE,MAAM,CAAAC,SAAO;AACd,eAAK,cAAc,KAAK,IAAI,gBAAgB,iCAAiCA,IAAG,CAAC;AACjF,iBAAOA,IAAG;AACV;AAAA,QACF,CAAC;AAAA,MACH,GAAG,CAAAA,SAAO;AACR,aAAK,OAAO,MAAM,oCAAoCA,IAAG;AACzD,aAAK,cAAc,KAAK,IAAI,gBAAgB,iCAAiCA,IAAG,CAAC;AACjF,eAAOA,IAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,KAAK,SAAS;AAChB,aAAK,KAAK,IAAI,KAAK,OAAO,EAAE,UAAU,UAAQ;AAC5C,eAAK,OAAO;AAIZ,kBAAQ,IAAI;AAAA,QACd,GAAG,CAAAA,SAAO;AACR,eAAK,OAAO,MAAM,sBAAsBA,IAAG;AAC3C,eAAK,cAAc,KAAK,IAAI,gBAAgB,mBAAmBA,IAAG,CAAC;AACnE,iBAAOA,IAAG;AAAA,QACZ,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,IAAI;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B,KAAK;AAC7B,QAAI;AACJ,QAAI,CAAC,KAAK,mBAAmB,IAAI,WAAW,KAAK,QAAQ;AACvD,WAAK,OAAO,MAAM,wCAAwC,eAAe,KAAK,QAAQ,cAAc,IAAI,MAAM;AAC9G,aAAO;AAAA,IACT;AACA,aAAS,KAAK,iCAAiC,IAAI,sBAAsB;AACzE,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,OAAO,MAAM,iEAAiE,MAAM;AACzF,aAAO;AAAA,IACT;AACA,aAAS,KAAK,iCAAiC,IAAI,oBAAoB;AACvE,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,OAAO,MAAM,+DAA+D,MAAM;AACvF,aAAO;AAAA,IACT;AACA,aAAS,KAAK,iCAAiC,IAAI,cAAc;AACjE,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,OAAO,MAAM,yDAAyD,MAAM;AAAA,IACnF;AACA,aAAS,KAAK,iCAAiC,IAAI,mBAAmB;AACtE,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,OAAO,MAAM,8DAA8D,MAAM;AAAA,IACxF;AACA,aAAS,KAAK,iCAAiC,IAAI,iBAAiB;AACpE,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,OAAO,MAAM,4DAA4D,MAAM;AACpF,aAAO;AAAA,IACT;AACA,aAAS,KAAK,iCAAiC,IAAI,QAAQ;AAC3D,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,OAAO,MAAM,mDAAmD,MAAM;AAC3E,aAAO;AAAA,IACT;AACA,QAAI,KAAK,wBAAwB,CAAC,IAAI,sBAAsB;AAC1D,WAAK,OAAO,KAAK,wGAA6G;AAAA,IAChI;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,8CAA8C,UAAU,UAAU,UAAU,IAAI,YAAY,GAAG;AAC7F,WAAO,KAAK,4BAA4B,UAAU,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,gBAAgB,CAAC;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,QAAI,CAAC,KAAK,oBAAoB,GAAG;AAC/B,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,QAAI,CAAC,KAAK,oBAAoB,KAAK,gBAAgB,GAAG;AACpD,YAAM,IAAI,MAAM,8IAA8I;AAAA,IAChK;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,UAAU,IAAI,YAAY,EAAE,IAAI,iBAAiB,YAAY,KAAK,eAAe,CAAC;AACxF,WAAK,KAAK,IAAI,KAAK,kBAAkB;AAAA,QACnC;AAAA,QACA,SAAS;AAAA,QACT,cAAc;AAAA,MAChB,CAAC,EAAE,UAAU,cAAY;AACvB,aAAK,MAAM,qBAAqB,KAAK,UAAU,QAAQ,CAAC;AACxD,YAAI,SAAS,QAAQ,IAAI,cAAc,EAAE,WAAW,kBAAkB,GAAG;AACvE,cAAI,OAAO,KAAK,MAAM,SAAS,IAAI;AACnC,gBAAM,iBAAiB,KAAK,kBAAkB,KAAK,CAAC;AACpD,cAAI,CAAC,KAAK,kBAAkB;AAC1B,gBAAI,KAAK,SAAS,CAAC,eAAe,KAAK,KAAK,KAAK,QAAQ,eAAe,KAAK,IAAI;AAC/E,oBAAMA,OAAM;AACZ,qBAAOA,IAAG;AACV;AAAA,YACF;AAAA,UACF;AACA,iBAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,IAAI;AAC7C,eAAK,SAAS,QAAQ,uBAAuB,KAAK,UAAU,IAAI,CAAC;AACjE,eAAK,cAAc,KAAK,IAAI,kBAAkB,qBAAqB,CAAC;AACpE,kBAAQ;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,eAAK,MAAM,8CAA8C;AACzD,eAAK,cAAc,KAAK,IAAI,kBAAkB,qBAAqB,CAAC;AACpE,kBAAQ,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,QACnC;AAAA,MACF,GAAG,CAAAA,SAAO;AACR,aAAK,OAAO,MAAM,2BAA2BA,IAAG;AAChD,aAAK,cAAc,KAAK,IAAI,gBAAgB,2BAA2BA,IAAG,CAAC;AAC3E,eAAOA,IAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B,UAAU,UAAU,UAAU,IAAI,YAAY,GAAG;AAC3E,UAAM,aAAa;AAAA,MACjB,UAAU;AAAA,MACV;AAAA,IACF;AACA,WAAO,KAAK,qBAAqB,YAAY,YAAY,OAAO;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB,WAAW,YAAY,UAAU,IAAI,YAAY,GAAG;AACvE,SAAK,mCAAmC,KAAK,eAAe,eAAe;AAO3E,QAAI,SAAS,IAAI,WAAW;AAAA,MAC1B,SAAS,IAAI,wBAAwB;AAAA,IACvC,CAAC,EAAE,IAAI,cAAc,SAAS,EAAE,IAAI,SAAS,KAAK,KAAK;AACvD,QAAI,KAAK,kBAAkB;AACzB,YAAM,SAAS,KAAK,GAAG,KAAK,QAAQ,IAAI,KAAK,iBAAiB,EAAE;AAChE,gBAAU,QAAQ,IAAI,iBAAiB,WAAW,MAAM;AAAA,IAC1D;AACA,QAAI,CAAC,KAAK,kBAAkB;AAC1B,eAAS,OAAO,IAAI,aAAa,KAAK,QAAQ;AAAA,IAChD;AACA,QAAI,CAAC,KAAK,oBAAoB,KAAK,mBAAmB;AACpD,eAAS,OAAO,IAAI,iBAAiB,KAAK,iBAAiB;AAAA,IAC7D;AACA,QAAI,KAAK,mBAAmB;AAC1B,iBAAW,OAAO,OAAO,oBAAoB,KAAK,iBAAiB,GAAG;AACpE,iBAAS,OAAO,IAAI,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACtD;AAAA,IACF;AAEA,eAAW,OAAO,OAAO,KAAK,UAAU,GAAG;AACzC,eAAS,OAAO,IAAI,KAAK,WAAW,GAAG,CAAC;AAAA,IAC1C;AACA,cAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,KAAK,KAAK,KAAK,eAAe,QAAQ;AAAA,QACzC;AAAA,MACF,CAAC,EAAE,UAAU,mBAAiB;AAC5B,aAAK,MAAM,iBAAiB,aAAa;AACzC,aAAK,yBAAyB,cAAc,cAAc,cAAc,eAAe,cAAc,cAAc,KAAK,wCAAwC,cAAc,OAAO,KAAK,kCAAkC,aAAa,CAAC;AAC1O,YAAI,KAAK,QAAQ,cAAc,UAAU;AACvC,eAAK,eAAe,cAAc,UAAU,cAAc,YAAY,EAAE,KAAK,YAAU;AACrF,iBAAK,aAAa,MAAM;AACxB,oBAAQ,aAAa;AAAA,UACvB,CAAC;AAAA,QACH;AACA,aAAK,cAAc,KAAK,IAAI,kBAAkB,gBAAgB,CAAC;AAC/D,gBAAQ,aAAa;AAAA,MACvB,GAAG,CAAAA,SAAO;AACR,aAAK,OAAO,MAAM,sCAAsCA,IAAG;AAC3D,aAAK,cAAc,KAAK,IAAI,gBAAgB,eAAeA,IAAG,CAAC;AAC/D,eAAOA,IAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe;AACb,SAAK,mCAAmC,KAAK,eAAe,eAAe;AAC3E,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,SAAS,IAAI,WAAW;AAAA,QAC1B,SAAS,IAAI,wBAAwB;AAAA,MACvC,CAAC,EAAE,IAAI,cAAc,eAAe,EAAE,IAAI,SAAS,KAAK,KAAK,EAAE,IAAI,iBAAiB,KAAK,SAAS,QAAQ,eAAe,CAAC;AAC1H,UAAI,UAAU,IAAI,YAAY,EAAE,IAAI,gBAAgB,mCAAmC;AACvF,UAAI,KAAK,kBAAkB;AACzB,cAAM,SAAS,KAAK,GAAG,KAAK,QAAQ,IAAI,KAAK,iBAAiB,EAAE;AAChE,kBAAU,QAAQ,IAAI,iBAAiB,WAAW,MAAM;AAAA,MAC1D;AACA,UAAI,CAAC,KAAK,kBAAkB;AAC1B,iBAAS,OAAO,IAAI,aAAa,KAAK,QAAQ;AAAA,MAChD;AACA,UAAI,CAAC,KAAK,oBAAoB,KAAK,mBAAmB;AACpD,iBAAS,OAAO,IAAI,iBAAiB,KAAK,iBAAiB;AAAA,MAC7D;AACA,UAAI,KAAK,mBAAmB;AAC1B,mBAAW,OAAO,OAAO,oBAAoB,KAAK,iBAAiB,GAAG;AACpE,mBAAS,OAAO,IAAI,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,QACtD;AAAA,MACF;AACA,WAAK,KAAK,KAAK,KAAK,eAAe,QAAQ;AAAA,QACzC;AAAA,MACF,CAAC,EAAE,KAAK,UAAU,mBAAiB;AACjC,YAAI,KAAK,QAAQ,cAAc,UAAU;AACvC,iBAAO,KAAK,KAAK,eAAe,cAAc,UAAU,cAAc,cAAc,IAAI,CAAC,EAAE,KAAK,IAAI,YAAU,KAAK,aAAa,MAAM,CAAC,GAAG,IAAI,MAAM,aAAa,CAAC;AAAA,QACpK,OAAO;AACL,iBAAO,GAAG,aAAa;AAAA,QACzB;AAAA,MACF,CAAC,CAAC,EAAE,UAAU,mBAAiB;AAC7B,aAAK,MAAM,yBAAyB,aAAa;AACjD,aAAK,yBAAyB,cAAc,cAAc,cAAc,eAAe,cAAc,cAAc,KAAK,wCAAwC,cAAc,OAAO,KAAK,kCAAkC,aAAa,CAAC;AAC1O,aAAK,cAAc,KAAK,IAAI,kBAAkB,gBAAgB,CAAC;AAC/D,aAAK,cAAc,KAAK,IAAI,kBAAkB,iBAAiB,CAAC;AAChE,gBAAQ,aAAa;AAAA,MACvB,GAAG,CAAAA,SAAO;AACR,aAAK,OAAO,MAAM,0BAA0BA,IAAG;AAC/C,aAAK,cAAc,KAAK,IAAI,gBAAgB,uBAAuBA,IAAG,CAAC;AACvE,eAAOA,IAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,mCAAmC;AACjC,QAAI,KAAK,uCAAuC;AAC9C,aAAO,oBAAoB,WAAW,KAAK,qCAAqC;AAChF,WAAK,wCAAwC;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,kCAAkC;AAChC,SAAK,iCAAiC;AACtC,SAAK,wCAAwC,OAAK;AAChD,YAAM,UAAU,KAAK,2BAA2B,CAAC;AACjD,UAAI,KAAK,eAAe,EAAE,WAAW,SAAS,QAAQ;AACpD,gBAAQ,MAAM,wCAAwC;AAAA,MACxD;AACA,WAAK,SAAS;AAAA,QACZ,oBAAoB;AAAA,QACpB,4BAA4B;AAAA,QAC5B,mBAAmB,KAAK,4BAA4B,KAAK;AAAA,MAC3D,CAAC,EAAE,MAAM,CAAAA,SAAO,KAAK,MAAM,yCAAyCA,IAAG,CAAC;AAAA,IAC1E;AACA,WAAO,iBAAiB,WAAW,KAAK,qCAAqC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,SAAS,CAAC,GAAG,WAAW,MAAM;AAC1C,UAAM,SAAS,KAAK,kBAAkB,KAAK,CAAC;AAC5C,QAAI,KAAK,kCAAkC,KAAK,gBAAgB,GAAG;AACjE,aAAO,eAAe,IAAI,KAAK,WAAW;AAAA,IAC5C;AACA,QAAI,CAAC,KAAK,oBAAoB,KAAK,QAAQ,GAAG;AAC5C,YAAM,IAAI,MAAM,uIAAuI;AAAA,IACzJ;AACA,QAAI,OAAO,KAAK,aAAa,aAAa;AACxC,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACpE;AACA,UAAM,iBAAiB,KAAK,SAAS,eAAe,KAAK,uBAAuB;AAChF,QAAI,gBAAgB;AAClB,WAAK,SAAS,KAAK,YAAY,cAAc;AAAA,IAC/C;AACA,SAAK,uBAAuB,OAAO,KAAK;AACxC,UAAM,SAAS,KAAK,SAAS,cAAc,QAAQ;AACnD,WAAO,KAAK,KAAK;AACjB,SAAK,gCAAgC;AACrC,UAAM,cAAc,KAAK,4BAA4B,KAAK;AAC1D,SAAK,eAAe,MAAM,MAAM,aAAa,UAAU,MAAM,EAAE,KAAK,SAAO;AACzE,aAAO,aAAa,OAAO,GAAG;AAC9B,UAAI,CAAC,KAAK,yBAAyB;AACjC,eAAO,MAAM,SAAS,IAAI;AAAA,MAC5B;AACA,WAAK,SAAS,KAAK,YAAY,MAAM;AAAA,IACvC,CAAC;AACD,UAAM,SAAS,KAAK,OAAO,KAAK,OAAO,OAAK,aAAa,eAAe,GAAG,MAAM,CAAC;AAClF,UAAM,UAAU,KAAK,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,gBAAgB,GAAG,MAAM,CAAC;AAClF,UAAMD,WAAU,GAAG,IAAI,gBAAgB,0BAA0B,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,oBAAoB,CAAC;AAC7G,WAAO,KAAK,CAAC,QAAQ,SAASA,QAAO,CAAC,EAAE,KAAK,IAAI,OAAK;AACpD,UAAI,aAAa,iBAAiB;AAChC,YAAI,EAAE,SAAS,0BAA0B;AACvC,eAAK,cAAc,KAAK,CAAC;AAAA,QAC3B,OAAO;AACL,cAAI,IAAI,gBAAgB,wBAAwB,CAAC;AACjD,eAAK,cAAc,KAAK,CAAC;AAAA,QAC3B;AACA,cAAM;AAAA,MACR,WAAW,EAAE,SAAS,kBAAkB;AACtC,YAAI,IAAI,kBAAkB,oBAAoB;AAC9C,aAAK,cAAc,KAAK,CAAC;AAAA,MAC3B;AACA,aAAO;AAAA,IACT,CAAC,CAAC,EAAE,UAAU;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAC/B,WAAO,KAAK,qBAAqB,OAAO;AAAA,EAC1C;AAAA,EACA,qBAAqB,SAAS;AAC5B,cAAU,WAAW,CAAC;AACtB,WAAO,KAAK,eAAe,MAAM,MAAM,KAAK,0BAA0B,OAAO;AAAA,MAC3E,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,SAAO;AACb,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAItC,cAAM,8BAA8B;AACpC,YAAI,YAAY;AAGhB,YAAI,CAAC,QAAQ,WAAW;AACtB,sBAAY,OAAO,KAAK,KAAK,yBAAyB,KAAK,uBAAuB,OAAO,CAAC;AAAA,QAC5F,WAAW,QAAQ,aAAa,CAAC,QAAQ,UAAU,QAAQ;AACzD,sBAAY,QAAQ;AACpB,oBAAU,SAAS,OAAO;AAAA,QAC5B;AACA,YAAI;AACJ,cAAM,WAAW,CAAAE,UAAQ;AACvB,eAAK,SAAS;AAAA,YACZ,oBAAoBA;AAAA,YACpB,4BAA4B;AAAA,YAC5B,mBAAmB,KAAK;AAAA,UAC1B,CAAC,EAAE,KAAK,MAAM;AACZ,oBAAQ;AACR,oBAAQ,IAAI;AAAA,UACd,GAAG,CAAAD,SAAO;AACR,oBAAQ;AACR,mBAAOA,IAAG;AAAA,UACZ,CAAC;AAAA,QACH;AACA,cAAM,sBAAsB,MAAM;AAChC,cAAI,CAAC,aAAa,UAAU,QAAQ;AAClC,oBAAQ;AACR,mBAAO,IAAI,gBAAgB,gBAAgB,CAAC,CAAC,CAAC;AAAA,UAChD;AAAA,QACF;AACA,YAAI,CAAC,WAAW;AACd,iBAAO,IAAI,gBAAgB,iBAAiB,CAAC,CAAC,CAAC;AAAA,QACjD,OAAO;AACL,qCAA2B,OAAO,YAAY,qBAAqB,2BAA2B;AAAA,QAChG;AACA,cAAM,UAAU,MAAM;AACpB,iBAAO,cAAc,wBAAwB;AAC7C,iBAAO,oBAAoB,WAAW,eAAe;AACrD,iBAAO,oBAAoB,WAAW,QAAQ;AAC9C,cAAI,cAAc,MAAM;AACtB,sBAAU,MAAM;AAAA,UAClB;AACA,sBAAY;AAAA,QACd;AACA,cAAM,WAAW,OAAK;AACpB,gBAAM,UAAU,KAAK,2BAA2B,CAAC;AACjD,cAAI,WAAW,YAAY,MAAM;AAC/B,mBAAO,oBAAoB,WAAW,eAAe;AACrD,qBAAS,OAAO;AAAA,UAClB,OAAO;AACL,oBAAQ,IAAI,oBAAoB;AAAA,UAClC;AAAA,QACF;AACA,cAAM,kBAAkB,WAAS;AAC/B,cAAI,MAAM,QAAQ,aAAa;AAC7B,mBAAO,oBAAoB,WAAW,QAAQ;AAC9C,qBAAS,MAAM,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,eAAO,iBAAiB,WAAW,QAAQ;AAC3C,eAAO,iBAAiB,WAAW,eAAe;AAAA,MACpD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,SAAS;AAE9B,UAAM,SAAS,QAAQ,UAAU;AACjC,UAAM,QAAQ,QAAQ,SAAS;AAC/B,UAAM,OAAO,OAAO,cAAc,OAAO,aAAa,SAAS;AAC/D,UAAM,MAAM,OAAO,aAAa,OAAO,cAAc,UAAU;AAC/D,WAAO,gCAAgC,KAAK,WAAW,MAAM,QAAQ,GAAG,SAAS,IAAI;AAAA,EACvF;AAAA,EACA,2BAA2B,GAAG;AAC5B,QAAI,iBAAiB;AACrB,QAAI,KAAK,4BAA4B;AACnC,wBAAkB,KAAK;AAAA,IACzB;AACA,QAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,OAAO,EAAE,SAAS,UAAU;AAC/C;AAAA,IACF;AACA,UAAM,kBAAkB,EAAE;AAC1B,QAAI,CAAC,gBAAgB,WAAW,cAAc,GAAG;AAC/C;AAAA,IACF;AACA,WAAO,MAAM,gBAAgB,OAAO,eAAe,MAAM;AAAA,EAC3D;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,uBAAuB;AAC/B,cAAQ,KAAK,yEAAyE;AACtF,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,CAAC,cAAc;AACjB,cAAQ,KAAK,iEAAiE;AAC9E,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,aAAa,aAAa;AACxC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iCAAiC;AAC/B,SAAK,gCAAgC;AACrC,SAAK,4BAA4B,OAAK;AACpC,YAAM,SAAS,EAAE,OAAO,YAAY;AACpC,YAAM,SAAS,KAAK,OAAO,YAAY;AACvC,WAAK,MAAM,2BAA2B;AACtC,UAAI,CAAC,OAAO,WAAW,MAAM,GAAG;AAC9B,aAAK,MAAM,6BAA6B,gBAAgB,QAAQ,YAAY,QAAQ,SAAS,CAAC;AAC9F;AAAA,MACF;AAEA,cAAQ,EAAE,MAAM;AAAA,QACd,KAAK;AACH,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,uBAAuB;AAAA,UAC9B,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,oBAAoB;AAAA,UAC3B,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,mBAAmB;AAAA,UAC1B,CAAC;AACD;AAAA,MACJ;AACA,WAAK,MAAM,uCAAuC,CAAC;AAAA,IACrD;AAEA,SAAK,OAAO,kBAAkB,MAAM;AAClC,aAAO,iBAAiB,WAAW,KAAK,yBAAyB;AAAA,IACnE,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,SAAK,MAAM,iBAAiB,mBAAmB;AAC/C,SAAK,cAAc,KAAK,IAAI,eAAe,mBAAmB,CAAC;AAAA,EACjE;AAAA,EACA,sBAAsB;AACpB,SAAK,cAAc,KAAK,IAAI,eAAe,iBAAiB,CAAC;AAC7D,SAAK,sBAAsB;AAC3B,QAAI,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,QAAQ;AAC1D,WAAK,aAAa,EAAE,KAAK,MAAM;AAC7B,aAAK,MAAM,2CAA2C;AAAA,MACxD,CAAC,EAAE,MAAM,MAAM;AACb,aAAK,MAAM,kDAAkD;AAC7D,aAAK,cAAc,KAAK,IAAI,eAAe,oBAAoB,CAAC;AAChE,aAAK,OAAO,IAAI;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,KAAK,0BAA0B;AACxC,WAAK,cAAc,EAAE,MAAM,MAAM,KAAK,MAAM,6CAA6C,CAAC;AAC1F,WAAK,uCAAuC;AAAA,IAC9C,OAAO;AACL,WAAK,cAAc,KAAK,IAAI,eAAe,oBAAoB,CAAC;AAChE,WAAK,OAAO,IAAI;AAAA,IAClB;AAAA,EACF;AAAA,EACA,yCAAyC;AACvC,SAAK,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,wBAAwB,EAAE,SAAS,4BAA4B,EAAE,SAAS,sBAAsB,GAAG,MAAM,CAAC,EAAE,UAAU,OAAK;AACjK,UAAI,EAAE,SAAS,sBAAsB;AACnC,aAAK,MAAM,mDAAmD;AAC9D,aAAK,cAAc,KAAK,IAAI,eAAe,oBAAoB,CAAC;AAChE,aAAK,OAAO,IAAI;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,sBAAsB;AAC3B,SAAK,cAAc,KAAK,IAAI,eAAe,eAAe,CAAC;AAAA,EAC7D;AAAA,EACA,kCAAkC;AAChC,QAAI,KAAK,2BAA2B;AAClC,aAAO,oBAAoB,WAAW,KAAK,yBAAyB;AACpE,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC;AAAA,IACF;AACA,UAAM,iBAAiB,KAAK,SAAS,eAAe,KAAK,sBAAsB;AAC/E,QAAI,gBAAgB;AAClB,WAAK,SAAS,KAAK,YAAY,cAAc;AAAA,IAC/C;AACA,UAAM,SAAS,KAAK,SAAS,cAAc,QAAQ;AACnD,WAAO,KAAK,KAAK;AACjB,SAAK,+BAA+B;AACpC,UAAM,MAAM,KAAK;AACjB,WAAO,aAAa,OAAO,GAAG;AAC9B,WAAO,MAAM,UAAU;AACvB,SAAK,SAAS,KAAK,YAAY,MAAM;AACrC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,yBAAyB;AACvB,SAAK,sBAAsB;AAC3B,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,oBAAoB,YAAY,KAAK,aAAa,KAAK,IAAI,GAAG,KAAK,qBAAqB;AAAA,IAC/F,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,mBAAmB;AAC1B,oBAAc,KAAK,iBAAiB;AACpC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,eAAe;AACb,UAAM,SAAS,KAAK,SAAS,eAAe,KAAK,sBAAsB;AACvE,QAAI,CAAC,QAAQ;AACX,WAAK,OAAO,KAAK,oCAAoC,KAAK,sBAAsB;AAAA,IAClF;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,CAAC,cAAc;AACjB,WAAK,sBAAsB;AAAA,IAC7B;AACA,UAAM,UAAU,KAAK,WAAW,MAAM;AACtC,WAAO,cAAc,YAAY,SAAS,KAAK,MAAM;AAAA,EACvD;AAAA,EACM,iBAAkG;AAAA,+CAAnF,QAAQ,IAAI,YAAY,IAAI,oBAAoB,IAAI,WAAW,OAAO,SAAS,CAAC,GAAG;AACtG,YAAM,OAAO;AACb,UAAI;AACJ,UAAI,mBAAmB;AACrB,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc,KAAK;AAAA,MACrB;AACA,YAAM,QAAQ,MAAM,KAAK,mBAAmB;AAC5C,UAAI,OAAO;AACT,gBAAQ,QAAQ,KAAK,OAAO,sBAAsB,mBAAmB,KAAK;AAAA,MAC5E,OAAO;AACL,gBAAQ;AAAA,MACV;AACA,UAAI,CAAC,KAAK,sBAAsB,CAAC,KAAK,MAAM;AAC1C,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AACA,UAAI,KAAK,OAAO,cAAc;AAC5B,aAAK,eAAe,KAAK,OAAO;AAAA,MAClC,OAAO;AACL,YAAI,KAAK,QAAQ,KAAK,oBAAoB;AACxC,eAAK,eAAe;AAAA,QACtB,WAAW,KAAK,QAAQ,CAAC,KAAK,oBAAoB;AAChD,eAAK,eAAe;AAAA,QACtB,OAAO;AACL,eAAK,eAAe;AAAA,QACtB;AAAA,MACF;AACA,YAAM,iBAAiB,KAAK,SAAS,QAAQ,GAAG,IAAI,KAAK,MAAM;AAC/D,UAAI,QAAQ,KAAK;AACjB,UAAI,KAAK,QAAQ,CAAC,MAAM,MAAM,oBAAoB,GAAG;AACnD,gBAAQ,YAAY;AAAA,MACtB;AACA,UAAI,MAAM,KAAK,WAAW,iBAAiB,mBAAmB,mBAAmB,KAAK,YAAY,IAAI,gBAAgB,mBAAmB,KAAK,QAAQ,IAAI,YAAY,mBAAmB,KAAK,IAAI,mBAAmB,mBAAmB,WAAW,IAAI,YAAY,mBAAmB,KAAK;AAC3R,UAAI,KAAK,aAAa,SAAS,MAAM,KAAK,CAAC,KAAK,aAAa;AAC3D,cAAM,CAAC,WAAW,QAAQ,IAAI,MAAM,KAAK,mCAAmC;AAC5E,YAAI,KAAK,4BAA4B,OAAO,OAAO,cAAc,MAAM,aAAa;AAClF,uBAAa,QAAQ,iBAAiB,QAAQ;AAAA,QAChD,OAAO;AACL,eAAK,SAAS,QAAQ,iBAAiB,QAAQ;AAAA,QACjD;AACA,eAAO,qBAAqB;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,WAAW;AACb,eAAO,iBAAiB,mBAAmB,SAAS;AAAA,MACtD;AACA,UAAI,KAAK,UAAU;AACjB,eAAO,eAAe,mBAAmB,KAAK,QAAQ;AAAA,MACxD;AACA,UAAI,KAAK,MAAM;AACb,eAAO,YAAY,mBAAmB,KAAK;AAAA,MAC7C;AACA,UAAI,UAAU;AACZ,eAAO;AAAA,MACT;AACA,iBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,eAAO,MAAM,mBAAmB,GAAG,IAAI,MAAM,mBAAmB,OAAO,GAAG,CAAC;AAAA,MAC7E;AACA,UAAI,KAAK,mBAAmB;AAC1B,mBAAW,OAAO,OAAO,oBAAoB,KAAK,iBAAiB,GAAG;AACpE,iBAAO,MAAM,MAAM,MAAM,mBAAmB,KAAK,kBAAkB,GAAG,CAAC;AAAA,QACzE;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,yBAAyB,kBAAkB,IAAI,SAAS,IAAI;AAC1D,QAAI,KAAK,gBAAgB;AACvB;AAAA,IACF;AACA,SAAK,iBAAiB;AACtB,QAAI,CAAC,KAAK,oBAAoB,KAAK,QAAQ,GAAG;AAC5C,YAAM,IAAI,MAAM,uIAAuI;AAAA,IACzJ;AACA,QAAI,YAAY,CAAC;AACjB,QAAI,YAAY;AAChB,QAAI,OAAO,WAAW,UAAU;AAC9B,kBAAY;AAAA,IACd,WAAW,OAAO,WAAW,UAAU;AACrC,kBAAY;AAAA,IACd;AACA,SAAK,eAAe,iBAAiB,WAAW,MAAM,OAAO,SAAS,EAAE,KAAK,KAAK,OAAO,OAAO,EAAE,MAAM,WAAS;AAC/G,cAAQ,MAAM,6BAA6B,KAAK;AAChD,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,kBAAkB,IAAI,SAAS,IAAI;AAClD,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,yBAAyB,iBAAiB,MAAM;AAAA,IACvD,OAAO;AACL,WAAK,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,2BAA2B,CAAC,EAAE,UAAU,MAAM,KAAK,yBAAyB,iBAAiB,MAAM,CAAC;AAAA,IAC9I;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,4BAA4B,SAAS;AACnC,UAAM,OAAO;AACb,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,cAAc;AAAA,QAClB,UAAU,KAAK,kBAAkB;AAAA,QACjC,SAAS,KAAK,WAAW;AAAA,QACzB,aAAa,KAAK,eAAe;AAAA,QACjC,OAAO,KAAK;AAAA,MACd;AACA,cAAQ,gBAAgB,WAAW;AAAA,IACrC;AAAA,EACF;AAAA,EACA,yBAAyB,aAAa,cAAc,WAAW,eAAe,kBAAkB;AAC9F,SAAK,SAAS,QAAQ,gBAAgB,WAAW;AACjD,QAAI,iBAAiB,CAAC,MAAM,QAAQ,aAAa,GAAG;AAClD,WAAK,SAAS,QAAQ,kBAAkB,KAAK,UAAU,cAAc,MAAM,GAAG,CAAC,CAAC;AAAA,IAClF,WAAW,iBAAiB,MAAM,QAAQ,aAAa,GAAG;AACxD,WAAK,SAAS,QAAQ,kBAAkB,KAAK,UAAU,aAAa,CAAC;AAAA,IACvE;AACA,SAAK,SAAS,QAAQ,0BAA0B,KAAK,KAAK,gBAAgB,IAAI,CAAC;AAC/E,QAAI,WAAW;AACb,YAAM,wBAAwB,YAAY;AAC1C,YAAM,MAAM,KAAK,gBAAgB,IAAI;AACrC,YAAM,YAAY,IAAI,QAAQ,IAAI;AAClC,WAAK,SAAS,QAAQ,cAAc,KAAK,SAAS;AAAA,IACpD;AACA,QAAI,cAAc;AAChB,WAAK,SAAS,QAAQ,iBAAiB,YAAY;AAAA,IACrD;AACA,QAAI,kBAAkB;AACpB,uBAAiB,QAAQ,CAAC,OAAO,QAAQ;AACvC,aAAK,SAAS,QAAQ,KAAK,KAAK;AAAA,MAClC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,UAAU,MAAM;AACvB,QAAI,KAAK,OAAO,iBAAiB,QAAQ;AACvC,aAAO,KAAK,iBAAiB,OAAO,EAAE,KAAK,MAAM,IAAI;AAAA,IACvD,OAAO;AACL,aAAO,KAAK,qBAAqB,OAAO;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,iBAAiB,aAAa;AAC5B,QAAI,CAAC,eAAe,YAAY,WAAW,GAAG;AAC5C,aAAO,CAAC;AAAA,IACV;AACA,QAAI,YAAY,OAAO,CAAC,MAAM,KAAK;AACjC,oBAAc,YAAY,OAAO,CAAC;AAAA,IACpC;AACA,WAAO,KAAK,UAAU,iBAAiB,WAAW;AAAA,EACpD;AAAA,EACM,iBAAiB,UAAU,MAAM;AAAA;AACrC,gBAAU,WAAW,CAAC;AACtB,YAAM,cAAc,QAAQ,qBAAqB,QAAQ,mBAAmB,UAAU,CAAC,IAAI,OAAO,SAAS;AAC3G,YAAM,QAAQ,KAAK,oBAAoB,WAAW;AAClD,YAAM,OAAO,MAAM,MAAM;AACzB,YAAM,QAAQ,MAAM,OAAO;AAC3B,YAAM,eAAe,MAAM,eAAe;AAC1C,UAAI,CAAC,QAAQ,4BAA4B;AACvC,cAAM,OAAO,SAAS,SAAS,SAAS,WAAW,SAAS,OAAO,QAAQ,eAAe,EAAE,EAAE,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,wBAAwB,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,QAAQ,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE,IAAI,SAAS;AAC3T,gBAAQ,aAAa,MAAM,OAAO,MAAM,IAAI;AAAA,MAC9C;AACA,YAAM,CAAC,cAAc,SAAS,IAAI,KAAK,WAAW,KAAK;AACvD,WAAK,QAAQ;AACb,UAAI,MAAM,OAAO,GAAG;AAClB,aAAK,MAAM,uBAAuB;AAClC,aAAK,iBAAiB,SAAS,KAAK;AACpC,cAAMA,OAAM,IAAI,gBAAgB,cAAc,CAAC,GAAG,KAAK;AACvD,aAAK,cAAc,KAAKA,IAAG;AAC3B,eAAO,QAAQ,OAAOA,IAAG;AAAA,MAC3B;AACA,UAAI,CAAC,QAAQ,mBAAmB;AAC9B,YAAI,CAAC,cAAc;AACjB,eAAK,mBAAmB;AACxB,iBAAO,QAAQ,QAAQ;AAAA,QACzB;AACA,YAAI,CAAC,QAAQ,yBAAyB;AACpC,gBAAM,UAAU,KAAK,cAAc,YAAY;AAC/C,cAAI,CAAC,SAAS;AACZ,kBAAM,QAAQ,IAAI,gBAAgB,0BAA0B,IAAI;AAChE,iBAAK,cAAc,KAAK,KAAK;AAC7B,mBAAO,QAAQ,OAAO,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,YAAY;AACnC,UAAI,MAAM;AACR,cAAM,KAAK,iBAAiB,MAAM,OAAO;AACzC,aAAK,sBAAsB;AAC3B,eAAO,QAAQ,QAAQ;AAAA,MACzB,OAAO;AACL,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAAA,IACF;AAAA;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,OAAO,wBAAwB;AACtC,WAAK,SAAS,QAAQ,mBAAmB,OAAO,SAAS,WAAW,OAAO,SAAS,MAAM;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,UAAM,iBAAiB,KAAK,SAAS,QAAQ,iBAAiB;AAC9D,QAAI,gBAAgB;AAClB,cAAQ,aAAa,MAAM,IAAI,OAAO,SAAS,SAAS,cAAc;AAAA,IACxE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,aAAa;AAC/B,QAAI,CAAC,eAAe,YAAY,WAAW,GAAG;AAC5C,aAAO,KAAK,UAAU,sBAAsB;AAAA,IAC9C;AAEA,QAAI,YAAY,OAAO,CAAC,MAAM,KAAK;AACjC,oBAAc,YAAY,OAAO,CAAC;AAAA,IACpC;AACA,WAAO,KAAK,UAAU,iBAAiB,WAAW;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM,SAAS;AAC9B,QAAI,SAAS,IAAI,WAAW;AAAA,MAC1B,SAAS,IAAI,wBAAwB;AAAA,IACvC,CAAC,EAAE,IAAI,cAAc,oBAAoB,EAAE,IAAI,QAAQ,IAAI,EAAE,IAAI,gBAAgB,QAAQ,qBAAqB,KAAK,WAAW;AAC9H,QAAI,CAAC,KAAK,aAAa;AACrB,UAAI;AACJ,UAAI,KAAK,4BAA4B,OAAO,OAAO,cAAc,MAAM,aAAa;AAClF,uBAAe,aAAa,QAAQ,eAAe;AAAA,MACrD,OAAO;AACL,uBAAe,KAAK,SAAS,QAAQ,eAAe;AAAA,MACtD;AACA,UAAI,CAAC,cAAc;AACjB,gBAAQ,KAAK,0CAA0C;AAAA,MACzD,OAAO;AACL,iBAAS,OAAO,IAAI,iBAAiB,YAAY;AAAA,MACnD;AAAA,IACF;AACA,WAAO,KAAK,qBAAqB,QAAQ,OAAO;AAAA,EAClD;AAAA,EACA,qBAAqB,QAAQ,SAAS;AACpC,cAAU,WAAW,CAAC;AACtB,SAAK,mCAAmC,KAAK,eAAe,eAAe;AAC3E,QAAI,UAAU,IAAI,YAAY,EAAE,IAAI,gBAAgB,mCAAmC;AACvF,QAAI,KAAK,kBAAkB;AACzB,YAAM,SAAS,KAAK,GAAG,KAAK,QAAQ,IAAI,KAAK,iBAAiB,EAAE;AAChE,gBAAU,QAAQ,IAAI,iBAAiB,WAAW,MAAM;AAAA,IAC1D;AACA,QAAI,CAAC,KAAK,kBAAkB;AAC1B,eAAS,OAAO,IAAI,aAAa,KAAK,QAAQ;AAAA,IAChD;AACA,QAAI,CAAC,KAAK,oBAAoB,KAAK,mBAAmB;AACpD,eAAS,OAAO,IAAI,iBAAiB,KAAK,iBAAiB;AAAA,IAC7D;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,KAAK,mBAAmB;AAC1B,mBAAW,OAAO,OAAO,oBAAoB,KAAK,iBAAiB,GAAG;AACpE,mBAAS,OAAO,IAAI,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,QACtD;AAAA,MACF;AACA,WAAK,KAAK,KAAK,KAAK,eAAe,QAAQ;AAAA,QACzC;AAAA,MACF,CAAC,EAAE,UAAU,mBAAiB;AAC5B,aAAK,MAAM,yBAAyB,aAAa;AACjD,aAAK,yBAAyB,cAAc,cAAc,cAAc,eAAe,cAAc,cAAc,KAAK,wCAAwC,cAAc,OAAO,KAAK,kCAAkC,aAAa,CAAC;AAC1O,YAAI,KAAK,QAAQ,cAAc,UAAU;AACvC,eAAK,eAAe,cAAc,UAAU,cAAc,cAAc,QAAQ,iBAAiB,EAAE,KAAK,YAAU;AAChH,iBAAK,aAAa,MAAM;AACxB,iBAAK,cAAc,KAAK,IAAI,kBAAkB,gBAAgB,CAAC;AAC/D,iBAAK,cAAc,KAAK,IAAI,kBAAkB,iBAAiB,CAAC;AAChE,oBAAQ,aAAa;AAAA,UACvB,CAAC,EAAE,MAAM,YAAU;AACjB,iBAAK,cAAc,KAAK,IAAI,gBAAgB,0BAA0B,MAAM,CAAC;AAC7E,oBAAQ,MAAM,yBAAyB;AACvC,oBAAQ,MAAM,MAAM;AACpB,mBAAO,MAAM;AAAA,UACf,CAAC;AAAA,QACH,OAAO;AACL,eAAK,cAAc,KAAK,IAAI,kBAAkB,gBAAgB,CAAC;AAC/D,eAAK,cAAc,KAAK,IAAI,kBAAkB,iBAAiB,CAAC;AAChE,kBAAQ,aAAa;AAAA,QACvB;AAAA,MACF,GAAG,CAAAA,SAAO;AACR,gBAAQ,MAAM,uBAAuBA,IAAG;AACxC,aAAK,cAAc,KAAK,IAAI,gBAAgB,uBAAuBA,IAAG,CAAC;AACvE,eAAOA,IAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,UAAU,MAAM;AACnC,cAAU,WAAW,CAAC;AACtB,QAAI;AACJ,QAAI,QAAQ,oBAAoB;AAC9B,cAAQ,KAAK,UAAU,sBAAsB,QAAQ,kBAAkB;AAAA,IACzE,OAAO;AACL,cAAQ,KAAK,UAAU,sBAAsB;AAAA,IAC/C;AACA,SAAK,MAAM,cAAc,KAAK;AAC9B,UAAM,QAAQ,MAAM,OAAO;AAC3B,UAAM,CAAC,cAAc,SAAS,IAAI,KAAK,WAAW,KAAK;AACvD,SAAK,QAAQ;AACb,QAAI,MAAM,OAAO,GAAG;AAClB,WAAK,MAAM,uBAAuB;AAClC,WAAK,iBAAiB,SAAS,KAAK;AACpC,YAAMA,OAAM,IAAI,gBAAgB,eAAe,CAAC,GAAG,KAAK;AACxD,WAAK,cAAc,KAAKA,IAAG;AAC3B,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AACA,UAAM,cAAc,MAAM,cAAc;AACxC,UAAM,UAAU,MAAM,UAAU;AAChC,UAAM,eAAe,MAAM,eAAe;AAC1C,UAAM,gBAAgB,MAAM,OAAO;AACnC,QAAI,CAAC,KAAK,sBAAsB,CAAC,KAAK,MAAM;AAC1C,aAAO,QAAQ,OAAO,2DAA2D;AAAA,IACnF;AACA,QAAI,KAAK,sBAAsB,CAAC,aAAa;AAC3C,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AACA,QAAI,KAAK,sBAAsB,CAAC,QAAQ,2BAA2B,CAAC,OAAO;AACzE,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AACA,QAAI,KAAK,QAAQ,CAAC,SAAS;AACzB,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AACA,QAAI,KAAK,wBAAwB,CAAC,cAAc;AAC9C,WAAK,OAAO,KAAK,iJAA2J;AAAA,IAC9K;AACA,QAAI,KAAK,sBAAsB,CAAC,QAAQ,mBAAmB;AACzD,YAAM,UAAU,KAAK,cAAc,YAAY;AAC/C,UAAI,CAAC,SAAS;AACZ,cAAM,QAAQ,IAAI,gBAAgB,0BAA0B,IAAI;AAChE,aAAK,cAAc,KAAK,KAAK;AAC7B,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB;AAC3B,WAAK,yBAAyB,aAAa,MAAM,MAAM,YAAY,KAAK,KAAK,wCAAwC,aAAa;AAAA,IACpI;AACA,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,cAAc,KAAK,IAAI,kBAAkB,gBAAgB,CAAC;AAC/D,UAAI,KAAK,uBAAuB,CAAC,QAAQ,4BAA4B;AACnE,aAAK,kBAAkB;AAAA,MACzB;AACA,WAAK,4BAA4B,OAAO;AACxC,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,WAAO,KAAK,eAAe,SAAS,aAAa,QAAQ,iBAAiB,EAAE,KAAK,YAAU;AACzF,UAAI,QAAQ,mBAAmB;AAC7B,eAAO,QAAQ,kBAAkB;AAAA,UAC/B;AAAA,UACA,UAAU,OAAO;AAAA,UACjB,SAAS,OAAO;AAAA,UAChB;AAAA,QACF,CAAC,EAAE,KAAK,MAAM,MAAM;AAAA,MACtB;AACA,aAAO;AAAA,IACT,CAAC,EAAE,KAAK,YAAU;AAChB,WAAK,aAAa,MAAM;AACxB,WAAK,kBAAkB,YAAY;AACnC,UAAI,KAAK,uBAAuB,CAAC,QAAQ,4BAA4B;AACnE,aAAK,kBAAkB;AAAA,MACzB;AACA,WAAK,cAAc,KAAK,IAAI,kBAAkB,gBAAgB,CAAC;AAC/D,WAAK,4BAA4B,OAAO;AACxC,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,YAAU;AACjB,WAAK,cAAc,KAAK,IAAI,gBAAgB,0BAA0B,MAAM,CAAC;AAC7E,WAAK,OAAO,MAAM,yBAAyB;AAC3C,WAAK,OAAO,MAAM,MAAM;AACxB,aAAO,QAAQ,OAAO,MAAM;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,QAAI,OAAO;AACT,YAAM,MAAM,MAAM,QAAQ,KAAK,OAAO,mBAAmB;AACzD,UAAI,MAAM,IAAI;AACZ,gBAAQ,MAAM,OAAO,GAAG,GAAG;AAC3B,oBAAY,MAAM,OAAO,MAAM,KAAK,OAAO,oBAAoB,MAAM;AAAA,MACvE;AAAA,IACF;AACA,WAAO,CAAC,OAAO,SAAS;AAAA,EAC1B;AAAA,EACA,cAAc,cAAc;AAC1B,QAAI;AACJ,QAAI,KAAK,4BAA4B,OAAO,OAAO,cAAc,MAAM,aAAa;AAClF,mBAAa,aAAa,QAAQ,OAAO;AAAA,IAC3C,OAAO;AACL,mBAAa,KAAK,SAAS,QAAQ,OAAO;AAAA,IAC5C;AACA,QAAI,eAAe,cAAc;AAC/B,YAAMA,OAAM;AACZ,cAAQ,MAAMA,MAAK,YAAY,YAAY;AAC3C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,SAAS;AACpB,SAAK,SAAS,QAAQ,YAAY,QAAQ,OAAO;AACjD,SAAK,SAAS,QAAQ,uBAAuB,QAAQ,iBAAiB;AACtE,SAAK,SAAS,QAAQ,uBAAuB,KAAK,QAAQ,gBAAgB;AAC1E,SAAK,SAAS,QAAQ,sBAAsB,KAAK,KAAK,gBAAgB,IAAI,CAAC;AAAA,EAC7E;AAAA,EACA,kBAAkB,cAAc;AAC9B,SAAK,SAAS,QAAQ,iBAAiB,YAAY;AAAA,EACrD;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS,QAAQ,eAAe;AAAA,EAC9C;AAAA,EACA,iBAAiB,SAAS,OAAO;AAC/B,QAAI,QAAQ,cAAc;AACxB,cAAQ,aAAa,KAAK;AAAA,IAC5B;AACA,QAAI,KAAK,uBAAuB,CAAC,QAAQ,4BAA4B;AACnE,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,mBAAmB,iBAAiB,KAAQ;AAC1C,QAAI,CAAC,KAAK,kBAAkB,KAAK,mBAAmB,GAAG;AACrD,aAAO;AAAA,IACT;AACA,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,SAAS,aAAa,iBAAiB,OAAO;AAC3D,UAAM,aAAa,QAAQ,MAAM,GAAG;AACpC,UAAM,eAAe,KAAK,UAAU,WAAW,CAAC,CAAC;AACjD,UAAM,aAAa,iBAAiB,YAAY;AAChD,UAAM,SAAS,KAAK,MAAM,UAAU;AACpC,UAAM,eAAe,KAAK,UAAU,WAAW,CAAC,CAAC;AACjD,UAAM,aAAa,iBAAiB,YAAY;AAChD,UAAM,SAAS,KAAK,MAAM,UAAU;AACpC,QAAI;AACJ,QAAI,KAAK,4BAA4B,OAAO,OAAO,cAAc,MAAM,aAAa;AAClF,mBAAa,aAAa,QAAQ,OAAO;AAAA,IAC3C,OAAO;AACL,mBAAa,KAAK,SAAS,QAAQ,OAAO;AAAA,IAC5C;AACA,QAAI,MAAM,QAAQ,OAAO,GAAG,GAAG;AAC7B,UAAI,OAAO,IAAI,MAAM,OAAK,MAAM,KAAK,QAAQ,GAAG;AAC9C,cAAMA,OAAM,qBAAqB,OAAO,IAAI,KAAK,GAAG;AACpD,aAAK,OAAO,KAAKA,IAAG;AACpB,eAAO,QAAQ,OAAOA,IAAG;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,UAAI,OAAO,QAAQ,KAAK,UAAU;AAChC,cAAMA,OAAM,qBAAqB,OAAO;AACxC,aAAK,OAAO,KAAKA,IAAG;AACpB,eAAO,QAAQ,OAAOA,IAAG;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,CAAC,OAAO,KAAK;AACf,YAAMA,OAAM;AACZ,WAAK,OAAO,KAAKA,IAAG;AACpB,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AAMA,QAAI,KAAK,wBAAwB,KAAK,wBAAwB,KAAK,yBAAyB,OAAO,KAAK,GAAG;AACzG,YAAMA,OAAM,8EAAmF,KAAK,oBAAoB,mBAAmB,OAAO,KAAK,CAAC;AACxJ,WAAK,OAAO,KAAKA,IAAG;AACpB,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AACA,QAAI,CAAC,OAAO,KAAK;AACf,YAAMA,OAAM;AACZ,WAAK,OAAO,KAAKA,IAAG;AACpB,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AACA,QAAI,CAAC,KAAK,mBAAmB,OAAO,QAAQ,KAAK,QAAQ;AACvD,YAAMA,OAAM,mBAAmB,OAAO;AACtC,WAAK,OAAO,KAAKA,IAAG;AACpB,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AACA,QAAI,CAAC,kBAAkB,OAAO,UAAU,YAAY;AAClD,YAAMA,OAAM,kBAAkB,OAAO;AACrC,WAAK,OAAO,KAAKA,IAAG;AACpB,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AAKA,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,cAAc,MAAM,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,aAAa;AACpI,WAAK,qBAAqB;AAAA,IAC5B;AACA,QAAI,CAAC,KAAK,sBAAsB,KAAK,sBAAsB,CAAC,OAAO,SAAS,GAAG;AAC7E,YAAMA,OAAM;AACZ,WAAK,OAAO,KAAKA,IAAG;AACpB,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AACA,UAAM,MAAM,KAAK,gBAAgB,IAAI;AACrC,UAAM,eAAe,OAAO,MAAM;AAClC,UAAM,gBAAgB,OAAO,MAAM;AACnC,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,eAAe,mBAAmB,OAAO,gBAAgB,kBAAkB,KAAK,2BAA2B,KAAK;AAClH,YAAMA,OAAM;AACZ,cAAQ,MAAMA,IAAG;AACjB,cAAQ,MAAM;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,QAAQ,OAAOA,IAAG;AAAA,IAC3B;AACA,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA,MAAM,KAAK;AAAA,MACX,eAAe;AAAA,MACf,eAAe;AAAA,MACf,UAAU,MAAM,KAAK,SAAS;AAAA,IAChC;AACA,QAAI,KAAK,oBAAoB;AAC3B,aAAO,KAAK,eAAe,gBAAgB,EAAE,KAAK,MAAM;AACtD,cAAM,SAAS;AAAA,UACb;AAAA,UACA,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,kBAAkB;AAAA,QACpB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO,KAAK,YAAY,gBAAgB,EAAE,KAAK,iBAAe;AAC5D,UAAI,CAAC,KAAK,sBAAsB,KAAK,sBAAsB,CAAC,aAAa;AACvE,cAAMA,OAAM;AACZ,aAAK,OAAO,KAAKA,IAAG;AACpB,eAAO,QAAQ,OAAOA,IAAG;AAAA,MAC3B;AACA,aAAO,KAAK,eAAe,gBAAgB,EAAE,KAAK,MAAM;AACtD,cAAM,qBAAqB,CAAC,KAAK;AACjC,cAAM,SAAS;AAAA,UACb;AAAA,UACA,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,kBAAkB;AAAA,QACpB;AACA,YAAI,oBAAoB;AACtB,iBAAO,KAAK,YAAY,gBAAgB,EAAE,KAAK,CAAAE,iBAAe;AAC5D,gBAAI,KAAK,sBAAsB,CAACA,cAAa;AAC3C,oBAAMF,OAAM;AACZ,mBAAK,OAAO,KAAKA,IAAG;AACpB,qBAAO,QAAQ,OAAOA,IAAG;AAAA,YAC3B,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,UAAM,SAAS,KAAK,SAAS,QAAQ,qBAAqB;AAC1D,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACjB,UAAM,SAAS,KAAK,SAAS,QAAQ,gBAAgB;AACrD,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,WAAO,KAAK,WAAW,KAAK,SAAS,QAAQ,UAAU,IAAI;AAAA,EAC7D;AAAA,EACA,UAAU,YAAY;AACpB,WAAO,WAAW,SAAS,MAAM,GAAG;AAClC,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,WAAO,KAAK,WAAW,KAAK,SAAS,QAAQ,cAAc,IAAI;AAAA,EACjE;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,WAAW,KAAK,SAAS,QAAQ,eAAe,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AACzB,QAAI,CAAC,KAAK,SAAS,QAAQ,YAAY,GAAG;AACxC,aAAO;AAAA,IACT;AACA,WAAO,SAAS,KAAK,SAAS,QAAQ,YAAY,GAAG,EAAE;AAAA,EACzD;AAAA,EACA,yBAAyB;AACvB,WAAO,SAAS,KAAK,SAAS,QAAQ,wBAAwB,GAAG,EAAE;AAAA,EACrE;AAAA,EACA,qBAAqB;AACnB,WAAO,SAAS,KAAK,SAAS,QAAQ,oBAAoB,GAAG,EAAE;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,SAAS,QAAQ,qBAAqB,GAAG;AACjD,aAAO;AAAA,IACT;AACA,WAAO,SAAS,KAAK,SAAS,QAAQ,qBAAqB,GAAG,EAAE;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,QAAI,KAAK,eAAe,GAAG;AACzB,YAAM,YAAY,KAAK,SAAS,QAAQ,YAAY;AACpD,YAAM,MAAM,KAAK,gBAAgB,IAAI;AACrC,UAAI,aAAa,SAAS,WAAW,EAAE,IAAI,KAAK,0BAA0B,IAAI,QAAQ,IAAI,KAAK,mBAAmB,GAAG;AACnH,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,QAAI,KAAK,WAAW,GAAG;AACrB,YAAM,YAAY,KAAK,SAAS,QAAQ,qBAAqB;AAC7D,YAAM,MAAM,KAAK,gBAAgB,IAAI;AACrC,UAAI,aAAa,SAAS,WAAW,EAAE,IAAI,KAAK,0BAA0B,IAAI,QAAQ,IAAI,KAAK,mBAAmB,GAAG;AACnH,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,+BAA+B,mBAAmB;AAChD,WAAO,KAAK,YAAY,KAAK,OAAO,yBAAyB,KAAK,OAAO,sBAAsB,QAAQ,iBAAiB,KAAK,KAAK,KAAK,SAAS,QAAQ,iBAAiB,MAAM,OAAO,KAAK,MAAM,KAAK,SAAS,QAAQ,iBAAiB,CAAC,IAAI;AAAA,EAC/O;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,WAAO,YAAY,KAAK,eAAe;AAAA,EACzC;AAAA,EACA,OAAO,mBAAmB,CAAC,GAAG,QAAQ,IAAI;AACxC,QAAI,wBAAwB;AAC5B,QAAI,OAAO,qBAAqB,WAAW;AACzC,8BAAwB;AACxB,yBAAmB,CAAC;AAAA,IACtB;AACA,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,SAAS,WAAW,cAAc;AACvC,SAAK,SAAS,WAAW,UAAU;AACnC,SAAK,SAAS,WAAW,eAAe;AACxC,QAAI,KAAK,0BAA0B;AACjC,mBAAa,WAAW,OAAO;AAC/B,mBAAa,WAAW,eAAe;AAAA,IACzC,OAAO;AACL,WAAK,SAAS,WAAW,OAAO;AAChC,WAAK,SAAS,WAAW,eAAe;AAAA,IAC1C;AACA,SAAK,SAAS,WAAW,YAAY;AACrC,SAAK,SAAS,WAAW,qBAAqB;AAC9C,SAAK,SAAS,WAAW,qBAAqB;AAC9C,SAAK,SAAS,WAAW,oBAAoB;AAC7C,SAAK,SAAS,WAAW,wBAAwB;AACjD,SAAK,SAAS,WAAW,gBAAgB;AACzC,SAAK,SAAS,WAAW,eAAe;AACxC,QAAI,KAAK,OAAO,uBAAuB;AACrC,WAAK,OAAO,sBAAsB,QAAQ,iBAAe,KAAK,SAAS,WAAW,WAAW,CAAC;AAAA,IAChG;AACA,SAAK,uBAAuB;AAC5B,SAAK,cAAc,KAAK,IAAI,eAAe,QAAQ,CAAC;AACpD,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,QAAI,uBAAuB;AACzB;AAAA,IACF;AAIA,QAAI;AACJ,QAAI,CAAC,KAAK,oBAAoB,KAAK,SAAS,GAAG;AAC7C,YAAM,IAAI,MAAM,wIAAwI;AAAA,IAC1J;AAEA,QAAI,KAAK,UAAU,QAAQ,IAAI,IAAI,IAAI;AACrC,kBAAY,KAAK,UAAU,QAAQ,oBAAoB,mBAAmB,QAAQ,CAAC,EAAE,QAAQ,qBAAqB,mBAAmB,KAAK,QAAQ,CAAC;AAAA,IACrJ,OAAO;AACL,UAAI,SAAS,IAAI,WAAW;AAAA,QAC1B,SAAS,IAAI,wBAAwB;AAAA,MACvC,CAAC;AACD,UAAI,UAAU;AACZ,iBAAS,OAAO,IAAI,iBAAiB,QAAQ;AAAA,MAC/C;AACA,YAAM,gBAAgB,KAAK,yBAAyB,KAAK,8CAA8C,KAAK,eAAe;AAC3H,UAAI,eAAe;AACjB,iBAAS,OAAO,IAAI,4BAA4B,aAAa;AAC7D,YAAI,OAAO;AACT,mBAAS,OAAO,IAAI,SAAS,KAAK;AAAA,QACpC;AAAA,MACF;AACA,iBAAW,OAAO,kBAAkB;AAClC,iBAAS,OAAO,IAAI,KAAK,iBAAiB,GAAG,CAAC;AAAA,MAChD;AACA,kBAAY,KAAK,aAAa,KAAK,UAAU,QAAQ,GAAG,IAAI,KAAK,MAAM,OAAO,OAAO,SAAS;AAAA,IAChG;AACA,SAAK,OAAO,QAAQ,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AACnB,UAAM,OAAO;AACb,WAAO,KAAK,YAAY,EAAE,KAAK,SAAU,OAAO;AAM9C,UAAI,KAAK,4BAA4B,OAAO,OAAO,cAAc,MAAM,aAAa;AAClF,qBAAa,QAAQ,SAAS,KAAK;AAAA,MACrC,OAAO;AACL,aAAK,SAAS,QAAQ,SAAS,KAAK;AAAA,MACtC;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,iCAAiC;AACtC,UAAM,qBAAqB,KAAK,SAAS,eAAe,KAAK,uBAAuB;AACpF,QAAI,oBAAoB;AACtB,yBAAmB,OAAO;AAAA,IAC5B;AACA,SAAK,sBAAsB;AAC3B,SAAK,gCAAgC;AACrC,UAAM,oBAAoB,KAAK,SAAS,eAAe,KAAK,sBAAsB;AAClF,QAAI,mBAAmB;AACrB,wBAAkB,OAAO;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,WAAO,IAAI,QAAQ,aAAW;AAC5B,UAAI,KAAK,QAAQ;AACf,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAOA,YAAM,aAAa;AACnB,UAAI,OAAO;AACX,UAAI,KAAK;AACT,YAAM,SAAS,OAAO,SAAS,cAAc,OAAO,KAAK,UAAU,KAAK,UAAU;AAClF,UAAI,QAAQ;AACV,YAAI,QAAQ,IAAI,WAAW,IAAI;AAC/B,eAAO,gBAAgB,KAAK;AAE5B,YAAI,CAAC,MAAM,KAAK;AACd,gBAAM,MAAM,MAAM,UAAU;AAAA,QAC9B;AACA,gBAAQ,MAAM,IAAI,OAAK,WAAW,WAAW,IAAI,WAAW,MAAM,CAAC;AACnE,aAAK,OAAO,aAAa,MAAM,MAAM,KAAK;AAAA,MAC5C,OAAO;AACL,eAAO,IAAI,QAAQ;AACjB,gBAAM,WAAW,KAAK,OAAO,IAAI,WAAW,SAAS,CAAC;AAAA,QACxD;AAAA,MACF;AACA,cAAQ,gBAAgB,EAAE,CAAC;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACM,YAAY,QAAQ;AAAA;AACxB,UAAI,CAAC,KAAK,wBAAwB;AAChC,aAAK,OAAO,KAAK,6DAA6D;AAC9E,eAAO;AAAA,MACT;AACA,aAAO,KAAK,uBAAuB,eAAe,MAAM;AAAA,IAC1D;AAAA;AAAA,EACA,eAAe,QAAQ;AACrB,QAAI,CAAC,KAAK,wBAAwB;AAChC,WAAK,OAAO,KAAK,+DAA+D;AAChF,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,WAAO,KAAK,uBAAuB,kBAAkB,MAAM;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,kBAAkB,IAAI,SAAS,CAAC,GAAG;AAC/C,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,KAAK,aAAa,iBAAiB,MAAM;AAAA,IAClD,OAAO;AACL,aAAO,KAAK,iBAAiB,iBAAiB,MAAM;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,kBAAkB,IAAI,SAAS,CAAC,GAAG;AAC9C,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,qBAAqB,iBAAiB,MAAM;AAAA,IACnD,OAAO;AACL,WAAK,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,2BAA2B,CAAC,EAAE,UAAU,MAAM,KAAK,qBAAqB,iBAAiB,MAAM,CAAC;AAAA,IAC1I;AAAA,EACF;AAAA,EACA,qBAAqB,kBAAkB,IAAI,SAAS,CAAC,GAAG;AACtD,QAAI,CAAC,KAAK,oBAAoB,KAAK,QAAQ,GAAG;AAC5C,YAAM,IAAI,MAAM,uIAAuI;AAAA,IACzJ;AACA,QAAI,YAAY,CAAC;AACjB,QAAI,YAAY;AAChB,QAAI,OAAO,WAAW,UAAU;AAC9B,kBAAY;AAAA,IACd,WAAW,OAAO,WAAW,UAAU;AACrC,kBAAY;AAAA,IACd;AACA,SAAK,eAAe,iBAAiB,WAAW,MAAM,OAAO,SAAS,EAAE,KAAK,KAAK,OAAO,OAAO,EAAE,MAAM,WAAS;AAC/G,cAAQ,MAAM,oCAAoC;AAClD,cAAQ,MAAM,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACM,qCAAqC;AAAA;AACzC,UAAI,CAAC,KAAK,QAAQ;AAChB,cAAM,IAAI,MAAM,mGAAmG;AAAA,MACrH;AACA,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,YAAM,eAAe,MAAM,KAAK,OAAO,SAAS,UAAU,SAAS;AACnE,YAAM,YAAY,gBAAgB,YAAY;AAC9C,aAAO,CAAC,WAAW,QAAQ;AAAA,IAC7B;AAAA;AAAA,EACA,kCAAkC,eAAe;AAC/C,UAAM,kBAAkB,oBAAI,IAAI;AAChC,QAAI,CAAC,KAAK,OAAO,uBAAuB;AACtC,aAAO;AAAA,IACT;AACA,SAAK,OAAO,sBAAsB,QAAQ,yBAAuB;AAC/D,UAAI,cAAc,mBAAmB,GAAG;AACtC,wBAAgB,IAAI,qBAAqB,KAAK,UAAU,cAAc,mBAAmB,CAAC,CAAC;AAAA,MAC7F;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,mBAAmB,CAAC,GAAG,mBAAmB,OAAO;AACpE,UAAM,iBAAiB,KAAK;AAC5B,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,CAAC,aAAa;AAChB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,QAAI,SAAS,IAAI,WAAW;AAAA,MAC1B,SAAS,IAAI,wBAAwB;AAAA,IACvC,CAAC;AACD,QAAI,UAAU,IAAI,YAAY,EAAE,IAAI,gBAAgB,mCAAmC;AACvF,QAAI,KAAK,kBAAkB;AACzB,YAAM,SAAS,KAAK,GAAG,KAAK,QAAQ,IAAI,KAAK,iBAAiB,EAAE;AAChE,gBAAU,QAAQ,IAAI,iBAAiB,WAAW,MAAM;AAAA,IAC1D;AACA,QAAI,CAAC,KAAK,kBAAkB;AAC1B,eAAS,OAAO,IAAI,aAAa,KAAK,QAAQ;AAAA,IAChD;AACA,QAAI,CAAC,KAAK,oBAAoB,KAAK,mBAAmB;AACpD,eAAS,OAAO,IAAI,iBAAiB,KAAK,iBAAiB;AAAA,IAC7D;AACA,QAAI,KAAK,mBAAmB;AAC1B,iBAAW,OAAO,OAAO,oBAAoB,KAAK,iBAAiB,GAAG;AACpE,iBAAS,OAAO,IAAI,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACtD;AAAA,IACF;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa;AACf,cAAM,mBAAmB,OAAO,IAAI,SAAS,WAAW,EAAE,IAAI,mBAAmB,cAAc;AAC/F,4BAAoB,KAAK,KAAK,KAAK,gBAAgB,kBAAkB;AAAA,UACnE;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,4BAAoB,GAAG,IAAI;AAAA,MAC7B;AACA,UAAI,cAAc;AAChB,cAAM,mBAAmB,OAAO,IAAI,SAAS,YAAY,EAAE,IAAI,mBAAmB,eAAe;AACjG,6BAAqB,KAAK,KAAK,KAAK,gBAAgB,kBAAkB;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,6BAAqB,GAAG,IAAI;AAAA,MAC9B;AACA,UAAI,kBAAkB;AACpB,4BAAoB,kBAAkB,KAAK,WAAW,CAAAA,SAAO;AAC3D,cAAIA,KAAI,WAAW,GAAG;AACpB,mBAAO,GAAG,IAAI;AAAA,UAChB;AACA,iBAAO,WAAWA,IAAG;AAAA,QACvB,CAAC,CAAC;AACF,6BAAqB,mBAAmB,KAAK,WAAW,CAAAA,SAAO;AAC7D,cAAIA,KAAI,WAAW,GAAG;AACpB,mBAAO,GAAG,IAAI;AAAA,UAChB;AACA,iBAAO,WAAWA,IAAG;AAAA,QACvB,CAAC,CAAC;AAAA,MACJ;AACA,oBAAc,CAAC,mBAAmB,kBAAkB,CAAC,EAAE,UAAU,SAAO;AACtE,aAAK,OAAO,gBAAgB;AAC5B,gBAAQ,GAAG;AACX,aAAK,OAAO,KAAK,4BAA4B;AAAA,MAC/C,GAAG,CAAAA,SAAO;AACR,aAAK,OAAO,MAAM,wBAAwBA,IAAG;AAC7C,aAAK,cAAc,KAAK,IAAI,gBAAgB,sBAAsBA,IAAG,CAAC;AACtE,eAAOA,IAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAGlB,QAAI,SAAS,QAAQ,IAAI;AACvB,eAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAYF;AAVI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAiB,SAAY,MAAM,GAAM,SAAY,UAAU,GAAM,SAAS,cAAc,CAAC,GAAM,SAAS,mBAAmB,CAAC,GAAM,SAAS,YAAY,CAAC,GAAM,SAAS,gBAAgB,GAAM,SAAS,WAAW,GAAM,SAAS,aAAa,CAAC,GAAM,SAAS,QAAQ,GAAM,SAAS,gBAAgB,CAAC;AAC5U;AAGA,cAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,cAAa;AACxB,CAAC;AAp4DL,IAAM,eAAN;AAAA,CAu4DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kCAAN,MAAsC;AAAC;AACvC,IAAM,sCAAN,MAA0C;AAAA,EACxC,YAAYA,MAAK;AACf,WAAO,WAAWA,IAAG;AAAA,EACvB;AACF;AACA,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,cAAc,cAAc,cAAc;AACpD,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,SAAS,KAAK;AACZ,QAAI,KAAK,aAAa,eAAe,qBAAqB;AACxD,aAAO,KAAK,aAAa,eAAe,oBAAoB,GAAG;AAAA,IACjE;AACA,QAAI,KAAK,aAAa,eAAe,aAAa;AAChD,aAAO,CAAC,CAAC,KAAK,aAAa,eAAe,YAAY,KAAK,OAAK,IAAI,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;AAAA,IAC/G;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,MAAM;AACnB,UAAM,MAAM,IAAI,IAAI,YAAY;AAChC,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,kBAAkB,CAAC,KAAK,SAAS,GAAG,GAAG;AAClF,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AACA,UAAM,kBAAkB,KAAK,aAAa,eAAe;AACzD,QAAI,CAAC,iBAAiB;AACpB,aAAO,KAAK,OAAO,GAAG,EAAE,KAAK,WAAW,CAAAA,SAAO,KAAK,aAAa,YAAYA,IAAG,CAAC,CAAC;AAAA,IACpF;AACA,WAAO,MAAM,GAAG,KAAK,aAAa,eAAe,CAAC,EAAE,KAAK,OAAO,WAAS,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,aAAa,OAAO;AAAA,MAAK,OAAO,OAAK,EAAE,SAAS,gBAAgB;AAAA,MAAG,QAAQ,KAAK,aAAa,sBAAsB,CAAC;AAAA,MAAG,WAAW,MAAM,GAAG,IAAI,CAAC;AAAA;AAAA,MAEzO,IAAI,MAAM,KAAK,aAAa,eAAe,CAAC;AAAA,IAAC,CAAC,EAAE,KAAK,KAAK,CAAC,GAAG,SAAS,WAAS;AAC9E,UAAI,OAAO;AACT,cAAM,SAAS,YAAY;AAC3B,cAAM,UAAU,IAAI,QAAQ,IAAI,iBAAiB,MAAM;AACvD,cAAM,IAAI,MAAM;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,KAAK,OAAO,GAAG,EAAE,KAAK,WAAW,CAAAA,SAAO,KAAK,aAAa,YAAYA,IAAG,CAAC,CAAC;AAAA,IACpF,CAAC,CAAC;AAAA,EACJ;AAYF;AAVI,yBAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,SAAO,KAAK,qBAAqB,0BAA4B,SAAS,YAAY,GAAM,SAAS,+BAA+B,GAAM,SAAS,mBAAmB,CAAC,CAAC;AACtK;AAGA,yBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,yBAAwB;AACnC,CAAC;AA9CL,IAAM,0BAAN;AAAA,CAiDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,SAAS,uBAAuB;AAC9B,SAAO,OAAO,mBAAmB,cAAc,iBAAiB,IAAI,cAAc;AACpF;AACA,SAAS,mBAAmB,SAAS,MAAM,yBAAyB,uBAAuB;AACzF,SAAO,yBAAyB,CAAC,cAAc,kBAAkB;AAAA,IAC/D,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,IAAM,eAAN,MAAM,aAAY;AAAA,EAChB,OAAO,QAAQ,SAAS,MAAM,yBAAyB,uBAAuB;AAC5E,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB,QAAQ,sBAAsB,CAAC;AAAA,IAChE;AAAA,EACF;AAiBF;AAfI,aAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,SAAO,KAAK,qBAAqB,cAAa;AAChD;AAGA,aAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,YAAY;AACxB,CAAC;AAGD,aAAK,OAAyB,iBAAiB;AAAA,EAC7C,SAAS,CAAC,YAAY;AACxB,CAAC;AArBL,IAAM,cAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC;AAAA,MACf,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBZ,IAAM,wBAAN,cAAoC,sBAAsB;AAAA,EACxD,cAAc;AACZ,UAAM;AACN,YAAQ,MAAM,GAAG;AAAA,EACnB;AACF;AACA,IAAM,cAAc,IAAI,eAAe,aAAa;", "names": ["hash", "from", "timeout", "err", "hash", "atHashValid"]}