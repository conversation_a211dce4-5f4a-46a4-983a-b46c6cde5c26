import {
  Cdk<PERSON><PERSON><PERSON><PERSON>ontainer,
  DEFAULT_DIALOG_CONFIG,
  DIALOG_DATA,
  DIALOG_SCROLL_STRATEGY,
  Dialog,
  DialogConfig,
  DialogModule,
  DialogRef,
  throwDialogContentAlreadyAttachedError
} from "./chunk-PLCTYYRC.js";
import "./chunk-L7NVYFBQ.js";
import "./chunk-4NJAG2UW.js";
import "./chunk-MRFHGDYA.js";
import {
  CdkPortal,
  CdkPortalOutlet,
  PortalHostDirective,
  TemplatePortalDirective
} from "./chunk-XK5UHNJ2.js";
import "./chunk-QCETVJKM.js";
import "./chunk-JVMORM5G.js";
import "./chunk-DQLYDRBB.js";
import "./chunk-LGNNZOMI.js";
import "./chunk-GWE4MCPP.js";
import "./chunk-H3D6QMOH.js";
import "./chunk-MPM73DZ3.js";
import "./chunk-UZB7EAJN.js";
import "./chunk-EOFW2REK.js";
import "./chunk-3KX2S6PK.js";
import "./chunk-5QPZYPEV.js";
import "./chunk-AAWW5C2W.js";
import "./chunk-YFKVMALY.js";
import "./chunk-XJKSSPTD.js";
import "./chunk-QQZDB4KQ.js";
import "./chunk-BYBDDJ2C.js";
import "./chunk-NY55TVYV.js";
import "./chunk-2O4VBYCZ.js";
import "./chunk-GJIVGOXW.js";
import "./chunk-K2JRLIHD.js";
import "./chunk-QDB2FYN3.js";
export {
  CdkDialogContainer,
  DEFAULT_DIALOG_CONFIG,
  DIALOG_DATA,
  DIALOG_SCROLL_STRATEGY,
  Dialog,
  DialogConfig,
  DialogModule,
  DialogRef,
  throwDialogContentAlreadyAttachedError,
  CdkPortal as ɵɵCdkPortal,
  CdkPortalOutlet as ɵɵCdkPortalOutlet,
  PortalHostDirective as ɵɵPortalHostDirective,
  TemplatePortalDirective as ɵɵTemplatePortalDirective
};
