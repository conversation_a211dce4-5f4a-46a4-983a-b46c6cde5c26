import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input, Injector } from '@angular/core';
import { ActionItem } from '@tec/rad-ui/common';
import { RadPage } from '@tec/rad-ui/layout';
import { CourseStore } from './course-store';
import { RadEjGrid } from '@tec/rad-xui/ej-grid';
import { AppDialogService } from '@tec/rad-core/abstractions';
import { CreateCourseView } from './create-course-view';

        
@Component({
    selector: 'ed-course-list',
    template: `
    <rad-page title='Courses' layout="card" [actions]="actions" [loading]="store.loading()">
        <rad-ej-grid [dataSource]="store.courses()" [columns]="columns" />
    </rad-page>
    `,
    styles: [],
    imports:[
        CommonModule,
        RadPage,
        RadEjGrid

    ],
    providers:[CourseStore]
    
})
export class CourseList {
        

    protected store = inject(CourseStore);
    #dialog = inject(AppDialogService);
    #injector = inject(Injector);

    actions: ActionItem[] = [
        { label: 'New', icon: 'add', display:'primary', action: this.createCourse.bind(this)  },
       
    ];

    columns = [
        { field: 'name', headerText: 'Name' },
        { field: 'description', headerText: 'Description' },
        { field: 'status', headerText: 'Status' },
    ];
        

    private async createCourse() {
        await this.#dialog.show(CreateCourseView, undefined, {injector: this.#injector});
    }
    
        
}