{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@ed/pep-activity": ["a-ecademy/pep/ed-pep-activity/src/index.ts"], "@ed/pep-guide": ["a-ecademy/pep/ed-pep-guide/src/index.ts"], "@ed/pep-shared": ["a-ecademy/pep/ed-pep-shared/src/index.ts"], "@ed/pep-ui": ["a-ecademy/pep/ed-pep-ui/src/index.ts"], "@ed/share": ["a-ecademy/shared/ed-shared/src/index.ts"], "@ed/share/data-academics": ["a-ecademy/shared/ed-shared/data-academics/index.ts"], "@ed/share/data-commerce": ["a-ecademy/shared/ed-shared/data-commerce/index.ts"], "@ed/share/data-content": ["a-ecademy/shared/ed-shared/data-content/index.ts"], "@ed/share/data-registry": ["a-ecademy/shared/ed-shared/data-registry/index.ts"], "@ed/studio-admin": ["a-ecademy/studio/ed-studio-admin/src/index.ts"], "@ed/studio-admin/courses": ["a-ecademy/studio/ed-studio-admin/courses/index.ts"], "@ed/studio-content": ["a-ecademy/studio/ed-studio-content/src/index.ts"], "@ed/studio-shared": ["a-ecademy/studio/ed-studio-shared/src/index.ts"], "@pro/admin": ["a-protrac/pro-admin/src/index.ts"], "@pro/admin/org": ["a-protrac/pro-admin/org/index.ts"], "@pro/admin/project": ["a-protrac/pro-admin/project/index.ts"], "@pro/admin/purchasing": ["a-protrac/pro-admin/purchasing/index.ts"], "@pro/shared": ["a-protrac/pro-shared/src/index.ts"], "@pro/shared/data-finance": ["a-protrac/pro-shared/data-finance/index.ts"], "@pro/shared/data-org": ["a-protrac/pro-shared/data-org/index.ts"], "@pro/shared/ui": ["a-protrac/pro-shared/ui/index.ts"], "@tec/rad-app": ["a-tec/@rad-app/src/index.ts"], "@tec/rad-app/auth": ["a-tec/@rad-app/auth/index.ts"], "@tec/rad-app/confirmation": ["a-tec/@rad-app/confirmation/index.ts"], "@tec/rad-app/core": ["a-tec/@rad-app/core/index.ts"], "@tec/rad-app/dialog": ["a-tec/@rad-app/dialog/index.ts"], "@tec/rad-app/notification": ["a-tec/@rad-app/notification/index.ts"], "@tec/rad-app/shell": ["a-tec/@rad-app/shell/index.ts"], "@tec/rad-core": ["a-tec/@rad-core/src/index.ts"], "@tec/rad-core/abstractions": ["a-tec/@rad-core/abstractions/index.ts"], "@tec/rad-core/composition": ["a-tec/@rad-core/composition/index.ts"], "@tec/rad-core/services": ["a-tec/@rad-core/services/index.ts"], "@tec/rad-core/utils": ["a-tec/@rad-core/utils/index.ts"], "@tec/rad-infra": ["a-tec/@rad-infra/src/index.ts"], "@tec/rad-infra/basic": ["a-tec/@rad-infra/basic/index.ts"], "@tec/rad-infra/data": ["a-tec/@rad-infra/data/index.ts"], "@tec/rad-infra/logging": ["a-tec/@rad-infra/logging/index.ts"], "@tec/rad-infra/modular": ["a-tec/@rad-infra/modular/index.ts"], "@tec/rad-infra/signalr": ["a-tec/@rad-infra/signalr/index.ts"], "@tec/rad-nx": ["a-tec/@rad-nx/src/index.ts"], "@tec/rad-ui": ["a-tec/@rad-ui/src/index.ts"], "@tec/rad-ui/animations": ["a-tec/@rad-ui/animations/index.ts"], "@tec/rad-ui/common": ["a-tec/@rad-ui/common/index.ts"], "@tec/rad-ui/component": ["a-tec/@rad-ui/component/index.ts"], "@tec/rad-ui/layout": ["a-tec/@rad-ui/layout/index.ts"], "@tec/rad-ui/menu": ["a-tec/@rad-ui/menu/index.ts"], "@tec/rad-ui/navbar": ["a-tec/@rad-ui/navbar/index.ts"], "@tec/rad-ui/resources": ["a-tec/@rad-ui/resources/index.ts"], "@tec/rad-ui/scroll": ["a-tec/@rad-ui/scroll/index.ts"], "@tec/rad-ui/tab": ["a-tec/@rad-ui/tab/index.ts"], "@tec/rad-xui": ["a-tec/@rad-xui/src/index.ts"], "@tec/rad-xui/common": ["a-tec/@rad-xui/common/index.ts"], "@tec/rad-xui/crud": ["a-tec/@rad-xui/crud/index.ts"], "@tec/rad-xui/data": ["a-tec/@rad-xui/data/index.ts"], "@tec/rad-xui/dx-grid": ["a-tec/@rad-xui/dx-grid/index.ts"], "@tec/rad-xui/ej-grid": ["a-tec/@rad-xui/ej-grid/src/index.ts"], "@tec/rad-xui/express": ["a-tec/@rad-xui/express/index.ts"], "@tec/rad-xui/filter": ["a-tec/@rad-xui/filter/index.ts"], "@tec/rad-xui/flex": ["a-tec/@rad-xui/flex/index.ts"], "@tec/rad-xui/form": ["a-tec/@rad-xui/form/index.ts"], "@tec/rad-xui/grid": ["a-tec/@rad-xui/grid/src/index.ts"], "@tec/rad-xui/lists": ["a-tec/@rad-xui/lists/index.ts"], "@tec/rad-xui/services": ["a-tec/@rad-xui/services/index.ts"]}, "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "strictNullChecks": false, "strictPropertyInitialization": false, "allowSyntheticDefaultImports": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "exclude": ["node_modules", "dist", "tmp"]}