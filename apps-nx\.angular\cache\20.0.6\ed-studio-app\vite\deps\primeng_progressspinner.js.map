{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-progressspinner.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { style } from '@primeuix/styles/progressspinner';\nimport { BaseStyle } from 'primeng/base';\nconst classes = {\n  root: () => ['p-progressspinner'],\n  spin: 'p-progressspinner-spin',\n  circle: 'p-progressspinner-circle'\n};\nclass ProgressSpinnerStyle extends BaseStyle {\n  name = 'progressspinner';\n  theme = style;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵProgressSpinnerStyle_BaseFactory;\n    return function ProgressSpinnerStyle_Factory(__ngFactoryType__) {\n      return (ɵProgressSpinnerStyle_BaseFactory || (ɵProgressSpinnerStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ProgressSpinnerStyle)))(__ngFactoryType__ || ProgressSpinnerStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ProgressSpinnerStyle,\n    factory: ProgressSpinnerStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * ProgressSpinner is a process status indicator.\n *\n * [Live Demo](https://www.primeng.org/progressspinner)\n *\n * @module progressspinnerstyle\n *\n */\nvar ProgressSpinnerClasses;\n(function (ProgressSpinnerClasses) {\n  /**\n   * Class name of the root element\n   */\n  ProgressSpinnerClasses[\"root\"] = \"p-progressspinner\";\n  /**\n   * Class name of the spin element\n   */\n  ProgressSpinnerClasses[\"spin\"] = \"p-progressspinner-spin\";\n  /**\n   * Class name of the circle element\n   */\n  ProgressSpinnerClasses[\"circle\"] = \"p-progressspinner-circle\";\n})(ProgressSpinnerClasses || (ProgressSpinnerClasses = {}));\n\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\nclass ProgressSpinner extends BaseComponent {\n  /**\n   * Class of the element.\n   * @deprecated since v20.0.0, use `class` instead.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Width of the circle stroke.\n   * @group Props\n   */\n  strokeWidth = '2';\n  /**\n   * Color for the background of the circle.\n   * @group Props\n   */\n  fill = 'none';\n  /**\n   * Duration of the rotate animation.\n   * @group Props\n   */\n  animationDuration = '2s';\n  /**\n   * Used to define a aria label attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  _componentStyle = inject(ProgressSpinnerStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵProgressSpinner_BaseFactory;\n    return function ProgressSpinner_Factory(__ngFactoryType__) {\n      return (ɵProgressSpinner_BaseFactory || (ɵProgressSpinner_BaseFactory = i0.ɵɵgetInheritedFactory(ProgressSpinner)))(__ngFactoryType__ || ProgressSpinner);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ProgressSpinner,\n    selectors: [[\"p-progressSpinner\"], [\"p-progress-spinner\"], [\"p-progressspinner\"]],\n    hostVars: 7,\n    hostBindings: function ProgressSpinner_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"role\", \"progressbar\")(\"data-pc-name\", \"progressspinner\")(\"data-pc-section\", \"root\")(\"aria-busy\", true);\n        i0.ɵɵclassMap(ctx.cn(ctx.cx(\"root\"), ctx.styleClass));\n      }\n    },\n    inputs: {\n      styleClass: \"styleClass\",\n      strokeWidth: \"strokeWidth\",\n      fill: \"fill\",\n      animationDuration: \"animationDuration\",\n      ariaLabel: \"ariaLabel\"\n    },\n    features: [i0.ɵɵProvidersFeature([ProgressSpinnerStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 9,\n    consts: [[\"viewBox\", \"25 25 50 50\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"20\", \"stroke-miterlimit\", \"10\"]],\n    template: function ProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0);\n        i0.ɵɵelement(1, \"circle\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.cx(\"spin\"));\n        i0.ɵɵstyleProp(\"animation-duration\", ctx.animationDuration);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.cx(\"circle\"));\n        i0.ɵɵattribute(\"fill\", ctx.fill)(\"stroke-width\", ctx.strokeWidth);\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'p-progressSpinner, p-progress-spinner, p-progressspinner',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <svg [class]=\"cx('spin')\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n            <circle [class]=\"cx('circle')\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n        </svg>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ProgressSpinnerStyle],\n      host: {\n        '[attr.aria-label]': 'ariaLabel',\n        '[attr.role]': \"'progressbar'\",\n        '[attr.data-pc-name]': \"'progressspinner'\",\n        '[attr.data-pc-section]': \"'root'\",\n        '[attr.aria-busy]': 'true',\n        '[class]': \"cn(cx('root'), styleClass)\"\n      }\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    strokeWidth: [{\n      type: Input\n    }],\n    fill: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }]\n  });\n})();\nclass ProgressSpinnerModule {\n  static ɵfac = function ProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ProgressSpinnerModule,\n    imports: [ProgressSpinner, SharedModule],\n    exports: [ProgressSpinner, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ProgressSpinner, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ProgressSpinner, SharedModule],\n      exports: [ProgressSpinner, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerClasses, ProgressSpinnerModule, ProgressSpinnerStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,UAAU;AAAA,EACd,MAAM,MAAM,CAAC,mBAAmB;AAAA,EAChC,MAAM;AAAA,EACN,QAAQ;AACV;AACA,IAAM,uBAAN,MAAM,8BAA6B,UAAU;AAAA,EAC3C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,6BAA6B,mBAAmB;AAC9D,cAAQ,sCAAsC,oCAAuC,sBAAsB,qBAAoB,IAAI,qBAAqB,qBAAoB;AAAA,IAC9K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,yBAAwB;AAIjC,EAAAA,wBAAuB,MAAM,IAAI;AAIjC,EAAAA,wBAAuB,MAAM,IAAI;AAIjC,EAAAA,wBAAuB,QAAQ,IAAI;AACrC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAM1D,IAAM,kBAAN,MAAM,yBAAwB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB;AAAA,EACA,kBAAkB,OAAO,oBAAoB;AAAA,EAC7C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,GAAG,CAAC,oBAAoB,GAAG,CAAC,mBAAmB,CAAC;AAAA,IAChF,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,gBAAgB,iBAAiB,EAAE,mBAAmB,MAAM,EAAE,aAAa,IAAI;AAClJ,QAAG,WAAW,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,UAAU,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,IACvF,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,aAAa,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,qBAAqB,IAAI,CAAC;AAAA,IACnG,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,MAAM,CAAC;AAC5B,QAAG,YAAY,sBAAsB,IAAI,iBAAiB;AAC1D,QAAG,YAAY,mBAAmB,MAAM;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,IAAI,GAAG,QAAQ,CAAC;AAC9B,QAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,gBAAgB,IAAI,WAAW;AAAA,MAClE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,oBAAoB;AAAA,MAChC,MAAM;AAAA,QACJ,qBAAqB;AAAA,QACrB,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,YAAY;AAAA,IACvC,SAAS,CAAC,iBAAiB,YAAY;AAAA,EACzC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,cAAc,YAAY;AAAA,EACvD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,YAAY;AAAA,MACvC,SAAS,CAAC,iBAAiB,YAAY;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ProgressSpinnerClasses"]}