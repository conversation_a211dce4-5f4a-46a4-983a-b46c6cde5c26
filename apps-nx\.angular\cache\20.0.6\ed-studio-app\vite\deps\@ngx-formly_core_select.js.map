{"version": 3, "sources": ["../../../../../../node_modules/@ngx-formly/core/fesm2022/ngx-formly-core-select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe, NgModule } from '@angular/core';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, filter, tap } from 'rxjs/operators';\nclass FormlySelectOptionsPipe {\n  transform(options, field) {\n    if (!(options instanceof Observable)) {\n      options = this.observableOf(options, field);\n    } else {\n      this.dispose();\n    }\n    return options.pipe(map(value => this.transformOptions(value, field)));\n  }\n  ngOnDestroy() {\n    this.dispose();\n  }\n  transformOptions(options, field) {\n    const to = this.transformSelectProps(field);\n    const opts = [];\n    const groups = {};\n    options?.forEach(option => {\n      const o = this.transformOption(option, to);\n      if (o.group) {\n        const id = groups[o.label];\n        if (id === undefined) {\n          groups[o.label] = opts.push(o) - 1;\n        } else {\n          o.group.forEach(o => opts[id].group.push(o));\n        }\n      } else {\n        opts.push(o);\n      }\n    });\n    return opts;\n  }\n  transformOption(option, props) {\n    const group = props.groupProp(option);\n    if (Array.isArray(group)) {\n      return {\n        label: props.labelProp(option),\n        group: group.map(opt => this.transformOption(opt, props))\n      };\n    }\n    option = {\n      label: props.labelProp(option),\n      value: props.valueProp(option),\n      disabled: !!props.disabledProp(option)\n    };\n    if (group) {\n      return {\n        label: group,\n        group: [option]\n      };\n    }\n    return option;\n  }\n  transformSelectProps(field) {\n    const props = field?.props || field?.templateOptions || {};\n    const selectPropFn = prop => typeof prop === 'function' ? prop : o => o[prop];\n    return {\n      groupProp: selectPropFn(props.groupProp || 'group'),\n      labelProp: selectPropFn(props.labelProp || 'label'),\n      valueProp: selectPropFn(props.valueProp || 'value'),\n      disabledProp: selectPropFn(props.disabledProp || 'disabled')\n    };\n  }\n  dispose() {\n    if (this._options) {\n      this._options.complete();\n      this._options = null;\n    }\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n      this._subscription = null;\n    }\n  }\n  observableOf(options, f) {\n    this.dispose();\n    if (f && f.options && f.options.fieldChanges) {\n      this._subscription = f.options.fieldChanges.pipe(filter(({\n        property,\n        type,\n        field\n      }) => {\n        return type === 'expressionChanges' && (property.indexOf('templateOptions.options') === 0 || property.indexOf('props.options') === 0) && field === f && Array.isArray(field.props.options) && !!this._options;\n      }), tap(() => this._options.next(f.props.options))).subscribe();\n    }\n    this._options = new BehaviorSubject(options);\n    return this._options.asObservable();\n  }\n  static {\n    this.ɵfac = function FormlySelectOptionsPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlySelectOptionsPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"formlySelectOptions\",\n      type: FormlySelectOptionsPipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlySelectOptionsPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'formlySelectOptions',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass LegacyFormlySelectOptionsPipe extends FormlySelectOptionsPipe {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵLegacyFormlySelectOptionsPipe_BaseFactory;\n      return function LegacyFormlySelectOptionsPipe_Factory(__ngFactoryType__) {\n        return (ɵLegacyFormlySelectOptionsPipe_BaseFactory || (ɵLegacyFormlySelectOptionsPipe_BaseFactory = i0.ɵɵgetInheritedFactory(LegacyFormlySelectOptionsPipe)))(__ngFactoryType__ || LegacyFormlySelectOptionsPipe);\n      };\n    })();\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"formlySelectOptions\",\n      type: LegacyFormlySelectOptionsPipe,\n      pure: true,\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LegacyFormlySelectOptionsPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'formlySelectOptions',\n      standalone: false\n    }]\n  }], null, null);\n})();\nclass FormlySelectModule {\n  static {\n    this.ɵfac = function FormlySelectModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormlySelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FormlySelectModule,\n      declarations: [LegacyFormlySelectOptionsPipe],\n      exports: [LegacyFormlySelectOptionsPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlySelectModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [LegacyFormlySelectOptionsPipe],\n      exports: [LegacyFormlySelectOptionsPipe]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FormlySelectModule, FormlySelectOptionsPipe, LegacyFormlySelectOptionsPipe };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B,UAAU,SAAS,OAAO;AACxB,QAAI,EAAE,mBAAmB,aAAa;AACpC,gBAAU,KAAK,aAAa,SAAS,KAAK;AAAA,IAC5C,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AACA,WAAO,QAAQ,KAAK,IAAI,WAAS,KAAK,iBAAiB,OAAO,KAAK,CAAC,CAAC;AAAA,EACvE;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,iBAAiB,SAAS,OAAO;AAC/B,UAAM,KAAK,KAAK,qBAAqB,KAAK;AAC1C,UAAM,OAAO,CAAC;AACd,UAAM,SAAS,CAAC;AAChB,aAAS,QAAQ,YAAU;AACzB,YAAM,IAAI,KAAK,gBAAgB,QAAQ,EAAE;AACzC,UAAI,EAAE,OAAO;AACX,cAAM,KAAK,OAAO,EAAE,KAAK;AACzB,YAAI,OAAO,QAAW;AACpB,iBAAO,EAAE,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI;AAAA,QACnC,OAAO;AACL,YAAE,MAAM,QAAQ,CAAAA,OAAK,KAAK,EAAE,EAAE,MAAM,KAAKA,EAAC,CAAC;AAAA,QAC7C;AAAA,MACF,OAAO;AACL,aAAK,KAAK,CAAC;AAAA,MACb;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,QAAQ,OAAO;AAC7B,UAAM,QAAQ,MAAM,UAAU,MAAM;AACpC,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO;AAAA,QACL,OAAO,MAAM,UAAU,MAAM;AAAA,QAC7B,OAAO,MAAM,IAAI,SAAO,KAAK,gBAAgB,KAAK,KAAK,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,aAAS;AAAA,MACP,OAAO,MAAM,UAAU,MAAM;AAAA,MAC7B,OAAO,MAAM,UAAU,MAAM;AAAA,MAC7B,UAAU,CAAC,CAAC,MAAM,aAAa,MAAM;AAAA,IACvC;AACA,QAAI,OAAO;AACT,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO,CAAC,MAAM;AAAA,MAChB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,OAAO;AAC1B,UAAM,QAAQ,OAAO,SAAS,OAAO,mBAAmB,CAAC;AACzD,UAAM,eAAe,UAAQ,OAAO,SAAS,aAAa,OAAO,OAAK,EAAE,IAAI;AAC5E,WAAO;AAAA,MACL,WAAW,aAAa,MAAM,aAAa,OAAO;AAAA,MAClD,WAAW,aAAa,MAAM,aAAa,OAAO;AAAA,MAClD,WAAW,aAAa,MAAM,aAAa,OAAO;AAAA,MAClD,cAAc,aAAa,MAAM,gBAAgB,UAAU;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,SAAS;AACvB,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAC/B,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,aAAa,SAAS,GAAG;AACvB,SAAK,QAAQ;AACb,QAAI,KAAK,EAAE,WAAW,EAAE,QAAQ,cAAc;AAC5C,WAAK,gBAAgB,EAAE,QAAQ,aAAa,KAAK,OAAO,CAAC;AAAA,QACvD;AAAA,QACA;AAAA,QACA;AAAA,MACF,MAAM;AACJ,eAAO,SAAS,wBAAwB,SAAS,QAAQ,yBAAyB,MAAM,KAAK,SAAS,QAAQ,eAAe,MAAM,MAAM,UAAU,KAAK,MAAM,QAAQ,MAAM,MAAM,OAAO,KAAK,CAAC,CAAC,KAAK;AAAA,MACvM,CAAC,GAAG,IAAI,MAAM,KAAK,SAAS,KAAK,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,UAAU;AAAA,IAChE;AACA,SAAK,WAAW,IAAI,gBAAgB,OAAO;AAC3C,WAAO,KAAK,SAAS,aAAa;AAAA,EACpC;AAaF;AAXI,yBAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,SAAO,KAAK,qBAAqB,0BAAyB;AAC5D;AAGA,yBAAK,QAA0B,aAAa;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAhGL,IAAM,0BAAN;AAAA,CAmGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iCAAN,MAAM,uCAAsC,wBAAwB;AAiBpE;AAfI,+BAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,sCAAsC,mBAAmB;AACvE,YAAQ,+CAA+C,6CAAgD,sBAAsB,8BAA6B,IAAI,qBAAqB,8BAA6B;AAAA,EAClN;AACF,GAAG;AAGH,+BAAK,QAA0B,aAAa;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAfL,IAAM,gCAAN;AAAA,CAkBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,oBAAmB;AAgBzB;AAdI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,cAAc,CAAC,6BAA6B;AAAA,EAC5C,SAAS,CAAC,6BAA6B;AACzC,CAAC;AAGD,oBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAdrD,IAAM,qBAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,6BAA6B;AAAA,MAC5C,SAAS,CAAC,6BAA6B;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["o"]}