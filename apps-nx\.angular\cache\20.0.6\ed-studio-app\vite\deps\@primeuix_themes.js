import {
  A,
  <PERSON>,
  Ee,
  Et,
  Lt,
  Mt,
  O,
  Oe,
  P,
  Q,
  R,
  S,
  Se,
  T,
  Ve,
  Vt,
  Y,
  _e,
  ae,
  ar,
  ce,
  ct,
  dt,
  g,
  gt,
  ie,
  ke,
  le,
  lt,
  mt,
  ne,
  oe,
  tr,
  v,
  vt,
  we
} from "./chunk-6MDX7AO6.js";
import "./chunk-TBEQWWM7.js";
import "./chunk-QDB2FYN3.js";

// node_modules/@primeuix/themes/dist/index.mjs
var t = (...t2) => Se(...t2);
var a = (...t2) => we(...t2);
var r = (t2) => Ce(t2);
var s = (t2) => Oe(t2);
var u = (...t2) => Ve(...t2);
var P2 = (theme) => Ee(theme);
export {
  tr as $dt,
  O as $t,
  lt as CALC_REGEX,
  v as EXPR_REGEX,
  vt as StyleSheet,
  g as Theme,
  R as ThemeService,
  S as ThemeUtils,
  ct as VAR_REGEX,
  ar as css,
  t as definePreset,
  P as dt,
  le as dtwt,
  oe as evaluateDtExpressions,
  Mt as getComputedValue,
  T as getRule,
  ne as getVariableName,
  Y as getVariableValue,
  dt as hasOddBraces,
  Et as merge,
  A as mix,
  gt as palette,
  _e as setProperty,
  ae as shade,
  ie as tint,
  mt as toNormalizePrefix,
  Q as toNormalizeVariable,
  Vt as toTokenKey,
  Lt as toUnit,
  ke as toValue,
  ce as toVariables,
  a as updatePreset,
  r as updatePrimaryPalette,
  s as updateSurfacePalette,
  u as usePreset,
  P2 as useTheme
};
