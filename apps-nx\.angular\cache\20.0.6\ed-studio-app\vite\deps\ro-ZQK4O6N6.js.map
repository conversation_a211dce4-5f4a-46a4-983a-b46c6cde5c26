{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/ro.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    if (!(v === 0) || (n === 0 || n % 100 === Math.floor(n % 100) && (n % 100 >= 2 && n % 100 <= 19)))\n        return 3;\n    return 5;\n}\nexport default [\"ro\", [[\"a.m.\", \"p.m.\"], u, u], u, [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dum.\", \"lun.\", \"mar.\", \"mie.\", \"joi\", \"vin.\", \"sâm.\"], [\"duminic<PERSON>\", \"luni\", \"marți\", \"miercuri\", \"joi\", \"vineri\", \"sâmbătă\"], [\"du.\", \"lu.\", \"ma.\", \"mi.\", \"joi\", \"vi.\", \"sâ.\"]], u, [[\"I\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"ian.\", \"feb.\", \"mar.\", \"apr.\", \"mai\", \"iun.\", \"iul.\", \"aug.\", \"sept.\", \"oct.\", \"nov.\", \"dec.\"], [\"ianuarie\", \"februarie\", \"martie\", \"aprilie\", \"mai\", \"iunie\", \"iulie\", \"august\", \"septembrie\", \"octombrie\", \"noiembrie\", \"decembrie\"]], u, [[\"î.Hr.\", \"d.Hr.\"], u, [\"înainte de Hristos\", \"după Hristos\"]], 1, [6, 0], [\"dd.MM.y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"RON\", \"RON\", \"leu românesc\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"EUR\": [u, \"€\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"], \"XCD\": [u, \"$\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC1F,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,MAAI,EAAE,MAAM,OAAO,MAAM,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO;AACzF,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,MAAM,GAAG,CAAC,YAAY,QAAQ,SAAS,YAAY,OAAO,UAAU,SAAS,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC,YAAY,aAAa,UAAU,WAAW,OAAO,SAAS,SAAS,UAAU,cAAc,aAAa,aAAa,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAC,sBAAsB,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,WAAW,YAAY,gBAAgB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,OAAO,gBAAgB,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,MAAM;", "names": []}