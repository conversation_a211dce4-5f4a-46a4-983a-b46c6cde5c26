{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/ru.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (v === 0 && (i % 10 === 1 && !(i % 100 === 11)))\n        return 1;\n    if (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)))\n        return 3;\n    if (v === 0 && i % 10 === 0 || (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 5 && i % 10 <= 9)) || v === 0 && (i % 100 === Math.floor(i % 100) && (i % 100 >= 11 && i % 100 <= 14))))\n        return 4;\n    return 5;\n}\nexport default [\"ru\", [[\"AM\", \"PM\"], u, u], u, [[\"В\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"], [\"вс\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"], [\"воскресенье\", \"понедельник\", \"вторник\", \"среда\", \"четверг\", \"пятница\", \"суббота\"], [\"вс\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"]], u, [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"янв.\", \"февр.\", \"мар.\", \"апр.\", \"мая\", \"июн.\", \"июл.\", \"авг.\", \"сент.\", \"окт.\", \"нояб.\", \"дек.\"], [\"января\", \"февраля\", \"марта\", \"апреля\", \"мая\", \"июня\", \"июля\", \"августа\", \"сентября\", \"октября\", \"ноября\", \"декабря\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"янв.\", \"февр.\", \"март\", \"апр.\", \"май\", \"июнь\", \"июль\", \"авг.\", \"сент.\", \"окт.\", \"нояб.\", \"дек.\"], [\"январь\", \"февраль\", \"март\", \"апрель\", \"май\", \"июнь\", \"июль\", \"август\", \"сентябрь\", \"октябрь\", \"ноябрь\", \"декабрь\"]], [[\"до н.э.\", \"н.э.\"], [\"до н. э.\", \"н. э.\"], [\"до Рождества Христова\", \"от Рождества Христова\"]], 1, [6, 0], [\"dd.MM.y\", \"d MMM y 'г'.\", \"d MMMM y 'г'.\", \"EEEE, d MMMM y 'г'.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"не число\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"RUB\", \"₽\", \"российский рубль\", { \"BYN\": [u, \"р.\"], \"GEL\": [u, \"ლ\"], \"PHP\": [u, \"₱\"], \"RON\": [u, \"L\"], \"RUB\": [\"₽\"], \"RUR\": [\"р.\"], \"THB\": [\"฿\"], \"TMT\": [\"ТМТ\"], \"TWD\": [\"NT$\"], \"UAH\": [\"₴\"], \"XXX\": [\"XXXX\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC1F,MAAI,MAAM,MAAM,IAAI,OAAO,KAAK,EAAE,IAAI,QAAQ;AAC1C,WAAO;AACX,MAAI,MAAM,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,EAAE,MAAM,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,EAAE,IAAI,OAAO,MAAM,IAAI,OAAO;AAC3G,WAAO;AACX,MAAI,MAAM,KAAK,IAAI,OAAO,MAAM,MAAM,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,EAAE,MAAM,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,MAAM,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO;AACtL,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,eAAe,eAAe,WAAW,SAAS,WAAW,WAAW,SAAS,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,SAAS,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,SAAS,MAAM,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,OAAO,QAAQ,QAAQ,WAAW,YAAY,WAAW,UAAU,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,SAAS,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,SAAS,MAAM,GAAG,CAAC,UAAU,WAAW,QAAQ,UAAU,OAAO,QAAQ,QAAQ,UAAU,YAAY,WAAW,UAAU,SAAS,CAAC,GAAG,CAAC,CAAC,WAAW,MAAM,GAAG,CAAC,YAAY,OAAO,GAAG,CAAC,yBAAyB,uBAAuB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,gBAAgB,iBAAiB,qBAAqB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,YAAY,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,oBAAoB,EAAE,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,OAAO,MAAM;", "names": []}