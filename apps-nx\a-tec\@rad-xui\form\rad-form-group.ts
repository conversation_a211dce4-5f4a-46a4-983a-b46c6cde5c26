import { FormGroup } from '@angular/forms';
import { FormlyFormOptions } from '@ngx-formly/core';
import { computed, DestroyRef, effect, output, signal, Signal } from '@angular/core';
import { distinctUntilChanged, filter } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IEditForm } from './form-types';
export class RadEditForm<T=unknown> implements IEditForm {

  private _form = new FormGroup({});
  get form() {
    return this._form;
  }

  private _data: Signal<T> = undefined;
  private _originalModel: T = undefined;
  private  _model = signal<T>(undefined);
  private _destroyRef: DestroyRef = undefined;


  model = computed(()=> {

    const model = this._model();
    return model;
  });

  private _updated = signal<T>(undefined);

  updated = computed(()=> {
    return this._updated();
  });

  changes = this._form.valueChanges.pipe(filter(() => this._form.dirty));
  

  private _internalEditing = signal(false);
  private _externalEditing:Signal<boolean> = undefined;
  private _hasExternalEditing = false;
  editing = computed(()=>{
    if(this._hasExternalEditing){
      return this._externalEditing();
    }
    return this._internalEditing();
  });

  constructor(data: Signal<T>, editable?: Signal<boolean>, destroyRef?: DestroyRef) {

    this._data = data;
    this._destroyRef = destroyRef;

    

    this.setupEditing(editable);


   

    effect(()=> {
      this._options.formState.disabled = !this.editing();
    });

    effect(()=> {
      const data = this._data();
      this._originalModel = this.cloneData(data) as T;
      this._model.set(data);
    });

    
    

  }

  private cloneData(data: any){

    if(!data){
        return {};
      }

      if(data?.clone){        
        return data.clone();
      }
      return structuredClone(data);
    }

  private setupEditing(editing: Signal<boolean>) {



      if(editing){
        this._externalEditing = editing;
        this._hasExternalEditing = true;
      }

  }

  private _options: FormlyFormOptions = { formState: { disabled: false } };

  get options() {
    return this._options;
  }


  validate() {
    //touch all form fields
    this.form.markAllAsTouched();
    return !this.form.invalid;
  }

  updateData(): T {

    const data = this._model();
    this._internalEditing.set(false);
    return structuredClone(data) as T;
  }

  edit() {

    this._internalEditing.set(true);
  }

  cancel() {
    this._internalEditing.set(false);
    this._model.set(this._originalModel);
  }

  computed(field: string){
    if(!this._destroyRef){
      throw new Error('DestroyRef is required');
    }
     const control = this.form.get(field);

     if(!control){
      throw new Error(`Field ${field} not found`);
    }

    const value = signal(undefined);

    control.valueChanges.pipe(takeUntilDestroyed(this._destroyRef), distinctUntilChanged())
    .subscribe(
      (val) => value.set(val)
    );


    return computed(()=> value());
  }

}