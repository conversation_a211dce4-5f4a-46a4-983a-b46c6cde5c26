{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/is.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), t = parseInt(val.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n    if (t === 0 && (i % 10 === 1 && !(i % 100 === 11)) || !(t === 0))\n        return 1;\n    return 5;\n}\nexport default [\"is\", [[\"f.\", \"e.\"], [\"f.h.\", \"e.h.\"], u], [[\"f.h.\", \"e.h.\"], u, u], [[\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"], [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"], [\"sunnudagur\", \"mánudagur\", \"þriðjudagur\", \"miðvikudagur\", \"fimmtudagur\", \"föstudagur\", \"laugardagur\"], [\"su.\", \"má.\", \"þr.\", \"mi.\", \"fi.\", \"fö.\", \"la.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maí\", \"jún.\", \"júl.\", \"ágú.\", \"sep.\", \"okt.\", \"nóv.\", \"des.\"], [\"janúar\", \"febrúar\", \"mars\", \"apríl\", \"maí\", \"júní\", \"júlí\", \"ágúst\", \"september\", \"október\", \"nóvember\", \"desember\"]], u, [[\"f.k.\", \"e.k.\"], [\"f.Kr.\", \"e.Kr.\"], [\"fyrir Krist\", \"eftir Krist\"]], 1, [6, 0], [\"d.M.y\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'kl'. {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"ISK\", \"ISK\", \"íslensk króna\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"CAD\": [u, \"$\"], \"EUR\": [u, \"€\"], \"GBP\": [u, \"£\"], \"INR\": [u, \"₹\"], \"JPY\": [\"JP¥\", \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,kBAAkB,EAAE,GAAG,EAAE,KAAK;AAChH,MAAI,MAAM,MAAM,IAAI,OAAO,KAAK,EAAE,IAAI,QAAQ,QAAQ,EAAE,MAAM;AAC1D,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,cAAc,aAAa,eAAe,gBAAgB,eAAe,cAAc,aAAa,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,QAAQ,SAAS,OAAO,QAAQ,QAAQ,SAAS,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,eAAe,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,YAAY,aAAa,iBAAiB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,OAAO,iBAAiB,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,MAAM;", "names": []}