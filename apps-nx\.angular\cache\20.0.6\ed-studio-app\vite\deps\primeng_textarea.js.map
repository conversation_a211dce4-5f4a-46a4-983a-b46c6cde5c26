{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-textarea.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, input, booleanAttribute, computed, EventEmitter, inject, HostListener, Output, Input, Directive, NgModule } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { BaseModelHolder } from 'primeng/basemodelholder';\nimport { Fluid } from 'primeng/fluid';\nimport { style } from '@primeuix/styles/textarea';\nimport { BaseStyle } from 'primeng/base';\nconst theme = /*css*/`\n    ${style}\n\n    /* For PrimeNG */\n    .p-textarea.ng-invalid.ng-dirty {\n        border-color: dt('textarea.invalid.border.color');\n    }\n    .p-textarea.ng-invalid.ng-dirty::placeholder {\n        color: dt('textarea.invalid.placeholder.color');\n    }\n`;\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-textarea p-component', {\n    'p-filled': instance.$filled(),\n    'p-textarea-resizable ': instance.autoResize,\n    'p-variant-filled': instance.$variant() === 'filled',\n    'p-textarea-fluid': instance.hasFluid,\n    'p-inputfield-sm p-textarea-sm': instance.pSize === 'small',\n    'p-textarea-lg p-inputfield-lg': instance.pSize === 'large',\n    'p-invalid': instance.invalid()\n  }]\n};\nclass TextareaStyle extends BaseStyle {\n  name = 'textarea';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTextareaStyle_BaseFactory;\n    return function TextareaStyle_Factory(__ngFactoryType__) {\n      return (ɵTextareaStyle_BaseFactory || (ɵTextareaStyle_BaseFactory = i0.ɵɵgetInheritedFactory(TextareaStyle)))(__ngFactoryType__ || TextareaStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TextareaStyle,\n    factory: TextareaStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextareaStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Textarea is a multi-line text input element.\n *\n * [Live Demo](https://www.primeng.org/textarea/)\n *\n * @module textareastyle\n *\n */\nvar TextareaClasses;\n(function (TextareaClasses) {\n  /**\n   * Class name of the root element\n   */\n  TextareaClasses[\"root\"] = \"p-textarea\";\n})(TextareaClasses || (TextareaClasses = {}));\n\n/**\n * Textarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\nclass Textarea extends BaseModelHolder {\n  /**\n   * When present, textarea size changes as being typed.\n   * @group Props\n   */\n  autoResize;\n  /**\n   * Defines the size of the component.\n   * @group Props\n   */\n  pSize;\n  /**\n   * Specifies the input variant of the component.\n   * @defaultValue undefined\n   * @group Props\n   */\n  variant = input();\n  /**\n   * Spans 100% width of the container when enabled.\n   * @defaultValue undefined\n   * @group Props\n   */\n  fluid = input(undefined, {\n    transform: booleanAttribute\n  });\n  /**\n   * When present, it specifies that the component should have invalid state style.\n   * @defaultValue false\n   * @group Props\n   */\n  invalid = input(undefined, {\n    transform: booleanAttribute\n  });\n  $variant = computed(() => this.variant() || this.config.inputStyle() || this.config.inputVariant());\n  /**\n   * Callback to invoke on textarea resize.\n   * @param {(Event | {})} event - Custom resize event.\n   * @group Emits\n   */\n  onResize = new EventEmitter();\n  ngModelSubscription;\n  ngControlSubscription;\n  _componentStyle = inject(TextareaStyle);\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  pcFluid = inject(Fluid, {\n    optional: true,\n    host: true,\n    skipSelf: true\n  });\n  get hasFluid() {\n    return this.fluid() ?? !!this.pcFluid;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    if (this.ngControl) {\n      this.ngControlSubscription = this.ngControl.valueChanges.subscribe(() => {\n        this.updateState();\n      });\n    }\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    if (this.autoResize) this.resize();\n    this.cd.detectChanges();\n  }\n  ngAfterViewChecked() {\n    if (this.autoResize) this.resize();\n  }\n  onInput(e) {\n    this.writeModelValue(e.target['value']);\n    this.updateState();\n  }\n  resize(event) {\n    this.el.nativeElement.style.height = 'auto';\n    this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n    if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n      this.el.nativeElement.style.overflowY = 'scroll';\n      this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n    } else {\n      this.el.nativeElement.style.overflow = 'hidden';\n    }\n    this.onResize.emit(event || {});\n  }\n  updateState() {\n    if (this.autoResize) {\n      this.resize();\n    }\n  }\n  ngOnDestroy() {\n    if (this.ngModelSubscription) {\n      this.ngModelSubscription.unsubscribe();\n    }\n    if (this.ngControlSubscription) {\n      this.ngControlSubscription.unsubscribe();\n    }\n    super.ngOnDestroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTextarea_BaseFactory;\n    return function Textarea_Factory(__ngFactoryType__) {\n      return (ɵTextarea_BaseFactory || (ɵTextarea_BaseFactory = i0.ɵɵgetInheritedFactory(Textarea)))(__ngFactoryType__ || Textarea);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Textarea,\n    selectors: [[\"\", \"pTextarea\", \"\"], [\"\", \"pInputTextarea\", \"\"]],\n    hostVars: 2,\n    hostBindings: function Textarea_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function Textarea_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.cx(\"root\"));\n      }\n    },\n    inputs: {\n      autoResize: [2, \"autoResize\", \"autoResize\", booleanAttribute],\n      pSize: \"pSize\",\n      variant: [1, \"variant\"],\n      fluid: [1, \"fluid\"],\n      invalid: [1, \"invalid\"]\n    },\n    outputs: {\n      onResize: \"onResize\"\n    },\n    features: [i0.ɵɵProvidersFeature([TextareaStyle]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Textarea, [{\n    type: Directive,\n    args: [{\n      selector: '[pTextarea], [pInputTextarea]',\n      standalone: true,\n      host: {\n        '[class]': \"cx('root')\"\n      },\n      providers: [TextareaStyle]\n    }]\n  }], null, {\n    autoResize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pSize: [{\n      type: Input\n    }],\n    onResize: [{\n      type: Output\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass TextareaModule {\n  static ɵfac = function TextareaModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TextareaModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TextareaModule,\n    imports: [Textarea],\n    exports: [Textarea]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextareaModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Textarea],\n      exports: [Textarea]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Textarea, TextareaClasses, TextareaModule, TextareaStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM;AAAA;AAAA,EAAe;AAAA,MACf,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUX,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,YAAY,SAAS,QAAQ;AAAA,IAC7B,yBAAyB,SAAS;AAAA,IAClC,oBAAoB,SAAS,SAAS,MAAM;AAAA,IAC5C,oBAAoB,SAAS;AAAA,IAC7B,iCAAiC,SAAS,UAAU;AAAA,IACpD,iCAAiC,SAAS,UAAU;AAAA,IACpD,aAAa,SAAS,QAAQ;AAAA,EAChC,CAAC;AACH;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAC5B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAM5C,IAAM,WAAN,MAAM,kBAAiB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,QAAQ,MAAM,QAAW;AAAA,IACvB,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,MAAM,QAAW;AAAA,IACzB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,WAAW,SAAS,MAAM,KAAK,QAAQ,KAAK,KAAK,OAAO,WAAW,KAAK,KAAK,OAAO,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlG,WAAW,IAAI,aAAa;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,aAAa;AAAA,EACtC,YAAY,OAAO,WAAW;AAAA,IAC5B,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,UAAU,OAAO,OAAO;AAAA,IACtB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,IAAI,WAAW;AACb,WAAO,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AAAA,EAChC;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,UAAU,aAAa,UAAU,MAAM;AACvE,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,QAAI,KAAK,WAAY,MAAK,OAAO;AACjC,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,WAAY,MAAK,OAAO;AAAA,EACnC;AAAA,EACA,QAAQ,GAAG;AACT,SAAK,gBAAgB,EAAE,OAAO,OAAO,CAAC;AACtC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,OAAO;AACZ,SAAK,GAAG,cAAc,MAAM,SAAS;AACrC,SAAK,GAAG,cAAc,MAAM,SAAS,KAAK,GAAG,cAAc,eAAe;AAC1E,QAAI,WAAW,KAAK,GAAG,cAAc,MAAM,MAAM,KAAK,WAAW,KAAK,GAAG,cAAc,MAAM,SAAS,GAAG;AACvG,WAAK,GAAG,cAAc,MAAM,YAAY;AACxC,WAAK,GAAG,cAAc,MAAM,SAAS,KAAK,GAAG,cAAc,MAAM;AAAA,IACnE,OAAO;AACL,WAAK,GAAG,cAAc,MAAM,WAAW;AAAA,IACzC;AACA,SAAK,SAAS,KAAK,SAAS,CAAC,CAAC;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,YAAY;AACnB,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AAAA,IACvC;AACA,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB,YAAY;AAAA,IACzC;AACA,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,iBAAiB,mBAAmB;AAClD,cAAQ,0BAA0B,wBAA2B,sBAAsB,SAAQ,IAAI,qBAAqB,SAAQ;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IAC7D,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,kCAAkC,QAAQ;AACxE,iBAAO,IAAI,QAAQ,MAAM;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,OAAO;AAAA,MACP,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,SAAS,CAAC,GAAG,SAAS;AAAA,IACxB;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA0B;AAAA,EAClF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ;AAAA,IAClB,SAAS,CAAC,QAAQ;AAAA,EACpB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,QAAQ;AAAA,MAClB,SAAS,CAAC,QAAQ;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["TextareaClasses"]}