{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-baseinput.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, input, booleanAttribute, computed, Directive } from '@angular/core';\nimport { BaseEditableHolder } from 'primeng/baseeditableholder';\nimport { Fluid } from 'primeng/fluid';\nclass BaseInput extends BaseEditableHolder {\n  pcFluid = inject(Fluid, {\n    optional: true,\n    host: true,\n    skipSelf: true\n  });\n  /**\n   * Spans 100% width of the container when enabled.\n   * @defaultValue false\n   * @group Props\n   */\n  fluid = input(undefined, {\n    transform: booleanAttribute\n  });\n  /**\n   * Specifies the input variant of the component.\n   * @defaultValue 'outlined'\n   * @group Props\n   */\n  variant = input();\n  /**\n   * Specifies the size of the component.\n   * @defaultValue undefined\n   * @group Props\n   */\n  size = input();\n  /**\n   * Specifies the visible width of the input element in characters.\n   * @defaultValue undefined\n   * @group Props\n   */\n  inputSize = input();\n  /**\n   * Specifies the value must match the pattern.\n   * @defaultValue undefined\n   * @group Props\n   */\n  pattern = input();\n  /**\n   * The value must be greater than or equal to the value.\n   * @defaultValue undefined\n   * @group Props\n   */\n  min = input();\n  /**\n   * The value must be less than or equal to the value.\n   * @defaultValue undefined\n   * @group Props\n   */\n  max = input();\n  /**\n   * Unless the step is set to the any literal, the value must be min + an integral multiple of the step.\n   * @defaultValue undefined\n   * @group Props\n   */\n  step = input();\n  /**\n   * The number of characters (code points) must not be less than the value of the attribute, if non-empty.\n   * @defaultValue undefined\n   * @group Props\n   */\n  minlength = input();\n  /**\n   * The number of characters (code points) must not exceed the value of the attribute.\n   * @defaultValue undefined\n   * @group Props\n   */\n  maxlength = input();\n  $variant = computed(() => this.variant() || this.config.inputStyle() || this.config.inputVariant());\n  get hasFluid() {\n    return this.fluid() ?? !!this.pcFluid;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBaseInput_BaseFactory;\n    return function BaseInput_Factory(__ngFactoryType__) {\n      return (ɵBaseInput_BaseFactory || (ɵBaseInput_BaseFactory = i0.ɵɵgetInheritedFactory(BaseInput)))(__ngFactoryType__ || BaseInput);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseInput,\n    inputs: {\n      fluid: [1, \"fluid\"],\n      variant: [1, \"variant\"],\n      size: [1, \"size\"],\n      inputSize: [1, \"inputSize\"],\n      pattern: [1, \"pattern\"],\n      min: [1, \"min\"],\n      max: [1, \"max\"],\n      step: [1, \"step\"],\n      minlength: [1, \"minlength\"],\n      maxlength: [1, \"maxlength\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseInput, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseInput };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,YAAN,MAAM,mBAAkB,mBAAmB;AAAA,EACzC,UAAU,OAAO,OAAO;AAAA,IACtB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,QAAQ,MAAM,QAAW;AAAA,IACvB,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,YAAY,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,UAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,MAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,MAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,YAAY,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,YAAY,MAAM;AAAA,EAClB,WAAW,SAAS,MAAM,KAAK,QAAQ,KAAK,KAAK,OAAO,WAAW,KAAK,KAAK,OAAO,aAAa,CAAC;AAAA,EAClG,IAAI,WAAW;AACb,WAAO,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AAAA,EAChC;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,WAAW,CAAC,GAAG,WAAW;AAAA,IAC5B;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}