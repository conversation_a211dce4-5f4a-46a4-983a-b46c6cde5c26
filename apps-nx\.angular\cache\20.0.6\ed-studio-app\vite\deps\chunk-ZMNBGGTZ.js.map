{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-fluid.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { style } from '@primeuix/styles/progressbar';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst classes = {\n  root: 'p-fluid'\n};\nclass FluidStyle extends BaseStyle {\n  name = 'fluid';\n  classes = classes;\n  theme = style;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFluidStyle_BaseFactory;\n    return function FluidStyle_Factory(__ngFactoryType__) {\n      return (ɵFluidStyle_BaseFactory || (ɵFluidStyle_BaseFactory = i0.ɵɵgetInheritedFactory(FluidStyle)))(__ngFactoryType__ || FluidStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FluidStyle,\n    factory: FluidStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FluidStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Fluid is a layout component to make descendant components span full width of their container.\n *\n * [Live Demo](https://www.primeng.org/fluid/)\n *\n * @module fluidstyle\n *\n */\nvar FluidClasses;\n(function (FluidClasses) {\n  /**\n   * Class name of the root element\n   */\n  FluidClasses[\"root\"] = \"p-fluid\";\n})(FluidClasses || (FluidClasses = {}));\n\n/**\n * Fluid is a layout component to make descendant components span full width of their container.\n * @group Components\n */\nclass Fluid extends BaseComponent {\n  _componentStyle = inject(FluidStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFluid_BaseFactory;\n    return function Fluid_Factory(__ngFactoryType__) {\n      return (ɵFluid_BaseFactory || (ɵFluid_BaseFactory = i0.ɵɵgetInheritedFactory(Fluid)))(__ngFactoryType__ || Fluid);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Fluid,\n    selectors: [[\"p-fluid\"]],\n    hostVars: 2,\n    hostBindings: function Fluid_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.cx(\"root\"));\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([FluidStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function Fluid_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Fluid, [{\n    type: Component,\n    args: [{\n      selector: 'p-fluid',\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      imports: [CommonModule],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [FluidStyle],\n      host: {\n        '[class]': \"cx('root')\"\n      }\n    }]\n  }], null, null);\n})();\nclass FluidModule {\n  static ɵfac = function FluidModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FluidModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FluidModule,\n    imports: [Fluid],\n    exports: [Fluid]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Fluid]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FluidModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Fluid],\n      exports: [Fluid]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Fluid, FluidClasses, FluidModule, FluidStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,aAAN,MAAM,oBAAmB,UAAU;AAAA,EACjC,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,YAAW;AAAA,EACtB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,eAAc;AAIvB,EAAAA,cAAa,MAAM,IAAI;AACzB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAMtC,IAAM,QAAN,MAAM,eAAc,cAAc;AAAA,EAChC,kBAAkB,OAAO,UAAU;AAAA,EACnC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,cAAc,mBAAmB;AAC/C,cAAQ,uBAAuB,qBAAwB,sBAAsB,MAAK,IAAI,qBAAqB,MAAK;AAAA,IAClH;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,IACvB,UAAU;AAAA,IACV,cAAc,SAAS,mBAAmB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,UAAU,CAAC,GAAM,0BAA0B;AAAA,IAC7E,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,eAAe,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,YAAY;AAAA,MACtB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,UAAU;AAAA,MACtB,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,KAAK;AAAA,IACf,SAAS,CAAC,KAAK;AAAA,EACjB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,KAAK;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,KAAK;AAAA,MACf,SAAS,CAAC,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["FluidClasses"]}