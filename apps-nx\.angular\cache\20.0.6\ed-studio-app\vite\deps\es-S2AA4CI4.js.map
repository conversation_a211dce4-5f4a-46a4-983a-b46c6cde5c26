{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/es.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n    if (n === 1)\n        return 1;\n    if (e === 0 && (!(i === 0) && (i % 1000000 === 0 && v === 0)) || !(e >= 0 && e <= 5))\n        return 4;\n    return 5;\n}\nexport default [\"es\", [[\"a. m.\", \"p. m.\"], u, u], u, [[\"D\", \"L\", \"M\", \"X\", \"J\", \"V\", \"S\"], [\"dom\", \"lun\", \"mar\", \"mié\", \"jue\", \"vie\", \"sáb\"], [\"domingo\", \"lunes\", \"martes\", \"miércoles\", \"jueves\", \"viernes\", \"sábado\"], [\"DO\", \"LU\", \"MA\", \"MI\", \"JU\", \"VI\", \"SA\"]], u, [[\"E\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"ene\", \"feb\", \"mar\", \"abr\", \"may\", \"jun\", \"jul\", \"ago\", \"sept\", \"oct\", \"nov\", \"dic\"], [\"enero\", \"febrero\", \"marzo\", \"abril\", \"mayo\", \"junio\", \"julio\", \"agosto\", \"septiembre\", \"octubre\", \"noviembre\", \"diciembre\"]], u, [[\"a. C.\", \"d. C.\"], u, [\"antes de Cristo\", \"después de Cristo\"]], 1, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss (zzzz)\"], [\"{1}, {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"EGP\": [], \"ESP\": [\"₧\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"RON\": [u, \"L\"], \"THB\": [\"฿\"], \"TWD\": [u, \"NT$\"], \"USD\": [\"US$\", \"$\"], \"XAF\": [], \"XCD\": [u, \"$\"], \"XOF\": [] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxK,MAAI,MAAM;AACN,WAAO;AACX,MAAI,MAAM,MAAM,EAAE,MAAM,OAAO,IAAI,QAAY,KAAK,MAAM,OAAO,EAAE,KAAK,KAAK,KAAK;AAC9E,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,SAAS,UAAU,aAAa,UAAU,WAAW,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,GAAG,CAAC,SAAS,WAAW,SAAS,SAAS,QAAQ,SAAS,SAAS,UAAU,cAAc,WAAW,aAAa,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAC,mBAAmB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,WAAW,sBAAsB,0BAA0B,GAAG,CAAC,QAAQ,WAAW,aAAa,gBAAgB,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,MAAM;", "names": []}