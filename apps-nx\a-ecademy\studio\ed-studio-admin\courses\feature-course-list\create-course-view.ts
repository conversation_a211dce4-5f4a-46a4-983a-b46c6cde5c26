import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input } from '@angular/core';
import { RadDialogLayout } from '@tec/rad-ui/layout';
import { CourseStore } from './course-store';
import { CreateCourseCommand } from '@ed/share/data-content';
import { RadEditForm, RadFormConfig, RadFormLayout } from '@tec/rad-xui/form';
import { injectViewContext } from '@tec/rad-core/composition';

        
@Component({
    selector: 'ed-create-course-view',
    template: `
    <rad-dialog-layout title="Create Course" (confirm)="createCourse()" [loading]="store.loading()">
        <rad-form-layout [form]="form" [items]="fields" [model]="cmd()"  />
    </rad-dialog-layout>
    `,
    styles: [],
    imports:[
        CommonModule,
        RadDialogLayout,
        RadFormLayout

    ],
    
})
export class CreateCourseView {
        

    protected context = injectViewContext();
    protected store = inject(CourseStore);

        
    protected cmd = signal(new CreateCourseCommand());

    protected form = new RadEditForm(this.cmd, signal(true));

    fields: RadFormConfig = [
        { key: 'name', label: 'Name', type: 'input', rowNum: 1},
        { key: 'description', label: 'Description', type: 'textarea', rowNum: 2},
    ]


    createCourse() {

        if(!this.form.validate()){
            return;
        }
        const command = this.form.model();

        this.store.createCourse(command);
        this.context.close();
    }
        
}