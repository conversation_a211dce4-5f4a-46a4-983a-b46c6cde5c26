{"version": 3, "sources": ["../../../../../../node_modules/@angular/core/fesm2022/primitives/di.mjs", "../../../../../../node_modules/@angular/core/fesm2022/signal.mjs", "../../../../../../node_modules/@angular/core/fesm2022/untracked.mjs", "../../../../../../node_modules/@angular/core/fesm2022/weak_ref.mjs", "../../../../../../node_modules/@angular/core/fesm2022/primitives/signals.mjs", "../../../../../../node_modules/@angular/core/fesm2022/root_effect_scheduler.mjs", "../../../../../../node_modules/@angular/core/fesm2022/resource.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * Current injector value used by `inject`.\n * - `undefined`: it is an error to call `inject`\n * - `null`: `inject` can be called but there is no injector (limp-mode).\n * - Injector instance: Use the injector for resolution.\n */\nlet _currentInjector = undefined;\nfunction getCurrentInjector() {\n    return _currentInjector;\n}\nfunction setCurrentInjector(injector) {\n    const former = _currentInjector;\n    _currentInjector = injector;\n    return former;\n}\n\n/**\n * Value returned if the key-value pair couldn't be found in the context\n * hierarchy.\n */\nconst NOT_FOUND = Symbol('NotFound');\n/**\n * Error thrown when the key-value pair couldn't be found in the context\n * hierarchy. Context can be attached below.\n */\nclass NotFoundError extends Error {\n    name = 'ɵNotFound';\n    constructor(message) {\n        super(message);\n    }\n}\n/**\n * Type guard for checking if an unknown value is a NotFound.\n */\nfunction isNotFound(e) {\n    return e === NOT_FOUND || e?.name === 'ɵNotFound';\n}\n\nexport { NOT_FOUND, NotFoundError, getCurrentInjector, isNotFound, setCurrentInjector };\n\n", "/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n    return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * If set, called after a producer `ReactiveNode` is created.\n */\nlet postProducerCreatedFn = null;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */ Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n    const prev = activeConsumer;\n    activeConsumer = consumer;\n    return prev;\n}\nfunction getActiveConsumer() {\n    return activeConsumer;\n}\nfunction isInNotificationPhase() {\n    return inNotificationPhase;\n}\nfunction isReactive(value) {\n    return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n    version: 0,\n    lastCleanEpoch: 0,\n    dirty: false,\n    producerNode: undefined,\n    producerLastReadVersion: undefined,\n    producerIndexOfThis: undefined,\n    nextProducerIndex: 0,\n    liveConsumerNode: undefined,\n    liveConsumerIndexOfThis: undefined,\n    consumerAllowSignalWrites: false,\n    consumerIsAlwaysLive: false,\n    kind: 'unknown',\n    producerMustRecompute: () => false,\n    producerRecomputeValue: () => { },\n    consumerMarkedDirty: () => { },\n    consumerOnSignalRead: () => { },\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n    if (inNotificationPhase) {\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode\n            ? `Assertion error: signal read during notification phase`\n            : '');\n    }\n    if (activeConsumer === null) {\n        // Accessed outside of a reactive context, so nothing to record.\n        return;\n    }\n    activeConsumer.consumerOnSignalRead(node);\n    // This producer is the `idx`th dependency of `activeConsumer`.\n    const idx = activeConsumer.nextProducerIndex++;\n    assertConsumerNode(activeConsumer);\n    if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n        // There's been a change in producers since the last execution of `activeConsumer`.\n        // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n        // replaced with `this`.\n        //\n        // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n        // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n        // to remove it from the stale producer's `liveConsumer`s.\n        if (consumerIsLive(activeConsumer)) {\n            const staleProducer = activeConsumer.producerNode[idx];\n            producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n            // At this point, the only record of `staleProducer` is the reference at\n            // `activeConsumer.producerNode[idx]` which will be overwritten below.\n        }\n    }\n    if (activeConsumer.producerNode[idx] !== node) {\n        // We're a new dependency of the consumer (at `idx`).\n        activeConsumer.producerNode[idx] = node;\n        // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n        // placeholder value.\n        activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer)\n            ? producerAddLiveConsumer(node, activeConsumer, idx)\n            : 0;\n    }\n    activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n    epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n    if (consumerIsLive(node) && !node.dirty) {\n        // A live consumer will be marked dirty by producers, so a clean state means that its version\n        // is guaranteed to be up-to-date.\n        return;\n    }\n    if (!node.dirty && node.lastCleanEpoch === epoch) {\n        // Even non-live consumers can skip polling if they previously found themselves to be clean at\n        // the current epoch, since their dependencies could not possibly have changed (such a change\n        // would've increased the epoch).\n        return;\n    }\n    if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n        // None of our producers report a change since the last time they were read, so no\n        // recomputation of our value is necessary, and we can consider ourselves clean.\n        producerMarkClean(node);\n        return;\n    }\n    node.producerRecomputeValue(node);\n    // After recomputing the value, we're no longer dirty.\n    producerMarkClean(node);\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n    if (node.liveConsumerNode === undefined) {\n        return;\n    }\n    // Prevent signal reads when we're updating the graph\n    const prev = inNotificationPhase;\n    inNotificationPhase = true;\n    try {\n        for (const consumer of node.liveConsumerNode) {\n            if (!consumer.dirty) {\n                consumerMarkDirty(consumer);\n            }\n        }\n    }\n    finally {\n        inNotificationPhase = prev;\n    }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n    return activeConsumer?.consumerAllowSignalWrites !== false;\n}\nfunction consumerMarkDirty(node) {\n    node.dirty = true;\n    producerNotifyConsumers(node);\n    node.consumerMarkedDirty?.(node);\n}\nfunction producerMarkClean(node) {\n    node.dirty = false;\n    node.lastCleanEpoch = epoch;\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n    node && (node.nextProducerIndex = 0);\n    return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n    setActiveConsumer(prevConsumer);\n    if (!node ||\n        node.producerNode === undefined ||\n        node.producerIndexOfThis === undefined ||\n        node.producerLastReadVersion === undefined) {\n        return;\n    }\n    if (consumerIsLive(node)) {\n        // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n        // which weren't dependencies after the recomputation.\n        for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate the producer tracking arrays.\n    // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n    // benchmarking has shown that individual pop operations are faster.\n    while (node.producerNode.length > node.nextProducerIndex) {\n        node.producerNode.pop();\n        node.producerLastReadVersion.pop();\n        node.producerIndexOfThis.pop();\n    }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n    assertConsumerNode(node);\n    // Poll producers for change.\n    for (let i = 0; i < node.producerNode.length; i++) {\n        const producer = node.producerNode[i];\n        const seenVersion = node.producerLastReadVersion[i];\n        // First check the versions. A mismatch means that the producer's value is known to have\n        // changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n        // The producer's version is the same as the last time we read it, but it might itself be\n        // stale. Force the producer to recompute its version (calculating a new value if necessary).\n        producerUpdateValueVersion(producer);\n        // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n        // versions still match then it has not changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n    assertConsumerNode(node);\n    if (consumerIsLive(node)) {\n        // Drop all connections from the graph to this node.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate all the arrays to drop all connection from this node to the graph.\n    node.producerNode.length =\n        node.producerLastReadVersion.length =\n            node.producerIndexOfThis.length =\n                0;\n    if (node.liveConsumerNode) {\n        node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n    }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n    assertProducerNode(node);\n    if (node.liveConsumerNode.length === 0 && isConsumerNode(node)) {\n        // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n        }\n    }\n    node.liveConsumerIndexOfThis.push(indexOfThis);\n    return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n    assertProducerNode(node);\n    if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n        throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n    }\n    if (node.liveConsumerNode.length === 1 && isConsumerNode(node)) {\n        // When removing the last live consumer, we will no longer be live. We need to remove\n        // ourselves from our producers' tracking (which may cause consumer-producers to lose\n        // liveness as well).\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n    // live consumer, this is a no-op.\n    const lastIdx = node.liveConsumerNode.length - 1;\n    node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n    node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n    // Truncate the array.\n    node.liveConsumerNode.length--;\n    node.liveConsumerIndexOfThis.length--;\n    // If the index is still valid, then we need to fix the index pointer from the producer to this\n    // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n    if (idx < node.liveConsumerNode.length) {\n        const idxProducer = node.liveConsumerIndexOfThis[idx];\n        const consumer = node.liveConsumerNode[idx];\n        assertConsumerNode(consumer);\n        consumer.producerIndexOfThis[idxProducer] = idx;\n    }\n}\nfunction consumerIsLive(node) {\n    return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\nfunction assertConsumerNode(node) {\n    node.producerNode ??= [];\n    node.producerIndexOfThis ??= [];\n    node.producerLastReadVersion ??= [];\n}\nfunction assertProducerNode(node) {\n    node.liveConsumerNode ??= [];\n    node.liveConsumerIndexOfThis ??= [];\n}\nfunction isConsumerNode(node) {\n    return node.producerNode !== undefined;\n}\nfunction runPostProducerCreatedFn(node) {\n    postProducerCreatedFn?.(node);\n}\nfunction setPostProducerCreatedFn(fn) {\n    const prev = postProducerCreatedFn;\n    postProducerCreatedFn = fn;\n    return prev;\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation, equal) {\n    const node = Object.create(COMPUTED_NODE);\n    node.computation = computation;\n    if (equal !== undefined) {\n        node.equal = equal;\n    }\n    const computed = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    computed[SIGNAL] = node;\n    if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n        const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n        computed.toString = () => `[Computed${debugName}: ${node.value}]`;\n    }\n    runPostProducerCreatedFn(node);\n    return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */ Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */ Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */ Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        kind: 'computed',\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            let wasEqual = false;\n            try {\n                newValue = node.computation();\n                // We want to mark this node as errored if calling `equal` throws; however, we don't want\n                // to track any reactive reads inside `equal`.\n                setActiveConsumer(null);\n                wasEqual =\n                    oldValue !== UNSET &&\n                        oldValue !== ERRORED &&\n                        newValue !== ERRORED &&\n                        node.equal(oldValue, newValue);\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (wasEqual) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\nfunction defaultThrowError() {\n    throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError(node) {\n    throwInvalidWriteToSignalErrorFn(node);\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n    throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Creates a `Signal` getter, setter, and updater function.\n */\nfunction createSignal(initialValue, equal) {\n    const node = Object.create(SIGNAL_NODE);\n    node.value = initialValue;\n    if (equal !== undefined) {\n        node.equal = equal;\n    }\n    const getter = (() => signalGetFn(node));\n    getter[SIGNAL] = node;\n    if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n        const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n        getter.toString = () => `[Signal${debugName}: ${node.value}]`;\n    }\n    runPostProducerCreatedFn(node);\n    const set = (newValue) => signalSetFn(node, newValue);\n    const update = (updateFn) => signalUpdateFn(node, updateFn);\n    return [getter, set, update];\n}\nfunction setPostSignalSetFn(fn) {\n    const prev = postSignalSetFn;\n    postSignalSetFn = fn;\n    return prev;\n}\nfunction signalGetFn(node) {\n    producerAccessed(node);\n    return node.value;\n}\nfunction signalSetFn(node, newValue) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError(node);\n    }\n    if (!node.equal(node.value, newValue)) {\n        node.value = newValue;\n        signalValueChanged(node);\n    }\n}\nfunction signalUpdateFn(node, updater) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError(node);\n    }\n    signalSetFn(node, updater(node.value));\n}\nfunction runPostSignalSetFn(node) {\n    postSignalSetFn?.(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        equal: defaultEquals,\n        value: undefined,\n        kind: 'signal',\n    };\n})();\nfunction signalValueChanged(node) {\n    node.version++;\n    producerIncrementEpoch();\n    producerNotifyConsumers(node);\n    postSignalSetFn?.(node);\n}\n\nexport { COMPUTING, ERRORED, REACTIVE_NODE, SIGNAL, SIGNAL_NODE, UNSET, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createComputed, createSignal, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostProducerCreatedFn, runPostSignalSetFn, setActiveConsumer, setPostProducerCreatedFn, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalGetFn, signalSetFn, signalUpdateFn };\n\n", "/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { SIGNAL, runPostProducerCreatedFn, producerUpdateValueVersion, signalSetFn, producerMarkClean, signalUpdateFn, REACTIVE_NODE, UNSET, defaultEquals, COMPUTING, consumerBeforeComputation, ERRORED, consumerAfterComputation, producerAccessed, setActiveConsumer } from './signal.mjs';\n\nfunction createLinkedSignal(sourceFn, computationFn, equalityFn) {\n    const node = Object.create(LINKED_SIGNAL_NODE);\n    node.source = sourceFn;\n    node.computation = computationFn;\n    if (equalityFn != undefined) {\n        node.equal = equalityFn;\n    }\n    const linkedSignalGetter = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    const getter = linkedSignalGetter;\n    getter[SIGNAL] = node;\n    if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n        const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n        getter.toString = () => `[LinkedSignal${debugName}: ${node.value}]`;\n    }\n    runPostProducerCreatedFn(node);\n    return getter;\n}\nfunction linkedSignalSetFn(node, newValue) {\n    producerUpdateValueVersion(node);\n    signalSetFn(node, newValue);\n    producerMarkClean(node);\n}\nfunction linkedSignalUpdateFn(node, updater) {\n    producerUpdateValueVersion(node);\n    signalUpdateFn(node, updater);\n    producerMarkClean(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst LINKED_SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        kind: 'linkedSignal',\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            try {\n                const newSourceValue = node.source();\n                const prev = oldValue === UNSET || oldValue === ERRORED\n                    ? undefined\n                    : {\n                        source: node.sourceValue,\n                        value: oldValue,\n                    };\n                newValue = node.computation(newSourceValue, prev);\n                node.sourceValue = newSourceValue;\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n    const prevConsumer = setActiveConsumer(null);\n    // We are not trying to catch any particular errors here, just making sure that the consumers\n    // stack is restored in case of errors.\n    try {\n        return nonReactiveReadsFn();\n    }\n    finally {\n        setActiveConsumer(prevConsumer);\n    }\n}\n\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked };\n\n", "/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction setAlternateWeakRefImpl(impl) {\n    // TODO: remove this function\n}\n\nexport { setAlternateWeakRefImpl };\n\n", "/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { consumerMarkDirty, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from '../signal.mjs';\nexport { SIGNAL_NODE, createComputed, createSignal, defaultEquals, getActiveConsumer, isReactive, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostProducerCreatedFn, runPostSignalSetFn, setActiveConsumer, setPostProducerCreatedFn, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalGetFn, signalSetFn, signalUpdateFn } from '../signal.mjs';\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked } from '../untracked.mjs';\nexport { setAlternateWeakRefImpl } from '../weak_ref.mjs';\n\nfunction createWatch(fn, schedule, allowSignalWrites) {\n    const node = Object.create(WATCH_NODE);\n    if (allowSignalWrites) {\n        node.consumerAllowSignalWrites = true;\n    }\n    node.fn = fn;\n    node.schedule = schedule;\n    const registerOnCleanup = (cleanupFn) => {\n        node.cleanupFn = cleanupFn;\n    };\n    function isWatchNodeDestroyed(node) {\n        return node.fn === null && node.schedule === null;\n    }\n    function destroyWatchNode(node) {\n        if (!isWatchNodeDestroyed(node)) {\n            consumerDestroy(node); // disconnect watcher from the reactive graph\n            node.cleanupFn();\n            // nullify references to the integration functions to mark node as destroyed\n            node.fn = null;\n            node.schedule = null;\n            node.cleanupFn = NOOP_CLEANUP_FN;\n        }\n    }\n    const run = () => {\n        if (node.fn === null) {\n            // trying to run a destroyed watch is noop\n            return;\n        }\n        if (isInNotificationPhase()) {\n            throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode\n                ? 'Schedulers cannot synchronously execute watches while scheduling.'\n                : '');\n        }\n        node.dirty = false;\n        if (node.hasRun && !consumerPollProducersForChange(node)) {\n            return;\n        }\n        node.hasRun = true;\n        const prevConsumer = consumerBeforeComputation(node);\n        try {\n            node.cleanupFn();\n            node.cleanupFn = NOOP_CLEANUP_FN;\n            node.fn(registerOnCleanup);\n        }\n        finally {\n            consumerAfterComputation(node, prevConsumer);\n        }\n    };\n    node.ref = {\n        notify: () => consumerMarkDirty(node),\n        run,\n        cleanup: () => node.cleanupFn(),\n        destroy: () => destroyWatchNode(node),\n        [SIGNAL]: node,\n    };\n    return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => { };\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        consumerIsAlwaysLive: true,\n        consumerAllowSignalWrites: false,\n        consumerMarkedDirty: (node) => {\n            if (node.schedule !== null) {\n                node.schedule(node.ref);\n            }\n        },\n        hasRun: false,\n        cleanupFn: NOOP_CLEANUP_FN,\n    };\n})();\n\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createWatch, isInNotificationPhase };\n\n", "/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isNotFound, getCurrentInjector, setCurrentInjector } from './primitives/di.mjs';\nimport { getActiveConsumer, SIGNAL, createSignal } from './signal.mjs';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { NotFoundError, isNotFound as isNotFound$1 } from '@angular/core/primitives/di';\nimport { setActiveConsumer } from '@angular/core/primitives/signals';\n\n/**\n * Base URL for the error details page.\n *\n * Keep this constant in sync across:\n *  - packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.ts\n *  - packages/core/src/error_details_base_url.ts\n */\nconst ERROR_DETAILS_PAGE_BASE_URL = 'https://angular.dev/errors';\n/**\n * URL for the XSS security documentation.\n */\nconst XSS_SECURITY_URL = 'https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss';\n\n/**\n * Class that represents a runtime error.\n * Formats and outputs the error message in a consistent way.\n *\n * Example:\n * ```ts\n *  throw new RuntimeError(\n *    RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,\n *    ngDevMode && 'Injector has already been destroyed.');\n * ```\n *\n * Note: the `message` argument contains a descriptive error message as a string in development\n * mode (when the `ngDevMode` is defined). In production mode (after tree-shaking pass), the\n * `message` argument becomes `false`, thus we account for it in the typings and the runtime\n * logic.\n */\nclass RuntimeError extends Error {\n    code;\n    constructor(code, message) {\n        super(formatRuntimeError(code, message));\n        this.code = code;\n    }\n}\nfunction formatRuntimeErrorCode(code) {\n    // Error code might be a negative number, which is a special marker that instructs the logic to\n    // generate a link to the error details page on angular.io.\n    // We also prepend `0` to non-compile-time errors.\n    return `NG0${Math.abs(code)}`;\n}\n/**\n * Called to format a runtime error.\n * See additional info on the `message` argument type in the `RuntimeError` class description.\n */\nfunction formatRuntimeError(code, message) {\n    const fullCode = formatRuntimeErrorCode(code);\n    let errorMessage = `${fullCode}${message ? ': ' + message : ''}`;\n    if (ngDevMode && code < 0) {\n        const addPeriodSeparator = !errorMessage.match(/[.,;!?\\n]$/);\n        const separator = addPeriodSeparator ? '.' : '';\n        errorMessage = `${errorMessage}${separator} Find more at ${ERROR_DETAILS_PAGE_BASE_URL}/${fullCode}`;\n    }\n    return errorMessage;\n}\n\nconst _global = globalThis;\n\nfunction ngDevModeResetPerfCounters() {\n    const locationString = typeof location !== 'undefined' ? location.toString() : '';\n    const newCounters = {\n        hydratedNodes: 0,\n        hydratedComponents: 0,\n        dehydratedViewsRemoved: 0,\n        dehydratedViewsCleanupRuns: 0,\n        componentsSkippedHydration: 0,\n        deferBlocksWithIncrementalHydration: 0,\n    };\n    // Make sure to refer to ngDevMode as ['ngDevMode'] for closure.\n    const allowNgDevModeTrue = locationString.indexOf('ngDevMode=false') === -1;\n    if (!allowNgDevModeTrue) {\n        _global['ngDevMode'] = false;\n    }\n    else {\n        if (typeof _global['ngDevMode'] !== 'object') {\n            _global['ngDevMode'] = {};\n        }\n        Object.assign(_global['ngDevMode'], newCounters);\n    }\n    return newCounters;\n}\n/**\n * This function checks to see if the `ngDevMode` has been set. If yes,\n * then we honor it, otherwise we default to dev mode with additional checks.\n *\n * The idea is that unless we are doing production build where we explicitly\n * set `ngDevMode == false` we should be helping the developer by providing\n * as much early warning and errors as possible.\n *\n * `ɵɵdefineComponent` is guaranteed to have been called before any component template functions\n * (and thus Ivy instructions), so a single initialization there is sufficient to ensure ngDevMode\n * is defined for the entire instruction set.\n *\n * When checking `ngDevMode` on toplevel, always init it before referencing it\n * (e.g. `((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode())`), otherwise you can\n *  get a `ReferenceError` like in https://github.com/angular/angular/issues/31595.\n *\n * Details on possible values for `ngDevMode` can be found on its docstring.\n */\nfunction initNgDevMode() {\n    // The below checks are to ensure that calling `initNgDevMode` multiple times does not\n    // reset the counters.\n    // If the `ngDevMode` is not an object, then it means we have not created the perf counters\n    // yet.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (typeof ngDevMode !== 'object' || Object.keys(ngDevMode).length === 0) {\n            ngDevModeResetPerfCounters();\n        }\n        return typeof ngDevMode !== 'undefined' && !!ngDevMode;\n    }\n    return false;\n}\n\nfunction getClosureSafeProperty(objWithPropertyToExtract) {\n    for (let key in objWithPropertyToExtract) {\n        if (objWithPropertyToExtract[key] === getClosureSafeProperty) {\n            return key;\n        }\n    }\n    // Cannot change it to `RuntimeError` because the `util` target cannot\n    // circularly depend on the `core` target.\n    throw Error(typeof ngDevMode !== 'undefined' && ngDevMode\n        ? 'Could not find renamed property on target object.'\n        : '');\n}\n/**\n * Sets properties on a target object from a source object, but only if\n * the property doesn't already exist on the target object.\n * @param target The target to set properties on\n * @param source The source of the property keys and values to set\n */\nfunction fillProperties(target, source) {\n    for (const key in source) {\n        if (source.hasOwnProperty(key) && !target.hasOwnProperty(key)) {\n            target[key] = source[key];\n        }\n    }\n}\n\nfunction stringify(token) {\n    if (typeof token === 'string') {\n        return token;\n    }\n    if (Array.isArray(token)) {\n        return `[${token.map(stringify).join(', ')}]`;\n    }\n    if (token == null) {\n        return '' + token;\n    }\n    const name = token.overriddenName || token.name;\n    if (name) {\n        return `${name}`;\n    }\n    const result = token.toString();\n    if (result == null) {\n        return '' + result;\n    }\n    const newLineIndex = result.indexOf('\\n');\n    return newLineIndex >= 0 ? result.slice(0, newLineIndex) : result;\n}\n/**\n * Concatenates two strings with separator, allocating new strings only when necessary.\n *\n * @param before before string.\n * @param separator separator string.\n * @param after after string.\n * @returns concatenated string.\n */\nfunction concatStringsWithSpace(before, after) {\n    if (!before)\n        return after || '';\n    if (!after)\n        return before;\n    return `${before} ${after}`;\n}\n/**\n * Ellipses the string in the middle when longer than the max length\n *\n * @param string\n * @param maxLength of the output string\n * @returns ellipsed string with ... in the middle\n */\nfunction truncateMiddle(str, maxLength = 100) {\n    if (!str || maxLength < 1 || str.length <= maxLength)\n        return str;\n    if (maxLength == 1)\n        return str.substring(0, 1) + '...';\n    const halfLimit = Math.round(maxLength / 2);\n    return str.substring(0, halfLimit) + '...' + str.substring(str.length - halfLimit);\n}\n\nconst __forward_ref__ = getClosureSafeProperty({ __forward_ref__: getClosureSafeProperty });\n/**\n * Allows to refer to references which are not yet defined.\n *\n * For instance, `forwardRef` is used when the `token` which we need to refer to for the purposes of\n * DI is declared, but not yet defined. It is also used when the `token` which we use when creating\n * a query is not yet defined.\n *\n * `forwardRef` is also used to break circularities in standalone components imports.\n *\n * @usageNotes\n * ### Circular dependency example\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='forward_ref'}\n *\n * ### Circular standalone reference import example\n * ```angular-ts\n * @Component({\n *   imports: [ChildComponent],\n *   selector: 'app-parent',\n *   template: `<app-child [hideParent]=\"hideParent()\"></app-child>`,\n * })\n * export class ParentComponent {\n *    hideParent = input.required<boolean>();\n * }\n *\n *\n * @Component({\n *   imports: [forwardRef(() => ParentComponent)],\n *   selector: 'app-child',\n *   template: `\n *    @if(!hideParent() {\n *       <app-parent/>\n *    }\n *  `,\n * })\n * export class ChildComponent {\n *    hideParent = input.required<boolean>();\n * }\n * ```\n *\n * @publicApi\n */\nfunction forwardRef(forwardRefFn) {\n    forwardRefFn.__forward_ref__ = forwardRef;\n    forwardRefFn.toString = function () {\n        return stringify(this());\n    };\n    return forwardRefFn;\n}\n/**\n * Lazily retrieves the reference value from a forwardRef.\n *\n * Acts as the identity function when given a non-forward-ref value.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='resolve_forward_ref'}\n *\n * @see {@link forwardRef}\n * @publicApi\n */\nfunction resolveForwardRef(type) {\n    return isForwardRef(type) ? type() : type;\n}\n/** Checks whether a function is wrapped by a `forwardRef`. */\nfunction isForwardRef(fn) {\n    return (typeof fn === 'function' &&\n        fn.hasOwnProperty(__forward_ref__) &&\n        fn.__forward_ref__ === forwardRef);\n}\n\n// The functions in this file verify that the assumptions we are making\n// about state in an instruction are correct before implementing any logic.\n// They are meant only to be called in dev mode as sanity checks.\nfunction assertNumber(actual, msg) {\n    if (!(typeof actual === 'number')) {\n        throwError(msg, typeof actual, 'number', '===');\n    }\n}\nfunction assertNumberInRange(actual, minInclusive, maxInclusive) {\n    assertNumber(actual, 'Expected a number');\n    assertLessThanOrEqual(actual, maxInclusive, 'Expected number to be less than or equal to');\n    assertGreaterThanOrEqual(actual, minInclusive, 'Expected number to be greater than or equal to');\n}\nfunction assertString(actual, msg) {\n    if (!(typeof actual === 'string')) {\n        throwError(msg, actual === null ? 'null' : typeof actual, 'string', '===');\n    }\n}\nfunction assertFunction(actual, msg) {\n    if (!(typeof actual === 'function')) {\n        throwError(msg, actual === null ? 'null' : typeof actual, 'function', '===');\n    }\n}\nfunction assertEqual(actual, expected, msg) {\n    if (!(actual == expected)) {\n        throwError(msg, actual, expected, '==');\n    }\n}\nfunction assertNotEqual(actual, expected, msg) {\n    if (!(actual != expected)) {\n        throwError(msg, actual, expected, '!=');\n    }\n}\nfunction assertSame(actual, expected, msg) {\n    if (!(actual === expected)) {\n        throwError(msg, actual, expected, '===');\n    }\n}\nfunction assertNotSame(actual, expected, msg) {\n    if (!(actual !== expected)) {\n        throwError(msg, actual, expected, '!==');\n    }\n}\nfunction assertLessThan(actual, expected, msg) {\n    if (!(actual < expected)) {\n        throwError(msg, actual, expected, '<');\n    }\n}\nfunction assertLessThanOrEqual(actual, expected, msg) {\n    if (!(actual <= expected)) {\n        throwError(msg, actual, expected, '<=');\n    }\n}\nfunction assertGreaterThan(actual, expected, msg) {\n    if (!(actual > expected)) {\n        throwError(msg, actual, expected, '>');\n    }\n}\nfunction assertGreaterThanOrEqual(actual, expected, msg) {\n    if (!(actual >= expected)) {\n        throwError(msg, actual, expected, '>=');\n    }\n}\nfunction assertNotDefined(actual, msg) {\n    if (actual != null) {\n        throwError(msg, actual, null, '==');\n    }\n}\nfunction assertDefined(actual, msg) {\n    if (actual == null) {\n        throwError(msg, actual, null, '!=');\n    }\n}\nfunction throwError(msg, actual, expected, comparison) {\n    throw new Error(`ASSERTION ERROR: ${msg}` +\n        (comparison == null ? '' : ` [Expected=> ${expected} ${comparison} ${actual} <=Actual]`));\n}\nfunction assertDomNode(node) {\n    if (!(node instanceof Node)) {\n        throwError(`The provided value must be an instance of a DOM Node but got ${stringify(node)}`);\n    }\n}\nfunction assertElement(node) {\n    if (!(node instanceof Element)) {\n        throwError(`The provided value must be an element but got ${stringify(node)}`);\n    }\n}\nfunction assertIndexInRange(arr, index) {\n    assertDefined(arr, 'Array must be defined.');\n    const maxLen = arr.length;\n    if (index < 0 || index >= maxLen) {\n        throwError(`Index expected to be less than ${maxLen} but got ${index}`);\n    }\n}\nfunction assertOneOf(value, ...validValues) {\n    if (validValues.indexOf(value) !== -1)\n        return true;\n    throwError(`Expected value to be one of ${JSON.stringify(validValues)} but was ${JSON.stringify(value)}.`);\n}\nfunction assertNotReactive(fn) {\n    if (getActiveConsumer() !== null) {\n        throwError(`${fn}() should never be called in a reactive context.`);\n    }\n}\n\n/**\n * Construct an injectable definition which defines how a token will be constructed by the DI\n * system, and in which injectors (if any) it will be available.\n *\n * This should be assigned to a static `ɵprov` field on a type, which will then be an\n * `InjectableType`.\n *\n * Options:\n * * `providedIn` determines which injectors will include the injectable, by either associating it\n *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be\n *   provided in the `'root'` injector, which will be the application-level injector in most apps.\n * * `factory` gives the zero argument function which will create an instance of the injectable.\n *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection\n * of dependencies.\n *\n * @codeGenApi\n * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.\n */\nfunction ɵɵdefineInjectable(opts) {\n    return {\n        token: opts.token,\n        providedIn: opts.providedIn || null,\n        factory: opts.factory,\n        value: undefined,\n    };\n}\n/**\n * @deprecated in v8, delete after v10. This API should be used only by generated code, and that\n * code should now use ɵɵdefineInjectable instead.\n * @publicApi\n */\nconst defineInjectable = ɵɵdefineInjectable;\n/**\n * Construct an `InjectorDef` which configures an injector.\n *\n * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an\n * `InjectorType`.\n *\n * Options:\n *\n * * `providers`: an optional array of providers to add to the injector. Each provider must\n *   either have a factory or point to a type which has a `ɵprov` static property (the\n *   type must be an `InjectableType`).\n * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s\n *   whose providers will also be added to the injector. Locally provided types will override\n *   providers from imports.\n *\n * @codeGenApi\n */\nfunction ɵɵdefineInjector(options) {\n    return { providers: options.providers || [], imports: options.imports || [] };\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading\n * inherited value.\n *\n * @param type A type which may have its own (non-inherited) `ɵprov`.\n */\nfunction getInjectableDef(type) {\n    return getOwnDefinition(type, NG_PROV_DEF);\n}\nfunction isInjectable(type) {\n    return getInjectableDef(type) !== null;\n}\n/**\n * Return definition only if it is defined directly on `type` and is not inherited from a base\n * class of `type`.\n */\nfunction getOwnDefinition(type, field) {\n    // if the ɵprov prop exist but is undefined we still want to return null\n    return (type.hasOwnProperty(field) && type[field]) || null;\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.\n *\n * @param type A type which may have `ɵprov`, via inheritance.\n *\n * @deprecated Will be removed in a future version of Angular, where an error will occur in the\n *     scenario if we find the `ɵprov` on an ancestor only.\n */\nfunction getInheritedInjectableDef(type) {\n    // if the ɵprov prop exist but is undefined we still want to return null\n    const def = type?.[NG_PROV_DEF] ?? null;\n    if (def) {\n        ngDevMode &&\n            console.warn(`DEPRECATED: DI is instantiating a token \"${type.name}\" that inherits its @Injectable decorator but does not provide one itself.\\n` +\n                `This will become an error in a future version of Angular. Please add @Injectable() to the \"${type.name}\" class.`);\n        return def;\n    }\n    else {\n        return null;\n    }\n}\n/**\n * Read the injector def type in a way which is immune to accidentally reading inherited value.\n *\n * @param type type which may have an injector def (`ɵinj`)\n */\nfunction getInjectorDef(type) {\n    return type && type.hasOwnProperty(NG_INJ_DEF) ? type[NG_INJ_DEF] : null;\n}\nconst NG_PROV_DEF = getClosureSafeProperty({ ɵprov: getClosureSafeProperty });\nconst NG_INJ_DEF = getClosureSafeProperty({ ɵinj: getClosureSafeProperty });\n\n/**\n * Creates a token that can be used in a DI Provider.\n *\n * Use an `InjectionToken` whenever the type you are injecting is not reified (does not have a\n * runtime representation) such as when injecting an interface, callable type, array or\n * parameterized type.\n *\n * `InjectionToken` is parameterized on `T` which is the type of object which will be returned by\n * the `Injector`. This provides an additional level of type safety.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * **Important Note**: Ensure that you use the same instance of the `InjectionToken` in both the\n * provider and the injection call. Creating a new instance of `InjectionToken` in different places,\n * even with the same description, will be treated as different tokens by Angular's DI system,\n * leading to a `NullInjectorError`.\n *\n * </div>\n *\n * {@example injection-token/src/main.ts region='InjectionToken'}\n *\n * When creating an `InjectionToken`, you can optionally specify a factory function which returns\n * (possibly by creating) a default value of the parameterized type `T`. This sets up the\n * `InjectionToken` using this factory as a provider as if it was defined explicitly in the\n * application's root injector. If the factory function, which takes zero arguments, needs to inject\n * dependencies, it can do so using the [`inject`](api/core/inject) function.\n * As you can see in the Tree-shakable InjectionToken example below.\n *\n * Additionally, if a `factory` is specified you can also specify the `providedIn` option, which\n * overrides the above behavior and marks the token as belonging to a particular `@NgModule` (note:\n * this option is now deprecated). As mentioned above, `'root'` is the default value for\n * `providedIn`.\n *\n * The `providedIn: NgModule` and `providedIn: 'any'` options are deprecated.\n *\n * @usageNotes\n * ### Basic Examples\n *\n * ### Plain InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='InjectionToken'}\n *\n * ### Tree-shakable InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='ShakableInjectionToken'}\n *\n * @publicApi\n */\nclass InjectionToken {\n    _desc;\n    /** @internal */\n    ngMetadataName = 'InjectionToken';\n    ɵprov;\n    /**\n     * @param _desc   Description for the token,\n     *                used only for debugging purposes,\n     *                it should but does not need to be unique\n     * @param options Options for the token's usage, as described above\n     */\n    constructor(_desc, options) {\n        this._desc = _desc;\n        this.ɵprov = undefined;\n        if (typeof options == 'number') {\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                assertLessThan(options, 0, 'Only negative numbers are supported here');\n            // This is a special hack to assign __NG_ELEMENT_ID__ to this instance.\n            // See `InjectorMarkers`\n            this.__NG_ELEMENT_ID__ = options;\n        }\n        else if (options !== undefined) {\n            this.ɵprov = ɵɵdefineInjectable({\n                token: this,\n                providedIn: options.providedIn || 'root',\n                factory: options.factory,\n            });\n        }\n    }\n    /**\n     * @internal\n     */\n    get multi() {\n        return this;\n    }\n    toString() {\n        return `InjectionToken ${this._desc}`;\n    }\n}\n\nlet _injectorProfilerContext;\nfunction getInjectorProfilerContext() {\n    !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');\n    return _injectorProfilerContext;\n}\nfunction setInjectorProfilerContext(context) {\n    !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');\n    const previous = _injectorProfilerContext;\n    _injectorProfilerContext = context;\n    return previous;\n}\nconst injectorProfilerCallbacks = [];\nconst NOOP_PROFILER_REMOVAL = () => { };\nfunction removeProfiler(profiler) {\n    const profilerIdx = injectorProfilerCallbacks.indexOf(profiler);\n    if (profilerIdx !== -1) {\n        injectorProfilerCallbacks.splice(profilerIdx, 1);\n    }\n}\n/**\n * Adds a callback function which will be invoked during certain DI events within the\n * runtime (for example: injecting services, creating injectable instances, configuring providers).\n * Multiple profiler callbacks can be set: in this case profiling events are\n * reported to every registered callback.\n *\n * Warning: this function is *INTERNAL* and should not be relied upon in application's code.\n * The contract of the function might be changed in any release and/or the function can be removed\n * completely.\n *\n * @param profiler function provided by the caller or null value to disable profiling.\n * @returns a cleanup function that, when invoked, removes a given profiler callback.\n */\nfunction setInjectorProfiler(injectorProfiler) {\n    !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');\n    if (injectorProfiler !== null) {\n        if (!injectorProfilerCallbacks.includes(injectorProfiler)) {\n            injectorProfilerCallbacks.push(injectorProfiler);\n        }\n        return () => removeProfiler(injectorProfiler);\n    }\n    else {\n        injectorProfilerCallbacks.length = 0;\n        return NOOP_PROFILER_REMOVAL;\n    }\n}\n/**\n * Injector profiler function which emits on DI events executed by the runtime.\n *\n * @param event InjectorProfilerEvent corresponding to the DI event being emitted\n */\nfunction injectorProfiler(event) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    for (let i = 0; i < injectorProfilerCallbacks.length; i++) {\n        const injectorProfilerCallback = injectorProfilerCallbacks[i];\n        injectorProfilerCallback(event);\n    }\n}\n/**\n * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the\n * emitted event includes the raw provider, as well as the token that provider is providing.\n *\n * @param eventProvider A provider object\n */\nfunction emitProviderConfiguredEvent(eventProvider, isViewProvider = false) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    let token;\n    // if the provider is a TypeProvider (typeof provider is function) then the token is the\n    // provider itself\n    if (typeof eventProvider === 'function') {\n        token = eventProvider;\n    }\n    // if the provider is an injection token, then the token is the injection token.\n    else if (eventProvider instanceof InjectionToken) {\n        token = eventProvider;\n    }\n    // in all other cases we can access the token via the `provide` property of the provider\n    else {\n        token = resolveForwardRef(eventProvider.provide);\n    }\n    let provider = eventProvider;\n    // Injection tokens may define their own default provider which gets attached to the token itself\n    // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the\n    // token itself.\n    if (eventProvider instanceof InjectionToken) {\n        provider = eventProvider.ɵprov || eventProvider;\n    }\n    injectorProfiler({\n        type: 2 /* InjectorProfilerEventType.ProviderConfigured */,\n        context: getInjectorProfilerContext(),\n        providerRecord: { token, provider, isViewProvider },\n    });\n}\n/**\n * Emits an event to the injector profiler when an instance corresponding to a given token is about to be created be an injector. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInjectorToCreateInstanceEvent(token) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 4 /* InjectorProfilerEventType.InjectorToCreateInstanceEvent */,\n        context: getInjectorProfilerContext(),\n        token: token,\n    });\n}\n/**\n * Emits an event to the injector profiler with the instance that was created. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInstanceCreatedByInjectorEvent(instance) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */,\n        context: getInjectorProfilerContext(),\n        instance: { value: instance },\n    });\n}\n/**\n * @param token DI token associated with injected service\n * @param value the instance of the injected service (i.e the result of `inject(token)`)\n * @param flags the flags that the token was injected with\n */\nfunction emitInjectEvent(token, value, flags) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 0 /* InjectorProfilerEventType.Inject */,\n        context: getInjectorProfilerContext(),\n        service: { token, value, flags },\n    });\n}\nfunction emitEffectCreatedEvent(effect) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 3 /* InjectorProfilerEventType.EffectCreated */,\n        context: getInjectorProfilerContext(),\n        effect,\n    });\n}\nfunction runInInjectorProfilerContext(injector, token, callback) {\n    !ngDevMode &&\n        throwError('runInInjectorProfilerContext should never be called in production mode');\n    const prevInjectContext = setInjectorProfilerContext({ injector, token });\n    try {\n        callback();\n    }\n    finally {\n        setInjectorProfilerContext(prevInjectContext);\n    }\n}\n\nfunction isEnvironmentProviders(value) {\n    return value && !!value.ɵproviders;\n}\n\nconst NG_COMP_DEF = getClosureSafeProperty({ ɵcmp: getClosureSafeProperty });\nconst NG_DIR_DEF = getClosureSafeProperty({ ɵdir: getClosureSafeProperty });\nconst NG_PIPE_DEF = getClosureSafeProperty({ ɵpipe: getClosureSafeProperty });\nconst NG_MOD_DEF = getClosureSafeProperty({ ɵmod: getClosureSafeProperty });\nconst NG_FACTORY_DEF = getClosureSafeProperty({ ɵfac: getClosureSafeProperty });\n/**\n * If a directive is diPublic, bloomAdd sets a property on the type with this constant as\n * the key and the directive's unique ID as the value. This allows us to map directives to their\n * bloom filter bit for DI.\n */\n// TODO(misko): This is wrong. The NG_ELEMENT_ID should never be minified.\nconst NG_ELEMENT_ID = getClosureSafeProperty({\n    __NG_ELEMENT_ID__: getClosureSafeProperty,\n});\n/**\n * The `NG_ENV_ID` field on a DI token indicates special processing in the `EnvironmentInjector`:\n * getting such tokens from the `EnvironmentInjector` will bypass the standard DI resolution\n * strategy and instead will return implementation produced by the `NG_ENV_ID` factory function.\n *\n * This particular retrieval of DI tokens is mostly done to eliminate circular dependencies and\n * improve tree-shaking.\n */\nconst NG_ENV_ID = getClosureSafeProperty({ __NG_ENV_ID__: getClosureSafeProperty });\n\n/**\n * Used for stringify render output in Ivy.\n * Important! This function is very performance-sensitive and we should\n * be extra careful not to introduce megamorphic reads in it.\n * Check `core/test/render3/perf/render_stringify` for benchmarks and alternate implementations.\n */\nfunction renderStringify(value) {\n    if (typeof value === 'string')\n        return value;\n    if (value == null)\n        return '';\n    // Use `String` so that it invokes the `toString` method of the value. Note that this\n    // appears to be faster than calling `value.toString` (see `render_stringify` benchmark).\n    return String(value);\n}\n/**\n * Used to stringify a value so that it can be displayed in an error message.\n *\n * Important! This function contains a megamorphic read and should only be\n * used for error messages.\n */\nfunction stringifyForError(value) {\n    if (typeof value === 'function')\n        return value.name || value.toString();\n    if (typeof value === 'object' && value != null && typeof value.type === 'function') {\n        return value.type.name || value.type.toString();\n    }\n    return renderStringify(value);\n}\n/**\n * Used to stringify a `Type` and including the file path and line number in which it is defined, if\n * possible, for better debugging experience.\n *\n * Important! This function contains a megamorphic read and should only be used for error messages.\n */\nfunction debugStringifyTypeForError(type) {\n    // TODO(pmvald): Do some refactoring so that we can use getComponentDef here without creating\n    // circular deps.\n    let componentDef = type[NG_COMP_DEF] || null;\n    if (componentDef !== null && componentDef.debugInfo) {\n        return stringifyTypeFromDebugInfo(componentDef.debugInfo);\n    }\n    return stringifyForError(type);\n}\n// TODO(pmvald): Do some refactoring so that we can use the type ClassDebugInfo for the param\n// debugInfo here without creating circular deps.\nfunction stringifyTypeFromDebugInfo(debugInfo) {\n    if (!debugInfo.filePath || !debugInfo.lineNumber) {\n        return debugInfo.className;\n    }\n    else {\n        return `${debugInfo.className} (at ${debugInfo.filePath}:${debugInfo.lineNumber})`;\n    }\n}\n\n/** Called when directives inject each other (creating a circular dependency) */\nfunction throwCyclicDependencyError(token, path) {\n    throw new RuntimeError(-200 /* RuntimeErrorCode.CYCLIC_DI_DEPENDENCY */, ngDevMode\n        ? `Circular dependency in DI detected for ${token}${path ? `. Dependency path: ${path.join(' > ')} > ${token}` : ''}`\n        : token);\n}\nfunction throwMixedMultiProviderError() {\n    throw new Error(`Cannot mix multi providers and regular providers`);\n}\nfunction throwInvalidProviderError(ngModuleType, providers, provider) {\n    if (ngModuleType && providers) {\n        const providerDetail = providers.map((v) => (v == provider ? '?' + provider + '?' : '...'));\n        throw new Error(`Invalid provider for the NgModule '${stringify(ngModuleType)}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`);\n    }\n    else if (isEnvironmentProviders(provider)) {\n        if (provider.ɵfromNgModule) {\n            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`);\n        }\n        else {\n            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`);\n        }\n    }\n    else {\n        throw new Error('Invalid provider');\n    }\n}\n/** Throws an error when a token is not found in DI. */\nfunction throwProviderNotFoundError(token, injectorName) {\n    const errorMessage = ngDevMode &&\n        `No provider for ${stringifyForError(token)} found${injectorName ? ` in ${injectorName}` : ''}`;\n    throw new RuntimeError(-201 /* RuntimeErrorCode.PROVIDER_NOT_FOUND */, errorMessage);\n}\n\n/**\n * Current implementation of inject.\n *\n * By default, it is `injectInjectorOnly`, which makes it `Injector`-only aware. It can be changed\n * to `directiveInject`, which brings in the `NodeInjector` system of ivy. It is designed this\n * way for two reasons:\n *  1. `Injector` should not depend on ivy logic.\n *  2. To maintain tree shake-ability we don't want to bring in unnecessary code.\n */\nlet _injectImplementation;\nfunction getInjectImplementation() {\n    return _injectImplementation;\n}\n/**\n * Sets the current inject implementation.\n */\nfunction setInjectImplementation(impl) {\n    const previous = _injectImplementation;\n    _injectImplementation = impl;\n    return previous;\n}\n/**\n * Injects `root` tokens in limp mode.\n *\n * If no injector exists, we can still inject tree-shakable providers which have `providedIn` set to\n * `\"root\"`. This is known as the limp mode injection. In such case the value is stored in the\n * injectable definition.\n */\nfunction injectRootLimpMode(token, notFoundValue, flags) {\n    const injectableDef = getInjectableDef(token);\n    if (injectableDef && injectableDef.providedIn == 'root') {\n        return injectableDef.value === undefined\n            ? (injectableDef.value = injectableDef.factory())\n            : injectableDef.value;\n    }\n    if (flags & 8 /* InternalInjectFlags.Optional */)\n        return null;\n    if (notFoundValue !== undefined)\n        return notFoundValue;\n    throwProviderNotFoundError(token, 'Injector');\n}\n/**\n * Assert that `_injectImplementation` is not `fn`.\n *\n * This is useful, to prevent infinite recursion.\n *\n * @param fn Function which it should not equal to\n */\nfunction assertInjectImplementationNotEqual(fn) {\n    ngDevMode &&\n        assertNotEqual(_injectImplementation, fn, 'Calling ɵɵinject would cause infinite recursion');\n}\n\nconst _THROW_IF_NOT_FOUND = {};\nconst THROW_IF_NOT_FOUND = _THROW_IF_NOT_FOUND;\n/*\n * Name of a property (that we patch onto DI decorator), which is used as an annotation of which\n * InjectFlag this decorator represents. This allows to avoid direct references to the DI decorators\n * in the code, thus making them tree-shakable.\n */\nconst DI_DECORATOR_FLAG = '__NG_DI_FLAG__';\n/**\n * A wrapper around an `Injector` that implements the `PrimitivesInjector` interface.\n *\n * This is used to allow the `inject` function to be used with the new primitives-based DI system.\n */\nclass RetrievingInjector {\n    injector;\n    constructor(injector) {\n        this.injector = injector;\n    }\n    retrieve(token, options) {\n        const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n        try {\n            return this.injector.get(token, \n            // When a dependency is requested with an optional flag, DI returns null as the default value.\n            (flags & 8 /* InternalInjectFlags.Optional */ ? null : THROW_IF_NOT_FOUND), flags);\n        }\n        catch (e) {\n            if (isNotFound(e)) {\n                return e;\n            }\n            throw e;\n        }\n    }\n}\nconst NG_TEMP_TOKEN_PATH = 'ngTempTokenPath';\nconst NG_TOKEN_PATH = 'ngTokenPath';\nconst NEW_LINE = /\\n/gm;\nconst NO_NEW_LINE = 'ɵ';\nconst SOURCE = '__source';\nfunction injectInjectorOnly(token, flags = 0 /* InternalInjectFlags.Default */) {\n    const currentInjector = getCurrentInjector();\n    if (currentInjector === undefined) {\n        throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode &&\n            `The \\`${stringify(token)}\\` token injection failed. \\`inject()\\` function must be called from an injection context such as a constructor, a factory function, a field initializer, or a function used with \\`runInInjectionContext\\`.`);\n    }\n    else if (currentInjector === null) {\n        return injectRootLimpMode(token, undefined, flags);\n    }\n    else {\n        const options = convertToInjectOptions(flags);\n        const value = currentInjector.retrieve(token, options);\n        ngDevMode && emitInjectEvent(token, value, flags);\n        if (isNotFound(value)) {\n            if (options.optional) {\n                return null;\n            }\n            throw value;\n        }\n        return value;\n    }\n}\nfunction ɵɵinject(token, flags = 0 /* InternalInjectFlags.Default */) {\n    return (getInjectImplementation() || injectInjectorOnly)(resolveForwardRef(token), flags);\n}\n/**\n * Throws an error indicating that a factory function could not be generated by the compiler for a\n * particular class.\n *\n * The name of the class is not mentioned here, but will be in the generated factory function name\n * and thus in the stack trace.\n *\n * @codeGenApi\n */\nfunction ɵɵinvalidFactoryDep(index) {\n    throw new RuntimeError(202 /* RuntimeErrorCode.INVALID_FACTORY_DEPENDENCY */, ngDevMode &&\n        `This constructor is not compatible with Angular Dependency Injection because its dependency at index ${index} of the parameter list is invalid.\nThis can happen if the dependency type is a primitive like a string or if an ancestor of this class is missing an Angular decorator.\n\nPlease check that 1) the type for the parameter at index ${index} is correct and 2) the correct Angular decorators are defined for this class and its ancestors.`);\n}\n/**\n * Injects a token from the currently active injector.\n * `inject` is only supported in an [injection context](guide/di/dependency-injection-context). It\n * can be used during:\n * - Construction (via the `constructor`) of a class being instantiated by the DI system, such\n * as an `@Injectable` or `@Component`.\n * - In the initializer for fields of such classes.\n * - In the factory function specified for `useFactory` of a `Provider` or an `@Injectable`.\n * - In the `factory` function specified for an `InjectionToken`.\n * - In a stackframe of a function call in a DI context\n *\n * @param token A token that represents a dependency that should be injected.\n * @param flags Optional flags that control how injection is executed.\n * The flags correspond to injection strategies that can be specified with\n * parameter decorators `@Host`, `@Self`, `@SkipSelf`, and `@Optional`.\n * @returns the injected value if operation is successful, `null` otherwise.\n * @throws if called outside of a supported context.\n *\n * @usageNotes\n * In practice the `inject()` calls are allowed in a constructor, a constructor parameter and a\n * field initializer:\n *\n * ```ts\n * @Injectable({providedIn: 'root'})\n * export class Car {\n *   radio: Radio|undefined;\n *   // OK: field initializer\n *   spareTyre = inject(Tyre);\n *\n *   constructor() {\n *     // OK: constructor body\n *     this.radio = inject(Radio);\n *   }\n * }\n * ```\n *\n * It is also legal to call `inject` from a provider's factory:\n *\n * ```ts\n * providers: [\n *   {provide: Car, useFactory: () => {\n *     // OK: a class factory\n *     const engine = inject(Engine);\n *     return new Car(engine);\n *   }}\n * ]\n * ```\n *\n * Calls to the `inject()` function outside of the class creation context will result in error. Most\n * notably, calls to `inject()` are disallowed after a class instance was created, in methods\n * (including lifecycle hooks):\n *\n * ```ts\n * @Component({ ... })\n * export class CarComponent {\n *   ngOnInit() {\n *     // ERROR: too late, the component instance was already created\n *     const engine = inject(Engine);\n *     engine.start();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction inject(token, options) {\n    // The `as any` here _shouldn't_ be necessary, but without it JSCompiler\n    // throws a disambiguation  error due to the multiple signatures.\n    return ɵɵinject(token, convertToBitFlags(options));\n}\n// Converts object-based DI flags (`InjectOptions`) to bit flags (`InjectFlags`).\nfunction convertToBitFlags(flags) {\n    if (typeof flags === 'undefined' || typeof flags === 'number') {\n        return flags;\n    }\n    // While TypeScript doesn't accept it without a cast, bitwise OR with false-y values in\n    // JavaScript is a no-op. We can use that for a very codesize-efficient conversion from\n    // `InjectOptions` to `InjectFlags`.\n    return (0 /* InternalInjectFlags.Default */ | // comment to force a line break in the formatter\n        (flags.optional && 8 /* InternalInjectFlags.Optional */) |\n        (flags.host && 1 /* InternalInjectFlags.Host */) |\n        (flags.self && 2 /* InternalInjectFlags.Self */) |\n        (flags.skipSelf && 4 /* InternalInjectFlags.SkipSelf */));\n}\n// Converts bitflags to inject options\nfunction convertToInjectOptions(flags) {\n    return {\n        optional: !!(flags & 8 /* InternalInjectFlags.Optional */),\n        host: !!(flags & 1 /* InternalInjectFlags.Host */),\n        self: !!(flags & 2 /* InternalInjectFlags.Self */),\n        skipSelf: !!(flags & 4 /* InternalInjectFlags.SkipSelf */),\n    };\n}\nfunction injectArgs(types) {\n    const args = [];\n    for (let i = 0; i < types.length; i++) {\n        const arg = resolveForwardRef(types[i]);\n        if (Array.isArray(arg)) {\n            if (arg.length === 0) {\n                throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && 'Arguments array must have arguments.');\n            }\n            let type = undefined;\n            let flags = 0 /* InternalInjectFlags.Default */;\n            for (let j = 0; j < arg.length; j++) {\n                const meta = arg[j];\n                const flag = getInjectFlag(meta);\n                if (typeof flag === 'number') {\n                    // Special case when we handle @Inject decorator.\n                    if (flag === -1 /* DecoratorFlags.Inject */) {\n                        type = meta.token;\n                    }\n                    else {\n                        flags |= flag;\n                    }\n                }\n                else {\n                    type = meta;\n                }\n            }\n            args.push(ɵɵinject(type, flags));\n        }\n        else {\n            args.push(ɵɵinject(arg));\n        }\n    }\n    return args;\n}\n/**\n * Attaches a given InjectFlag to a given decorator using monkey-patching.\n * Since DI decorators can be used in providers `deps` array (when provider is configured using\n * `useFactory`) without initialization (e.g. `Host`) and as an instance (e.g. `new Host()`), we\n * attach the flag to make it available both as a static property and as a field on decorator\n * instance.\n *\n * @param decorator Provided DI decorator.\n * @param flag InjectFlag that should be applied.\n */\nfunction attachInjectFlag(decorator, flag) {\n    decorator[DI_DECORATOR_FLAG] = flag;\n    decorator.prototype[DI_DECORATOR_FLAG] = flag;\n    return decorator;\n}\n/**\n * Reads monkey-patched property that contains InjectFlag attached to a decorator.\n *\n * @param token Token that may contain monkey-patched DI flags property.\n */\nfunction getInjectFlag(token) {\n    return token[DI_DECORATOR_FLAG];\n}\nfunction catchInjectorError(e, token, injectorErrorName, source) {\n    const tokenPath = e[NG_TEMP_TOKEN_PATH];\n    if (token[SOURCE]) {\n        tokenPath.unshift(token[SOURCE]);\n    }\n    e.message = formatError('\\n' + e.message, tokenPath, injectorErrorName, source);\n    e[NG_TOKEN_PATH] = tokenPath;\n    e[NG_TEMP_TOKEN_PATH] = null;\n    throw e;\n}\nfunction formatError(text, obj, injectorErrorName, source = null) {\n    text = text && text.charAt(0) === '\\n' && text.charAt(1) == NO_NEW_LINE ? text.slice(2) : text;\n    let context = stringify(obj);\n    if (Array.isArray(obj)) {\n        context = obj.map(stringify).join(' -> ');\n    }\n    else if (typeof obj === 'object') {\n        let parts = [];\n        for (let key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                let value = obj[key];\n                parts.push(key + ':' + (typeof value === 'string' ? JSON.stringify(value) : stringify(value)));\n            }\n        }\n        context = `{${parts.join(', ')}}`;\n    }\n    return `${injectorErrorName}${source ? '(' + source + ')' : ''}[${context}]: ${text.replace(NEW_LINE, '\\n  ')}`;\n}\n\nfunction getFactoryDef(type, throwNotFound) {\n    const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);\n    if (!hasFactoryDef && throwNotFound === true && ngDevMode) {\n        throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);\n    }\n    return hasFactoryDef ? type[NG_FACTORY_DEF] : null;\n}\n\n/**\n * Determines if the contents of two arrays is identical\n *\n * @param a first array\n * @param b second array\n * @param identityAccessor Optional function for extracting stable object identity from a value in\n *     the array.\n */\nfunction arrayEquals(a, b, identityAccessor) {\n    if (a.length !== b.length)\n        return false;\n    for (let i = 0; i < a.length; i++) {\n        let valueA = a[i];\n        let valueB = b[i];\n        if (identityAccessor) {\n            valueA = identityAccessor(valueA);\n            valueB = identityAccessor(valueB);\n        }\n        if (valueB !== valueA) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Flattens an array.\n */\nfunction flatten(list) {\n    return list.flat(Number.POSITIVE_INFINITY);\n}\nfunction deepForEach(input, fn) {\n    input.forEach((value) => (Array.isArray(value) ? deepForEach(value, fn) : fn(value)));\n}\nfunction addToArray(arr, index, value) {\n    // perf: array.push is faster than array.splice!\n    if (index >= arr.length) {\n        arr.push(value);\n    }\n    else {\n        arr.splice(index, 0, value);\n    }\n}\nfunction removeFromArray(arr, index) {\n    // perf: array.pop is faster than array.splice!\n    if (index >= arr.length - 1) {\n        return arr.pop();\n    }\n    else {\n        return arr.splice(index, 1)[0];\n    }\n}\nfunction newArray(size, value) {\n    const list = [];\n    for (let i = 0; i < size; i++) {\n        list.push(value);\n    }\n    return list;\n}\n/**\n * Remove item from array (Same as `Array.splice()` but faster.)\n *\n * `Array.splice()` is not as fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * https://jsperf.com/fast-array-splice (About 20x faster)\n *\n * @param array Array to splice\n * @param index Index of element in array to remove.\n * @param count Number of items to remove.\n */\nfunction arraySplice(array, index, count) {\n    const length = array.length - count;\n    while (index < length) {\n        array[index] = array[index + count];\n        index++;\n    }\n    while (count--) {\n        array.pop(); // shrink the array\n    }\n}\n/**\n * Same as `Array.splice2(index, 0, value1, value2)` but faster.\n *\n * `Array.splice()` is not fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * @param array Array to splice.\n * @param index Index in array where the `value` should be added.\n * @param value1 Value to add to array.\n * @param value2 Value to add to array.\n */\nfunction arrayInsert2(array, index, value1, value2) {\n    ngDevMode && assertLessThanOrEqual(index, array.length, \"Can't insert past array end.\");\n    let end = array.length;\n    if (end == index) {\n        // inserting at the end.\n        array.push(value1, value2);\n    }\n    else if (end === 1) {\n        // corner case when we have less items in array than we have items to insert.\n        array.push(value2, array[0]);\n        array[0] = value1;\n    }\n    else {\n        end--;\n        array.push(array[end - 1], array[end]);\n        while (end > index) {\n            const previousEnd = end - 2;\n            array[end] = array[previousEnd];\n            end--;\n        }\n        array[index] = value1;\n        array[index + 1] = value2;\n    }\n}\n/**\n * Set a `value` for a `key`.\n *\n * @param keyValueArray to modify.\n * @param key The key to locate or create.\n * @param value The value to set for a `key`.\n * @returns index (always even) of where the value vas set.\n */\nfunction keyValueArraySet(keyValueArray, key, value) {\n    let index = keyValueArrayIndexOf(keyValueArray, key);\n    if (index >= 0) {\n        // if we found it set it.\n        keyValueArray[index | 1] = value;\n    }\n    else {\n        index = ~index;\n        arrayInsert2(keyValueArray, index, key, value);\n    }\n    return index;\n}\n/**\n * Retrieve a `value` for a `key` (on `undefined` if not found.)\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @return The `value` stored at the `key` location or `undefined if not found.\n */\nfunction keyValueArrayGet(keyValueArray, key) {\n    const index = keyValueArrayIndexOf(keyValueArray, key);\n    if (index >= 0) {\n        // if we found it retrieve it.\n        return keyValueArray[index | 1];\n    }\n    return undefined;\n}\n/**\n * Retrieve a `key` index value in the array or `-1` if not found.\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @returns index of where the key is (or should have been.)\n *   - positive (even) index if key found.\n *   - negative index if key not found. (`~index` (even) to get the index where it should have\n *     been inserted.)\n */\nfunction keyValueArrayIndexOf(keyValueArray, key) {\n    return _arrayIndexOfSorted(keyValueArray, key, 1);\n}\n/**\n * INTERNAL: Get an index of an `value` in a sorted `array` by grouping search by `shift`.\n *\n * NOTE:\n * - This uses binary search algorithm for fast removals.\n *\n * @param array A sorted array to binary search.\n * @param value The value to look for.\n * @param shift grouping shift.\n *   - `0` means look at every location\n *   - `1` means only look at every other (even) location (the odd locations are to be ignored as\n *         they are values.)\n * @returns index of the value.\n *   - positive index if value found.\n *   - negative index if value not found. (`~index` to get the value where it should have been\n * inserted)\n */\nfunction _arrayIndexOfSorted(array, value, shift) {\n    ngDevMode && assertEqual(Array.isArray(array), true, 'Expecting an array');\n    let start = 0;\n    let end = array.length >> shift;\n    while (end !== start) {\n        const middle = start + ((end - start) >> 1); // find the middle.\n        const current = array[middle << shift];\n        if (value === current) {\n            return middle << shift;\n        }\n        else if (current > value) {\n            end = middle;\n        }\n        else {\n            start = middle + 1; // We already searched middle so make it non-inclusive by adding 1\n        }\n    }\n    return ~(end << shift);\n}\n\n/**\n * This file contains reuseable \"empty\" symbols that can be used as default return values\n * in different parts of the rendering code. Because the same symbols are returned, this\n * allows for identity checks against these values to be consistently used by the framework\n * code.\n */\nconst EMPTY_OBJ = {};\nconst EMPTY_ARRAY = [];\n// freezing the values prevents any code from accidentally inserting new values in\nif ((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode()) {\n    // These property accesses can be ignored because ngDevMode will be set to false\n    // when optimizing code and the whole if statement will be dropped.\n    // tslint:disable-next-line:no-toplevel-property-access\n    Object.freeze(EMPTY_OBJ);\n    // tslint:disable-next-line:no-toplevel-property-access\n    Object.freeze(EMPTY_ARRAY);\n}\n\n/**\n * A multi-provider token for initialization functions that will run upon construction of an\n * environment injector.\n *\n * @deprecated from v19.0.0, use provideEnvironmentInitializer instead\n *\n * @see {@link provideEnvironmentInitializer}\n *\n * Note: As opposed to the `APP_INITIALIZER` token, the `ENVIRONMENT_INITIALIZER` functions are not awaited,\n * hence they should not be `async`.\n *\n * @publicApi\n */\nconst ENVIRONMENT_INITIALIZER = new InjectionToken(ngDevMode ? 'ENVIRONMENT_INITIALIZER' : '');\n\n/**\n * An InjectionToken that gets the current `Injector` for `createInjector()`-style injectors.\n *\n * Requesting this token instead of `Injector` allows `StaticInjector` to be tree-shaken from a\n * project.\n *\n * @publicApi\n */\nconst INJECTOR$1 = new InjectionToken(ngDevMode ? 'INJECTOR' : '', \n// Disable tslint because this is const enum which gets inlined not top level prop access.\n// tslint:disable-next-line: no-toplevel-property-access\n-1 /* InjectorMarkers.Injector */);\n\nconst INJECTOR_DEF_TYPES = new InjectionToken(ngDevMode ? 'INJECTOR_DEF_TYPES' : '');\n\nclass NullInjector {\n    get(token, notFoundValue = THROW_IF_NOT_FOUND) {\n        if (notFoundValue === THROW_IF_NOT_FOUND) {\n            const error = new NotFoundError(`NullInjectorError: No provider for ${stringify(token)}!`);\n            throw error;\n        }\n        return notFoundValue;\n    }\n}\n\nfunction getNgModuleDef(type) {\n    return type[NG_MOD_DEF] || null;\n}\nfunction getNgModuleDefOrThrow(type) {\n    const ngModuleDef = getNgModuleDef(type);\n    if (!ngModuleDef) {\n        throw new RuntimeError(915 /* RuntimeErrorCode.MISSING_NG_MODULE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `Type ${stringify(type)} does not have 'ɵmod' property.`);\n    }\n    return ngModuleDef;\n}\n/**\n * The following getter methods retrieve the definition from the type. Currently the retrieval\n * honors inheritance, but in the future we may change the rule to require that definitions are\n * explicit. This would require some sort of migration strategy.\n */\nfunction getComponentDef(type) {\n    return type[NG_COMP_DEF] || null;\n}\nfunction getDirectiveDefOrThrow(type) {\n    const def = getDirectiveDef(type);\n    if (!def) {\n        throw new RuntimeError(916 /* RuntimeErrorCode.MISSING_DIRECTIVE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `Type ${stringify(type)} does not have 'ɵdir' property.`);\n    }\n    return def;\n}\nfunction getDirectiveDef(type) {\n    return type[NG_DIR_DEF] || null;\n}\nfunction getPipeDef(type) {\n    return type[NG_PIPE_DEF] || null;\n}\n/**\n * Checks whether a given Component, Directive or Pipe is marked as standalone.\n * This will return false if passed anything other than a Component, Directive, or Pipe class\n * See [this guide](guide/components/importing) for additional information:\n *\n * @param type A reference to a Component, Directive or Pipe.\n * @publicApi\n */\nfunction isStandalone(type) {\n    const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);\n    return def !== null && def.standalone;\n}\n\n/**\n * Wrap an array of `Provider`s into `EnvironmentProviders`, preventing them from being accidentally\n * referenced in `@Component` in a component injector.\n *\n * @publicApi\n */\nfunction makeEnvironmentProviders(providers) {\n    return {\n        ɵproviders: providers,\n    };\n}\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon construction\n * of an environment injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `ENVIRONMENT_INITIALIZER` token which is now deprecated.\n *\n * @see {@link ENVIRONMENT_INITIALIZER}\n *\n * @usageNotes\n * The following example illustrates how to configure an initialization function using\n * `provideEnvironmentInitializer()`\n * ```ts\n * createEnvironmentInjector(\n *   [\n *     provideEnvironmentInitializer(() => {\n *       console.log('environment initialized');\n *     }),\n *   ],\n *   parentInjector\n * );\n * ```\n *\n * @publicApi\n */\nfunction provideEnvironmentInitializer(initializerFn) {\n    return makeEnvironmentProviders([\n        {\n            provide: ENVIRONMENT_INITIALIZER,\n            multi: true,\n            useValue: initializerFn,\n        },\n    ]);\n}\n/**\n * Collects providers from all NgModules and standalone components, including transitively imported\n * ones.\n *\n * Providers extracted via `importProvidersFrom` are only usable in an application injector or\n * another environment injector (such as a route injector). They should not be used in component\n * providers.\n *\n * More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The results of the `importProvidersFrom` call can be used in the `bootstrapApplication` call:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(NgModuleOne, NgModuleTwo)\n *   ]\n * });\n * ```\n *\n * You can also use the `importProvidersFrom` results in the `providers` field of a route, when a\n * standalone component is used:\n *\n * ```ts\n * export const ROUTES: Route[] = [\n *   {\n *     path: 'foo',\n *     providers: [\n *       importProvidersFrom(NgModuleOne, NgModuleTwo)\n *     ],\n *     component: YourStandaloneComponent\n *   }\n * ];\n * ```\n *\n * @returns Collected providers from the specified list of types.\n * @publicApi\n */\nfunction importProvidersFrom(...sources) {\n    return {\n        ɵproviders: internalImportProvidersFrom(true, sources),\n        ɵfromNgModule: true,\n    };\n}\nfunction internalImportProvidersFrom(checkForStandaloneCmp, ...sources) {\n    const providersOut = [];\n    const dedup = new Set(); // already seen types\n    let injectorTypesWithProviders;\n    const collectProviders = (provider) => {\n        providersOut.push(provider);\n    };\n    deepForEach(sources, (source) => {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && checkForStandaloneCmp) {\n            const cmpDef = getComponentDef(source);\n            if (cmpDef?.standalone) {\n                throw new RuntimeError(800 /* RuntimeErrorCode.IMPORT_PROVIDERS_FROM_STANDALONE */, `Importing providers supports NgModule or ModuleWithProviders but got a standalone component \"${stringifyForError(source)}\"`);\n            }\n        }\n        // Narrow `source` to access the internal type analogue for `ModuleWithProviders`.\n        const internalSource = source;\n        if (walkProviderTree(internalSource, collectProviders, [], dedup)) {\n            injectorTypesWithProviders ||= [];\n            injectorTypesWithProviders.push(internalSource);\n        }\n    });\n    // Collect all providers from `ModuleWithProviders` types.\n    if (injectorTypesWithProviders !== undefined) {\n        processInjectorTypesWithProviders(injectorTypesWithProviders, collectProviders);\n    }\n    return providersOut;\n}\n/**\n * Collects all providers from the list of `ModuleWithProviders` and appends them to the provided\n * array.\n */\nfunction processInjectorTypesWithProviders(typesWithProviders, visitor) {\n    for (let i = 0; i < typesWithProviders.length; i++) {\n        const { ngModule, providers } = typesWithProviders[i];\n        deepForEachProvider(providers, (provider) => {\n            ngDevMode && validateProvider(provider, providers || EMPTY_ARRAY, ngModule);\n            visitor(provider, ngModule);\n        });\n    }\n}\n/**\n * The logic visits an `InjectorType`, an `InjectorTypeWithProviders`, or a standalone\n * `ComponentType`, and all of its transitive providers and collects providers.\n *\n * If an `InjectorTypeWithProviders` that declares providers besides the type is specified,\n * the function will return \"true\" to indicate that the providers of the type definition need\n * to be processed. This allows us to process providers of injector types after all imports of\n * an injector definition are processed. (following View Engine semantics: see FW-1349)\n */\nfunction walkProviderTree(container, visitor, parents, dedup) {\n    container = resolveForwardRef(container);\n    if (!container)\n        return false;\n    // The actual type which had the definition. Usually `container`, but may be an unwrapped type\n    // from `InjectorTypeWithProviders`.\n    let defType = null;\n    let injDef = getInjectorDef(container);\n    const cmpDef = !injDef && getComponentDef(container);\n    if (!injDef && !cmpDef) {\n        // `container` is not an injector type or a component type. It might be:\n        //  * An `InjectorTypeWithProviders` that wraps an injector type.\n        //  * A standalone directive or pipe that got pulled in from a standalone component's\n        //    dependencies.\n        // Try to unwrap it as an `InjectorTypeWithProviders` first.\n        const ngModule = container\n            .ngModule;\n        injDef = getInjectorDef(ngModule);\n        if (injDef) {\n            defType = ngModule;\n        }\n        else {\n            // Not a component or injector type, so ignore it.\n            return false;\n        }\n    }\n    else if (cmpDef && !cmpDef.standalone) {\n        return false;\n    }\n    else {\n        defType = container;\n    }\n    // Check for circular dependencies.\n    if (ngDevMode && parents.indexOf(defType) !== -1) {\n        const defName = stringify(defType);\n        const path = parents.map(stringify);\n        throwCyclicDependencyError(defName, path);\n    }\n    // Check for multiple imports of the same module\n    const isDuplicate = dedup.has(defType);\n    if (cmpDef) {\n        if (isDuplicate) {\n            // This component definition has already been processed.\n            return false;\n        }\n        dedup.add(defType);\n        if (cmpDef.dependencies) {\n            const deps = typeof cmpDef.dependencies === 'function' ? cmpDef.dependencies() : cmpDef.dependencies;\n            for (const dep of deps) {\n                walkProviderTree(dep, visitor, parents, dedup);\n            }\n        }\n    }\n    else if (injDef) {\n        // First, include providers from any imports.\n        if (injDef.imports != null && !isDuplicate) {\n            // Before processing defType's imports, add it to the set of parents. This way, if it ends\n            // up deeply importing itself, this can be detected.\n            ngDevMode && parents.push(defType);\n            // Add it to the set of dedups. This way we can detect multiple imports of the same module\n            dedup.add(defType);\n            let importTypesWithProviders;\n            try {\n                deepForEach(injDef.imports, (imported) => {\n                    if (walkProviderTree(imported, visitor, parents, dedup)) {\n                        importTypesWithProviders ||= [];\n                        // If the processed import is an injector type with providers, we store it in the\n                        // list of import types with providers, so that we can process those afterwards.\n                        importTypesWithProviders.push(imported);\n                    }\n                });\n            }\n            finally {\n                // Remove it from the parents set when finished.\n                ngDevMode && parents.pop();\n            }\n            // Imports which are declared with providers (TypeWithProviders) need to be processed\n            // after all imported modules are processed. This is similar to how View Engine\n            // processes/merges module imports in the metadata resolver. See: FW-1349.\n            if (importTypesWithProviders !== undefined) {\n                processInjectorTypesWithProviders(importTypesWithProviders, visitor);\n            }\n        }\n        if (!isDuplicate) {\n            // Track the InjectorType and add a provider for it.\n            // It's important that this is done after the def's imports.\n            const factory = getFactoryDef(defType) || (() => new defType());\n            // Append extra providers to make more info available for consumers (to retrieve an injector\n            // type), as well as internally (to calculate an injection scope correctly and eagerly\n            // instantiate a `defType` when an injector is created).\n            // Provider to create `defType` using its factory.\n            visitor({ provide: defType, useFactory: factory, deps: EMPTY_ARRAY }, defType);\n            // Make this `defType` available to an internal logic that calculates injector scope.\n            visitor({ provide: INJECTOR_DEF_TYPES, useValue: defType, multi: true }, defType);\n            // Provider to eagerly instantiate `defType` via `INJECTOR_INITIALIZER`.\n            visitor({ provide: ENVIRONMENT_INITIALIZER, useValue: () => ɵɵinject(defType), multi: true }, defType);\n        }\n        // Next, include providers listed on the definition itself.\n        const defProviders = injDef.providers;\n        if (defProviders != null && !isDuplicate) {\n            const injectorType = container;\n            deepForEachProvider(defProviders, (provider) => {\n                ngDevMode && validateProvider(provider, defProviders, injectorType);\n                visitor(provider, injectorType);\n            });\n        }\n    }\n    else {\n        // Should not happen, but just in case.\n        return false;\n    }\n    return (defType !== container && container.providers !== undefined);\n}\nfunction validateProvider(provider, providers, containerType) {\n    if (isTypeProvider(provider) ||\n        isValueProvider(provider) ||\n        isFactoryProvider(provider) ||\n        isExistingProvider(provider)) {\n        return;\n    }\n    // Here we expect the provider to be a `useClass` provider (by elimination).\n    const classRef = resolveForwardRef(provider && (provider.useClass || provider.provide));\n    if (!classRef) {\n        throwInvalidProviderError(containerType, providers, provider);\n    }\n}\nfunction deepForEachProvider(providers, fn) {\n    for (let provider of providers) {\n        if (isEnvironmentProviders(provider)) {\n            provider = provider.ɵproviders;\n        }\n        if (Array.isArray(provider)) {\n            deepForEachProvider(provider, fn);\n        }\n        else {\n            fn(provider);\n        }\n    }\n}\nconst USE_VALUE = getClosureSafeProperty({\n    provide: String,\n    useValue: getClosureSafeProperty,\n});\nfunction isValueProvider(value) {\n    return value !== null && typeof value == 'object' && USE_VALUE in value;\n}\nfunction isExistingProvider(value) {\n    return !!(value && value.useExisting);\n}\nfunction isFactoryProvider(value) {\n    return !!(value && value.useFactory);\n}\nfunction isTypeProvider(value) {\n    return typeof value === 'function';\n}\nfunction isClassProvider(value) {\n    return !!value.useClass;\n}\n\n/**\n * An internal token whose presence in an injector indicates that the injector should treat itself\n * as a root scoped injector when processing requests for unknown tokens which may indicate\n * they are provided in the root scope.\n */\nconst INJECTOR_SCOPE = new InjectionToken(ngDevMode ? 'Set Injector scope.' : '');\n\n/**\n * Marker which indicates that a value has not yet been created from the factory function.\n */\nconst NOT_YET = {};\n/**\n * Marker which indicates that the factory function for a token is in the process of being called.\n *\n * If the injector is asked to inject a token with its value set to CIRCULAR, that indicates\n * injection of a dependency has recursively attempted to inject the original token, and there is\n * a circular dependency among the providers.\n */\nconst CIRCULAR = {};\n/**\n * A lazily initialized NullInjector.\n */\nlet NULL_INJECTOR = undefined;\nfunction getNullInjector() {\n    if (NULL_INJECTOR === undefined) {\n        NULL_INJECTOR = new NullInjector();\n    }\n    return NULL_INJECTOR;\n}\n/**\n * An `Injector` that's part of the environment injector hierarchy, which exists outside of the\n * component tree.\n *\n * @publicApi\n */\nclass EnvironmentInjector {\n}\nclass R3Injector extends EnvironmentInjector {\n    parent;\n    source;\n    scopes;\n    /**\n     * Map of tokens to records which contain the instances of those tokens.\n     * - `null` value implies that we don't have the record. Used by tree-shakable injectors\n     * to prevent further searches.\n     */\n    records = new Map();\n    /**\n     * Set of values instantiated by this injector which contain `ngOnDestroy` lifecycle hooks.\n     */\n    _ngOnDestroyHooks = new Set();\n    _onDestroyHooks = [];\n    /**\n     * Flag indicating that this injector was previously destroyed.\n     */\n    get destroyed() {\n        return this._destroyed;\n    }\n    _destroyed = false;\n    injectorDefTypes;\n    constructor(providers, parent, source, scopes) {\n        super();\n        this.parent = parent;\n        this.source = source;\n        this.scopes = scopes;\n        // Start off by creating Records for every provider.\n        forEachSingleProvider(providers, (provider) => this.processProvider(provider));\n        // Make sure the INJECTOR token provides this injector.\n        this.records.set(INJECTOR$1, makeRecord(undefined, this));\n        // And `EnvironmentInjector` if the current injector is supposed to be env-scoped.\n        if (scopes.has('environment')) {\n            this.records.set(EnvironmentInjector, makeRecord(undefined, this));\n        }\n        // Detect whether this injector has the APP_ROOT_SCOPE token and thus should provide\n        // any injectable scoped to APP_ROOT_SCOPE.\n        const record = this.records.get(INJECTOR_SCOPE);\n        if (record != null && typeof record.value === 'string') {\n            this.scopes.add(record.value);\n        }\n        this.injectorDefTypes = new Set(this.get(INJECTOR_DEF_TYPES, EMPTY_ARRAY, { self: true }));\n    }\n    retrieve(token, options) {\n        const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n        try {\n            return this.get(token, \n            // When a dependency is requested with an optional flag, DI returns null as the default value.\n            THROW_IF_NOT_FOUND, flags);\n        }\n        catch (e) {\n            if (isNotFound$1(e)) {\n                return e;\n            }\n            throw e;\n        }\n    }\n    /**\n     * Destroy the injector and release references to every instance or provider associated with it.\n     *\n     * Also calls the `OnDestroy` lifecycle hooks of every instance that was created for which a\n     * hook was found.\n     */\n    destroy() {\n        assertNotDestroyed(this);\n        // Set destroyed = true first, in case lifecycle hooks re-enter destroy().\n        this._destroyed = true;\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            // Call all the lifecycle hooks.\n            for (const service of this._ngOnDestroyHooks) {\n                service.ngOnDestroy();\n            }\n            const onDestroyHooks = this._onDestroyHooks;\n            // Reset the _onDestroyHooks array before iterating over it to prevent hooks that unregister\n            // themselves from mutating the array during iteration.\n            this._onDestroyHooks = [];\n            for (const hook of onDestroyHooks) {\n                hook();\n            }\n        }\n        finally {\n            // Release all references.\n            this.records.clear();\n            this._ngOnDestroyHooks.clear();\n            this.injectorDefTypes.clear();\n            setActiveConsumer(prevConsumer);\n        }\n    }\n    onDestroy(callback) {\n        assertNotDestroyed(this);\n        this._onDestroyHooks.push(callback);\n        return () => this.removeOnDestroy(callback);\n    }\n    runInContext(fn) {\n        assertNotDestroyed(this);\n        const previousInjector = setCurrentInjector(this);\n        const previousInjectImplementation = setInjectImplementation(undefined);\n        let prevInjectContext;\n        if (ngDevMode) {\n            prevInjectContext = setInjectorProfilerContext({ injector: this, token: null });\n        }\n        try {\n            return fn();\n        }\n        finally {\n            setCurrentInjector(previousInjector);\n            setInjectImplementation(previousInjectImplementation);\n            ngDevMode && setInjectorProfilerContext(prevInjectContext);\n        }\n    }\n    get(token, notFoundValue = THROW_IF_NOT_FOUND, options) {\n        assertNotDestroyed(this);\n        if (token.hasOwnProperty(NG_ENV_ID)) {\n            return token[NG_ENV_ID](this);\n        }\n        const flags = convertToBitFlags(options);\n        // Set the injection context.\n        let prevInjectContext;\n        if (ngDevMode) {\n            prevInjectContext = setInjectorProfilerContext({ injector: this, token: token });\n        }\n        const previousInjector = setCurrentInjector(this);\n        const previousInjectImplementation = setInjectImplementation(undefined);\n        try {\n            // Check for the SkipSelf flag.\n            if (!(flags & 4 /* InternalInjectFlags.SkipSelf */)) {\n                // SkipSelf isn't set, check if the record belongs to this injector.\n                let record = this.records.get(token);\n                if (record === undefined) {\n                    // No record, but maybe the token is scoped to this injector. Look for an injectable\n                    // def with a scope matching this injector.\n                    const def = couldBeInjectableType(token) && getInjectableDef(token);\n                    if (def && this.injectableDefInScope(def)) {\n                        // Found an injectable def and it's scoped to this injector. Pretend as if it was here\n                        // all along.\n                        if (ngDevMode) {\n                            runInInjectorProfilerContext(this, token, () => {\n                                emitProviderConfiguredEvent(token);\n                            });\n                        }\n                        record = makeRecord(injectableDefOrInjectorDefFactory(token), NOT_YET);\n                    }\n                    else {\n                        record = null;\n                    }\n                    this.records.set(token, record);\n                }\n                // If a record was found, get the instance for it and return it.\n                if (record != null /* NOT null || undefined */) {\n                    return this.hydrate(token, record);\n                }\n            }\n            // Select the next injector based on the Self flag - if self is set, the next injector is\n            // the NullInjector, otherwise it's the parent.\n            const nextInjector = !(flags & 2 /* InternalInjectFlags.Self */) ? this.parent : getNullInjector();\n            // Set the notFoundValue based on the Optional flag - if optional is set and notFoundValue\n            // is undefined, the value is null, otherwise it's the notFoundValue.\n            notFoundValue =\n                flags & 8 /* InternalInjectFlags.Optional */ && notFoundValue === THROW_IF_NOT_FOUND\n                    ? null\n                    : notFoundValue;\n            return nextInjector.get(token, notFoundValue);\n        }\n        catch (e) {\n            if (isNotFound$1(e)) {\n                // @ts-ignore\n                const path = (e[NG_TEMP_TOKEN_PATH] = e[NG_TEMP_TOKEN_PATH] || []);\n                path.unshift(stringify(token));\n                if (previousInjector) {\n                    // We still have a parent injector, keep throwing\n                    throw e;\n                }\n                else {\n                    // Format & throw the final error message when we don't have any previous injector\n                    return catchInjectorError(e, token, 'R3InjectorError', this.source);\n                }\n            }\n            else {\n                throw e;\n            }\n        }\n        finally {\n            // Lastly, restore the previous injection context.\n            setInjectImplementation(previousInjectImplementation);\n            setCurrentInjector(previousInjector);\n            ngDevMode && setInjectorProfilerContext(prevInjectContext);\n        }\n    }\n    /** @internal */\n    resolveInjectorInitializers() {\n        const prevConsumer = setActiveConsumer(null);\n        const previousInjector = setCurrentInjector(this);\n        const previousInjectImplementation = setInjectImplementation(undefined);\n        let prevInjectContext;\n        if (ngDevMode) {\n            prevInjectContext = setInjectorProfilerContext({ injector: this, token: null });\n        }\n        try {\n            const initializers = this.get(ENVIRONMENT_INITIALIZER, EMPTY_ARRAY, { self: true });\n            if (ngDevMode && !Array.isArray(initializers)) {\n                throw new RuntimeError(-209 /* RuntimeErrorCode.INVALID_MULTI_PROVIDER */, 'Unexpected type of the `ENVIRONMENT_INITIALIZER` token value ' +\n                    `(expected an array, but got ${typeof initializers}). ` +\n                    'Please check that the `ENVIRONMENT_INITIALIZER` token is configured as a ' +\n                    '`multi: true` provider.');\n            }\n            for (const initializer of initializers) {\n                initializer();\n            }\n        }\n        finally {\n            setCurrentInjector(previousInjector);\n            setInjectImplementation(previousInjectImplementation);\n            ngDevMode && setInjectorProfilerContext(prevInjectContext);\n            setActiveConsumer(prevConsumer);\n        }\n    }\n    toString() {\n        const tokens = [];\n        const records = this.records;\n        for (const token of records.keys()) {\n            tokens.push(stringify(token));\n        }\n        return `R3Injector[${tokens.join(', ')}]`;\n    }\n    /**\n     * Process a `SingleProvider` and add it.\n     */\n    processProvider(provider) {\n        // Determine the token from the provider. Either it's its own token, or has a {provide: ...}\n        // property.\n        provider = resolveForwardRef(provider);\n        let token = isTypeProvider(provider)\n            ? provider\n            : resolveForwardRef(provider && provider.provide);\n        // Construct a `Record` for the provider.\n        const record = providerToRecord(provider);\n        if (ngDevMode) {\n            runInInjectorProfilerContext(this, token, () => {\n                // Emit InjectorProfilerEventType.Create if provider is a value provider because\n                // these are the only providers that do not go through the value hydration logic\n                // where this event would normally be emitted from.\n                if (isValueProvider(provider)) {\n                    emitInjectorToCreateInstanceEvent(token);\n                    emitInstanceCreatedByInjectorEvent(provider.useValue);\n                }\n                emitProviderConfiguredEvent(provider);\n            });\n        }\n        if (!isTypeProvider(provider) && provider.multi === true) {\n            // If the provider indicates that it's a multi-provider, process it specially.\n            // First check whether it's been defined already.\n            let multiRecord = this.records.get(token);\n            if (multiRecord) {\n                // It has. Throw a nice error if\n                if (ngDevMode && multiRecord.multi === undefined) {\n                    throwMixedMultiProviderError();\n                }\n            }\n            else {\n                multiRecord = makeRecord(undefined, NOT_YET, true);\n                multiRecord.factory = () => injectArgs(multiRecord.multi);\n                this.records.set(token, multiRecord);\n            }\n            token = provider;\n            multiRecord.multi.push(provider);\n        }\n        else {\n            if (ngDevMode) {\n                const existing = this.records.get(token);\n                if (existing && existing.multi !== undefined) {\n                    throwMixedMultiProviderError();\n                }\n            }\n        }\n        this.records.set(token, record);\n    }\n    hydrate(token, record) {\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            if (record.value === CIRCULAR) {\n                throwCyclicDependencyError(stringify(token));\n            }\n            else if (record.value === NOT_YET) {\n                record.value = CIRCULAR;\n                if (ngDevMode) {\n                    runInInjectorProfilerContext(this, token, () => {\n                        emitInjectorToCreateInstanceEvent(token);\n                        record.value = record.factory();\n                        emitInstanceCreatedByInjectorEvent(record.value);\n                    });\n                }\n                else {\n                    record.value = record.factory();\n                }\n            }\n            if (typeof record.value === 'object' && record.value && hasOnDestroy(record.value)) {\n                this._ngOnDestroyHooks.add(record.value);\n            }\n            return record.value;\n        }\n        finally {\n            setActiveConsumer(prevConsumer);\n        }\n    }\n    injectableDefInScope(def) {\n        if (!def.providedIn) {\n            return false;\n        }\n        const providedIn = resolveForwardRef(def.providedIn);\n        if (typeof providedIn === 'string') {\n            return providedIn === 'any' || this.scopes.has(providedIn);\n        }\n        else {\n            return this.injectorDefTypes.has(providedIn);\n        }\n    }\n    removeOnDestroy(callback) {\n        const destroyCBIdx = this._onDestroyHooks.indexOf(callback);\n        if (destroyCBIdx !== -1) {\n            this._onDestroyHooks.splice(destroyCBIdx, 1);\n        }\n    }\n}\nfunction injectableDefOrInjectorDefFactory(token) {\n    // Most tokens will have an injectable def directly on them, which specifies a factory directly.\n    const injectableDef = getInjectableDef(token);\n    const factory = injectableDef !== null ? injectableDef.factory : getFactoryDef(token);\n    if (factory !== null) {\n        return factory;\n    }\n    // InjectionTokens should have an injectable def (ɵprov) and thus should be handled above.\n    // If it's missing that, it's an error.\n    if (token instanceof InjectionToken) {\n        throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `Token ${stringify(token)} is missing a ɵprov definition.`);\n    }\n    // Undecorated types can sometimes be created if they have no constructor arguments.\n    if (token instanceof Function) {\n        return getUndecoratedInjectableFactory(token);\n    }\n    // There was no way to resolve a factory for this token.\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && 'unreachable');\n}\nfunction getUndecoratedInjectableFactory(token) {\n    // If the token has parameters then it has dependencies that we cannot resolve implicitly.\n    const paramLength = token.length;\n    if (paramLength > 0) {\n        throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode &&\n            `Can't resolve all parameters for ${stringify(token)}: (${newArray(paramLength, '?').join(', ')}).`);\n    }\n    // The constructor function appears to have no parameters.\n    // This might be because it inherits from a super-class. In which case, use an injectable\n    // def from an ancestor if there is one.\n    // Otherwise this really is a simple class with no dependencies, so return a factory that\n    // just instantiates the zero-arg constructor.\n    const inheritedInjectableDef = getInheritedInjectableDef(token);\n    if (inheritedInjectableDef !== null) {\n        return () => inheritedInjectableDef.factory(token);\n    }\n    else {\n        return () => new token();\n    }\n}\nfunction providerToRecord(provider) {\n    if (isValueProvider(provider)) {\n        return makeRecord(undefined, provider.useValue);\n    }\n    else {\n        const factory = providerToFactory(provider);\n        return makeRecord(factory, NOT_YET);\n    }\n}\n/**\n * Converts a `SingleProvider` into a factory function.\n *\n * @param provider provider to convert to factory\n */\nfunction providerToFactory(provider, ngModuleType, providers) {\n    let factory = undefined;\n    if (ngDevMode && isEnvironmentProviders(provider)) {\n        throwInvalidProviderError(undefined, providers, provider);\n    }\n    if (isTypeProvider(provider)) {\n        const unwrappedProvider = resolveForwardRef(provider);\n        return getFactoryDef(unwrappedProvider) || injectableDefOrInjectorDefFactory(unwrappedProvider);\n    }\n    else {\n        if (isValueProvider(provider)) {\n            factory = () => resolveForwardRef(provider.useValue);\n        }\n        else if (isFactoryProvider(provider)) {\n            factory = () => provider.useFactory(...injectArgs(provider.deps || []));\n        }\n        else if (isExistingProvider(provider)) {\n            factory = () => ɵɵinject(resolveForwardRef(provider.useExisting));\n        }\n        else {\n            const classRef = resolveForwardRef(provider &&\n                (provider.useClass || provider.provide));\n            if (ngDevMode && !classRef) {\n                throwInvalidProviderError(ngModuleType, providers, provider);\n            }\n            if (hasDeps(provider)) {\n                factory = () => new classRef(...injectArgs(provider.deps));\n            }\n            else {\n                return getFactoryDef(classRef) || injectableDefOrInjectorDefFactory(classRef);\n            }\n        }\n    }\n    return factory;\n}\nfunction assertNotDestroyed(injector) {\n    if (injector.destroyed) {\n        throw new RuntimeError(205 /* RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED */, ngDevMode && 'Injector has already been destroyed.');\n    }\n}\nfunction makeRecord(factory, value, multi = false) {\n    return {\n        factory: factory,\n        value: value,\n        multi: multi ? [] : undefined,\n    };\n}\nfunction hasDeps(value) {\n    return !!value.deps;\n}\nfunction hasOnDestroy(value) {\n    return (value !== null &&\n        typeof value === 'object' &&\n        typeof value.ngOnDestroy === 'function');\n}\nfunction couldBeInjectableType(value) {\n    return (typeof value === 'function' ||\n        (typeof value === 'object' && value.ngMetadataName === 'InjectionToken'));\n}\nfunction forEachSingleProvider(providers, fn) {\n    for (const provider of providers) {\n        if (Array.isArray(provider)) {\n            forEachSingleProvider(provider, fn);\n        }\n        else if (provider && isEnvironmentProviders(provider)) {\n            forEachSingleProvider(provider.ɵproviders, fn);\n        }\n        else {\n            fn(provider);\n        }\n    }\n}\n\n/**\n * Runs the given function in the [context](guide/di/dependency-injection-context) of the given\n * `Injector`.\n *\n * Within the function's stack frame, [`inject`](api/core/inject) can be used to inject dependencies\n * from the given `Injector`. Note that `inject` is only usable synchronously, and cannot be used in\n * any asynchronous callbacks or after any `await` points.\n *\n * @param injector the injector which will satisfy calls to [`inject`](api/core/inject) while `fn`\n *     is executing\n * @param fn the closure to be run in the context of `injector`\n * @returns the return value of the function, if any\n * @publicApi\n */\nfunction runInInjectionContext(injector, fn) {\n    let internalInjector;\n    if (injector instanceof R3Injector) {\n        assertNotDestroyed(injector);\n        internalInjector = injector;\n    }\n    else {\n        internalInjector = new RetrievingInjector(injector);\n    }\n    let prevInjectorProfilerContext;\n    if (ngDevMode) {\n        prevInjectorProfilerContext = setInjectorProfilerContext({ injector, token: null });\n    }\n    const prevInjector = setCurrentInjector(internalInjector);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    try {\n        return fn();\n    }\n    finally {\n        setCurrentInjector(prevInjector);\n        ngDevMode && setInjectorProfilerContext(prevInjectorProfilerContext);\n        setInjectImplementation(previousInjectImplementation);\n    }\n}\n/**\n * Whether the current stack frame is inside an injection context.\n */\nfunction isInInjectionContext() {\n    return getInjectImplementation() !== undefined || getCurrentInjector() != null;\n}\n/**\n * Asserts that the current stack frame is within an [injection\n * context](guide/di/dependency-injection-context) and has access to `inject`.\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertInInjectionContext(debugFn) {\n    // Taking a `Function` instead of a string name here prevents the unminified name of the function\n    // from being retained in the bundle regardless of minification.\n    if (!isInInjectionContext()) {\n        throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode &&\n            debugFn.name +\n                '() can only be used within an injection context such as a constructor, a factory function, a field initializer, or a function used with `runInInjectionContext`');\n    }\n}\n\n// Below are constants for LView indices to help us look up LView members\n// without having to remember the specific indices.\n// Uglify will inline these when minifying so there shouldn't be a cost.\nconst HOST = 0;\nconst TVIEW = 1;\n// Shared with LContainer\nconst FLAGS = 2;\nconst PARENT = 3;\nconst NEXT = 4;\nconst T_HOST = 5;\n// End shared with LContainer\nconst HYDRATION = 6;\nconst CLEANUP = 7;\nconst CONTEXT = 8;\nconst INJECTOR = 9;\nconst ENVIRONMENT = 10;\nconst RENDERER = 11;\nconst CHILD_HEAD = 12;\nconst CHILD_TAIL = 13;\n// FIXME(misko): Investigate if the three declarations aren't all same thing.\nconst DECLARATION_VIEW = 14;\nconst DECLARATION_COMPONENT_VIEW = 15;\nconst DECLARATION_LCONTAINER = 16;\nconst PREORDER_HOOK_FLAGS = 17;\nconst QUERIES = 18;\nconst ID = 19;\nconst EMBEDDED_VIEW_INJECTOR = 20;\nconst ON_DESTROY_HOOKS = 21;\nconst EFFECTS_TO_SCHEDULE = 22;\nconst EFFECTS = 23;\nconst REACTIVE_TEMPLATE_CONSUMER = 24;\nconst AFTER_RENDER_SEQUENCES_TO_ADD = 25;\n/**\n * Size of LView's header. Necessary to adjust for it when setting slots.\n *\n * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate\n * instruction index into `LView` index. All other indexes should be in the `LView` index space and\n * there should be no need to refer to `HEADER_OFFSET` anywhere else.\n */\nconst HEADER_OFFSET = 26;\n\n/**\n * Special location which allows easy identification of type. If we have an array which was\n * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is\n * `LContainer`.\n */\nconst TYPE = 1;\n/**\n * Below are constants for LContainer indices to help us look up LContainer members\n * without having to remember the specific indices.\n * Uglify will inline these when minifying so there shouldn't be a cost.\n */\n// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5\n// As we already have these constants in LView, we don't need to re-create them.\nconst DEHYDRATED_VIEWS = 6;\nconst NATIVE = 7;\nconst VIEW_REFS = 8;\nconst MOVED_VIEWS = 9;\n/**\n * Size of LContainer's header. Represents the index after which all views in the\n * container will be inserted. We need to keep a record of current views so we know\n * which views are already in the DOM (and don't need to be re-added) and so we can\n * remove views from the DOM when they are no longer required.\n */\nconst CONTAINER_HEADER_OFFSET = 10;\n\n/**\n * True if `value` is `LView`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLView(value) {\n    return Array.isArray(value) && typeof value[TYPE] === 'object';\n}\n/**\n * True if `value` is `LContainer`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLContainer(value) {\n    return Array.isArray(value) && value[TYPE] === true;\n}\nfunction isContentQueryHost(tNode) {\n    return (tNode.flags & 4 /* TNodeFlags.hasContentQuery */) !== 0;\n}\nfunction isComponentHost(tNode) {\n    return tNode.componentOffset > -1;\n}\nfunction isDirectiveHost(tNode) {\n    return (tNode.flags & 1 /* TNodeFlags.isDirectiveHost */) === 1 /* TNodeFlags.isDirectiveHost */;\n}\nfunction isComponentDef(def) {\n    return !!def.template;\n}\nfunction isRootView(target) {\n    // Determines whether a given LView is marked as a root view.\n    return (target[FLAGS] & 512 /* LViewFlags.IsRoot */) !== 0;\n}\nfunction isProjectionTNode(tNode) {\n    return (tNode.type & 16 /* TNodeType.Projection */) === 16 /* TNodeType.Projection */;\n}\nfunction hasI18n(lView) {\n    return (lView[FLAGS] & 32 /* LViewFlags.HasI18n */) === 32 /* LViewFlags.HasI18n */;\n}\nfunction isDestroyed(lView) {\n    // Determines whether a given LView is marked as destroyed.\n    return (lView[FLAGS] & 256 /* LViewFlags.Destroyed */) === 256 /* LViewFlags.Destroyed */;\n}\n\n// [Assert functions do not constraint type when they are guarded by a truthy\n// expression.](https://github.com/microsoft/TypeScript/issues/37295)\nfunction assertTNodeForLView(tNode, lView) {\n    assertTNodeForTView(tNode, lView[TVIEW]);\n}\nfunction assertTNodeForTView(tNode, tView) {\n    assertTNode(tNode);\n    const tData = tView.data;\n    for (let i = HEADER_OFFSET; i < tData.length; i++) {\n        if (tData[i] === tNode) {\n            return;\n        }\n    }\n    throwError('This TNode does not belong to this TView.');\n}\nfunction assertTNode(tNode) {\n    assertDefined(tNode, 'TNode must be defined');\n    if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {\n        throwError('Not of type TNode, got: ' + tNode);\n    }\n}\nfunction assertTIcu(tIcu) {\n    assertDefined(tIcu, 'Expected TIcu to be defined');\n    if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {\n        throwError('Object is not of TIcu type.');\n    }\n}\nfunction assertComponentType(actual, msg = \"Type passed in is not ComponentType, it does not have 'ɵcmp' property.\") {\n    if (!getComponentDef(actual)) {\n        throwError(msg);\n    }\n}\nfunction assertNgModuleType(actual, msg = \"Type passed in is not NgModuleType, it does not have 'ɵmod' property.\") {\n    if (!getNgModuleDef(actual)) {\n        throwError(msg);\n    }\n}\nfunction assertHasParent(tNode) {\n    assertDefined(tNode, 'currentTNode should exist!');\n    assertDefined(tNode.parent, 'currentTNode should have a parent');\n}\nfunction assertLContainer(value) {\n    assertDefined(value, 'LContainer must be defined');\n    assertEqual(isLContainer(value), true, 'Expecting LContainer');\n}\nfunction assertLViewOrUndefined(value) {\n    value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');\n}\nfunction assertLView(value) {\n    assertDefined(value, 'LView must be defined');\n    assertEqual(isLView(value), true, 'Expecting LView');\n}\nfunction assertFirstCreatePass(tView, errMessage) {\n    assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');\n}\nfunction assertFirstUpdatePass(tView, errMessage) {\n    assertEqual(tView.firstUpdatePass, true, 'Should only be called in first update pass.');\n}\n/**\n * This is a basic sanity check that an object is probably a directive def. DirectiveDef is\n * an interface, so we can't do a direct instanceof check.\n */\nfunction assertDirectiveDef(obj) {\n    if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {\n        throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);\n    }\n}\nfunction assertIndexInDeclRange(tView, index) {\n    assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);\n}\nfunction assertIndexInExpandoRange(lView, index) {\n    const tView = lView[1];\n    assertBetween(tView.expandoStartIndex, lView.length, index);\n}\nfunction assertBetween(lower, upper, index) {\n    if (!(lower <= index && index < upper)) {\n        throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);\n    }\n}\nfunction assertProjectionSlots(lView, errMessage) {\n    assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');\n    assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, 'Components with projection nodes (<ng-content>) must have projection slots defined.');\n}\nfunction assertParentView(lView, errMessage) {\n    assertDefined(lView, \"Component views should always have a parent view (component's host view)\");\n}\n/**\n * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a\n * NodeInjector data structure.\n *\n * @param lView `LView` which should be checked.\n * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.\n */\nfunction assertNodeInjector(lView, injectorIndex) {\n    assertIndexInExpandoRange(lView, injectorIndex);\n    assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);\n    assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');\n}\n\nconst SVG_NAMESPACE = 'svg';\nconst MATH_ML_NAMESPACE = 'math';\n\n/**\n * For efficiency reasons we often put several different data types (`RNode`, `LView`, `LContainer`)\n * in same location in `LView`. This is because we don't want to pre-allocate space for it\n * because the storage is sparse. This file contains utilities for dealing with such data types.\n *\n * How do we know what is stored at a given location in `LView`.\n * - `Array.isArray(value) === false` => `RNode` (The normal storage value)\n * - `Array.isArray(value) === true` => then the `value[0]` represents the wrapped value.\n *   - `typeof value[TYPE] === 'object'` => `LView`\n *      - This happens when we have a component at a given location\n *   - `typeof value[TYPE] === true` => `LContainer`\n *      - This happens when we have `LContainer` binding at a given location.\n *\n *\n * NOTE: it is assumed that `Array.isArray` and `typeof` operations are very efficient.\n */\n/**\n * Returns `RNode`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapRNode(value) {\n    while (Array.isArray(value)) {\n        value = value[HOST];\n    }\n    return value;\n}\n/**\n * Returns `LView` or `null` if not found.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapLView(value) {\n    while (Array.isArray(value)) {\n        // This check is same as `isLView()` but we don't call at as we don't want to call\n        // `Array.isArray()` twice and give JITer more work for inlining.\n        if (typeof value[TYPE] === 'object')\n            return value;\n        value = value[HOST];\n    }\n    return null;\n}\n/**\n * Retrieves an element value from the provided `viewData`, by unwrapping\n * from any containers, component views, or style contexts.\n */\nfunction getNativeByIndex(index, lView) {\n    ngDevMode && assertIndexInRange(lView, index);\n    ngDevMode && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Expected to be past HEADER_OFFSET');\n    return unwrapRNode(lView[index]);\n}\n/**\n * Retrieve an `RNode` for a given `TNode` and `LView`.\n *\n * This function guarantees in dev mode to retrieve a non-null `RNode`.\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNode(tNode, lView) {\n    ngDevMode && assertTNodeForLView(tNode, lView);\n    ngDevMode && assertIndexInRange(lView, tNode.index);\n    const node = unwrapRNode(lView[tNode.index]);\n    return node;\n}\n/**\n * Retrieve an `RNode` or `null` for a given `TNode` and `LView`.\n *\n * Some `TNode`s don't have associated `RNode`s. For example `Projection`\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNodeOrNull(tNode, lView) {\n    const index = tNode === null ? -1 : tNode.index;\n    if (index !== -1) {\n        ngDevMode && assertTNodeForLView(tNode, lView);\n        const node = unwrapRNode(lView[index]);\n        return node;\n    }\n    return null;\n}\n// fixme(misko): The return Type should be `TNode|null`\nfunction getTNode(tView, index) {\n    ngDevMode && assertGreaterThan(index, -1, 'wrong index for TNode');\n    ngDevMode && assertLessThan(index, tView.data.length, 'wrong index for TNode');\n    const tNode = tView.data[index];\n    ngDevMode && tNode !== null && assertTNode(tNode);\n    return tNode;\n}\n/** Retrieves a value from any `LView` or `TData`. */\nfunction load(view, index) {\n    ngDevMode && assertIndexInRange(view, index);\n    return view[index];\n}\n/** Store a value in the `data` at a given `index`. */\nfunction store(tView, lView, index, value) {\n    // We don't store any static data for local variables, so the first time\n    // we see the template, we should store as null to avoid a sparse array\n    if (index >= tView.data.length) {\n        tView.data[index] = null;\n        tView.blueprint[index] = null;\n    }\n    lView[index] = value;\n}\nfunction getComponentLViewByIndex(nodeIndex, hostView) {\n    // Could be an LView or an LContainer. If LContainer, unwrap to find LView.\n    ngDevMode && assertIndexInRange(hostView, nodeIndex);\n    const slotValue = hostView[nodeIndex];\n    const lView = isLView(slotValue) ? slotValue : slotValue[HOST];\n    return lView;\n}\n/** Checks whether a given view is in creation mode */\nfunction isCreationMode(view) {\n    return (view[FLAGS] & 4 /* LViewFlags.CreationMode */) === 4 /* LViewFlags.CreationMode */;\n}\n/**\n * Returns a boolean for whether the view is attached to the change detection tree.\n *\n * Note: This determines whether a view should be checked, not whether it's inserted\n * into a container. For that, you'll want `viewAttachedToContainer` below.\n */\nfunction viewAttachedToChangeDetector(view) {\n    return (view[FLAGS] & 128 /* LViewFlags.Attached */) === 128 /* LViewFlags.Attached */;\n}\n/** Returns a boolean for whether the view is attached to a container. */\nfunction viewAttachedToContainer(view) {\n    return isLContainer(view[PARENT]);\n}\nfunction getConstant(consts, index) {\n    if (index === null || index === undefined)\n        return null;\n    ngDevMode && assertIndexInRange(consts, index);\n    return consts[index];\n}\n/**\n * Resets the pre-order hook flags of the view.\n * @param lView the LView on which the flags are reset\n */\nfunction resetPreOrderHookFlags(lView) {\n    lView[PREORDER_HOOK_FLAGS] = 0;\n}\n/**\n * Adds the `RefreshView` flag from the lView and updates HAS_CHILD_VIEWS_TO_REFRESH flag of\n * parents.\n */\nfunction markViewForRefresh(lView) {\n    if (lView[FLAGS] & 1024 /* LViewFlags.RefreshView */) {\n        return;\n    }\n    lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n    if (viewAttachedToChangeDetector(lView)) {\n        markAncestorsForTraversal(lView);\n    }\n}\n/**\n * Walks up the LView hierarchy.\n * @param nestingLevel Number of times to walk up in hierarchy.\n * @param currentView View from which to start the lookup.\n */\nfunction walkUpViews(nestingLevel, currentView) {\n    while (nestingLevel > 0) {\n        ngDevMode &&\n            assertDefined(currentView[DECLARATION_VIEW], 'Declaration view should be defined if nesting level is greater than 0.');\n        currentView = currentView[DECLARATION_VIEW];\n        nestingLevel--;\n    }\n    return currentView;\n}\nfunction requiresRefreshOrTraversal(lView) {\n    return !!(lView[FLAGS] & (1024 /* LViewFlags.RefreshView */ | 8192 /* LViewFlags.HasChildViewsToRefresh */) ||\n        lView[REACTIVE_TEMPLATE_CONSUMER]?.dirty);\n}\n/**\n * Updates the `HasChildViewsToRefresh` flag on the parents of the `LView` as well as the\n * parents above.\n */\nfunction updateAncestorTraversalFlagsOnAttach(lView) {\n    lView[ENVIRONMENT].changeDetectionScheduler?.notify(8 /* NotificationSource.ViewAttached */);\n    if (lView[FLAGS] & 64 /* LViewFlags.Dirty */) {\n        lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n    }\n    if (requiresRefreshOrTraversal(lView)) {\n        markAncestorsForTraversal(lView);\n    }\n}\n/**\n * Ensures views above the given `lView` are traversed during change detection even when they are\n * not dirty.\n *\n * This is done by setting the `HAS_CHILD_VIEWS_TO_REFRESH` flag up to the root, stopping when the\n * flag is already `true` or the `lView` is detached.\n */\nfunction markAncestorsForTraversal(lView) {\n    lView[ENVIRONMENT].changeDetectionScheduler?.notify(0 /* NotificationSource.MarkAncestorsForTraversal */);\n    let parent = getLViewParent(lView);\n    while (parent !== null) {\n        // We stop adding markers to the ancestors once we reach one that already has the marker. This\n        // is to avoid needlessly traversing all the way to the root when the marker already exists.\n        if (parent[FLAGS] & 8192 /* LViewFlags.HasChildViewsToRefresh */) {\n            break;\n        }\n        parent[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n        if (!viewAttachedToChangeDetector(parent)) {\n            break;\n        }\n        parent = getLViewParent(parent);\n    }\n}\n/**\n * Stores a LView-specific destroy callback.\n */\nfunction storeLViewOnDestroy(lView, onDestroyCallback) {\n    if (isDestroyed(lView)) {\n        throw new RuntimeError(911 /* RuntimeErrorCode.VIEW_ALREADY_DESTROYED */, ngDevMode && 'View has already been destroyed.');\n    }\n    if (lView[ON_DESTROY_HOOKS] === null) {\n        lView[ON_DESTROY_HOOKS] = [];\n    }\n    lView[ON_DESTROY_HOOKS].push(onDestroyCallback);\n}\n/**\n * Removes previously registered LView-specific destroy callback.\n */\nfunction removeLViewOnDestroy(lView, onDestroyCallback) {\n    if (lView[ON_DESTROY_HOOKS] === null)\n        return;\n    const destroyCBIdx = lView[ON_DESTROY_HOOKS].indexOf(onDestroyCallback);\n    if (destroyCBIdx !== -1) {\n        lView[ON_DESTROY_HOOKS].splice(destroyCBIdx, 1);\n    }\n}\n/**\n * Gets the parent LView of the passed LView, if the PARENT is an LContainer, will get the parent of\n * that LContainer, which is an LView\n * @param lView the lView whose parent to get\n */\nfunction getLViewParent(lView) {\n    ngDevMode && assertLView(lView);\n    const parent = lView[PARENT];\n    return isLContainer(parent) ? parent[PARENT] : parent;\n}\nfunction getOrCreateLViewCleanup(view) {\n    // top level variables should not be exported for performance reasons (PERF_NOTES.md)\n    return (view[CLEANUP] ??= []);\n}\nfunction getOrCreateTViewCleanup(tView) {\n    return (tView.cleanup ??= []);\n}\n/**\n * Saves context for this cleanup function in LView.cleanupInstances.\n *\n * On the first template pass, saves in TView:\n * - Cleanup function\n * - Index of context we just saved in LView.cleanupInstances\n */\nfunction storeCleanupWithContext(tView, lView, context, cleanupFn) {\n    const lCleanup = getOrCreateLViewCleanup(lView);\n    // Historically the `storeCleanupWithContext` was used to register both framework-level and\n    // user-defined cleanup callbacks, but over time those two types of cleanups were separated.\n    // This dev mode checks assures that user-level cleanup callbacks are _not_ stored in data\n    // structures reserved for framework-specific hooks.\n    ngDevMode &&\n        assertDefined(context, 'Cleanup context is mandatory when registering framework-level destroy hooks');\n    lCleanup.push(context);\n    if (tView.firstCreatePass) {\n        getOrCreateTViewCleanup(tView).push(cleanupFn, lCleanup.length - 1);\n    }\n    else {\n        // Make sure that no new framework-level cleanup functions are registered after the first\n        // template pass is done (and TView data structures are meant to fully constructed).\n        if (ngDevMode) {\n            Object.freeze(getOrCreateTViewCleanup(tView));\n        }\n    }\n}\n\nconst instructionState = {\n    lFrame: createLFrame(null),\n    bindingsEnabled: true,\n    skipHydrationRootTNode: null,\n};\nvar CheckNoChangesMode;\n(function (CheckNoChangesMode) {\n    CheckNoChangesMode[CheckNoChangesMode[\"Off\"] = 0] = \"Off\";\n    CheckNoChangesMode[CheckNoChangesMode[\"Exhaustive\"] = 1] = \"Exhaustive\";\n    CheckNoChangesMode[CheckNoChangesMode[\"OnlyDirtyViews\"] = 2] = \"OnlyDirtyViews\";\n})(CheckNoChangesMode || (CheckNoChangesMode = {}));\n/**\n * In this mode, any changes in bindings will throw an ExpressionChangedAfterChecked error.\n *\n * Necessary to support ChangeDetectorRef.checkNoChanges().\n *\n * The `checkNoChanges` function is invoked only in ngDevMode=true and verifies that no unintended\n * changes exist in the change detector or its children.\n */\nlet _checkNoChangesMode = 0; /* CheckNoChangesMode.Off */\n/**\n * Flag used to indicate that we are in the middle running change detection on a view\n *\n * @see detectChangesInViewWhileDirty\n */\nlet _isRefreshingViews = false;\nfunction getElementDepthCount() {\n    return instructionState.lFrame.elementDepthCount;\n}\nfunction increaseElementDepthCount() {\n    instructionState.lFrame.elementDepthCount++;\n}\nfunction decreaseElementDepthCount() {\n    instructionState.lFrame.elementDepthCount--;\n}\nfunction getBindingsEnabled() {\n    return instructionState.bindingsEnabled;\n}\n/**\n * Returns true if currently inside a skip hydration block.\n * @returns boolean\n */\nfunction isInSkipHydrationBlock() {\n    return instructionState.skipHydrationRootTNode !== null;\n}\n/**\n * Returns true if this is the root TNode of the skip hydration block.\n * @param tNode the current TNode\n * @returns boolean\n */\nfunction isSkipHydrationRootTNode(tNode) {\n    return instructionState.skipHydrationRootTNode === tNode;\n}\n/**\n * Enables directive matching on elements.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵenableBindings() {\n    instructionState.bindingsEnabled = true;\n}\n/**\n * Sets a flag to specify that the TNode is in a skip hydration block.\n * @param tNode the current TNode\n */\nfunction enterSkipHydrationBlock(tNode) {\n    instructionState.skipHydrationRootTNode = tNode;\n}\n/**\n * Disables directive matching on element.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵdisableBindings() {\n    instructionState.bindingsEnabled = false;\n}\n/**\n * Clears the root skip hydration node when leaving a skip hydration block.\n */\nfunction leaveSkipHydrationBlock() {\n    instructionState.skipHydrationRootTNode = null;\n}\n/**\n * Return the current `LView`.\n */\nfunction getLView() {\n    return instructionState.lFrame.lView;\n}\n/**\n * Return the current `TView`.\n */\nfunction getTView() {\n    return instructionState.lFrame.tView;\n}\n/**\n * Restores `contextViewData` to the given OpaqueViewState instance.\n *\n * Used in conjunction with the getCurrentView() instruction to save a snapshot\n * of the current view and restore it when listeners are invoked. This allows\n * walking the declaration view tree in listeners to get vars from parent views.\n *\n * @param viewToRestore The OpaqueViewState instance to restore.\n * @returns Context of the restored OpaqueViewState instance.\n *\n * @codeGenApi\n */\nfunction ɵɵrestoreView(viewToRestore) {\n    instructionState.lFrame.contextLView = viewToRestore;\n    return viewToRestore[CONTEXT];\n}\n/**\n * Clears the view set in `ɵɵrestoreView` from memory. Returns the passed in\n * value so that it can be used as a return value of an instruction.\n *\n * @codeGenApi\n */\nfunction ɵɵresetView(value) {\n    instructionState.lFrame.contextLView = null;\n    return value;\n}\nfunction getCurrentTNode() {\n    let currentTNode = getCurrentTNodePlaceholderOk();\n    while (currentTNode !== null && currentTNode.type === 64 /* TNodeType.Placeholder */) {\n        currentTNode = currentTNode.parent;\n    }\n    return currentTNode;\n}\nfunction getCurrentTNodePlaceholderOk() {\n    return instructionState.lFrame.currentTNode;\n}\nfunction getCurrentParentTNode() {\n    const lFrame = instructionState.lFrame;\n    const currentTNode = lFrame.currentTNode;\n    return lFrame.isParent ? currentTNode : currentTNode.parent;\n}\nfunction setCurrentTNode(tNode, isParent) {\n    ngDevMode && tNode && assertTNodeForTView(tNode, instructionState.lFrame.tView);\n    const lFrame = instructionState.lFrame;\n    lFrame.currentTNode = tNode;\n    lFrame.isParent = isParent;\n}\nfunction isCurrentTNodeParent() {\n    return instructionState.lFrame.isParent;\n}\nfunction setCurrentTNodeAsNotParent() {\n    instructionState.lFrame.isParent = false;\n}\nfunction getContextLView() {\n    const contextLView = instructionState.lFrame.contextLView;\n    ngDevMode && assertDefined(contextLView, 'contextLView must be defined.');\n    return contextLView;\n}\nfunction isInCheckNoChangesMode() {\n    !ngDevMode && throwError('Must never be called in production mode');\n    return _checkNoChangesMode !== CheckNoChangesMode.Off;\n}\nfunction isExhaustiveCheckNoChanges() {\n    !ngDevMode && throwError('Must never be called in production mode');\n    return _checkNoChangesMode === CheckNoChangesMode.Exhaustive;\n}\nfunction setIsInCheckNoChangesMode(mode) {\n    !ngDevMode && throwError('Must never be called in production mode');\n    _checkNoChangesMode = mode;\n}\nfunction isRefreshingViews() {\n    return _isRefreshingViews;\n}\nfunction setIsRefreshingViews(mode) {\n    const prev = _isRefreshingViews;\n    _isRefreshingViews = mode;\n    return prev;\n}\n// top level variables should not be exported for performance reasons (PERF_NOTES.md)\nfunction getBindingRoot() {\n    const lFrame = instructionState.lFrame;\n    let index = lFrame.bindingRootIndex;\n    if (index === -1) {\n        index = lFrame.bindingRootIndex = lFrame.tView.bindingStartIndex;\n    }\n    return index;\n}\nfunction getBindingIndex() {\n    return instructionState.lFrame.bindingIndex;\n}\nfunction setBindingIndex(value) {\n    return (instructionState.lFrame.bindingIndex = value);\n}\nfunction nextBindingIndex() {\n    return instructionState.lFrame.bindingIndex++;\n}\nfunction incrementBindingIndex(count) {\n    const lFrame = instructionState.lFrame;\n    const index = lFrame.bindingIndex;\n    lFrame.bindingIndex = lFrame.bindingIndex + count;\n    return index;\n}\nfunction isInI18nBlock() {\n    return instructionState.lFrame.inI18n;\n}\nfunction setInI18nBlock(isInI18nBlock) {\n    instructionState.lFrame.inI18n = isInI18nBlock;\n}\n/**\n * Set a new binding root index so that host template functions can execute.\n *\n * Bindings inside the host template are 0 index. But because we don't know ahead of time\n * how many host bindings we have we can't pre-compute them. For this reason they are all\n * 0 index and we just shift the root so that they match next available location in the LView.\n *\n * @param bindingRootIndex Root index for `hostBindings`\n * @param currentDirectiveIndex `TData[currentDirectiveIndex]` will point to the current directive\n *        whose `hostBindings` are being processed.\n */\nfunction setBindingRootForHostBindings(bindingRootIndex, currentDirectiveIndex) {\n    const lFrame = instructionState.lFrame;\n    lFrame.bindingIndex = lFrame.bindingRootIndex = bindingRootIndex;\n    setCurrentDirectiveIndex(currentDirectiveIndex);\n}\n/**\n * When host binding is executing this points to the directive index.\n * `TView.data[getCurrentDirectiveIndex()]` is `DirectiveDef`\n * `LView[getCurrentDirectiveIndex()]` is directive instance.\n */\nfunction getCurrentDirectiveIndex() {\n    return instructionState.lFrame.currentDirectiveIndex;\n}\n/**\n * Sets an index of a directive whose `hostBindings` are being processed.\n *\n * @param currentDirectiveIndex `TData` index where current directive instance can be found.\n */\nfunction setCurrentDirectiveIndex(currentDirectiveIndex) {\n    instructionState.lFrame.currentDirectiveIndex = currentDirectiveIndex;\n}\n/**\n * Retrieve the current `DirectiveDef` which is active when `hostBindings` instruction is being\n * executed.\n *\n * @param tData Current `TData` where the `DirectiveDef` will be looked up at.\n */\nfunction getCurrentDirectiveDef(tData) {\n    const currentDirectiveIndex = instructionState.lFrame.currentDirectiveIndex;\n    return currentDirectiveIndex === -1 ? null : tData[currentDirectiveIndex];\n}\nfunction getCurrentQueryIndex() {\n    return instructionState.lFrame.currentQueryIndex;\n}\nfunction setCurrentQueryIndex(value) {\n    instructionState.lFrame.currentQueryIndex = value;\n}\n/**\n * Returns a `TNode` of the location where the current `LView` is declared at.\n *\n * @param lView an `LView` that we want to find parent `TNode` for.\n */\nfunction getDeclarationTNode(lView) {\n    const tView = lView[TVIEW];\n    // Return the declaration parent for embedded views\n    if (tView.type === 2 /* TViewType.Embedded */) {\n        ngDevMode && assertDefined(tView.declTNode, 'Embedded TNodes should have declaration parents.');\n        return tView.declTNode;\n    }\n    // Components don't have `TView.declTNode` because each instance of component could be\n    // inserted in different location, hence `TView.declTNode` is meaningless.\n    // Falling back to `T_HOST` in case we cross component boundary.\n    if (tView.type === 1 /* TViewType.Component */) {\n        return lView[T_HOST];\n    }\n    // Remaining TNode type is `TViewType.Root` which doesn't have a parent TNode.\n    return null;\n}\n/**\n * This is a light weight version of the `enterView` which is needed by the DI system.\n *\n * @param lView `LView` location of the DI context.\n * @param tNode `TNode` for DI context\n * @param flags DI context flags. if `SkipSelf` flag is set than we walk up the declaration\n *     tree from `tNode`  until we find parent declared `TElementNode`.\n * @returns `true` if we have successfully entered DI associated with `tNode` (or with declared\n *     `TNode` if `flags` has  `SkipSelf`). Failing to enter DI implies that no associated\n *     `NodeInjector` can be found and we should instead use `ModuleInjector`.\n *     - If `true` than this call must be fallowed by `leaveDI`\n *     - If `false` than this call failed and we should NOT call `leaveDI`\n */\nfunction enterDI(lView, tNode, flags) {\n    ngDevMode && assertLViewOrUndefined(lView);\n    if (flags & 4 /* InternalInjectFlags.SkipSelf */) {\n        ngDevMode && assertTNodeForTView(tNode, lView[TVIEW]);\n        let parentTNode = tNode;\n        let parentLView = lView;\n        while (true) {\n            ngDevMode && assertDefined(parentTNode, 'Parent TNode should be defined');\n            parentTNode = parentTNode.parent;\n            if (parentTNode === null && !(flags & 1 /* InternalInjectFlags.Host */)) {\n                parentTNode = getDeclarationTNode(parentLView);\n                if (parentTNode === null)\n                    break;\n                // In this case, a parent exists and is definitely an element. So it will definitely\n                // have an existing lView as the declaration view, which is why we can assume it's defined.\n                ngDevMode && assertDefined(parentLView, 'Parent LView should be defined');\n                parentLView = parentLView[DECLARATION_VIEW];\n                // In Ivy there are Comment nodes that correspond to ngIf and NgFor embedded directives\n                // We want to skip those and look only at Elements and ElementContainers to ensure\n                // we're looking at true parent nodes, and not content or other types.\n                if (parentTNode.type & (2 /* TNodeType.Element */ | 8 /* TNodeType.ElementContainer */)) {\n                    break;\n                }\n            }\n            else {\n                break;\n            }\n        }\n        if (parentTNode === null) {\n            // If we failed to find a parent TNode this means that we should use module injector.\n            return false;\n        }\n        else {\n            tNode = parentTNode;\n            lView = parentLView;\n        }\n    }\n    ngDevMode && assertTNodeForLView(tNode, lView);\n    const lFrame = (instructionState.lFrame = allocLFrame());\n    lFrame.currentTNode = tNode;\n    lFrame.lView = lView;\n    return true;\n}\n/**\n * Swap the current lView with a new lView.\n *\n * For performance reasons we store the lView in the top level of the module.\n * This way we minimize the number of properties to read. Whenever a new view\n * is entered we have to store the lView for later, and when the view is\n * exited the state has to be restored\n *\n * @param newView New lView to become active\n * @returns the previously active lView;\n */\nfunction enterView(newView) {\n    ngDevMode && assertNotEqual(newView[0], newView[1], '????');\n    ngDevMode && assertLViewOrUndefined(newView);\n    const newLFrame = allocLFrame();\n    if (ngDevMode) {\n        assertEqual(newLFrame.isParent, true, 'Expected clean LFrame');\n        assertEqual(newLFrame.lView, null, 'Expected clean LFrame');\n        assertEqual(newLFrame.tView, null, 'Expected clean LFrame');\n        assertEqual(newLFrame.selectedIndex, -1, 'Expected clean LFrame');\n        assertEqual(newLFrame.elementDepthCount, 0, 'Expected clean LFrame');\n        assertEqual(newLFrame.currentDirectiveIndex, -1, 'Expected clean LFrame');\n        assertEqual(newLFrame.currentNamespace, null, 'Expected clean LFrame');\n        assertEqual(newLFrame.bindingRootIndex, -1, 'Expected clean LFrame');\n        assertEqual(newLFrame.currentQueryIndex, 0, 'Expected clean LFrame');\n    }\n    const tView = newView[TVIEW];\n    instructionState.lFrame = newLFrame;\n    ngDevMode && tView.firstChild && assertTNodeForTView(tView.firstChild, tView);\n    newLFrame.currentTNode = tView.firstChild;\n    newLFrame.lView = newView;\n    newLFrame.tView = tView;\n    newLFrame.contextLView = newView;\n    newLFrame.bindingIndex = tView.bindingStartIndex;\n    newLFrame.inI18n = false;\n}\n/**\n * Allocates next free LFrame. This function tries to reuse the `LFrame`s to lower memory pressure.\n */\nfunction allocLFrame() {\n    const currentLFrame = instructionState.lFrame;\n    const childLFrame = currentLFrame === null ? null : currentLFrame.child;\n    const newLFrame = childLFrame === null ? createLFrame(currentLFrame) : childLFrame;\n    return newLFrame;\n}\nfunction createLFrame(parent) {\n    const lFrame = {\n        currentTNode: null,\n        isParent: true,\n        lView: null,\n        tView: null,\n        selectedIndex: -1,\n        contextLView: null,\n        elementDepthCount: 0,\n        currentNamespace: null,\n        currentDirectiveIndex: -1,\n        bindingRootIndex: -1,\n        bindingIndex: -1,\n        currentQueryIndex: 0,\n        parent: parent,\n        child: null,\n        inI18n: false,\n    };\n    parent !== null && (parent.child = lFrame); // link the new LFrame for reuse.\n    return lFrame;\n}\n/**\n * A lightweight version of leave which is used with DI.\n *\n * This function only resets `currentTNode` and `LView` as those are the only properties\n * used with DI (`enterDI()`).\n *\n * NOTE: This function is reexported as `leaveDI`. However `leaveDI` has return type of `void` where\n * as `leaveViewLight` has `LFrame`. This is so that `leaveViewLight` can be used in `leaveView`.\n */\nfunction leaveViewLight() {\n    const oldLFrame = instructionState.lFrame;\n    instructionState.lFrame = oldLFrame.parent;\n    oldLFrame.currentTNode = null;\n    oldLFrame.lView = null;\n    return oldLFrame;\n}\n/**\n * This is a lightweight version of the `leaveView` which is needed by the DI system.\n *\n * NOTE: this function is an alias so that we can change the type of the function to have `void`\n * return type.\n */\nconst leaveDI = leaveViewLight;\n/**\n * Leave the current `LView`\n *\n * This pops the `LFrame` with the associated `LView` from the stack.\n *\n * IMPORTANT: We must zero out the `LFrame` values here otherwise they will be retained. This is\n * because for performance reasons we don't release `LFrame` but rather keep it for next use.\n */\nfunction leaveView() {\n    const oldLFrame = leaveViewLight();\n    oldLFrame.isParent = true;\n    oldLFrame.tView = null;\n    oldLFrame.selectedIndex = -1;\n    oldLFrame.contextLView = null;\n    oldLFrame.elementDepthCount = 0;\n    oldLFrame.currentDirectiveIndex = -1;\n    oldLFrame.currentNamespace = null;\n    oldLFrame.bindingRootIndex = -1;\n    oldLFrame.bindingIndex = -1;\n    oldLFrame.currentQueryIndex = 0;\n}\nfunction nextContextImpl(level) {\n    const contextLView = (instructionState.lFrame.contextLView = walkUpViews(level, instructionState.lFrame.contextLView));\n    return contextLView[CONTEXT];\n}\n/**\n * Gets the currently selected element index.\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n */\nfunction getSelectedIndex() {\n    return instructionState.lFrame.selectedIndex;\n}\n/**\n * Sets the most recent index passed to {@link select}\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n *\n * (Note that if an \"exit function\" was set earlier (via `setElementExitFn()`) then that will be\n * run if and when the provided `index` value is different from the current selected index value.)\n */\nfunction setSelectedIndex(index) {\n    ngDevMode &&\n        index !== -1 &&\n        assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Index must be past HEADER_OFFSET (or -1).');\n    ngDevMode &&\n        assertLessThan(index, instructionState.lFrame.lView.length, \"Can't set index passed end of LView\");\n    instructionState.lFrame.selectedIndex = index;\n}\n/**\n * Gets the `tNode` that represents currently selected element.\n */\nfunction getSelectedTNode() {\n    const lFrame = instructionState.lFrame;\n    return getTNode(lFrame.tView, lFrame.selectedIndex);\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/2000/svg'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceSVG() {\n    instructionState.lFrame.currentNamespace = SVG_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/1998/MathML/'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceMathML() {\n    instructionState.lFrame.currentNamespace = MATH_ML_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceHTML() {\n    namespaceHTMLInternal();\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n */\nfunction namespaceHTMLInternal() {\n    instructionState.lFrame.currentNamespace = null;\n}\nfunction getNamespace() {\n    return instructionState.lFrame.currentNamespace;\n}\nlet _wasLastNodeCreated = true;\n/**\n * Retrieves a global flag that indicates whether the most recent DOM node\n * was created or hydrated.\n */\nfunction wasLastNodeCreated() {\n    return _wasLastNodeCreated;\n}\n/**\n * Sets a global flag to indicate whether the most recent DOM node\n * was created or hydrated.\n */\nfunction lastNodeWasCreated(flag) {\n    _wasLastNodeCreated = flag;\n}\n\n/**\n * Create a new `Injector` which is configured using a `defType` of `InjectorType<any>`s.\n */\nfunction createInjector(defType, parent = null, additionalProviders = null, name) {\n    const injector = createInjectorWithoutInjectorInstances(defType, parent, additionalProviders, name);\n    injector.resolveInjectorInitializers();\n    return injector;\n}\n/**\n * Creates a new injector without eagerly resolving its injector types. Can be used in places\n * where resolving the injector types immediately can lead to an infinite loop. The injector types\n * should be resolved at a later point by calling `_resolveInjectorDefTypes`.\n */\nfunction createInjectorWithoutInjectorInstances(defType, parent = null, additionalProviders = null, name, scopes = new Set()) {\n    const providers = [additionalProviders || EMPTY_ARRAY, importProvidersFrom(defType)];\n    name = name || (typeof defType === 'object' ? undefined : stringify(defType));\n    return new R3Injector(providers, parent || getNullInjector(), name || null, scopes);\n}\n\n/**\n * Concrete injectors implement this interface. Injectors are configured\n * with [providers](guide/di/dependency-injection-providers) that associate\n * dependencies of various types with [injection tokens](guide/di/dependency-injection-providers).\n *\n * @see [DI Providers](guide/di/dependency-injection-providers).\n * @see {@link StaticProvider}\n *\n * @usageNotes\n *\n *  The following example creates a service injector instance.\n *\n * {@example core/di/ts/provider_spec.ts region='ConstructorProvider'}\n *\n * ### Usage example\n *\n * {@example core/di/ts/injector_spec.ts region='Injector'}\n *\n * `Injector` returns itself when given `Injector` as a token:\n *\n * {@example core/di/ts/injector_spec.ts region='injectInjector'}\n *\n * @publicApi\n */\nclass Injector {\n    static THROW_IF_NOT_FOUND = THROW_IF_NOT_FOUND;\n    static NULL = new NullInjector();\n    static create(options, parent) {\n        if (Array.isArray(options)) {\n            return createInjector({ name: '' }, parent, options, '');\n        }\n        else {\n            const name = options.name ?? '';\n            return createInjector({ name }, options.parent, options.providers, name);\n        }\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: Injector,\n        providedIn: 'any',\n        factory: () => ɵɵinject(INJECTOR$1),\n    });\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = -1 /* InjectorMarkers.Injector */;\n}\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [Domino](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = new InjectionToken(ngDevMode ? 'DocumentToken' : '');\n\n/**\n * `DestroyRef` lets you set callbacks to run for any cleanup or destruction behavior.\n * The scope of this destruction depends on where `DestroyRef` is injected. If `DestroyRef`\n * is injected in a component or directive, the callbacks run when that component or\n * directive is destroyed. Otherwise the callbacks run when a corresponding injector is destroyed.\n *\n * @publicApi\n */\nclass DestroyRef {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectDestroyRef;\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ENV_ID__ = (injector) => injector;\n}\nclass NodeInjectorDestroyRef extends DestroyRef {\n    _lView;\n    constructor(_lView) {\n        super();\n        this._lView = _lView;\n    }\n    get destroyed() {\n        return isDestroyed(this._lView);\n    }\n    onDestroy(callback) {\n        const lView = this._lView;\n        storeLViewOnDestroy(lView, callback);\n        return () => removeLViewOnDestroy(lView, callback);\n    }\n}\nfunction injectDestroyRef() {\n    return new NodeInjectorDestroyRef(getLView());\n}\n\n/**\n * Provides a hook for centralized exception handling.\n *\n * The default implementation of `ErrorHandler` prints error messages to the `console`. To\n * intercept error handling, write a custom exception handler that replaces this default as\n * appropriate for your app.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * class MyErrorHandler implements ErrorHandler {\n *   handleError(error) {\n *     // do something with the exception\n *   }\n * }\n *\n * // Provide in standalone apps\n * bootstrapApplication(AppComponent, {\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n *\n * // Provide in module-based apps\n * @NgModule({\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n * class MyModule {}\n * ```\n *\n * @publicApi\n */\nclass ErrorHandler {\n    /**\n     * @internal\n     */\n    _console = console;\n    handleError(error) {\n        this._console.error('ERROR', error);\n    }\n}\n/**\n * `InjectionToken` used to configure how to call the `ErrorHandler`.\n */\nconst INTERNAL_APPLICATION_ERROR_HANDLER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'internal error handler' : '', {\n    providedIn: 'root',\n    factory: () => {\n        // The user's error handler may depend on things that create a circular dependency\n        // so we inject it lazily.\n        const injector = inject(EnvironmentInjector);\n        let userErrorHandler;\n        return (e) => {\n            if (injector.destroyed && !userErrorHandler) {\n                setTimeout(() => {\n                    throw e;\n                });\n            }\n            else {\n                userErrorHandler ??= injector.get(ErrorHandler);\n                userErrorHandler.handleError(e);\n            }\n        };\n    },\n});\nconst errorHandlerEnvironmentInitializer = {\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => void inject(ErrorHandler),\n    multi: true,\n};\nconst globalErrorListeners = new InjectionToken(ngDevMode ? 'GlobalErrorListeners' : '', {\n    providedIn: 'root',\n    factory: () => {\n        if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n            return;\n        }\n        const window = inject(DOCUMENT).defaultView;\n        if (!window) {\n            return;\n        }\n        const errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n        const rejectionListener = (e) => {\n            errorHandler(e.reason);\n            e.preventDefault();\n        };\n        const errorListener = (e) => {\n            if (e.error) {\n                errorHandler(e.error);\n            }\n            else {\n                errorHandler(new Error(ngDevMode\n                    ? `An ErrorEvent with no error occurred. See Error.cause for details: ${e.message}`\n                    : e.message, { cause: e }));\n            }\n            e.preventDefault();\n        };\n        const setupEventListeners = () => {\n            window.addEventListener('unhandledrejection', rejectionListener);\n            window.addEventListener('error', errorListener);\n        };\n        // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n        // the scope of this functionality.\n        if (typeof Zone !== 'undefined') {\n            Zone.root.run(setupEventListeners);\n        }\n        else {\n            setupEventListeners();\n        }\n        inject(DestroyRef).onDestroy(() => {\n            window.removeEventListener('error', errorListener);\n            window.removeEventListener('unhandledrejection', rejectionListener);\n        });\n    },\n});\n/**\n * Provides an environment initializer which forwards unhandled errors to the ErrorHandler.\n *\n * The listeners added are for the window's 'unhandledrejection' and 'error' events.\n *\n * @publicApi\n */\nfunction provideBrowserGlobalErrorListeners() {\n    return makeEnvironmentProviders([\n        provideEnvironmentInitializer(() => void inject(globalErrorListeners)),\n    ]);\n}\n\n/**\n * Checks if the given `value` is a reactive `Signal`.\n *\n * @publicApi 17.0\n */\nfunction isSignal(value) {\n    return typeof value === 'function' && value[SIGNAL] !== undefined;\n}\n\n/**\n * Utility function used during template type checking to extract the value from a `WritableSignal`.\n * @codeGenApi\n */\nfunction ɵunwrapWritableSignal(value) {\n    // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,\n    // because the latter incorrectly unwraps non-signal getter functions.\n    return null;\n}\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction signal(initialValue, options) {\n    const [get, set, update] = createSignal(initialValue, options?.equal);\n    const signalFn = get;\n    const node = signalFn[SIGNAL];\n    signalFn.set = set;\n    signalFn.update = update;\n    signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn);\n    if (ngDevMode) {\n        signalFn.toString = () => `[Signal: ${signalFn()}]`;\n        node.debugName = options?.debugName;\n    }\n    return signalFn;\n}\nfunction signalAsReadonlyFn() {\n    const node = this[SIGNAL];\n    if (node.readonlyFn === undefined) {\n        const readonlyFn = () => this();\n        readonlyFn[SIGNAL] = node;\n        node.readonlyFn = readonlyFn;\n    }\n    return node.readonlyFn;\n}\n/**\n * Checks if the given `value` is a writeable signal.\n */\nfunction isWritableSignal(value) {\n    return isSignal(value) && typeof value.set === 'function';\n}\n\n/**\n * Injectable that is notified when an `LView` is made aware of changes to application state.\n */\nclass ChangeDetectionScheduler {\n}\n/** Token used to indicate if zoneless was enabled via provideZonelessChangeDetection(). */\nconst ZONELESS_ENABLED = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless enabled' : '', { providedIn: 'root', factory: () => false });\n/** Token used to indicate `provideZonelessChangeDetection` was used. */\nconst PROVIDED_ZONELESS = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless provided' : '', { providedIn: 'root', factory: () => false });\nconst ZONELESS_SCHEDULER_DISABLED = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'scheduler disabled' : '');\n// TODO(atscott): Remove in v19. Scheduler should be done with runOutsideAngular.\nconst SCHEDULE_IN_ROOT_ZONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'run changes outside zone in root' : '');\n\n/**\n * Asserts that the current stack frame is not within a reactive context. Useful\n * to disallow certain code from running inside a reactive context (see {@link /api/core/rxjs-interop/toSignal toSignal})\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertNotInReactiveContext(debugFn, extraContext) {\n    // Taking a `Function` instead of a string name here prevents the un-minified name of the function\n    // from being retained in the bundle regardless of minification.\n    if (getActiveConsumer() !== null) {\n        throw new RuntimeError(-602 /* RuntimeErrorCode.ASSERTION_NOT_INSIDE_REACTIVE_CONTEXT */, ngDevMode &&\n            `${debugFn.name}() cannot be called from within a reactive context.${extraContext ? ` ${extraContext}` : ''}`);\n    }\n}\n\nclass ViewContext {\n    view;\n    node;\n    constructor(view, node) {\n        this.view = view;\n        this.node = node;\n    }\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectViewContext;\n}\nfunction injectViewContext() {\n    return new ViewContext(getLView(), getCurrentTNode());\n}\n\n/**\n * Internal implementation of the pending tasks service.\n */\nclass PendingTasksInternal {\n    taskId = 0;\n    pendingTasks = new Set();\n    destroyed = false;\n    pendingTask = new BehaviorSubject(false);\n    get hasPendingTasks() {\n        // Accessing the value of a closed `BehaviorSubject` throws an error.\n        return this.destroyed ? false : this.pendingTask.value;\n    }\n    /**\n     * In case the service is about to be destroyed, return a self-completing observable.\n     * Otherwise, return the observable that emits the current state of pending tasks.\n     */\n    get hasPendingTasksObservable() {\n        if (this.destroyed) {\n            // Manually creating the observable pulls less symbols from RxJS than `of(false)`.\n            return new Observable((subscriber) => {\n                subscriber.next(false);\n                subscriber.complete();\n            });\n        }\n        return this.pendingTask;\n    }\n    add() {\n        // Emitting a value to a closed subject throws an error.\n        if (!this.hasPendingTasks && !this.destroyed) {\n            this.pendingTask.next(true);\n        }\n        const taskId = this.taskId++;\n        this.pendingTasks.add(taskId);\n        return taskId;\n    }\n    has(taskId) {\n        return this.pendingTasks.has(taskId);\n    }\n    remove(taskId) {\n        this.pendingTasks.delete(taskId);\n        if (this.pendingTasks.size === 0 && this.hasPendingTasks) {\n            this.pendingTask.next(false);\n        }\n    }\n    ngOnDestroy() {\n        this.pendingTasks.clear();\n        if (this.hasPendingTasks) {\n            this.pendingTask.next(false);\n        }\n        // We call `unsubscribe()` to release observers, as users may forget to\n        // unsubscribe manually when subscribing to `isStable`. We do not call\n        // `complete()` because it is unsafe; if someone subscribes using the `first`\n        // operator and the observable completes before emitting a value,\n        // RxJS will throw an error.\n        this.destroyed = true;\n        this.pendingTask.unsubscribe();\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: PendingTasksInternal,\n        providedIn: 'root',\n        factory: () => new PendingTasksInternal(),\n    });\n}\n/**\n * Service that keeps track of pending tasks contributing to the stableness of Angular\n * application. While several existing Angular services (ex.: `HttpClient`) will internally manage\n * tasks influencing stability, this API gives control over stability to library and application\n * developers for specific cases not covered by Angular internals.\n *\n * The concept of stability comes into play in several important scenarios:\n * - SSR process needs to wait for the application stability before serializing and sending rendered\n * HTML;\n * - tests might want to delay assertions until the application becomes stable;\n *\n * @usageNotes\n * ```ts\n * const pendingTasks = inject(PendingTasks);\n * const taskCleanup = pendingTasks.add();\n * // do work that should block application's stability and then:\n * taskCleanup();\n * ```\n *\n * @publicApi 20.0\n */\nclass PendingTasks {\n    internalPendingTasks = inject(PendingTasksInternal);\n    scheduler = inject(ChangeDetectionScheduler);\n    errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    /**\n     * Adds a new task that should block application's stability.\n     * @returns A cleanup function that removes a task when called.\n     */\n    add() {\n        const taskId = this.internalPendingTasks.add();\n        return () => {\n            if (!this.internalPendingTasks.has(taskId)) {\n                // This pending task has already been cleared.\n                return;\n            }\n            // Notifying the scheduler will hold application stability open until the next tick.\n            this.scheduler.notify(11 /* NotificationSource.PendingTaskRemoved */);\n            this.internalPendingTasks.remove(taskId);\n        };\n    }\n    /**\n     * Runs an asynchronous function and blocks the application's stability until the function completes.\n     *\n     * ```ts\n     * pendingTasks.run(async () => {\n     *   const userData = await fetch('/api/user');\n     *   this.userData.set(userData);\n     * });\n     * ```\n     *\n     * @param fn The asynchronous function to execute\n     * @developerPreview 19.0\n     */\n    run(fn) {\n        const removeTask = this.add();\n        fn().catch(this.errorHandler).finally(removeTask);\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: PendingTasks,\n        providedIn: 'root',\n        factory: () => new PendingTasks(),\n    });\n}\n\nfunction noop(...args) {\n    // Do nothing.\n}\n\n/**\n * A scheduler which manages the execution of effects.\n */\nclass EffectScheduler {\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: EffectScheduler,\n        providedIn: 'root',\n        factory: () => new ZoneAwareEffectScheduler(),\n    });\n}\n/**\n * A wrapper around `ZoneAwareQueueingScheduler` that schedules flushing via the microtask queue\n * when.\n */\nclass ZoneAwareEffectScheduler {\n    dirtyEffectCount = 0;\n    queues = new Map();\n    add(handle) {\n        this.enqueue(handle);\n        this.schedule(handle);\n    }\n    schedule(handle) {\n        if (!handle.dirty) {\n            return;\n        }\n        this.dirtyEffectCount++;\n    }\n    remove(handle) {\n        const zone = handle.zone;\n        const queue = this.queues.get(zone);\n        if (!queue.has(handle)) {\n            return;\n        }\n        queue.delete(handle);\n        if (handle.dirty) {\n            this.dirtyEffectCount--;\n        }\n    }\n    enqueue(handle) {\n        const zone = handle.zone;\n        if (!this.queues.has(zone)) {\n            this.queues.set(zone, new Set());\n        }\n        const queue = this.queues.get(zone);\n        if (queue.has(handle)) {\n            return;\n        }\n        queue.add(handle);\n    }\n    /**\n     * Run all scheduled effects.\n     *\n     * Execution order of effects within the same zone is guaranteed to be FIFO, but there is no\n     * ordering guarantee between effects scheduled in different zones.\n     */\n    flush() {\n        while (this.dirtyEffectCount > 0) {\n            let ranOneEffect = false;\n            for (const [zone, queue] of this.queues) {\n                // `zone` here must be defined.\n                if (zone === null) {\n                    ranOneEffect ||= this.flushQueue(queue);\n                }\n                else {\n                    ranOneEffect ||= zone.run(() => this.flushQueue(queue));\n                }\n            }\n            // Safeguard against infinite looping if somehow our dirty effect count gets out of sync with\n            // the dirty flag across all the effects.\n            if (!ranOneEffect) {\n                this.dirtyEffectCount = 0;\n            }\n        }\n    }\n    flushQueue(queue) {\n        let ranOneEffect = false;\n        for (const handle of queue) {\n            if (!handle.dirty) {\n                continue;\n            }\n            this.dirtyEffectCount--;\n            ranOneEffect = true;\n            // TODO: what happens if this throws an error?\n            handle.run();\n        }\n        return ranOneEffect;\n    }\n}\n\nexport { AFTER_RENDER_SEQUENCES_TO_ADD, CHILD_HEAD, CHILD_TAIL, CLEANUP, CONTAINER_HEADER_OFFSET, CONTEXT, ChangeDetectionScheduler, CheckNoChangesMode, DECLARATION_COMPONENT_VIEW, DECLARATION_LCONTAINER, DECLARATION_VIEW, DEHYDRATED_VIEWS, DOCUMENT, DestroyRef, EFFECTS, EFFECTS_TO_SCHEDULE, EMBEDDED_VIEW_INJECTOR, EMPTY_ARRAY, EMPTY_OBJ, ENVIRONMENT, ENVIRONMENT_INITIALIZER, EffectScheduler, EnvironmentInjector, ErrorHandler, FLAGS, HEADER_OFFSET, HOST, HYDRATION, ID, INJECTOR$1 as INJECTOR, INJECTOR as INJECTOR$1, INJECTOR_DEF_TYPES, INJECTOR_SCOPE, INTERNAL_APPLICATION_ERROR_HANDLER, InjectionToken, Injector, MATH_ML_NAMESPACE, MOVED_VIEWS, NATIVE, NEXT, NG_COMP_DEF, NG_DIR_DEF, NG_ELEMENT_ID, NG_FACTORY_DEF, NG_INJ_DEF, NG_MOD_DEF, NG_PIPE_DEF, NG_PROV_DEF, NodeInjectorDestroyRef, NullInjector, ON_DESTROY_HOOKS, PARENT, PREORDER_HOOK_FLAGS, PROVIDED_ZONELESS, PendingTasks, PendingTasksInternal, QUERIES, R3Injector, REACTIVE_TEMPLATE_CONSUMER, RENDERER, RuntimeError, SCHEDULE_IN_ROOT_ZONE, SVG_NAMESPACE, TVIEW, T_HOST, VIEW_REFS, ViewContext, XSS_SECURITY_URL, ZONELESS_ENABLED, ZONELESS_SCHEDULER_DISABLED, _global, addToArray, arrayEquals, arrayInsert2, arraySplice, assertComponentType, assertDefined, assertDirectiveDef, assertDomNode, assertElement, assertEqual, assertFirstCreatePass, assertFirstUpdatePass, assertFunction, assertGreaterThan, assertGreaterThanOrEqual, assertHasParent, assertInInjectionContext, assertIndexInDeclRange, assertIndexInExpandoRange, assertIndexInRange, assertInjectImplementationNotEqual, assertLContainer, assertLView, assertLessThan, assertNgModuleType, assertNodeInjector, assertNotDefined, assertNotEqual, assertNotInReactiveContext, assertNotReactive, assertNotSame, assertNumber, assertNumberInRange, assertOneOf, assertParentView, assertProjectionSlots, assertSame, assertString, assertTIcu, assertTNode, assertTNodeForLView, assertTNodeForTView, attachInjectFlag, concatStringsWithSpace, convertToBitFlags, createInjector, createInjectorWithoutInjectorInstances, debugStringifyTypeForError, decreaseElementDepthCount, deepForEach, defineInjectable, emitEffectCreatedEvent, emitInjectEvent, emitInjectorToCreateInstanceEvent, emitInstanceCreatedByInjectorEvent, emitProviderConfiguredEvent, enterDI, enterSkipHydrationBlock, enterView, errorHandlerEnvironmentInitializer, fillProperties, flatten, formatRuntimeError, forwardRef, getBindingIndex, getBindingRoot, getBindingsEnabled, getClosureSafeProperty, getComponentDef, getComponentLViewByIndex, getConstant, getContextLView, getCurrentDirectiveDef, getCurrentDirectiveIndex, getCurrentParentTNode, getCurrentQueryIndex, getCurrentTNode, getCurrentTNodePlaceholderOk, getDirectiveDef, getDirectiveDefOrThrow, getElementDepthCount, getFactoryDef, getInjectableDef, getInjectorDef, getLView, getLViewParent, getNamespace, getNativeByIndex, getNativeByTNode, getNativeByTNodeOrNull, getNgModuleDef, getNgModuleDefOrThrow, getNullInjector, getOrCreateLViewCleanup, getOrCreateTViewCleanup, getPipeDef, getSelectedIndex, getSelectedTNode, getTNode, getTView, hasI18n, importProvidersFrom, increaseElementDepthCount, incrementBindingIndex, initNgDevMode, inject, injectRootLimpMode, internalImportProvidersFrom, isClassProvider, isComponentDef, isComponentHost, isContentQueryHost, isCreationMode, isCurrentTNodeParent, isDestroyed, isDirectiveHost, isEnvironmentProviders, isExhaustiveCheckNoChanges, isForwardRef, isInCheckNoChangesMode, isInI18nBlock, isInInjectionContext, isInSkipHydrationBlock, isInjectable, isLContainer, isLView, isProjectionTNode, isRefreshingViews, isRootView, isSignal, isSkipHydrationRootTNode, isStandalone, isTypeProvider, isWritableSignal, keyValueArrayGet, keyValueArrayIndexOf, keyValueArraySet, lastNodeWasCreated, leaveDI, leaveSkipHydrationBlock, leaveView, load, makeEnvironmentProviders, markAncestorsForTraversal, markViewForRefresh, newArray, nextBindingIndex, nextContextImpl, noop, provideBrowserGlobalErrorListeners, provideEnvironmentInitializer, providerToFactory, removeFromArray, removeLViewOnDestroy, renderStringify, requiresRefreshOrTraversal, resetPreOrderHookFlags, resolveForwardRef, runInInjectionContext, runInInjectorProfilerContext, setBindingIndex, setBindingRootForHostBindings, setCurrentDirectiveIndex, setCurrentQueryIndex, setCurrentTNode, setCurrentTNodeAsNotParent, setInI18nBlock, setInjectImplementation, setInjectorProfiler, setInjectorProfilerContext, setIsInCheckNoChangesMode, setIsRefreshingViews, setSelectedIndex, signal, signalAsReadonlyFn, store, storeCleanupWithContext, storeLViewOnDestroy, stringify, stringifyForError, throwCyclicDependencyError, throwError, throwProviderNotFoundError, truncateMiddle, unwrapLView, unwrapRNode, updateAncestorTraversalFlagsOnAttach, viewAttachedToChangeDetector, viewAttachedToContainer, walkProviderTree, walkUpViews, wasLastNodeCreated, ɵunwrapWritableSignal, ɵɵdefineInjectable, ɵɵdefineInjector, ɵɵdisableBindings, ɵɵenableBindings, ɵɵinject, ɵɵinvalidFactoryDep, ɵɵnamespaceHTML, ɵɵnamespaceMathML, ɵɵnamespaceSVG, ɵɵresetView, ɵɵrestoreView };\n\n", "/**\n * @license Angular v20.0.7\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { inject, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DestroyRef, RuntimeError, formatRuntimeError, assertNotInReactiveContext, assertInInjectionContext, Injector, ViewContext, ChangeDetectionScheduler, EffectScheduler, setInjectorProfilerContext, emitEffectCreatedEvent, EFFECTS, NodeInjectorDestroyRef, FLAGS, markAncestorsForTraversal, noop, setIsRefreshingViews, signalAsReadonlyFn, PendingTasks, signal } from './root_effect_scheduler.mjs';\nimport { setActiveConsumer, createComputed, SIGNAL, consumerDestroy, REACTIVE_NODE, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal.mjs';\nimport { untracked as untracked$1, createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn } from './untracked.mjs';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nclass OutputEmitterRef {\n    destroyed = false;\n    listeners = null;\n    errorHandler = inject(ErrorHandler, { optional: true });\n    /** @internal */\n    destroyRef = inject(DestroyRef);\n    constructor() {\n        // Clean-up all listeners and mark as destroyed upon destroy.\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n            this.listeners = null;\n        });\n    }\n    subscribe(callback) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        (this.listeners ??= []).push(callback);\n        return {\n            unsubscribe: () => {\n                const idx = this.listeners?.indexOf(callback);\n                if (idx !== undefined && idx !== -1) {\n                    this.listeners?.splice(idx, 1);\n                }\n            },\n        };\n    }\n    /** Emits a new value to the output. */\n    emit(value) {\n        if (this.destroyed) {\n            console.warn(formatRuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected emit for destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.'));\n            return;\n        }\n        if (this.listeners === null) {\n            return;\n        }\n        const previousConsumer = setActiveConsumer(null);\n        try {\n            for (const listenerFn of this.listeners) {\n                try {\n                    listenerFn(value);\n                }\n                catch (err) {\n                    this.errorHandler?.handleError(err);\n                }\n            }\n        }\n        finally {\n            setActiveConsumer(previousConsumer);\n        }\n    }\n}\n/** Gets the owning `DestroyRef` for the given output. */\nfunction getOutputDestroyRef(ref) {\n    return ref.destroyRef;\n}\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n    return untracked$1(nonReactiveReadsFn);\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nfunction computed(computation, options) {\n    const getter = createComputed(computation, options?.equal);\n    if (ngDevMode) {\n        getter.toString = () => `[Computed: ${getter()}]`;\n        getter[SIGNAL].debugName = options?.debugName;\n    }\n    return getter;\n}\n\nclass EffectRefImpl {\n    [SIGNAL];\n    constructor(node) {\n        this[SIGNAL] = node;\n    }\n    destroy() {\n        this[SIGNAL].destroy();\n    }\n}\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n *\n * @publicApi 20.0\n */\nfunction effect(effectFn, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(effect, 'Call `effect` outside of a reactive context. For example, schedule the ' +\n            'effect inside the component constructor.');\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(effect);\n    }\n    if (ngDevMode && options?.allowSignalWrites !== undefined) {\n        console.warn(`The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n    let node;\n    const viewContext = injector.get(ViewContext, null, { optional: true });\n    const notifier = injector.get(ChangeDetectionScheduler);\n    if (viewContext !== null) {\n        // This effect was created in the context of a view, and will be associated with the view.\n        node = createViewEffect(viewContext.view, notifier, effectFn);\n        if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n            // The effect is being created in the same view as the `DestroyRef` references, so it will be\n            // automatically destroyed without the need for an explicit `DestroyRef` registration.\n            destroyRef = null;\n        }\n    }\n    else {\n        // This effect was created outside the context of a view, and will be scheduled independently.\n        node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n    }\n    node.injector = injector;\n    if (destroyRef !== null) {\n        // If we need to register for cleanup, do that here.\n        node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n    }\n    const effectRef = new EffectRefImpl(node);\n    if (ngDevMode) {\n        node.debugName = options?.debugName ?? '';\n        const prevInjectorProfilerContext = setInjectorProfilerContext({ injector, token: null });\n        try {\n            emitEffectCreatedEvent(effectRef);\n        }\n        finally {\n            setInjectorProfilerContext(prevInjectorProfilerContext);\n        }\n    }\n    return effectRef;\n}\nconst BASE_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: true,\n    dirty: true,\n    hasRun: false,\n    cleanupFns: undefined,\n    zone: null,\n    kind: 'effect',\n    onDestroyFn: noop,\n    run() {\n        this.dirty = false;\n        if (ngDevMode && isInNotificationPhase()) {\n            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n        }\n        if (this.hasRun && !consumerPollProducersForChange(this)) {\n            return;\n        }\n        this.hasRun = true;\n        const registerCleanupFn = (cleanupFn) => (this.cleanupFns ??= []).push(cleanupFn);\n        const prevNode = consumerBeforeComputation(this);\n        // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n        // cause CD to reach the component in question.\n        const prevRefreshingViews = setIsRefreshingViews(false);\n        try {\n            this.maybeCleanup();\n            this.fn(registerCleanupFn);\n        }\n        finally {\n            setIsRefreshingViews(prevRefreshingViews);\n            consumerAfterComputation(this, prevNode);\n        }\n    },\n    maybeCleanup() {\n        if (!this.cleanupFns?.length) {\n            return;\n        }\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n            // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n            // from the cleanup function will still crash the current run of the effect.\n            while (this.cleanupFns.length) {\n                this.cleanupFns.pop()();\n            }\n        }\n        finally {\n            this.cleanupFns = [];\n            setActiveConsumer(prevConsumer);\n        }\n    },\n}))();\nconst ROOT_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty() {\n        this.scheduler.schedule(this);\n        this.notifier.notify(12 /* NotificationSource.RootEffect */);\n    },\n    destroy() {\n        consumerDestroy(this);\n        this.onDestroyFn();\n        this.maybeCleanup();\n        this.scheduler.remove(this);\n    },\n}))();\nconst VIEW_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty() {\n        this.view[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n        markAncestorsForTraversal(this.view);\n        this.notifier.notify(13 /* NotificationSource.ViewEffect */);\n    },\n    destroy() {\n        consumerDestroy(this);\n        this.onDestroyFn();\n        this.maybeCleanup();\n        this.view[EFFECTS]?.delete(this);\n    },\n}))();\nfunction createViewEffect(view, notifier, fn) {\n    const node = Object.create(VIEW_EFFECT_NODE);\n    node.view = view;\n    node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n    node.notifier = notifier;\n    node.fn = fn;\n    view[EFFECTS] ??= new Set();\n    view[EFFECTS].add(node);\n    node.consumerMarkedDirty(node);\n    return node;\n}\nfunction createRootEffect(fn, scheduler, notifier) {\n    const node = Object.create(ROOT_EFFECT_NODE);\n    node.fn = fn;\n    node.scheduler = scheduler;\n    node.notifier = notifier;\n    node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n    node.scheduler.add(node);\n    node.notifier.notify(12 /* NotificationSource.RootEffect */);\n    return node;\n}\n\nconst identityFn = (v) => v;\nfunction linkedSignal(optionsOrComputation, options) {\n    if (typeof optionsOrComputation === 'function') {\n        const getter = createLinkedSignal(optionsOrComputation, (identityFn), options?.equal);\n        return upgradeLinkedSignalGetter(getter);\n    }\n    else {\n        const getter = createLinkedSignal(optionsOrComputation.source, optionsOrComputation.computation, optionsOrComputation.equal);\n        return upgradeLinkedSignalGetter(getter);\n    }\n}\nfunction upgradeLinkedSignalGetter(getter) {\n    if (ngDevMode) {\n        getter.toString = () => `[LinkedSignal: ${getter()}]`;\n    }\n    const node = getter[SIGNAL];\n    const upgradedGetter = getter;\n    upgradedGetter.set = (newValue) => linkedSignalSetFn(node, newValue);\n    upgradedGetter.update = (updateFn) => linkedSignalUpdateFn(node, updateFn);\n    upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter);\n    return upgradedGetter;\n}\n\n/**\n * Whether a `Resource.value()` should throw an error when the resource is in the error state.\n *\n * This internal flag is being used to gradually roll out this behavior.\n */\nconst RESOURCE_VALUE_THROWS_ERRORS_DEFAULT = true;\nfunction resource(options) {\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(resource);\n    }\n    const oldNameForParams = options.request;\n    const params = (options.params ?? oldNameForParams ?? (() => null));\n    return new ResourceImpl(params, getLoader(options), options.defaultValue, options.equal ? wrapEqualityFn(options.equal) : undefined, options.injector ?? inject(Injector), RESOURCE_VALUE_THROWS_ERRORS_DEFAULT);\n}\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nclass BaseWritableResource {\n    value;\n    constructor(value) {\n        this.value = value;\n        this.value.set = this.set.bind(this);\n        this.value.update = this.update.bind(this);\n        this.value.asReadonly = signalAsReadonlyFn;\n    }\n    isError = computed(() => this.status() === 'error');\n    update(updateFn) {\n        this.set(updateFn(untracked(this.value)));\n    }\n    isLoading = computed(() => this.status() === 'loading' || this.status() === 'reloading');\n    hasValue() {\n        // Note: we specifically read `isError()` instead of `status()` here to avoid triggering\n        // reactive consumers which read `hasValue()`. This way, if `hasValue()` is used inside of an\n        // effect, it doesn't cause the effect to rerun on every status change.\n        if (this.isError()) {\n            return false;\n        }\n        return this.value() !== undefined;\n    }\n    asReadonly() {\n        return this;\n    }\n}\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nclass ResourceImpl extends BaseWritableResource {\n    loaderFn;\n    equal;\n    pendingTasks;\n    /**\n     * The current state of the resource. Status, value, and error are derived from this.\n     */\n    state;\n    /**\n     * Combines the current request with a reload counter which allows the resource to be reloaded on\n     * imperative command.\n     */\n    extRequest;\n    effectRef;\n    pendingController;\n    resolvePendingTask = undefined;\n    destroyed = false;\n    unregisterOnDestroy;\n    constructor(request, loaderFn, defaultValue, equal, injector, throwErrorsFromValue = RESOURCE_VALUE_THROWS_ERRORS_DEFAULT) {\n        super(\n        // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n        // `WritableSignal` that delegates to `ResourceImpl.set`.\n        computed(() => {\n            const streamValue = this.state().stream?.();\n            if (!streamValue) {\n                return defaultValue;\n            }\n            // Prevents `hasValue()` from throwing an error when a reload happened in the error state\n            if (this.state().status === 'loading' && this.error()) {\n                return defaultValue;\n            }\n            if (!isResolved(streamValue)) {\n                if (throwErrorsFromValue) {\n                    throw new ResourceValueError(this.error());\n                }\n                else {\n                    return defaultValue;\n                }\n            }\n            return streamValue.value;\n        }, { equal }));\n        this.loaderFn = loaderFn;\n        this.equal = equal;\n        // Extend `request()` to include a writable reload signal.\n        this.extRequest = linkedSignal({\n            source: request,\n            computation: (request) => ({ request, reload: 0 }),\n        });\n        // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n        // state instantaneously when the request signal changes.\n        this.state = linkedSignal({\n            // Whenever the request changes,\n            source: this.extRequest,\n            // Compute the state of the resource given a change in status.\n            computation: (extRequest, previous) => {\n                const status = extRequest.request === undefined ? 'idle' : 'loading';\n                if (!previous) {\n                    return {\n                        extRequest,\n                        status,\n                        previousStatus: 'idle',\n                        stream: undefined,\n                    };\n                }\n                else {\n                    return {\n                        extRequest,\n                        status,\n                        previousStatus: projectStatusOfState(previous.value),\n                        // If the request hasn't changed, keep the previous stream.\n                        stream: previous.value.extRequest.request === extRequest.request\n                            ? previous.value.stream\n                            : undefined,\n                    };\n                }\n            },\n        });\n        this.effectRef = effect(this.loadEffect.bind(this), {\n            injector,\n            manualCleanup: true,\n        });\n        this.pendingTasks = injector.get(PendingTasks);\n        // Cancel any pending request when the resource itself is destroyed.\n        this.unregisterOnDestroy = injector.get(DestroyRef).onDestroy(() => this.destroy());\n    }\n    status = computed(() => projectStatusOfState(this.state()));\n    error = computed(() => {\n        const stream = this.state().stream?.();\n        return stream && !isResolved(stream) ? stream.error : undefined;\n    });\n    /**\n     * Called either directly via `WritableResource.set` or via `.value.set()`.\n     */\n    set(value) {\n        if (this.destroyed) {\n            return;\n        }\n        const error = untracked(this.error);\n        const state = untracked(this.state);\n        if (!error) {\n            const current = untracked(this.value);\n            if (state.status === 'local' &&\n                (this.equal ? this.equal(current, value) : current === value)) {\n                return;\n            }\n        }\n        // Enter Local state with the user-defined value.\n        this.state.set({\n            extRequest: state.extRequest,\n            status: 'local',\n            previousStatus: 'local',\n            stream: signal({ value }),\n        });\n        // We're departing from whatever state the resource was in previously, so cancel any in-progress\n        // loading operations.\n        this.abortInProgressLoad();\n    }\n    reload() {\n        // We don't want to restart in-progress loads.\n        const { status } = untracked(this.state);\n        if (status === 'idle' || status === 'loading') {\n            return false;\n        }\n        // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n        this.extRequest.update(({ request, reload }) => ({ request, reload: reload + 1 }));\n        return true;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.unregisterOnDestroy();\n        this.effectRef.destroy();\n        this.abortInProgressLoad();\n        // Destroyed resources enter Idle state.\n        this.state.set({\n            extRequest: { request: undefined, reload: 0 },\n            status: 'idle',\n            previousStatus: 'idle',\n            stream: undefined,\n        });\n    }\n    async loadEffect() {\n        const extRequest = this.extRequest();\n        // Capture the previous status before any state transitions. Note that this is `untracked` since\n        // we do not want the effect to depend on the state of the resource, only on the request.\n        const { status: currentStatus, previousStatus } = untracked(this.state);\n        if (extRequest.request === undefined) {\n            // Nothing to load (and we should already be in a non-loading state).\n            return;\n        }\n        else if (currentStatus !== 'loading') {\n            // We're not in a loading or reloading state, so this loading request is stale.\n            return;\n        }\n        // Cancel any previous loading attempts.\n        this.abortInProgressLoad();\n        // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n        // resolve it twice:\n        //\n        //  1. when the loading function promise resolves/rejects\n        //  2. when cancelling the loading operation\n        //\n        // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n        // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n        // response to (1) below, we need to cancel the locally saved task.\n        let resolvePendingTask = (this.resolvePendingTask =\n            this.pendingTasks.add());\n        const { signal: abortSignal } = (this.pendingController = new AbortController());\n        try {\n            // The actual loading is run through `untracked` - only the request side of `resource` is\n            // reactive. This avoids any confusion with signals tracking or not tracking depending on\n            // which side of the `await` they are.\n            const stream = await untracked(() => {\n                return this.loaderFn({\n                    params: extRequest.request,\n                    // TODO(alxhub): cleanup after g3 removal of `request` alias.\n                    request: extRequest.request,\n                    abortSignal,\n                    previous: {\n                        status: previousStatus,\n                    },\n                });\n            });\n            // If this request has been aborted, or the current request no longer\n            // matches this load, then we should ignore this resolution.\n            if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n                return;\n            }\n            this.state.set({\n                extRequest,\n                status: 'resolved',\n                previousStatus: 'resolved',\n                stream,\n            });\n        }\n        catch (err) {\n            if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n                return;\n            }\n            this.state.set({\n                extRequest,\n                status: 'resolved',\n                previousStatus: 'error',\n                stream: signal({ error: encapsulateResourceError(err) }),\n            });\n        }\n        finally {\n            // Resolve the pending task now that the resource has a value.\n            resolvePendingTask?.();\n            resolvePendingTask = undefined;\n        }\n    }\n    abortInProgressLoad() {\n        untracked(() => this.pendingController?.abort());\n        this.pendingController = undefined;\n        // Once the load is aborted, we no longer want to block stability on its resolution.\n        this.resolvePendingTask?.();\n        this.resolvePendingTask = undefined;\n    }\n}\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn(equal) {\n    return (a, b) => (a === undefined || b === undefined ? a === b : equal(a, b));\n}\nfunction getLoader(options) {\n    if (isStreamingResourceOptions(options)) {\n        return options.stream;\n    }\n    return async (params) => {\n        try {\n            return signal({ value: await options.loader(params) });\n        }\n        catch (err) {\n            return signal({ error: encapsulateResourceError(err) });\n        }\n    };\n}\nfunction isStreamingResourceOptions(options) {\n    return !!options.stream;\n}\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state) {\n    switch (state.status) {\n        case 'loading':\n            return state.extRequest.reload === 0 ? 'loading' : 'reloading';\n        case 'resolved':\n            return isResolved(state.stream()) ? 'resolved' : 'error';\n        default:\n            return state.status;\n    }\n}\nfunction isResolved(state) {\n    return state.error === undefined;\n}\nfunction encapsulateResourceError(error) {\n    if (error instanceof Error) {\n        return error;\n    }\n    return new ResourceWrappedError(error);\n}\nclass ResourceValueError extends Error {\n    constructor(error) {\n        super(ngDevMode\n            ? `Resource is currently in an error state (see Error.cause for details): ${error.message}`\n            : error.message, { cause: error });\n    }\n}\nclass ResourceWrappedError extends Error {\n    constructor(error) {\n        super(ngDevMode\n            ? `Resource returned an error that's not an Error instance: ${String(error)}. Check this error's .cause for the actual error.`\n            : String(error), { cause: error });\n    }\n}\n\nexport { OutputEmitterRef, ResourceImpl, computed, effect, encapsulateResourceError, getOutputDestroyRef, linkedSignal, resource, untracked };\n\n"], "mappings": ";;;;;;;;;;;AAYA,IAAI,mBAAmB;AACvB,SAAS,qBAAqB;AAC1B,SAAO;AACX;AACA,SAAS,mBAAmB,UAAU;AAClC,QAAM,SAAS;AACf,qBAAmB;AACnB,SAAO;AACX;AAMA,IAAM,YAAY,OAAO,UAAU;AAKnC,IAAM,gBAAN,cAA4B,MAAM;AAAA,EAC9B,OAAO;AAAA,EACP,YAAY,SAAS;AACjB,UAAM,OAAO;AAAA,EACjB;AACJ;AAIA,SAAS,WAAW,GAAG;AACnB,SAAO,MAAM,aAAa,GAAG,SAAS;AAC1C;;;ACjCA,SAAS,cAAc,GAAG,GAAG;AACzB,SAAO,OAAO,GAAG,GAAG,CAAC;AACzB;AAOA,IAAI,iBAAiB;AACrB,IAAI,sBAAsB;AAI1B,IAAI,QAAQ;AAIZ,IAAI,wBAAwB;AAM5B,IAAM,SAAyB,OAAO,QAAQ;AAC9C,SAAS,kBAAkB,UAAU;AACjC,QAAM,OAAO;AACb,mBAAiB;AACjB,SAAO;AACX;AACA,SAAS,oBAAoB;AACzB,SAAO;AACX;AACA,SAAS,wBAAwB;AAC7B,SAAO;AACX;AAIA,IAAM,gBAAgB;AAAA,EAClB,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,cAAc;AAAA,EACd,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,sBAAsB;AAAA,EACtB,MAAM;AAAA,EACN,uBAAuB,MAAM;AAAA,EAC7B,wBAAwB,MAAM;AAAA,EAAE;AAAA,EAChC,qBAAqB,MAAM;AAAA,EAAE;AAAA,EAC7B,sBAAsB,MAAM;AAAA,EAAE;AAClC;AAIA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,qBAAqB;AACrB,UAAM,IAAI,MAAM,OAAO,cAAc,eAAe,YAC9C,2DACA,EAAE;AAAA,EACZ;AACA,MAAI,mBAAmB,MAAM;AAEzB;AAAA,EACJ;AACA,iBAAe,qBAAqB,IAAI;AAExC,QAAM,MAAM,eAAe;AAC3B,qBAAmB,cAAc;AACjC,MAAI,MAAM,eAAe,aAAa,UAAU,eAAe,aAAa,GAAG,MAAM,MAAM;AAQvF,QAAI,eAAe,cAAc,GAAG;AAChC,YAAM,gBAAgB,eAAe,aAAa,GAAG;AACrD,wCAAkC,eAAe,eAAe,oBAAoB,GAAG,CAAC;AAAA,IAG5F;AAAA,EACJ;AACA,MAAI,eAAe,aAAa,GAAG,MAAM,MAAM;AAE3C,mBAAe,aAAa,GAAG,IAAI;AAGnC,mBAAe,oBAAoB,GAAG,IAAI,eAAe,cAAc,IACjE,wBAAwB,MAAM,gBAAgB,GAAG,IACjD;AAAA,EACV;AACA,iBAAe,wBAAwB,GAAG,IAAI,KAAK;AACvD;AAMA,SAAS,yBAAyB;AAC9B;AACJ;AAIA,SAAS,2BAA2B,MAAM;AACtC,MAAI,eAAe,IAAI,KAAK,CAAC,KAAK,OAAO;AAGrC;AAAA,EACJ;AACA,MAAI,CAAC,KAAK,SAAS,KAAK,mBAAmB,OAAO;AAI9C;AAAA,EACJ;AACA,MAAI,CAAC,KAAK,sBAAsB,IAAI,KAAK,CAAC,+BAA+B,IAAI,GAAG;AAG5E,sBAAkB,IAAI;AACtB;AAAA,EACJ;AACA,OAAK,uBAAuB,IAAI;AAEhC,oBAAkB,IAAI;AAC1B;AAIA,SAAS,wBAAwB,MAAM;AACnC,MAAI,KAAK,qBAAqB,QAAW;AACrC;AAAA,EACJ;AAEA,QAAM,OAAO;AACb,wBAAsB;AACtB,MAAI;AACA,eAAW,YAAY,KAAK,kBAAkB;AAC1C,UAAI,CAAC,SAAS,OAAO;AACjB,0BAAkB,QAAQ;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ,UACA;AACI,0BAAsB;AAAA,EAC1B;AACJ;AAKA,SAAS,yBAAyB;AAC9B,SAAO,gBAAgB,8BAA8B;AACzD;AACA,SAAS,kBAAkB,MAAM;AAC7B,OAAK,QAAQ;AACb,0BAAwB,IAAI;AAC5B,OAAK,sBAAsB,IAAI;AACnC;AACA,SAAS,kBAAkB,MAAM;AAC7B,OAAK,QAAQ;AACb,OAAK,iBAAiB;AAC1B;AAOA,SAAS,0BAA0B,MAAM;AACrC,WAAS,KAAK,oBAAoB;AAClC,SAAO,kBAAkB,IAAI;AACjC;AAOA,SAAS,yBAAyB,MAAM,cAAc;AAClD,oBAAkB,YAAY;AAC9B,MAAI,CAAC,QACD,KAAK,iBAAiB,UACtB,KAAK,wBAAwB,UAC7B,KAAK,4BAA4B,QAAW;AAC5C;AAAA,EACJ;AACA,MAAI,eAAe,IAAI,GAAG;AAGtB,aAAS,IAAI,KAAK,mBAAmB,IAAI,KAAK,aAAa,QAAQ,KAAK;AACpE,wCAAkC,KAAK,aAAa,CAAC,GAAG,KAAK,oBAAoB,CAAC,CAAC;AAAA,IACvF;AAAA,EACJ;AAIA,SAAO,KAAK,aAAa,SAAS,KAAK,mBAAmB;AACtD,SAAK,aAAa,IAAI;AACtB,SAAK,wBAAwB,IAAI;AACjC,SAAK,oBAAoB,IAAI;AAAA,EACjC;AACJ;AAKA,SAAS,+BAA+B,MAAM;AAC1C,qBAAmB,IAAI;AAEvB,WAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AAC/C,UAAM,WAAW,KAAK,aAAa,CAAC;AACpC,UAAM,cAAc,KAAK,wBAAwB,CAAC;AAGlD,QAAI,gBAAgB,SAAS,SAAS;AAClC,aAAO;AAAA,IACX;AAGA,+BAA2B,QAAQ;AAGnC,QAAI,gBAAgB,SAAS,SAAS;AAClC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAIA,SAAS,gBAAgB,MAAM;AAC3B,qBAAmB,IAAI;AACvB,MAAI,eAAe,IAAI,GAAG;AAEtB,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AAC/C,wCAAkC,KAAK,aAAa,CAAC,GAAG,KAAK,oBAAoB,CAAC,CAAC;AAAA,IACvF;AAAA,EACJ;AAEA,OAAK,aAAa,SACd,KAAK,wBAAwB,SACzB,KAAK,oBAAoB,SACrB;AACZ,MAAI,KAAK,kBAAkB;AACvB,SAAK,iBAAiB,SAAS,KAAK,wBAAwB,SAAS;AAAA,EACzE;AACJ;AAOA,SAAS,wBAAwB,MAAM,UAAU,aAAa;AAC1D,qBAAmB,IAAI;AACvB,MAAI,KAAK,iBAAiB,WAAW,KAAK,eAAe,IAAI,GAAG;AAE5D,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AAC/C,WAAK,oBAAoB,CAAC,IAAI,wBAAwB,KAAK,aAAa,CAAC,GAAG,MAAM,CAAC;AAAA,IACvF;AAAA,EACJ;AACA,OAAK,wBAAwB,KAAK,WAAW;AAC7C,SAAO,KAAK,iBAAiB,KAAK,QAAQ,IAAI;AAClD;AAIA,SAAS,kCAAkC,MAAM,KAAK;AAClD,qBAAmB,IAAI;AACvB,MAAI,OAAO,cAAc,eAAe,aAAa,OAAO,KAAK,iBAAiB,QAAQ;AACtF,UAAM,IAAI,MAAM,0CAA0C,GAAG,wBAAwB,KAAK,iBAAiB,MAAM,aAAa;AAAA,EAClI;AACA,MAAI,KAAK,iBAAiB,WAAW,KAAK,eAAe,IAAI,GAAG;AAI5D,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AAC/C,wCAAkC,KAAK,aAAa,CAAC,GAAG,KAAK,oBAAoB,CAAC,CAAC;AAAA,IACvF;AAAA,EACJ;AAGA,QAAM,UAAU,KAAK,iBAAiB,SAAS;AAC/C,OAAK,iBAAiB,GAAG,IAAI,KAAK,iBAAiB,OAAO;AAC1D,OAAK,wBAAwB,GAAG,IAAI,KAAK,wBAAwB,OAAO;AAExE,OAAK,iBAAiB;AACtB,OAAK,wBAAwB;AAG7B,MAAI,MAAM,KAAK,iBAAiB,QAAQ;AACpC,UAAM,cAAc,KAAK,wBAAwB,GAAG;AACpD,UAAM,WAAW,KAAK,iBAAiB,GAAG;AAC1C,uBAAmB,QAAQ;AAC3B,aAAS,oBAAoB,WAAW,IAAI;AAAA,EAChD;AACJ;AACA,SAAS,eAAe,MAAM;AAC1B,SAAO,KAAK,yBAAyB,MAAM,kBAAkB,UAAU,KAAK;AAChF;AACA,SAAS,mBAAmB,MAAM;AAC9B,OAAK,iBAAiB,CAAC;AACvB,OAAK,wBAAwB,CAAC;AAC9B,OAAK,4BAA4B,CAAC;AACtC;AACA,SAAS,mBAAmB,MAAM;AAC9B,OAAK,qBAAqB,CAAC;AAC3B,OAAK,4BAA4B,CAAC;AACtC;AACA,SAAS,eAAe,MAAM;AAC1B,SAAO,KAAK,iBAAiB;AACjC;AACA,SAAS,yBAAyB,MAAM;AACpC,0BAAwB,IAAI;AAChC;AAUA,SAAS,eAAe,aAAa,OAAO;AACxC,QAAM,OAAO,OAAO,OAAO,aAAa;AACxC,OAAK,cAAc;AACnB,MAAI,UAAU,QAAW;AACrB,SAAK,QAAQ;AAAA,EACjB;AACA,QAAMA,YAAW,MAAM;AAEnB,+BAA2B,IAAI;AAE/B,qBAAiB,IAAI;AACrB,QAAI,KAAK,UAAU,SAAS;AACxB,YAAM,KAAK;AAAA,IACf;AACA,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,MAAM,IAAI;AACnB,MAAI,OAAO,cAAc,eAAe,WAAW;AAC/C,UAAM,YAAY,KAAK,YAAY,OAAO,KAAK,YAAY,MAAM;AACjE,IAAAA,UAAS,WAAW,MAAM,YAAY,SAAS,KAAK,KAAK,KAAK;AAAA,EAClE;AACA,2BAAyB,IAAI;AAC7B,SAAOA;AACX;AAKA,IAAM,QAAwB,OAAO,OAAO;AAM5C,IAAM,YAA4B,OAAO,WAAW;AAMpD,IAAM,UAA0B,OAAO,SAAS;AAIhD,IAAM,iBAAiC,MAAM;AACzC,SAAO,iCACA,gBADA;AAAA,IAEH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,sBAAsB,MAAM;AAGxB,aAAO,KAAK,UAAU,SAAS,KAAK,UAAU;AAAA,IAClD;AAAA,IACA,uBAAuB,MAAM;AACzB,UAAI,KAAK,UAAU,WAAW;AAE1B,cAAM,IAAI,MAAM,OAAO,cAAc,eAAe,YAAY,oCAAoC,EAAE;AAAA,MAC1G;AACA,YAAM,WAAW,KAAK;AACtB,WAAK,QAAQ;AACb,YAAM,eAAe,0BAA0B,IAAI;AACnD,UAAI;AACJ,UAAI,WAAW;AACf,UAAI;AACA,mBAAW,KAAK,YAAY;AAG5B,0BAAkB,IAAI;AACtB,mBACI,aAAa,SACT,aAAa,WACb,aAAa,WACb,KAAK,MAAM,UAAU,QAAQ;AAAA,MACzC,SACO,KAAK;AACR,mBAAW;AACX,aAAK,QAAQ;AAAA,MACjB,UACA;AACI,iCAAyB,MAAM,YAAY;AAAA,MAC/C;AACA,UAAI,UAAU;AAGV,aAAK,QAAQ;AACb;AAAA,MACJ;AACA,WAAK,QAAQ;AACb,WAAK;AAAA,IACT;AAAA,EACJ;AACJ,GAAG;AAEH,SAAS,oBAAoB;AACzB,QAAM,IAAI,MAAM;AACpB;AACA,IAAI,mCAAmC;AACvC,SAAS,+BAA+B,MAAM;AAC1C,mCAAiC,IAAI;AACzC;AACA,SAAS,kCAAkC,IAAI;AAC3C,qCAAmC;AACvC;AAQA,IAAI,kBAAkB;AAItB,SAAS,aAAa,cAAc,OAAO;AACvC,QAAM,OAAO,OAAO,OAAO,WAAW;AACtC,OAAK,QAAQ;AACb,MAAI,UAAU,QAAW;AACrB,SAAK,QAAQ;AAAA,EACjB;AACA,QAAM,SAAU,MAAM,YAAY,IAAI;AACtC,SAAO,MAAM,IAAI;AACjB,MAAI,OAAO,cAAc,eAAe,WAAW;AAC/C,UAAM,YAAY,KAAK,YAAY,OAAO,KAAK,YAAY,MAAM;AACjE,WAAO,WAAW,MAAM,UAAU,SAAS,KAAK,KAAK,KAAK;AAAA,EAC9D;AACA,2BAAyB,IAAI;AAC7B,QAAM,MAAM,CAAC,aAAa,YAAY,MAAM,QAAQ;AACpD,QAAM,SAAS,CAAC,aAAa,eAAe,MAAM,QAAQ;AAC1D,SAAO,CAAC,QAAQ,KAAK,MAAM;AAC/B;AAMA,SAAS,YAAY,MAAM;AACvB,mBAAiB,IAAI;AACrB,SAAO,KAAK;AAChB;AACA,SAAS,YAAY,MAAM,UAAU;AACjC,MAAI,CAAC,uBAAuB,GAAG;AAC3B,mCAA+B,IAAI;AAAA,EACvC;AACA,MAAI,CAAC,KAAK,MAAM,KAAK,OAAO,QAAQ,GAAG;AACnC,SAAK,QAAQ;AACb,uBAAmB,IAAI;AAAA,EAC3B;AACJ;AACA,SAAS,eAAe,MAAM,SAAS;AACnC,MAAI,CAAC,uBAAuB,GAAG;AAC3B,mCAA+B,IAAI;AAAA,EACvC;AACA,cAAY,MAAM,QAAQ,KAAK,KAAK,CAAC;AACzC;AAOA,IAAM,eAA+B,MAAM;AACvC,SAAO,iCACA,gBADA;AAAA,IAEH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,EACV;AACJ,GAAG;AACH,SAAS,mBAAmB,MAAM;AAC9B,OAAK;AACL,yBAAuB;AACvB,0BAAwB,IAAI;AAC5B,oBAAkB,IAAI;AAC1B;;;AChgBA,SAAS,mBAAmB,UAAU,eAAe,YAAY;AAC7D,QAAM,OAAO,OAAO,OAAO,kBAAkB;AAC7C,OAAK,SAAS;AACd,OAAK,cAAc;AACnB,MAAI,cAAc,QAAW;AACzB,SAAK,QAAQ;AAAA,EACjB;AACA,QAAM,qBAAqB,MAAM;AAE7B,+BAA2B,IAAI;AAE/B,qBAAiB,IAAI;AACrB,QAAI,KAAK,UAAU,SAAS;AACxB,YAAM,KAAK;AAAA,IACf;AACA,WAAO,KAAK;AAAA,EAChB;AACA,QAAM,SAAS;AACf,SAAO,MAAM,IAAI;AACjB,MAAI,OAAO,cAAc,eAAe,WAAW;AAC/C,UAAM,YAAY,KAAK,YAAY,OAAO,KAAK,YAAY,MAAM;AACjE,WAAO,WAAW,MAAM,gBAAgB,SAAS,KAAK,KAAK,KAAK;AAAA,EACpE;AACA,2BAAyB,IAAI;AAC7B,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM,UAAU;AACvC,6BAA2B,IAAI;AAC/B,cAAY,MAAM,QAAQ;AAC1B,oBAAkB,IAAI;AAC1B;AACA,SAAS,qBAAqB,MAAM,SAAS;AACzC,6BAA2B,IAAI;AAC/B,iBAAe,MAAM,OAAO;AAC5B,oBAAkB,IAAI;AAC1B;AAIA,IAAM,sBAAsC,MAAM;AAC9C,SAAO,iCACA,gBADA;AAAA,IAEH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,sBAAsB,MAAM;AAGxB,aAAO,KAAK,UAAU,SAAS,KAAK,UAAU;AAAA,IAClD;AAAA,IACA,uBAAuB,MAAM;AACzB,UAAI,KAAK,UAAU,WAAW;AAE1B,cAAM,IAAI,MAAM,OAAO,cAAc,eAAe,YAAY,oCAAoC,EAAE;AAAA,MAC1G;AACA,YAAM,WAAW,KAAK;AACtB,WAAK,QAAQ;AACb,YAAM,eAAe,0BAA0B,IAAI;AACnD,UAAI;AACJ,UAAI;AACA,cAAM,iBAAiB,KAAK,OAAO;AACnC,cAAM,OAAO,aAAa,SAAS,aAAa,UAC1C,SACA;AAAA,UACE,QAAQ,KAAK;AAAA,UACb,OAAO;AAAA,QACX;AACJ,mBAAW,KAAK,YAAY,gBAAgB,IAAI;AAChD,aAAK,cAAc;AAAA,MACvB,SACO,KAAK;AACR,mBAAW;AACX,aAAK,QAAQ;AAAA,MACjB,UACA;AACI,iCAAyB,MAAM,YAAY;AAAA,MAC/C;AACA,UAAI,aAAa,SAAS,aAAa,WAAW,KAAK,MAAM,UAAU,QAAQ,GAAG;AAG9E,aAAK,QAAQ;AACb;AAAA,MACJ;AACA,WAAK,QAAQ;AACb,WAAK;AAAA,IACT;AAAA,EACJ;AACJ,GAAG;AAMH,SAAS,UAAU,oBAAoB;AACnC,QAAM,eAAe,kBAAkB,IAAI;AAG3C,MAAI;AACA,WAAO,mBAAmB;AAAA,EAC9B,UACA;AACI,sBAAkB,YAAY;AAAA,EAClC;AACJ;;;AC3GA,SAAS,wBAAwB,MAAM;AAEvC;;;AC4DA,IAAM,kBAAkB,MAAM;AAAE;AAIhC,IAAM,cAA8B,MAAM;AACtC,SAAO,iCACA,gBADA;AAAA,IAEH,sBAAsB;AAAA,IACtB,2BAA2B;AAAA,IAC3B,qBAAqB,CAAC,SAAS;AAC3B,UAAI,KAAK,aAAa,MAAM;AACxB,aAAK,SAAS,KAAK,GAAG;AAAA,MAC1B;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,EACf;AACJ,GAAG;;;AClEH,IAAM,8BAA8B;AAIpC,IAAM,mBAAmB;AAkBzB,IAAM,eAAN,cAA2B,MAAM;AAAA,EAC7B;AAAA,EACA,YAAY,MAAM,SAAS;AACvB,UAAM,mBAAmB,MAAM,OAAO,CAAC;AACvC,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,SAAS,uBAAuB,MAAM;AAIlC,SAAO,MAAM,KAAK,IAAI,IAAI,CAAC;AAC/B;AAKA,SAAS,mBAAmB,MAAM,SAAS;AACvC,QAAM,WAAW,uBAAuB,IAAI;AAC5C,MAAI,eAAe,GAAG,QAAQ,GAAG,UAAU,OAAO,UAAU,EAAE;AAC9D,MAAI,aAAa,OAAO,GAAG;AACvB,UAAM,qBAAqB,CAAC,aAAa,MAAM,YAAY;AAC3D,UAAM,YAAY,qBAAqB,MAAM;AAC7C,mBAAe,GAAG,YAAY,GAAG,SAAS,iBAAiB,2BAA2B,IAAI,QAAQ;AAAA,EACtG;AACA,SAAO;AACX;AAEA,IAAM,UAAU;AAEhB,SAAS,6BAA6B;AAClC,QAAM,iBAAiB,OAAO,aAAa,cAAc,SAAS,SAAS,IAAI;AAC/E,QAAM,cAAc;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,qCAAqC;AAAA,EACzC;AAEA,QAAM,qBAAqB,eAAe,QAAQ,iBAAiB,MAAM;AACzE,MAAI,CAAC,oBAAoB;AACrB,YAAQ,WAAW,IAAI;AAAA,EAC3B,OACK;AACD,QAAI,OAAO,QAAQ,WAAW,MAAM,UAAU;AAC1C,cAAQ,WAAW,IAAI,CAAC;AAAA,IAC5B;AACA,WAAO,OAAO,QAAQ,WAAW,GAAG,WAAW;AAAA,EACnD;AACA,SAAO;AACX;AAmBA,SAAS,gBAAgB;AAKrB,MAAI,OAAO,cAAc,eAAe,WAAW;AAC/C,QAAI,OAAO,cAAc,YAAY,OAAO,KAAK,SAAS,EAAE,WAAW,GAAG;AACtE,iCAA2B;AAAA,IAC/B;AACA,WAAO,OAAO,cAAc,eAAe,CAAC,CAAC;AAAA,EACjD;AACA,SAAO;AACX;AAEA,SAAS,uBAAuB,0BAA0B;AACtD,WAAS,OAAO,0BAA0B;AACtC,QAAI,yBAAyB,GAAG,MAAM,wBAAwB;AAC1D,aAAO;AAAA,IACX;AAAA,EACJ;AAGA,QAAM,MAAM,OAAO,cAAc,eAAe,YAC1C,sDACA,EAAE;AACZ;AAOA,SAAS,eAAe,QAAQ,QAAQ;AACpC,aAAW,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,KAAK,CAAC,OAAO,eAAe,GAAG,GAAG;AAC3D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC5B;AAAA,EACJ;AACJ;AAEA,SAAS,UAAU,OAAO;AACtB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,IAAI,MAAM,IAAI,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC9C;AACA,MAAI,SAAS,MAAM;AACf,WAAO,KAAK;AAAA,EAChB;AACA,QAAM,OAAO,MAAM,kBAAkB,MAAM;AAC3C,MAAI,MAAM;AACN,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,QAAM,SAAS,MAAM,SAAS;AAC9B,MAAI,UAAU,MAAM;AAChB,WAAO,KAAK;AAAA,EAChB;AACA,QAAM,eAAe,OAAO,QAAQ,IAAI;AACxC,SAAO,gBAAgB,IAAI,OAAO,MAAM,GAAG,YAAY,IAAI;AAC/D;AASA,SAAS,uBAAuB,QAAQ,OAAO;AAC3C,MAAI,CAAC;AACD,WAAO,SAAS;AACpB,MAAI,CAAC;AACD,WAAO;AACX,SAAO,GAAG,MAAM,IAAI,KAAK;AAC7B;AAQA,SAAS,eAAe,KAAK,YAAY,KAAK;AAC1C,MAAI,CAAC,OAAO,YAAY,KAAK,IAAI,UAAU;AACvC,WAAO;AACX,MAAI,aAAa;AACb,WAAO,IAAI,UAAU,GAAG,CAAC,IAAI;AACjC,QAAM,YAAY,KAAK,MAAM,YAAY,CAAC;AAC1C,SAAO,IAAI,UAAU,GAAG,SAAS,IAAI,QAAQ,IAAI,UAAU,IAAI,SAAS,SAAS;AACrF;AAEA,IAAM,kBAAkB,uBAAuB,EAAE,iBAAiB,uBAAuB,CAAC;AA0C1F,SAAS,WAAW,cAAc;AAC9B,eAAa,kBAAkB;AAC/B,eAAa,WAAW,WAAY;AAChC,WAAO,UAAU,KAAK,CAAC;AAAA,EAC3B;AACA,SAAO;AACX;AAcA,SAAS,kBAAkB,MAAM;AAC7B,SAAO,aAAa,IAAI,IAAI,KAAK,IAAI;AACzC;AAEA,SAAS,aAAa,IAAI;AACtB,SAAQ,OAAO,OAAO,cAClB,GAAG,eAAe,eAAe,KACjC,GAAG,oBAAoB;AAC/B;AAKA,SAAS,aAAa,QAAQ,KAAK;AAC/B,MAAI,EAAE,OAAO,WAAW,WAAW;AAC/B,eAAW,KAAK,OAAO,QAAQ,UAAU,KAAK;AAAA,EAClD;AACJ;AACA,SAAS,oBAAoB,QAAQ,cAAc,cAAc;AAC7D,eAAa,QAAQ,mBAAmB;AACxC,wBAAsB,QAAQ,cAAc,6CAA6C;AACzF,2BAAyB,QAAQ,cAAc,gDAAgD;AACnG;AACA,SAAS,aAAa,QAAQ,KAAK;AAC/B,MAAI,EAAE,OAAO,WAAW,WAAW;AAC/B,eAAW,KAAK,WAAW,OAAO,SAAS,OAAO,QAAQ,UAAU,KAAK;AAAA,EAC7E;AACJ;AACA,SAAS,eAAe,QAAQ,KAAK;AACjC,MAAI,EAAE,OAAO,WAAW,aAAa;AACjC,eAAW,KAAK,WAAW,OAAO,SAAS,OAAO,QAAQ,YAAY,KAAK;AAAA,EAC/E;AACJ;AACA,SAAS,YAAY,QAAQ,UAAU,KAAK;AACxC,MAAI,EAAE,UAAU,WAAW;AACvB,eAAW,KAAK,QAAQ,UAAU,IAAI;AAAA,EAC1C;AACJ;AACA,SAAS,eAAe,QAAQ,UAAU,KAAK;AAC3C,MAAI,EAAE,UAAU,WAAW;AACvB,eAAW,KAAK,QAAQ,UAAU,IAAI;AAAA,EAC1C;AACJ;AACA,SAAS,WAAW,QAAQ,UAAU,KAAK;AACvC,MAAI,EAAE,WAAW,WAAW;AACxB,eAAW,KAAK,QAAQ,UAAU,KAAK;AAAA,EAC3C;AACJ;AACA,SAAS,cAAc,QAAQ,UAAU,KAAK;AAC1C,MAAI,EAAE,WAAW,WAAW;AACxB,eAAW,KAAK,QAAQ,UAAU,KAAK;AAAA,EAC3C;AACJ;AACA,SAAS,eAAe,QAAQ,UAAU,KAAK;AAC3C,MAAI,EAAE,SAAS,WAAW;AACtB,eAAW,KAAK,QAAQ,UAAU,GAAG;AAAA,EACzC;AACJ;AACA,SAAS,sBAAsB,QAAQ,UAAU,KAAK;AAClD,MAAI,EAAE,UAAU,WAAW;AACvB,eAAW,KAAK,QAAQ,UAAU,IAAI;AAAA,EAC1C;AACJ;AACA,SAAS,kBAAkB,QAAQ,UAAU,KAAK;AAC9C,MAAI,EAAE,SAAS,WAAW;AACtB,eAAW,KAAK,QAAQ,UAAU,GAAG;AAAA,EACzC;AACJ;AACA,SAAS,yBAAyB,QAAQ,UAAU,KAAK;AACrD,MAAI,EAAE,UAAU,WAAW;AACvB,eAAW,KAAK,QAAQ,UAAU,IAAI;AAAA,EAC1C;AACJ;AACA,SAAS,iBAAiB,QAAQ,KAAK;AACnC,MAAI,UAAU,MAAM;AAChB,eAAW,KAAK,QAAQ,MAAM,IAAI;AAAA,EACtC;AACJ;AACA,SAAS,cAAc,QAAQ,KAAK;AAChC,MAAI,UAAU,MAAM;AAChB,eAAW,KAAK,QAAQ,MAAM,IAAI;AAAA,EACtC;AACJ;AACA,SAAS,WAAW,KAAK,QAAQ,UAAU,YAAY;AACnD,QAAM,IAAI,MAAM,oBAAoB,GAAG,MAClC,cAAc,OAAO,KAAK,gBAAgB,QAAQ,IAAI,UAAU,IAAI,MAAM,aAAa;AAChG;AACA,SAAS,cAAc,MAAM;AACzB,MAAI,EAAE,gBAAgB,OAAO;AACzB,eAAW,gEAAgE,UAAU,IAAI,CAAC,EAAE;AAAA,EAChG;AACJ;AACA,SAAS,cAAc,MAAM;AACzB,MAAI,EAAE,gBAAgB,UAAU;AAC5B,eAAW,iDAAiD,UAAU,IAAI,CAAC,EAAE;AAAA,EACjF;AACJ;AACA,SAAS,mBAAmB,KAAK,OAAO;AACpC,gBAAc,KAAK,wBAAwB;AAC3C,QAAM,SAAS,IAAI;AACnB,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAC9B,eAAW,kCAAkC,MAAM,YAAY,KAAK,EAAE;AAAA,EAC1E;AACJ;AACA,SAAS,YAAY,UAAU,aAAa;AACxC,MAAI,YAAY,QAAQ,KAAK,MAAM;AAC/B,WAAO;AACX,aAAW,+BAA+B,KAAK,UAAU,WAAW,CAAC,YAAY,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7G;AACA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,kBAAkB,MAAM,MAAM;AAC9B,eAAW,GAAG,EAAE,kDAAkD;AAAA,EACtE;AACJ;AAoBA,SAAS,mBAAmB,MAAM;AAC9B,SAAO;AAAA,IACH,OAAO,KAAK;AAAA,IACZ,YAAY,KAAK,cAAc;AAAA,IAC/B,SAAS,KAAK;AAAA,IACd,OAAO;AAAA,EACX;AACJ;AAMA,IAAM,mBAAmB;AAkBzB,SAAS,iBAAiB,SAAS;AAC/B,SAAO,EAAE,WAAW,QAAQ,aAAa,CAAC,GAAG,SAAS,QAAQ,WAAW,CAAC,EAAE;AAChF;AAOA,SAAS,iBAAiB,MAAM;AAC5B,SAAO,iBAAiB,MAAM,WAAW;AAC7C;AACA,SAAS,aAAa,MAAM;AACxB,SAAO,iBAAiB,IAAI,MAAM;AACtC;AAKA,SAAS,iBAAiB,MAAM,OAAO;AAEnC,SAAQ,KAAK,eAAe,KAAK,KAAK,KAAK,KAAK,KAAM;AAC1D;AASA,SAAS,0BAA0B,MAAM;AAErC,QAAM,MAAM,OAAO,WAAW,KAAK;AACnC,MAAI,KAAK;AACL,iBACI,QAAQ,KAAK,4CAA4C,KAAK,IAAI;AAAA,6FACgC,KAAK,IAAI,UAAU;AACzH,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAMA,SAAS,eAAe,MAAM;AAC1B,SAAO,QAAQ,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AACxE;AACA,IAAM,cAAc,uBAAuB,EAAE,OAAO,uBAAuB,CAAC;AAC5E,IAAM,aAAa,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAkD1E,IAAM,iBAAN,MAAqB;AAAA,EACjB;AAAA;AAAA,EAEA,iBAAiB;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO,SAAS;AACxB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,QAAI,OAAO,WAAW,UAAU;AAC5B,OAAC,OAAO,cAAc,eAAe,cACjC,eAAe,SAAS,GAAG,0CAA0C;AAGzE,WAAK,oBAAoB;AAAA,IAC7B,WACS,YAAY,QAAW;AAC5B,WAAK,QAAQ,mBAAmB;AAAA,QAC5B,OAAO;AAAA,QACP,YAAY,QAAQ,cAAc;AAAA,QAClC,SAAS,QAAQ;AAAA,MACrB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACR,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACP,WAAO,kBAAkB,KAAK,KAAK;AAAA,EACvC;AACJ;AAEA,IAAI;AACJ,SAAS,6BAA6B;AAClC,GAAC,aAAa,WAAW,sEAAsE;AAC/F,SAAO;AACX;AACA,SAAS,2BAA2B,SAAS;AACzC,GAAC,aAAa,WAAW,sEAAsE;AAC/F,QAAM,WAAW;AACjB,6BAA2B;AAC3B,SAAO;AACX;AACA,IAAM,4BAA4B,CAAC;AACnC,IAAM,wBAAwB,MAAM;AAAE;AACtC,SAAS,eAAe,UAAU;AAC9B,QAAM,cAAc,0BAA0B,QAAQ,QAAQ;AAC9D,MAAI,gBAAgB,IAAI;AACpB,8BAA0B,OAAO,aAAa,CAAC;AAAA,EACnD;AACJ;AAcA,SAAS,oBAAoBC,mBAAkB;AAC3C,GAAC,aAAa,WAAW,+DAA+D;AACxF,MAAIA,sBAAqB,MAAM;AAC3B,QAAI,CAAC,0BAA0B,SAASA,iBAAgB,GAAG;AACvD,gCAA0B,KAAKA,iBAAgB;AAAA,IACnD;AACA,WAAO,MAAM,eAAeA,iBAAgB;AAAA,EAChD,OACK;AACD,8BAA0B,SAAS;AACnC,WAAO;AAAA,EACX;AACJ;AAMA,SAAS,iBAAiB,OAAO;AAC7B,GAAC,aAAa,WAAW,6DAA6D;AACtF,WAAS,IAAI,GAAG,IAAI,0BAA0B,QAAQ,KAAK;AACvD,UAAM,2BAA2B,0BAA0B,CAAC;AAC5D,6BAAyB,KAAK;AAAA,EAClC;AACJ;AAOA,SAAS,4BAA4B,eAAe,iBAAiB,OAAO;AACxE,GAAC,aAAa,WAAW,6DAA6D;AACtF,MAAI;AAGJ,MAAI,OAAO,kBAAkB,YAAY;AACrC,YAAQ;AAAA,EACZ,WAES,yBAAyB,gBAAgB;AAC9C,YAAQ;AAAA,EACZ,OAEK;AACD,YAAQ,kBAAkB,cAAc,OAAO;AAAA,EACnD;AACA,MAAI,WAAW;AAIf,MAAI,yBAAyB,gBAAgB;AACzC,eAAW,cAAc,SAAS;AAAA,EACtC;AACA,mBAAiB;AAAA,IACb,MAAM;AAAA,IACN,SAAS,2BAA2B;AAAA,IACpC,gBAAgB,EAAE,OAAO,UAAU,eAAe;AAAA,EACtD,CAAC;AACL;AAOA,SAAS,kCAAkC,OAAO;AAC9C,GAAC,aAAa,WAAW,6DAA6D;AACtF,mBAAiB;AAAA,IACb,MAAM;AAAA,IACN,SAAS,2BAA2B;AAAA,IACpC;AAAA,EACJ,CAAC;AACL;AAOA,SAAS,mCAAmC,UAAU;AAClD,GAAC,aAAa,WAAW,6DAA6D;AACtF,mBAAiB;AAAA,IACb,MAAM;AAAA,IACN,SAAS,2BAA2B;AAAA,IACpC,UAAU,EAAE,OAAO,SAAS;AAAA,EAChC,CAAC;AACL;AAMA,SAAS,gBAAgB,OAAO,OAAO,OAAO;AAC1C,GAAC,aAAa,WAAW,6DAA6D;AACtF,mBAAiB;AAAA,IACb,MAAM;AAAA,IACN,SAAS,2BAA2B;AAAA,IACpC,SAAS,EAAE,OAAO,OAAO,MAAM;AAAA,EACnC,CAAC;AACL;AACA,SAAS,uBAAuBC,SAAQ;AACpC,GAAC,aAAa,WAAW,6DAA6D;AACtF,mBAAiB;AAAA,IACb,MAAM;AAAA,IACN,SAAS,2BAA2B;AAAA,IACpC,QAAAA;AAAA,EACJ,CAAC;AACL;AACA,SAAS,6BAA6B,UAAU,OAAO,UAAU;AAC7D,GAAC,aACG,WAAW,wEAAwE;AACvF,QAAM,oBAAoB,2BAA2B,EAAE,UAAU,MAAM,CAAC;AACxE,MAAI;AACA,aAAS;AAAA,EACb,UACA;AACI,+BAA2B,iBAAiB;AAAA,EAChD;AACJ;AAEA,SAAS,uBAAuB,OAAO;AACnC,SAAO,SAAS,CAAC,CAAC,MAAM;AAC5B;AAEA,IAAM,cAAc,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAC3E,IAAM,aAAa,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAC1E,IAAM,cAAc,uBAAuB,EAAE,OAAO,uBAAuB,CAAC;AAC5E,IAAM,aAAa,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAC1E,IAAM,iBAAiB,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAO9E,IAAM,gBAAgB,uBAAuB;AAAA,EACzC,mBAAmB;AACvB,CAAC;AASD,IAAM,YAAY,uBAAuB,EAAE,eAAe,uBAAuB,CAAC;AAQlF,SAAS,gBAAgB,OAAO;AAC5B,MAAI,OAAO,UAAU;AACjB,WAAO;AACX,MAAI,SAAS;AACT,WAAO;AAGX,SAAO,OAAO,KAAK;AACvB;AAOA,SAAS,kBAAkB,OAAO;AAC9B,MAAI,OAAO,UAAU;AACjB,WAAO,MAAM,QAAQ,MAAM,SAAS;AACxC,MAAI,OAAO,UAAU,YAAY,SAAS,QAAQ,OAAO,MAAM,SAAS,YAAY;AAChF,WAAO,MAAM,KAAK,QAAQ,MAAM,KAAK,SAAS;AAAA,EAClD;AACA,SAAO,gBAAgB,KAAK;AAChC;AAOA,SAAS,2BAA2B,MAAM;AAGtC,MAAI,eAAe,KAAK,WAAW,KAAK;AACxC,MAAI,iBAAiB,QAAQ,aAAa,WAAW;AACjD,WAAO,2BAA2B,aAAa,SAAS;AAAA,EAC5D;AACA,SAAO,kBAAkB,IAAI;AACjC;AAGA,SAAS,2BAA2B,WAAW;AAC3C,MAAI,CAAC,UAAU,YAAY,CAAC,UAAU,YAAY;AAC9C,WAAO,UAAU;AAAA,EACrB,OACK;AACD,WAAO,GAAG,UAAU,SAAS,QAAQ,UAAU,QAAQ,IAAI,UAAU,UAAU;AAAA,EACnF;AACJ;AAGA,SAAS,2BAA2B,OAAO,MAAM;AAC7C,QAAM,IAAI,aAAa,MAAkD,YACnE,0CAA0C,KAAK,GAAG,OAAO,sBAAsB,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE,KACjH,KAAK;AACf;AACA,SAAS,+BAA+B;AACpC,QAAM,IAAI,MAAM,kDAAkD;AACtE;AACA,SAAS,0BAA0B,cAAc,WAAW,UAAU;AAClE,MAAI,gBAAgB,WAAW;AAC3B,UAAM,iBAAiB,UAAU,IAAI,CAAC,MAAO,KAAK,WAAW,MAAM,WAAW,MAAM,KAAM;AAC1F,UAAM,IAAI,MAAM,sCAAsC,UAAU,YAAY,CAAC,8DAA8D,eAAe,KAAK,IAAI,CAAC,GAAG;AAAA,EAC3K,WACS,uBAAuB,QAAQ,GAAG;AACvC,QAAI,SAAS,eAAe;AACxB,YAAM,IAAI,aAAa,KAAsD,kJAAkJ;AAAA,IACnO,OACK;AACD,YAAM,IAAI,aAAa,KAAsD,wHAAwH;AAAA,IACzM;AAAA,EACJ,OACK;AACD,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACtC;AACJ;AAEA,SAAS,2BAA2B,OAAO,cAAc;AACrD,QAAM,eAAe,aACjB,mBAAmB,kBAAkB,KAAK,CAAC,SAAS,eAAe,OAAO,YAAY,KAAK,EAAE;AACjG,QAAM,IAAI,aAAa,MAAgD,YAAY;AACvF;AAWA,IAAI;AACJ,SAAS,0BAA0B;AAC/B,SAAO;AACX;AAIA,SAAS,wBAAwB,MAAM;AACnC,QAAM,WAAW;AACjB,0BAAwB;AACxB,SAAO;AACX;AAQA,SAAS,mBAAmB,OAAO,eAAe,OAAO;AACrD,QAAM,gBAAgB,iBAAiB,KAAK;AAC5C,MAAI,iBAAiB,cAAc,cAAc,QAAQ;AACrD,WAAO,cAAc,UAAU,SACxB,cAAc,QAAQ,cAAc,QAAQ,IAC7C,cAAc;AAAA,EACxB;AACA,MAAI,QAAQ;AACR,WAAO;AACX,MAAI,kBAAkB;AAClB,WAAO;AACX,6BAA2B,OAAO,UAAU;AAChD;AAQA,SAAS,mCAAmC,IAAI;AAC5C,eACI,eAAe,uBAAuB,IAAI,iDAAiD;AACnG;AAEA,IAAM,sBAAsB,CAAC;AAC7B,IAAM,qBAAqB;AAM3B,IAAM,oBAAoB;AAM1B,IAAM,qBAAN,MAAyB;AAAA,EACrB;AAAA,EACA,YAAY,UAAU;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,SAAS,OAAO,SAAS;AACrB,UAAM,QAAQ,kBAAkB,OAAO,KAAK;AAC5C,QAAI;AACA,aAAO,KAAK,SAAS;AAAA,QAAI;AAAA;AAAA,QAExB,QAAQ,IAAuC,OAAO;AAAA,QAAqB;AAAA,MAAK;AAAA,IACrF,SACO,GAAG;AACN,UAAI,WAAW,CAAC,GAAG;AACf,eAAO;AAAA,MACX;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;AACA,IAAM,qBAAqB;AAC3B,IAAM,gBAAgB;AACtB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,SAAS;AACf,SAAS,mBAAmB,OAAO,QAAQ,GAAqC;AAC5E,QAAM,kBAAkB,mBAAmB;AAC3C,MAAI,oBAAoB,QAAW;AAC/B,UAAM,IAAI,aAAa,MAAuD,aAC1E,SAAS,UAAU,KAAK,CAAC,8MAA8M;AAAA,EAC/O,WACS,oBAAoB,MAAM;AAC/B,WAAO,mBAAmB,OAAO,QAAW,KAAK;AAAA,EACrD,OACK;AACD,UAAM,UAAU,uBAAuB,KAAK;AAC5C,UAAM,QAAQ,gBAAgB,SAAS,OAAO,OAAO;AACrD,iBAAa,gBAAgB,OAAO,OAAO,KAAK;AAChD,QAAI,WAAW,KAAK,GAAG;AACnB,UAAI,QAAQ,UAAU;AAClB,eAAO;AAAA,MACX;AACA,YAAM;AAAA,IACV;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,SAAS,OAAO,QAAQ,GAAqC;AAClE,UAAQ,wBAAwB,KAAK,oBAAoB,kBAAkB,KAAK,GAAG,KAAK;AAC5F;AAUA,SAAS,oBAAoB,OAAO;AAChC,QAAM,IAAI,aAAa,KAAuD,aAC1E,wGAAwG,KAAK;AAAA;AAAA;AAAA,2DAG1D,KAAK,iGAAiG;AACjK;AAkEA,SAAS,OAAO,OAAO,SAAS;AAG5B,SAAO,SAAS,OAAO,kBAAkB,OAAO,CAAC;AACrD;AAEA,SAAS,kBAAkB,OAAO;AAC9B,MAAI,OAAO,UAAU,eAAe,OAAO,UAAU,UAAU;AAC3D,WAAO;AAAA,EACX;AAIA,SAAQ;AAAA,GACH,MAAM,YAAY,MAClB,MAAM,QAAQ,MACd,MAAM,QAAQ,MACd,MAAM,YAAY;AAC3B;AAEA,SAAS,uBAAuB,OAAO;AACnC,SAAO;AAAA,IACH,UAAU,CAAC,EAAE,QAAQ;AAAA,IACrB,MAAM,CAAC,EAAE,QAAQ;AAAA,IACjB,MAAM,CAAC,EAAE,QAAQ;AAAA,IACjB,UAAU,CAAC,EAAE,QAAQ;AAAA,EACzB;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,MAAM,kBAAkB,MAAM,CAAC,CAAC;AACtC,QAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,UAAI,IAAI,WAAW,GAAG;AAClB,cAAM,IAAI,aAAa,KAAiD,aAAa,sCAAsC;AAAA,MAC/H;AACA,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,cAAM,OAAO,IAAI,CAAC;AAClB,cAAM,OAAO,cAAc,IAAI;AAC/B,YAAI,OAAO,SAAS,UAAU;AAE1B,cAAI,SAAS,IAAgC;AACzC,mBAAO,KAAK;AAAA,UAChB,OACK;AACD,qBAAS;AAAA,UACb;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,WAAK,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,IACnC,OACK;AACD,WAAK,KAAK,SAAS,GAAG,CAAC;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AAWA,SAAS,iBAAiB,WAAW,MAAM;AACvC,YAAU,iBAAiB,IAAI;AAC/B,YAAU,UAAU,iBAAiB,IAAI;AACzC,SAAO;AACX;AAMA,SAAS,cAAc,OAAO;AAC1B,SAAO,MAAM,iBAAiB;AAClC;AACA,SAAS,mBAAmB,GAAG,OAAO,mBAAmB,QAAQ;AAC7D,QAAM,YAAY,EAAE,kBAAkB;AACtC,MAAI,MAAM,MAAM,GAAG;AACf,cAAU,QAAQ,MAAM,MAAM,CAAC;AAAA,EACnC;AACA,IAAE,UAAU,YAAY,OAAO,EAAE,SAAS,WAAW,mBAAmB,MAAM;AAC9E,IAAE,aAAa,IAAI;AACnB,IAAE,kBAAkB,IAAI;AACxB,QAAM;AACV;AACA,SAAS,YAAY,MAAM,KAAK,mBAAmB,SAAS,MAAM;AAC9D,SAAO,QAAQ,KAAK,OAAO,CAAC,MAAM,QAAQ,KAAK,OAAO,CAAC,KAAK,cAAc,KAAK,MAAM,CAAC,IAAI;AAC1F,MAAI,UAAU,UAAU,GAAG;AAC3B,MAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,cAAU,IAAI,IAAI,SAAS,EAAE,KAAK,MAAM;AAAA,EAC5C,WACS,OAAO,QAAQ,UAAU;AAC9B,QAAI,QAAQ,CAAC;AACb,aAAS,OAAO,KAAK;AACjB,UAAI,IAAI,eAAe,GAAG,GAAG;AACzB,YAAI,QAAQ,IAAI,GAAG;AACnB,cAAM,KAAK,MAAM,OAAO,OAAO,UAAU,WAAW,KAAK,UAAU,KAAK,IAAI,UAAU,KAAK,EAAE;AAAA,MACjG;AAAA,IACJ;AACA,cAAU,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EAClC;AACA,SAAO,GAAG,iBAAiB,GAAG,SAAS,MAAM,SAAS,MAAM,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,UAAU,MAAM,CAAC;AACjH;AAEA,SAAS,cAAc,MAAM,eAAe;AACxC,QAAM,gBAAgB,KAAK,eAAe,cAAc;AACxD,MAAI,CAAC,iBAAiB,kBAAkB,QAAQ,WAAW;AACvD,UAAM,IAAI,MAAM,QAAQ,UAAU,IAAI,CAAC,iCAAiC;AAAA,EAC5E;AACA,SAAO,gBAAgB,KAAK,cAAc,IAAI;AAClD;AAUA,SAAS,YAAY,GAAG,GAAG,kBAAkB;AACzC,MAAI,EAAE,WAAW,EAAE;AACf,WAAO;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,QAAI,SAAS,EAAE,CAAC;AAChB,QAAI,SAAS,EAAE,CAAC;AAChB,QAAI,kBAAkB;AAClB,eAAS,iBAAiB,MAAM;AAChC,eAAS,iBAAiB,MAAM;AAAA,IACpC;AACA,QAAI,WAAW,QAAQ;AACnB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,MAAM;AACnB,SAAO,KAAK,KAAK,OAAO,iBAAiB;AAC7C;AACA,SAAS,YAAY,OAAO,IAAI;AAC5B,QAAM,QAAQ,CAAC,UAAW,MAAM,QAAQ,KAAK,IAAI,YAAY,OAAO,EAAE,IAAI,GAAG,KAAK,CAAE;AACxF;AACA,SAAS,WAAW,KAAK,OAAO,OAAO;AAEnC,MAAI,SAAS,IAAI,QAAQ;AACrB,QAAI,KAAK,KAAK;AAAA,EAClB,OACK;AACD,QAAI,OAAO,OAAO,GAAG,KAAK;AAAA,EAC9B;AACJ;AACA,SAAS,gBAAgB,KAAK,OAAO;AAEjC,MAAI,SAAS,IAAI,SAAS,GAAG;AACzB,WAAO,IAAI,IAAI;AAAA,EACnB,OACK;AACD,WAAO,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC;AAAA,EACjC;AACJ;AACA,SAAS,SAAS,MAAM,OAAO;AAC3B,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,SAAK,KAAK,KAAK;AAAA,EACnB;AACA,SAAO;AACX;AAcA,SAAS,YAAY,OAAO,OAAO,OAAO;AACtC,QAAM,SAAS,MAAM,SAAS;AAC9B,SAAO,QAAQ,QAAQ;AACnB,UAAM,KAAK,IAAI,MAAM,QAAQ,KAAK;AAClC;AAAA,EACJ;AACA,SAAO,SAAS;AACZ,UAAM,IAAI;AAAA,EACd;AACJ;AAaA,SAAS,aAAa,OAAO,OAAO,QAAQ,QAAQ;AAChD,eAAa,sBAAsB,OAAO,MAAM,QAAQ,8BAA8B;AACtF,MAAI,MAAM,MAAM;AAChB,MAAI,OAAO,OAAO;AAEd,UAAM,KAAK,QAAQ,MAAM;AAAA,EAC7B,WACS,QAAQ,GAAG;AAEhB,UAAM,KAAK,QAAQ,MAAM,CAAC,CAAC;AAC3B,UAAM,CAAC,IAAI;AAAA,EACf,OACK;AACD;AACA,UAAM,KAAK,MAAM,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AACrC,WAAO,MAAM,OAAO;AAChB,YAAM,cAAc,MAAM;AAC1B,YAAM,GAAG,IAAI,MAAM,WAAW;AAC9B;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AACf,UAAM,QAAQ,CAAC,IAAI;AAAA,EACvB;AACJ;AASA,SAAS,iBAAiB,eAAe,KAAK,OAAO;AACjD,MAAI,QAAQ,qBAAqB,eAAe,GAAG;AACnD,MAAI,SAAS,GAAG;AAEZ,kBAAc,QAAQ,CAAC,IAAI;AAAA,EAC/B,OACK;AACD,YAAQ,CAAC;AACT,iBAAa,eAAe,OAAO,KAAK,KAAK;AAAA,EACjD;AACA,SAAO;AACX;AAQA,SAAS,iBAAiB,eAAe,KAAK;AAC1C,QAAM,QAAQ,qBAAqB,eAAe,GAAG;AACrD,MAAI,SAAS,GAAG;AAEZ,WAAO,cAAc,QAAQ,CAAC;AAAA,EAClC;AACA,SAAO;AACX;AAWA,SAAS,qBAAqB,eAAe,KAAK;AAC9C,SAAO,oBAAoB,eAAe,KAAK,CAAC;AACpD;AAkBA,SAAS,oBAAoB,OAAO,OAAO,OAAO;AAC9C,eAAa,YAAY,MAAM,QAAQ,KAAK,GAAG,MAAM,oBAAoB;AACzE,MAAI,QAAQ;AACZ,MAAI,MAAM,MAAM,UAAU;AAC1B,SAAO,QAAQ,OAAO;AAClB,UAAM,SAAS,SAAU,MAAM,SAAU;AACzC,UAAM,UAAU,MAAM,UAAU,KAAK;AACrC,QAAI,UAAU,SAAS;AACnB,aAAO,UAAU;AAAA,IACrB,WACS,UAAU,OAAO;AACtB,YAAM;AAAA,IACV,OACK;AACD,cAAQ,SAAS;AAAA,IACrB;AAAA,EACJ;AACA,SAAO,EAAE,OAAO;AACpB;AAQA,IAAM,YAAY,CAAC;AACnB,IAAM,cAAc,CAAC;AAErB,KAAK,OAAO,cAAc,eAAe,cAAc,cAAc,GAAG;AAIpE,SAAO,OAAO,SAAS;AAEvB,SAAO,OAAO,WAAW;AAC7B;AAeA,IAAM,0BAA0B,IAAI,eAAe,YAAY,4BAA4B,EAAE;AAU7F,IAAM,aAAa,IAAI;AAAA,EAAe,YAAY,aAAa;AAAA;AAAA;AAAA,EAG/D;AAAA;AAAiC;AAEjC,IAAM,qBAAqB,IAAI,eAAe,YAAY,uBAAuB,EAAE;AAEnF,IAAM,eAAN,MAAmB;AAAA,EACf,IAAI,OAAO,gBAAgB,oBAAoB;AAC3C,QAAI,kBAAkB,oBAAoB;AACtC,YAAM,QAAQ,IAAI,cAAc,sCAAsC,UAAU,KAAK,CAAC,GAAG;AACzF,YAAM;AAAA,IACV;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,eAAe,MAAM;AAC1B,SAAO,KAAK,UAAU,KAAK;AAC/B;AACA,SAAS,sBAAsB,MAAM;AACjC,QAAM,cAAc,eAAe,IAAI;AACvC,MAAI,CAAC,aAAa;AACd,UAAM,IAAI,aAAa,MAA0D,OAAO,cAAc,eAAe,cACjH,QAAQ,UAAU,IAAI,CAAC,iCAAiC;AAAA,EAChE;AACA,SAAO;AACX;AAMA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,KAAK,WAAW,KAAK;AAChC;AACA,SAAS,uBAAuB,MAAM;AAClC,QAAM,MAAM,gBAAgB,IAAI;AAChC,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,aAAa,MAA0D,OAAO,cAAc,eAAe,cACjH,QAAQ,UAAU,IAAI,CAAC,iCAAiC;AAAA,EAChE;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,KAAK,UAAU,KAAK;AAC/B;AACA,SAAS,WAAW,MAAM;AACtB,SAAO,KAAK,WAAW,KAAK;AAChC;AASA,SAAS,aAAa,MAAM;AACxB,QAAM,MAAM,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,KAAK,WAAW,IAAI;AAC7E,SAAO,QAAQ,QAAQ,IAAI;AAC/B;AAQA,SAAS,yBAAyB,WAAW;AACzC,SAAO;AAAA,IACH,YAAY;AAAA,EAChB;AACJ;AA4BA,SAAS,8BAA8B,eAAe;AAClD,SAAO,yBAAyB;AAAA,IAC5B;AAAA,MACI,SAAS;AAAA,MACT,OAAO;AAAA,MACP,UAAU;AAAA,IACd;AAAA,EACJ,CAAC;AACL;AAyCA,SAAS,uBAAuB,SAAS;AACrC,SAAO;AAAA,IACH,YAAY,4BAA4B,MAAM,OAAO;AAAA,IACrD,eAAe;AAAA,EACnB;AACJ;AACA,SAAS,4BAA4B,0BAA0B,SAAS;AACpE,QAAM,eAAe,CAAC;AACtB,QAAM,QAAQ,oBAAI,IAAI;AACtB,MAAI;AACJ,QAAM,mBAAmB,CAAC,aAAa;AACnC,iBAAa,KAAK,QAAQ;AAAA,EAC9B;AACA,cAAY,SAAS,CAAC,WAAW;AAC7B,SAAK,OAAO,cAAc,eAAe,cAAc,uBAAuB;AAC1E,YAAM,SAAS,gBAAgB,MAAM;AACrC,UAAI,QAAQ,YAAY;AACpB,cAAM,IAAI,aAAa,KAA6D,gGAAgG,kBAAkB,MAAM,CAAC,GAAG;AAAA,MACpN;AAAA,IACJ;AAEA,UAAM,iBAAiB;AACvB,QAAI,iBAAiB,gBAAgB,kBAAkB,CAAC,GAAG,KAAK,GAAG;AAC/D,qCAA+B,CAAC;AAChC,iCAA2B,KAAK,cAAc;AAAA,IAClD;AAAA,EACJ,CAAC;AAED,MAAI,+BAA+B,QAAW;AAC1C,sCAAkC,4BAA4B,gBAAgB;AAAA,EAClF;AACA,SAAO;AACX;AAKA,SAAS,kCAAkC,oBAAoB,SAAS;AACpE,WAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAChD,UAAM,EAAE,UAAU,UAAU,IAAI,mBAAmB,CAAC;AACpD,wBAAoB,WAAW,CAAC,aAAa;AACzC,mBAAa,iBAAiB,UAAU,aAAa,aAAa,QAAQ;AAC1E,cAAQ,UAAU,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACL;AACJ;AAUA,SAAS,iBAAiB,WAAW,SAAS,SAAS,OAAO;AAC1D,cAAY,kBAAkB,SAAS;AACvC,MAAI,CAAC;AACD,WAAO;AAGX,MAAI,UAAU;AACd,MAAI,SAAS,eAAe,SAAS;AACrC,QAAM,SAAS,CAAC,UAAU,gBAAgB,SAAS;AACnD,MAAI,CAAC,UAAU,CAAC,QAAQ;AAMpB,UAAM,WAAW,UACZ;AACL,aAAS,eAAe,QAAQ;AAChC,QAAI,QAAQ;AACR,gBAAU;AAAA,IACd,OACK;AAED,aAAO;AAAA,IACX;AAAA,EACJ,WACS,UAAU,CAAC,OAAO,YAAY;AACnC,WAAO;AAAA,EACX,OACK;AACD,cAAU;AAAA,EACd;AAEA,MAAI,aAAa,QAAQ,QAAQ,OAAO,MAAM,IAAI;AAC9C,UAAM,UAAU,UAAU,OAAO;AACjC,UAAM,OAAO,QAAQ,IAAI,SAAS;AAClC,+BAA2B,SAAS,IAAI;AAAA,EAC5C;AAEA,QAAM,cAAc,MAAM,IAAI,OAAO;AACrC,MAAI,QAAQ;AACR,QAAI,aAAa;AAEb,aAAO;AAAA,IACX;AACA,UAAM,IAAI,OAAO;AACjB,QAAI,OAAO,cAAc;AACrB,YAAM,OAAO,OAAO,OAAO,iBAAiB,aAAa,OAAO,aAAa,IAAI,OAAO;AACxF,iBAAW,OAAO,MAAM;AACpB,yBAAiB,KAAK,SAAS,SAAS,KAAK;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ,WACS,QAAQ;AAEb,QAAI,OAAO,WAAW,QAAQ,CAAC,aAAa;AAGxC,mBAAa,QAAQ,KAAK,OAAO;AAEjC,YAAM,IAAI,OAAO;AACjB,UAAI;AACJ,UAAI;AACA,oBAAY,OAAO,SAAS,CAAC,aAAa;AACtC,cAAI,iBAAiB,UAAU,SAAS,SAAS,KAAK,GAAG;AACrD,yCAA6B,CAAC;AAG9B,qCAAyB,KAAK,QAAQ;AAAA,UAC1C;AAAA,QACJ,CAAC;AAAA,MACL,UACA;AAEI,qBAAa,QAAQ,IAAI;AAAA,MAC7B;AAIA,UAAI,6BAA6B,QAAW;AACxC,0CAAkC,0BAA0B,OAAO;AAAA,MACvE;AAAA,IACJ;AACA,QAAI,CAAC,aAAa;AAGd,YAAM,UAAU,cAAc,OAAO,MAAM,MAAM,IAAI,QAAQ;AAK7D,cAAQ,EAAE,SAAS,SAAS,YAAY,SAAS,MAAM,YAAY,GAAG,OAAO;AAE7E,cAAQ,EAAE,SAAS,oBAAoB,UAAU,SAAS,OAAO,KAAK,GAAG,OAAO;AAEhF,cAAQ,EAAE,SAAS,yBAAyB,UAAU,MAAM,SAAS,OAAO,GAAG,OAAO,KAAK,GAAG,OAAO;AAAA,IACzG;AAEA,UAAM,eAAe,OAAO;AAC5B,QAAI,gBAAgB,QAAQ,CAAC,aAAa;AACtC,YAAM,eAAe;AACrB,0BAAoB,cAAc,CAAC,aAAa;AAC5C,qBAAa,iBAAiB,UAAU,cAAc,YAAY;AAClE,gBAAQ,UAAU,YAAY;AAAA,MAClC,CAAC;AAAA,IACL;AAAA,EACJ,OACK;AAED,WAAO;AAAA,EACX;AACA,SAAQ,YAAY,aAAa,UAAU,cAAc;AAC7D;AACA,SAAS,iBAAiB,UAAU,WAAW,eAAe;AAC1D,MAAI,eAAe,QAAQ,KACvB,gBAAgB,QAAQ,KACxB,kBAAkB,QAAQ,KAC1B,mBAAmB,QAAQ,GAAG;AAC9B;AAAA,EACJ;AAEA,QAAM,WAAW,kBAAkB,aAAa,SAAS,YAAY,SAAS,QAAQ;AACtF,MAAI,CAAC,UAAU;AACX,8BAA0B,eAAe,WAAW,QAAQ;AAAA,EAChE;AACJ;AACA,SAAS,oBAAoB,WAAW,IAAI;AACxC,WAAS,YAAY,WAAW;AAC5B,QAAI,uBAAuB,QAAQ,GAAG;AAClC,iBAAW,SAAS;AAAA,IACxB;AACA,QAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,0BAAoB,UAAU,EAAE;AAAA,IACpC,OACK;AACD,SAAG,QAAQ;AAAA,IACf;AAAA,EACJ;AACJ;AACA,IAAM,YAAY,uBAAuB;AAAA,EACrC,SAAS;AAAA,EACT,UAAU;AACd,CAAC;AACD,SAAS,gBAAgB,OAAO;AAC5B,SAAO,UAAU,QAAQ,OAAO,SAAS,YAAY,aAAa;AACtE;AACA,SAAS,mBAAmB,OAAO;AAC/B,SAAO,CAAC,EAAE,SAAS,MAAM;AAC7B;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,CAAC,EAAE,SAAS,MAAM;AAC7B;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU;AAC5B;AACA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,CAAC,CAAC,MAAM;AACnB;AAOA,IAAM,iBAAiB,IAAI,eAAe,YAAY,wBAAwB,EAAE;AAKhF,IAAM,UAAU,CAAC;AAQjB,IAAM,WAAW,CAAC;AAIlB,IAAI,gBAAgB;AACpB,SAAS,kBAAkB;AACvB,MAAI,kBAAkB,QAAW;AAC7B,oBAAgB,IAAI,aAAa;AAAA,EACrC;AACA,SAAO;AACX;AAOA,IAAM,sBAAN,MAA0B;AAC1B;AACA,IAAM,aAAN,cAAyB,oBAAoB;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAIlB,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA,EAInB,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,WAAW,QAAQ,QAAQ,QAAQ;AAC3C,UAAM;AACN,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,SAAS;AAEd,0BAAsB,WAAW,CAAC,aAAa,KAAK,gBAAgB,QAAQ,CAAC;AAE7E,SAAK,QAAQ,IAAI,YAAY,WAAW,QAAW,IAAI,CAAC;AAExD,QAAI,OAAO,IAAI,aAAa,GAAG;AAC3B,WAAK,QAAQ,IAAI,qBAAqB,WAAW,QAAW,IAAI,CAAC;AAAA,IACrE;AAGA,UAAM,SAAS,KAAK,QAAQ,IAAI,cAAc;AAC9C,QAAI,UAAU,QAAQ,OAAO,OAAO,UAAU,UAAU;AACpD,WAAK,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC;AACA,SAAK,mBAAmB,IAAI,IAAI,KAAK,IAAI,oBAAoB,aAAa,EAAE,MAAM,KAAK,CAAC,CAAC;AAAA,EAC7F;AAAA,EACA,SAAS,OAAO,SAAS;AACrB,UAAM,QAAQ,kBAAkB,OAAO,KAAK;AAC5C,QAAI;AACA,aAAO,KAAK;AAAA,QAAI;AAAA;AAAA,QAEhB;AAAA,QAAoB;AAAA,MAAK;AAAA,IAC7B,SACO,GAAG;AACN,UAAI,WAAa,CAAC,GAAG;AACjB,eAAO;AAAA,MACX;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACN,uBAAmB,IAAI;AAEvB,SAAK,aAAa;AAClB,UAAM,eAAe,kBAAkB,IAAI;AAC3C,QAAI;AAEA,iBAAW,WAAW,KAAK,mBAAmB;AAC1C,gBAAQ,YAAY;AAAA,MACxB;AACA,YAAM,iBAAiB,KAAK;AAG5B,WAAK,kBAAkB,CAAC;AACxB,iBAAW,QAAQ,gBAAgB;AAC/B,aAAK;AAAA,MACT;AAAA,IACJ,UACA;AAEI,WAAK,QAAQ,MAAM;AACnB,WAAK,kBAAkB,MAAM;AAC7B,WAAK,iBAAiB,MAAM;AAC5B,wBAAkB,YAAY;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,uBAAmB,IAAI;AACvB,SAAK,gBAAgB,KAAK,QAAQ;AAClC,WAAO,MAAM,KAAK,gBAAgB,QAAQ;AAAA,EAC9C;AAAA,EACA,aAAa,IAAI;AACb,uBAAmB,IAAI;AACvB,UAAM,mBAAmB,mBAAmB,IAAI;AAChD,UAAM,+BAA+B,wBAAwB,MAAS;AACtE,QAAI;AACJ,QAAI,WAAW;AACX,0BAAoB,2BAA2B,EAAE,UAAU,MAAM,OAAO,KAAK,CAAC;AAAA,IAClF;AACA,QAAI;AACA,aAAO,GAAG;AAAA,IACd,UACA;AACI,yBAAmB,gBAAgB;AACnC,8BAAwB,4BAA4B;AACpD,mBAAa,2BAA2B,iBAAiB;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,IAAI,OAAO,gBAAgB,oBAAoB,SAAS;AACpD,uBAAmB,IAAI;AACvB,QAAI,MAAM,eAAe,SAAS,GAAG;AACjC,aAAO,MAAM,SAAS,EAAE,IAAI;AAAA,IAChC;AACA,UAAM,QAAQ,kBAAkB,OAAO;AAEvC,QAAI;AACJ,QAAI,WAAW;AACX,0BAAoB,2BAA2B,EAAE,UAAU,MAAM,MAAa,CAAC;AAAA,IACnF;AACA,UAAM,mBAAmB,mBAAmB,IAAI;AAChD,UAAM,+BAA+B,wBAAwB,MAAS;AACtE,QAAI;AAEA,UAAI,EAAE,QAAQ,IAAuC;AAEjD,YAAI,SAAS,KAAK,QAAQ,IAAI,KAAK;AACnC,YAAI,WAAW,QAAW;AAGtB,gBAAM,MAAM,sBAAsB,KAAK,KAAK,iBAAiB,KAAK;AAClE,cAAI,OAAO,KAAK,qBAAqB,GAAG,GAAG;AAGvC,gBAAI,WAAW;AACX,2CAA6B,MAAM,OAAO,MAAM;AAC5C,4CAA4B,KAAK;AAAA,cACrC,CAAC;AAAA,YACL;AACA,qBAAS,WAAW,kCAAkC,KAAK,GAAG,OAAO;AAAA,UACzE,OACK;AACD,qBAAS;AAAA,UACb;AACA,eAAK,QAAQ,IAAI,OAAO,MAAM;AAAA,QAClC;AAEA,YAAI,UAAU,MAAkC;AAC5C,iBAAO,KAAK,QAAQ,OAAO,MAAM;AAAA,QACrC;AAAA,MACJ;AAGA,YAAM,eAAe,EAAE,QAAQ,KAAoC,KAAK,SAAS,gBAAgB;AAGjG,sBACI,QAAQ,KAAwC,kBAAkB,qBAC5D,OACA;AACV,aAAO,aAAa,IAAI,OAAO,aAAa;AAAA,IAChD,SACO,GAAG;AACN,UAAI,WAAa,CAAC,GAAG;AAEjB,cAAM,OAAQ,EAAE,kBAAkB,IAAI,EAAE,kBAAkB,KAAK,CAAC;AAChE,aAAK,QAAQ,UAAU,KAAK,CAAC;AAC7B,YAAI,kBAAkB;AAElB,gBAAM;AAAA,QACV,OACK;AAED,iBAAO,mBAAmB,GAAG,OAAO,mBAAmB,KAAK,MAAM;AAAA,QACtE;AAAA,MACJ,OACK;AACD,cAAM;AAAA,MACV;AAAA,IACJ,UACA;AAEI,8BAAwB,4BAA4B;AACpD,yBAAmB,gBAAgB;AACnC,mBAAa,2BAA2B,iBAAiB;AAAA,IAC7D;AAAA,EACJ;AAAA;AAAA,EAEA,8BAA8B;AAC1B,UAAM,eAAe,kBAAkB,IAAI;AAC3C,UAAM,mBAAmB,mBAAmB,IAAI;AAChD,UAAM,+BAA+B,wBAAwB,MAAS;AACtE,QAAI;AACJ,QAAI,WAAW;AACX,0BAAoB,2BAA2B,EAAE,UAAU,MAAM,OAAO,KAAK,CAAC;AAAA,IAClF;AACA,QAAI;AACA,YAAM,eAAe,KAAK,IAAI,yBAAyB,aAAa,EAAE,MAAM,KAAK,CAAC;AAClF,UAAI,aAAa,CAAC,MAAM,QAAQ,YAAY,GAAG;AAC3C,cAAM,IAAI,aAAa,MAAoD,8FACxC,OAAO,YAAY,yGAEzB;AAAA,MACjC;AACA,iBAAW,eAAe,cAAc;AACpC,oBAAY;AAAA,MAChB;AAAA,IACJ,UACA;AACI,yBAAmB,gBAAgB;AACnC,8BAAwB,4BAA4B;AACpD,mBAAa,2BAA2B,iBAAiB;AACzD,wBAAkB,YAAY;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,WAAW;AACP,UAAM,SAAS,CAAC;AAChB,UAAM,UAAU,KAAK;AACrB,eAAW,SAAS,QAAQ,KAAK,GAAG;AAChC,aAAO,KAAK,UAAU,KAAK,CAAC;AAAA,IAChC;AACA,WAAO,cAAc,OAAO,KAAK,IAAI,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,UAAU;AAGtB,eAAW,kBAAkB,QAAQ;AACrC,QAAI,QAAQ,eAAe,QAAQ,IAC7B,WACA,kBAAkB,YAAY,SAAS,OAAO;AAEpD,UAAM,SAAS,iBAAiB,QAAQ;AACxC,QAAI,WAAW;AACX,mCAA6B,MAAM,OAAO,MAAM;AAI5C,YAAI,gBAAgB,QAAQ,GAAG;AAC3B,4CAAkC,KAAK;AACvC,6CAAmC,SAAS,QAAQ;AAAA,QACxD;AACA,oCAA4B,QAAQ;AAAA,MACxC,CAAC;AAAA,IACL;AACA,QAAI,CAAC,eAAe,QAAQ,KAAK,SAAS,UAAU,MAAM;AAGtD,UAAI,cAAc,KAAK,QAAQ,IAAI,KAAK;AACxC,UAAI,aAAa;AAEb,YAAI,aAAa,YAAY,UAAU,QAAW;AAC9C,uCAA6B;AAAA,QACjC;AAAA,MACJ,OACK;AACD,sBAAc,WAAW,QAAW,SAAS,IAAI;AACjD,oBAAY,UAAU,MAAM,WAAW,YAAY,KAAK;AACxD,aAAK,QAAQ,IAAI,OAAO,WAAW;AAAA,MACvC;AACA,cAAQ;AACR,kBAAY,MAAM,KAAK,QAAQ;AAAA,IACnC,OACK;AACD,UAAI,WAAW;AACX,cAAM,WAAW,KAAK,QAAQ,IAAI,KAAK;AACvC,YAAI,YAAY,SAAS,UAAU,QAAW;AAC1C,uCAA6B;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,QAAQ,IAAI,OAAO,MAAM;AAAA,EAClC;AAAA,EACA,QAAQ,OAAO,QAAQ;AACnB,UAAM,eAAe,kBAAkB,IAAI;AAC3C,QAAI;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,mCAA2B,UAAU,KAAK,CAAC;AAAA,MAC/C,WACS,OAAO,UAAU,SAAS;AAC/B,eAAO,QAAQ;AACf,YAAI,WAAW;AACX,uCAA6B,MAAM,OAAO,MAAM;AAC5C,8CAAkC,KAAK;AACvC,mBAAO,QAAQ,OAAO,QAAQ;AAC9B,+CAAmC,OAAO,KAAK;AAAA,UACnD,CAAC;AAAA,QACL,OACK;AACD,iBAAO,QAAQ,OAAO,QAAQ;AAAA,QAClC;AAAA,MACJ;AACA,UAAI,OAAO,OAAO,UAAU,YAAY,OAAO,SAAS,aAAa,OAAO,KAAK,GAAG;AAChF,aAAK,kBAAkB,IAAI,OAAO,KAAK;AAAA,MAC3C;AACA,aAAO,OAAO;AAAA,IAClB,UACA;AACI,wBAAkB,YAAY;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,qBAAqB,KAAK;AACtB,QAAI,CAAC,IAAI,YAAY;AACjB,aAAO;AAAA,IACX;AACA,UAAM,aAAa,kBAAkB,IAAI,UAAU;AACnD,QAAI,OAAO,eAAe,UAAU;AAChC,aAAO,eAAe,SAAS,KAAK,OAAO,IAAI,UAAU;AAAA,IAC7D,OACK;AACD,aAAO,KAAK,iBAAiB,IAAI,UAAU;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,gBAAgB,UAAU;AACtB,UAAM,eAAe,KAAK,gBAAgB,QAAQ,QAAQ;AAC1D,QAAI,iBAAiB,IAAI;AACrB,WAAK,gBAAgB,OAAO,cAAc,CAAC;AAAA,IAC/C;AAAA,EACJ;AACJ;AACA,SAAS,kCAAkC,OAAO;AAE9C,QAAM,gBAAgB,iBAAiB,KAAK;AAC5C,QAAM,UAAU,kBAAkB,OAAO,cAAc,UAAU,cAAc,KAAK;AACpF,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX;AAGA,MAAI,iBAAiB,gBAAgB;AACjC,UAAM,IAAI,aAAa,KAAoD,aAAa,SAAS,UAAU,KAAK,CAAC,iCAAiC;AAAA,EACtJ;AAEA,MAAI,iBAAiB,UAAU;AAC3B,WAAO,gCAAgC,KAAK;AAAA,EAChD;AAEA,QAAM,IAAI,aAAa,KAAoD,aAAa,aAAa;AACzG;AACA,SAAS,gCAAgC,OAAO;AAE5C,QAAM,cAAc,MAAM;AAC1B,MAAI,cAAc,GAAG;AACjB,UAAM,IAAI,aAAa,KAAoD,aACvE,oCAAoC,UAAU,KAAK,CAAC,MAAM,SAAS,aAAa,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI;AAAA,EAC3G;AAMA,QAAM,yBAAyB,0BAA0B,KAAK;AAC9D,MAAI,2BAA2B,MAAM;AACjC,WAAO,MAAM,uBAAuB,QAAQ,KAAK;AAAA,EACrD,OACK;AACD,WAAO,MAAM,IAAI,MAAM;AAAA,EAC3B;AACJ;AACA,SAAS,iBAAiB,UAAU;AAChC,MAAI,gBAAgB,QAAQ,GAAG;AAC3B,WAAO,WAAW,QAAW,SAAS,QAAQ;AAAA,EAClD,OACK;AACD,UAAM,UAAU,kBAAkB,QAAQ;AAC1C,WAAO,WAAW,SAAS,OAAO;AAAA,EACtC;AACJ;AAMA,SAAS,kBAAkB,UAAU,cAAc,WAAW;AAC1D,MAAI,UAAU;AACd,MAAI,aAAa,uBAAuB,QAAQ,GAAG;AAC/C,8BAA0B,QAAW,WAAW,QAAQ;AAAA,EAC5D;AACA,MAAI,eAAe,QAAQ,GAAG;AAC1B,UAAM,oBAAoB,kBAAkB,QAAQ;AACpD,WAAO,cAAc,iBAAiB,KAAK,kCAAkC,iBAAiB;AAAA,EAClG,OACK;AACD,QAAI,gBAAgB,QAAQ,GAAG;AAC3B,gBAAU,MAAM,kBAAkB,SAAS,QAAQ;AAAA,IACvD,WACS,kBAAkB,QAAQ,GAAG;AAClC,gBAAU,MAAM,SAAS,WAAW,GAAG,WAAW,SAAS,QAAQ,CAAC,CAAC,CAAC;AAAA,IAC1E,WACS,mBAAmB,QAAQ,GAAG;AACnC,gBAAU,MAAM,SAAS,kBAAkB,SAAS,WAAW,CAAC;AAAA,IACpE,OACK;AACD,YAAM,WAAW,kBAAkB,aAC9B,SAAS,YAAY,SAAS,QAAQ;AAC3C,UAAI,aAAa,CAAC,UAAU;AACxB,kCAA0B,cAAc,WAAW,QAAQ;AAAA,MAC/D;AACA,UAAI,QAAQ,QAAQ,GAAG;AACnB,kBAAU,MAAM,IAAI,SAAS,GAAG,WAAW,SAAS,IAAI,CAAC;AAAA,MAC7D,OACK;AACD,eAAO,cAAc,QAAQ,KAAK,kCAAkC,QAAQ;AAAA,MAChF;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,UAAU;AAClC,MAAI,SAAS,WAAW;AACpB,UAAM,IAAI,aAAa,KAAuD,aAAa,sCAAsC;AAAA,EACrI;AACJ;AACA,SAAS,WAAW,SAAS,OAAO,QAAQ,OAAO;AAC/C,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,OAAO,QAAQ,CAAC,IAAI;AAAA,EACxB;AACJ;AACA,SAAS,QAAQ,OAAO;AACpB,SAAO,CAAC,CAAC,MAAM;AACnB;AACA,SAAS,aAAa,OAAO;AACzB,SAAQ,UAAU,QACd,OAAO,UAAU,YACjB,OAAO,MAAM,gBAAgB;AACrC;AACA,SAAS,sBAAsB,OAAO;AAClC,SAAQ,OAAO,UAAU,cACpB,OAAO,UAAU,YAAY,MAAM,mBAAmB;AAC/D;AACA,SAAS,sBAAsB,WAAW,IAAI;AAC1C,aAAW,YAAY,WAAW;AAC9B,QAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,4BAAsB,UAAU,EAAE;AAAA,IACtC,WACS,YAAY,uBAAuB,QAAQ,GAAG;AACnD,4BAAsB,SAAS,YAAY,EAAE;AAAA,IACjD,OACK;AACD,SAAG,QAAQ;AAAA,IACf;AAAA,EACJ;AACJ;AAgBA,SAAS,sBAAsB,UAAU,IAAI;AACzC,MAAI;AACJ,MAAI,oBAAoB,YAAY;AAChC,uBAAmB,QAAQ;AAC3B,uBAAmB;AAAA,EACvB,OACK;AACD,uBAAmB,IAAI,mBAAmB,QAAQ;AAAA,EACtD;AACA,MAAI;AACJ,MAAI,WAAW;AACX,kCAA8B,2BAA2B,EAAE,UAAU,OAAO,KAAK,CAAC;AAAA,EACtF;AACA,QAAM,eAAe,mBAAmB,gBAAgB;AACxD,QAAM,+BAA+B,wBAAwB,MAAS;AACtE,MAAI;AACA,WAAO,GAAG;AAAA,EACd,UACA;AACI,uBAAmB,YAAY;AAC/B,iBAAa,2BAA2B,2BAA2B;AACnE,4BAAwB,4BAA4B;AAAA,EACxD;AACJ;AAIA,SAAS,uBAAuB;AAC5B,SAAO,wBAAwB,MAAM,UAAa,mBAAmB,KAAK;AAC9E;AASA,SAAS,yBAAyB,SAAS;AAGvC,MAAI,CAAC,qBAAqB,GAAG;AACzB,UAAM,IAAI,aAAa,MAAuD,aAC1E,QAAQ,OACJ,iKAAiK;AAAA,EAC7K;AACJ;AAKA,IAAM,OAAO;AACb,IAAM,QAAQ;AAEd,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,OAAO;AACb,IAAM,SAAS;AAEf,IAAM,YAAY;AAClB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,aAAa;AAEnB,IAAM,mBAAmB;AACzB,IAAM,6BAA6B;AACnC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAC5B,IAAM,UAAU;AAChB,IAAM,KAAK;AACX,IAAM,yBAAyB;AAC/B,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,IAAM,UAAU;AAChB,IAAM,6BAA6B;AACnC,IAAM,gCAAgC;AAQtC,IAAM,gBAAgB;AAOtB,IAAM,OAAO;AAQb,IAAM,mBAAmB;AACzB,IAAM,SAAS;AACf,IAAM,YAAY;AAClB,IAAM,cAAc;AAOpB,IAAM,0BAA0B;AAMhC,SAAS,QAAQ,OAAO;AACpB,SAAO,MAAM,QAAQ,KAAK,KAAK,OAAO,MAAM,IAAI,MAAM;AAC1D;AAKA,SAAS,aAAa,OAAO;AACzB,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,IAAI,MAAM;AACnD;AACA,SAAS,mBAAmB,OAAO;AAC/B,UAAQ,MAAM,QAAQ,OAAwC;AAClE;AACA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,MAAM,kBAAkB;AACnC;AACA,SAAS,gBAAgB,OAAO;AAC5B,UAAQ,MAAM,QAAQ,OAAwC;AAClE;AACA,SAAS,eAAe,KAAK;AACzB,SAAO,CAAC,CAAC,IAAI;AACjB;AACA,SAAS,WAAW,QAAQ;AAExB,UAAQ,OAAO,KAAK,IAAI,SAAiC;AAC7D;AACA,SAAS,kBAAkB,OAAO;AAC9B,UAAQ,MAAM,OAAO,QAAmC;AAC5D;AACA,SAAS,QAAQ,OAAO;AACpB,UAAQ,MAAM,KAAK,IAAI,QAAiC;AAC5D;AACA,SAAS,YAAY,OAAO;AAExB,UAAQ,MAAM,KAAK,IAAI,SAAoC;AAC/D;AAIA,SAAS,oBAAoB,OAAO,OAAO;AACvC,sBAAoB,OAAO,MAAM,KAAK,CAAC;AAC3C;AACA,SAAS,oBAAoB,OAAO,OAAO;AACvC,cAAY,KAAK;AACjB,QAAM,QAAQ,MAAM;AACpB,WAAS,IAAI,eAAe,IAAI,MAAM,QAAQ,KAAK;AAC/C,QAAI,MAAM,CAAC,MAAM,OAAO;AACpB;AAAA,IACJ;AAAA,EACJ;AACA,aAAW,2CAA2C;AAC1D;AACA,SAAS,YAAY,OAAO;AACxB,gBAAc,OAAO,uBAAuB;AAC5C,MAAI,EAAE,SAAS,OAAO,UAAU,YAAY,MAAM,eAAe,sBAAsB,IAAI;AACvF,eAAW,6BAA6B,KAAK;AAAA,EACjD;AACJ;AACA,SAAS,WAAW,MAAM;AACtB,gBAAc,MAAM,6BAA6B;AACjD,MAAI,EAAE,OAAO,KAAK,0BAA0B,WAAW;AACnD,eAAW,6BAA6B;AAAA,EAC5C;AACJ;AACA,SAAS,oBAAoB,QAAQ,MAAM,0EAA0E;AACjH,MAAI,CAAC,gBAAgB,MAAM,GAAG;AAC1B,eAAW,GAAG;AAAA,EAClB;AACJ;AACA,SAAS,mBAAmB,QAAQ,MAAM,yEAAyE;AAC/G,MAAI,CAAC,eAAe,MAAM,GAAG;AACzB,eAAW,GAAG;AAAA,EAClB;AACJ;AACA,SAAS,gBAAgB,OAAO;AAC5B,gBAAc,OAAO,4BAA4B;AACjD,gBAAc,MAAM,QAAQ,mCAAmC;AACnE;AACA,SAAS,iBAAiB,OAAO;AAC7B,gBAAc,OAAO,4BAA4B;AACjD,cAAY,aAAa,KAAK,GAAG,MAAM,sBAAsB;AACjE;AACA,SAAS,uBAAuB,OAAO;AACnC,WAAS,YAAY,QAAQ,KAAK,GAAG,MAAM,sCAAsC;AACrF;AACA,SAAS,YAAY,OAAO;AACxB,gBAAc,OAAO,uBAAuB;AAC5C,cAAY,QAAQ,KAAK,GAAG,MAAM,iBAAiB;AACvD;AACA,SAAS,sBAAsB,OAAO,YAAY;AAC9C,cAAY,MAAM,iBAAiB,MAAM,cAAc,6CAA6C;AACxG;AACA,SAAS,sBAAsB,OAAO,YAAY;AAC9C,cAAY,MAAM,iBAAiB,MAAM,6CAA6C;AAC1F;AAKA,SAAS,mBAAmB,KAAK;AAC7B,MAAI,IAAI,SAAS,UAAa,IAAI,aAAa,UAAa,IAAI,WAAW,QAAW;AAClF,eAAW,gGAAgG;AAAA,EAC/G;AACJ;AACA,SAAS,uBAAuB,OAAO,OAAO;AAC1C,gBAAc,eAAe,MAAM,mBAAmB,KAAK;AAC/D;AACA,SAAS,0BAA0B,OAAO,OAAO;AAC7C,QAAM,QAAQ,MAAM,CAAC;AACrB,gBAAc,MAAM,mBAAmB,MAAM,QAAQ,KAAK;AAC9D;AACA,SAAS,cAAc,OAAO,OAAO,OAAO;AACxC,MAAI,EAAE,SAAS,SAAS,QAAQ,QAAQ;AACpC,eAAW,iCAAiC,KAAK,OAAO,KAAK,MAAM,KAAK,GAAG;AAAA,EAC/E;AACJ;AACA,SAAS,sBAAsB,OAAO,YAAY;AAC9C,gBAAc,MAAM,0BAA0B,GAAG,+BAA+B;AAChF,gBAAc,MAAM,0BAA0B,EAAE,MAAM,EAAE,YAAY,qFAAqF;AAC7J;AACA,SAAS,iBAAiB,OAAO,YAAY;AACzC,gBAAc,OAAO,0EAA0E;AACnG;AAQA,SAAS,mBAAmB,OAAO,eAAe;AAC9C,4BAA0B,OAAO,aAAa;AAC9C;AAAA,IAA0B;AAAA,IAAO,gBAAgB;AAAA;AAAA,EAAiC;AAClF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa,MAAM,gBAAgB,CAAC,GAAG,8CAA8C;AACrF,eAAa;AAAA,IAAM,gBAAgB;AAAA;AAAA,EAAiC,GAAG,+CAA+C;AAC1H;AAEA,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAsB1B,SAAS,YAAY,OAAO;AACxB,SAAO,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAQ,MAAM,IAAI;AAAA,EACtB;AACA,SAAO;AACX;AAKA,SAAS,YAAY,OAAO;AACxB,SAAO,MAAM,QAAQ,KAAK,GAAG;AAGzB,QAAI,OAAO,MAAM,IAAI,MAAM;AACvB,aAAO;AACX,YAAQ,MAAM,IAAI;AAAA,EACtB;AACA,SAAO;AACX;AAKA,SAAS,iBAAiB,OAAO,OAAO;AACpC,eAAa,mBAAmB,OAAO,KAAK;AAC5C,eAAa,yBAAyB,OAAO,eAAe,mCAAmC;AAC/F,SAAO,YAAY,MAAM,KAAK,CAAC;AACnC;AASA,SAAS,iBAAiB,OAAO,OAAO;AACpC,eAAa,oBAAoB,OAAO,KAAK;AAC7C,eAAa,mBAAmB,OAAO,MAAM,KAAK;AAClD,QAAM,OAAO,YAAY,MAAM,MAAM,KAAK,CAAC;AAC3C,SAAO;AACX;AASA,SAAS,uBAAuB,OAAO,OAAO;AAC1C,QAAM,QAAQ,UAAU,OAAO,KAAK,MAAM;AAC1C,MAAI,UAAU,IAAI;AACd,iBAAa,oBAAoB,OAAO,KAAK;AAC7C,UAAM,OAAO,YAAY,MAAM,KAAK,CAAC;AACrC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,SAAS,OAAO,OAAO;AAC5B,eAAa,kBAAkB,OAAO,IAAI,uBAAuB;AACjE,eAAa,eAAe,OAAO,MAAM,KAAK,QAAQ,uBAAuB;AAC7E,QAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,eAAa,UAAU,QAAQ,YAAY,KAAK;AAChD,SAAO;AACX;AAEA,SAAS,KAAK,MAAM,OAAO;AACvB,eAAa,mBAAmB,MAAM,KAAK;AAC3C,SAAO,KAAK,KAAK;AACrB;AAEA,SAAS,MAAM,OAAO,OAAO,OAAO,OAAO;AAGvC,MAAI,SAAS,MAAM,KAAK,QAAQ;AAC5B,UAAM,KAAK,KAAK,IAAI;AACpB,UAAM,UAAU,KAAK,IAAI;AAAA,EAC7B;AACA,QAAM,KAAK,IAAI;AACnB;AACA,SAAS,yBAAyB,WAAW,UAAU;AAEnD,eAAa,mBAAmB,UAAU,SAAS;AACnD,QAAM,YAAY,SAAS,SAAS;AACpC,QAAM,QAAQ,QAAQ,SAAS,IAAI,YAAY,UAAU,IAAI;AAC7D,SAAO;AACX;AAEA,SAAS,eAAe,MAAM;AAC1B,UAAQ,KAAK,KAAK,IAAI,OAAqC;AAC/D;AAOA,SAAS,6BAA6B,MAAM;AACxC,UAAQ,KAAK,KAAK,IAAI,SAAmC;AAC7D;AAEA,SAAS,wBAAwB,MAAM;AACnC,SAAO,aAAa,KAAK,MAAM,CAAC;AACpC;AACA,SAAS,YAAY,QAAQ,OAAO;AAChC,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,eAAa,mBAAmB,QAAQ,KAAK;AAC7C,SAAO,OAAO,KAAK;AACvB;AAKA,SAAS,uBAAuB,OAAO;AACnC,QAAM,mBAAmB,IAAI;AACjC;AAKA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,MAAM,KAAK,IAAI,MAAmC;AAClD;AAAA,EACJ;AACA,QAAM,KAAK,KAAK;AAChB,MAAI,6BAA6B,KAAK,GAAG;AACrC,8BAA0B,KAAK;AAAA,EACnC;AACJ;AAMA,SAAS,YAAY,cAAc,aAAa;AAC5C,SAAO,eAAe,GAAG;AACrB,iBACI,cAAc,YAAY,gBAAgB,GAAG,wEAAwE;AACzH,kBAAc,YAAY,gBAAgB;AAC1C;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,OAAO;AACvC,SAAO,CAAC,EAAE,MAAM,KAAK,KAAK,OAAoC,SAC1D,MAAM,0BAA0B,GAAG;AAC3C;AAKA,SAAS,qCAAqC,OAAO;AACjD,QAAM,WAAW,EAAE,0BAA0B;AAAA,IAAO;AAAA;AAAA,EAAuC;AAC3F,MAAI,MAAM,KAAK,IAAI,IAA2B;AAC1C,UAAM,KAAK,KAAK;AAAA,EACpB;AACA,MAAI,2BAA2B,KAAK,GAAG;AACnC,8BAA0B,KAAK;AAAA,EACnC;AACJ;AAQA,SAAS,0BAA0B,OAAO;AACtC,QAAM,WAAW,EAAE,0BAA0B;AAAA,IAAO;AAAA;AAAA,EAAoD;AACxG,MAAI,SAAS,eAAe,KAAK;AACjC,SAAO,WAAW,MAAM;AAGpB,QAAI,OAAO,KAAK,IAAI,MAA8C;AAC9D;AAAA,IACJ;AACA,WAAO,KAAK,KAAK;AACjB,QAAI,CAAC,6BAA6B,MAAM,GAAG;AACvC;AAAA,IACJ;AACA,aAAS,eAAe,MAAM;AAAA,EAClC;AACJ;AAIA,SAAS,oBAAoB,OAAO,mBAAmB;AACnD,MAAI,YAAY,KAAK,GAAG;AACpB,UAAM,IAAI,aAAa,KAAmD,aAAa,kCAAkC;AAAA,EAC7H;AACA,MAAI,MAAM,gBAAgB,MAAM,MAAM;AAClC,UAAM,gBAAgB,IAAI,CAAC;AAAA,EAC/B;AACA,QAAM,gBAAgB,EAAE,KAAK,iBAAiB;AAClD;AAIA,SAAS,qBAAqB,OAAO,mBAAmB;AACpD,MAAI,MAAM,gBAAgB,MAAM;AAC5B;AACJ,QAAM,eAAe,MAAM,gBAAgB,EAAE,QAAQ,iBAAiB;AACtE,MAAI,iBAAiB,IAAI;AACrB,UAAM,gBAAgB,EAAE,OAAO,cAAc,CAAC;AAAA,EAClD;AACJ;AAMA,SAAS,eAAe,OAAO;AAC3B,eAAa,YAAY,KAAK;AAC9B,QAAM,SAAS,MAAM,MAAM;AAC3B,SAAO,aAAa,MAAM,IAAI,OAAO,MAAM,IAAI;AACnD;AACA,SAAS,wBAAwB,MAAM;AAEnC,SAAQ,KAAK,OAAO,MAAM,CAAC;AAC/B;AACA,SAAS,wBAAwB,OAAO;AACpC,SAAQ,MAAM,YAAY,CAAC;AAC/B;AAQA,SAAS,wBAAwB,OAAO,OAAO,SAAS,WAAW;AAC/D,QAAM,WAAW,wBAAwB,KAAK;AAK9C,eACI,cAAc,SAAS,6EAA6E;AACxG,WAAS,KAAK,OAAO;AACrB,MAAI,MAAM,iBAAiB;AACvB,4BAAwB,KAAK,EAAE,KAAK,WAAW,SAAS,SAAS,CAAC;AAAA,EACtE,OACK;AAGD,QAAI,WAAW;AACX,aAAO,OAAO,wBAAwB,KAAK,CAAC;AAAA,IAChD;AAAA,EACJ;AACJ;AAEA,IAAM,mBAAmB;AAAA,EACrB,QAAQ,aAAa,IAAI;AAAA,EACzB,iBAAiB;AAAA,EACjB,wBAAwB;AAC5B;AACA,IAAI;AAAA,CACH,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmBA,oBAAmB,KAAK,IAAI,CAAC,IAAI;AACpD,EAAAA,oBAAmBA,oBAAmB,YAAY,IAAI,CAAC,IAAI;AAC3D,EAAAA,oBAAmBA,oBAAmB,gBAAgB,IAAI,CAAC,IAAI;AACnE,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AASlD,IAAI,sBAAsB;AAM1B,IAAI,qBAAqB;AACzB,SAAS,uBAAuB;AAC5B,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,4BAA4B;AACjC,mBAAiB,OAAO;AAC5B;AACA,SAAS,4BAA4B;AACjC,mBAAiB,OAAO;AAC5B;AACA,SAAS,qBAAqB;AAC1B,SAAO,iBAAiB;AAC5B;AAKA,SAAS,yBAAyB;AAC9B,SAAO,iBAAiB,2BAA2B;AACvD;AAMA,SAAS,yBAAyB,OAAO;AACrC,SAAO,iBAAiB,2BAA2B;AACvD;AAoBA,SAAS,mBAAmB;AACxB,mBAAiB,kBAAkB;AACvC;AAKA,SAAS,wBAAwB,OAAO;AACpC,mBAAiB,yBAAyB;AAC9C;AAoBA,SAAS,oBAAoB;AACzB,mBAAiB,kBAAkB;AACvC;AAIA,SAAS,0BAA0B;AAC/B,mBAAiB,yBAAyB;AAC9C;AAIA,SAAS,WAAW;AAChB,SAAO,iBAAiB,OAAO;AACnC;AAIA,SAAS,WAAW;AAChB,SAAO,iBAAiB,OAAO;AACnC;AAaA,SAAS,cAAc,eAAe;AAClC,mBAAiB,OAAO,eAAe;AACvC,SAAO,cAAc,OAAO;AAChC;AAOA,SAAS,YAAY,OAAO;AACxB,mBAAiB,OAAO,eAAe;AACvC,SAAO;AACX;AACA,SAAS,kBAAkB;AACvB,MAAI,eAAe,6BAA6B;AAChD,SAAO,iBAAiB,QAAQ,aAAa,SAAS,IAAgC;AAClF,mBAAe,aAAa;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,+BAA+B;AACpC,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,wBAAwB;AAC7B,QAAM,SAAS,iBAAiB;AAChC,QAAM,eAAe,OAAO;AAC5B,SAAO,OAAO,WAAW,eAAe,aAAa;AACzD;AACA,SAAS,gBAAgB,OAAO,UAAU;AACtC,eAAa,SAAS,oBAAoB,OAAO,iBAAiB,OAAO,KAAK;AAC9E,QAAM,SAAS,iBAAiB;AAChC,SAAO,eAAe;AACtB,SAAO,WAAW;AACtB;AACA,SAAS,uBAAuB;AAC5B,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,6BAA6B;AAClC,mBAAiB,OAAO,WAAW;AACvC;AACA,SAAS,kBAAkB;AACvB,QAAM,eAAe,iBAAiB,OAAO;AAC7C,eAAa,cAAc,cAAc,+BAA+B;AACxE,SAAO;AACX;AACA,SAAS,yBAAyB;AAC9B,GAAC,aAAa,WAAW,yCAAyC;AAClE,SAAO,wBAAwB,mBAAmB;AACtD;AACA,SAAS,6BAA6B;AAClC,GAAC,aAAa,WAAW,yCAAyC;AAClE,SAAO,wBAAwB,mBAAmB;AACtD;AACA,SAAS,0BAA0B,MAAM;AACrC,GAAC,aAAa,WAAW,yCAAyC;AAClE,wBAAsB;AAC1B;AACA,SAAS,oBAAoB;AACzB,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM;AAChC,QAAM,OAAO;AACb,uBAAqB;AACrB,SAAO;AACX;AAEA,SAAS,iBAAiB;AACtB,QAAM,SAAS,iBAAiB;AAChC,MAAI,QAAQ,OAAO;AACnB,MAAI,UAAU,IAAI;AACd,YAAQ,OAAO,mBAAmB,OAAO,MAAM;AAAA,EACnD;AACA,SAAO;AACX;AACA,SAAS,kBAAkB;AACvB,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,gBAAgB,OAAO;AAC5B,SAAQ,iBAAiB,OAAO,eAAe;AACnD;AACA,SAAS,mBAAmB;AACxB,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,sBAAsB,OAAO;AAClC,QAAM,SAAS,iBAAiB;AAChC,QAAM,QAAQ,OAAO;AACrB,SAAO,eAAe,OAAO,eAAe;AAC5C,SAAO;AACX;AACA,SAAS,gBAAgB;AACrB,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,eAAeC,gBAAe;AACnC,mBAAiB,OAAO,SAASA;AACrC;AAYA,SAAS,8BAA8B,kBAAkB,uBAAuB;AAC5E,QAAM,SAAS,iBAAiB;AAChC,SAAO,eAAe,OAAO,mBAAmB;AAChD,2BAAyB,qBAAqB;AAClD;AAMA,SAAS,2BAA2B;AAChC,SAAO,iBAAiB,OAAO;AACnC;AAMA,SAAS,yBAAyB,uBAAuB;AACrD,mBAAiB,OAAO,wBAAwB;AACpD;AAOA,SAAS,uBAAuB,OAAO;AACnC,QAAM,wBAAwB,iBAAiB,OAAO;AACtD,SAAO,0BAA0B,KAAK,OAAO,MAAM,qBAAqB;AAC5E;AACA,SAAS,uBAAuB;AAC5B,SAAO,iBAAiB,OAAO;AACnC;AACA,SAAS,qBAAqB,OAAO;AACjC,mBAAiB,OAAO,oBAAoB;AAChD;AAMA,SAAS,oBAAoB,OAAO;AAChC,QAAM,QAAQ,MAAM,KAAK;AAEzB,MAAI,MAAM,SAAS,GAA4B;AAC3C,iBAAa,cAAc,MAAM,WAAW,kDAAkD;AAC9F,WAAO,MAAM;AAAA,EACjB;AAIA,MAAI,MAAM,SAAS,GAA6B;AAC5C,WAAO,MAAM,MAAM;AAAA,EACvB;AAEA,SAAO;AACX;AAcA,SAAS,QAAQ,OAAO,OAAO,OAAO;AAClC,eAAa,uBAAuB,KAAK;AACzC,MAAI,QAAQ,GAAsC;AAC9C,iBAAa,oBAAoB,OAAO,MAAM,KAAK,CAAC;AACpD,QAAI,cAAc;AAClB,QAAI,cAAc;AAClB,WAAO,MAAM;AACT,mBAAa,cAAc,aAAa,gCAAgC;AACxE,oBAAc,YAAY;AAC1B,UAAI,gBAAgB,QAAQ,EAAE,QAAQ,IAAmC;AACrE,sBAAc,oBAAoB,WAAW;AAC7C,YAAI,gBAAgB;AAChB;AAGJ,qBAAa,cAAc,aAAa,gCAAgC;AACxE,sBAAc,YAAY,gBAAgB;AAI1C,YAAI,YAAY,QAAQ,IAA4B,IAAqC;AACrF;AAAA,QACJ;AAAA,MACJ,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,gBAAgB,MAAM;AAEtB,aAAO;AAAA,IACX,OACK;AACD,cAAQ;AACR,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,eAAa,oBAAoB,OAAO,KAAK;AAC7C,QAAM,SAAU,iBAAiB,SAAS,YAAY;AACtD,SAAO,eAAe;AACtB,SAAO,QAAQ;AACf,SAAO;AACX;AAYA,SAAS,UAAU,SAAS;AACxB,eAAa,eAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,MAAM;AAC1D,eAAa,uBAAuB,OAAO;AAC3C,QAAM,YAAY,YAAY;AAC9B,MAAI,WAAW;AACX,gBAAY,UAAU,UAAU,MAAM,uBAAuB;AAC7D,gBAAY,UAAU,OAAO,MAAM,uBAAuB;AAC1D,gBAAY,UAAU,OAAO,MAAM,uBAAuB;AAC1D,gBAAY,UAAU,eAAe,IAAI,uBAAuB;AAChE,gBAAY,UAAU,mBAAmB,GAAG,uBAAuB;AACnE,gBAAY,UAAU,uBAAuB,IAAI,uBAAuB;AACxE,gBAAY,UAAU,kBAAkB,MAAM,uBAAuB;AACrE,gBAAY,UAAU,kBAAkB,IAAI,uBAAuB;AACnE,gBAAY,UAAU,mBAAmB,GAAG,uBAAuB;AAAA,EACvE;AACA,QAAM,QAAQ,QAAQ,KAAK;AAC3B,mBAAiB,SAAS;AAC1B,eAAa,MAAM,cAAc,oBAAoB,MAAM,YAAY,KAAK;AAC5E,YAAU,eAAe,MAAM;AAC/B,YAAU,QAAQ;AAClB,YAAU,QAAQ;AAClB,YAAU,eAAe;AACzB,YAAU,eAAe,MAAM;AAC/B,YAAU,SAAS;AACvB;AAIA,SAAS,cAAc;AACnB,QAAM,gBAAgB,iBAAiB;AACvC,QAAM,cAAc,kBAAkB,OAAO,OAAO,cAAc;AAClE,QAAM,YAAY,gBAAgB,OAAO,aAAa,aAAa,IAAI;AACvE,SAAO;AACX;AACA,SAAS,aAAa,QAAQ;AAC1B,QAAM,SAAS;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,eAAe;AAAA,IACf,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AACA,aAAW,SAAS,OAAO,QAAQ;AACnC,SAAO;AACX;AAUA,SAAS,iBAAiB;AACtB,QAAM,YAAY,iBAAiB;AACnC,mBAAiB,SAAS,UAAU;AACpC,YAAU,eAAe;AACzB,YAAU,QAAQ;AAClB,SAAO;AACX;AAOA,IAAM,UAAU;AAShB,SAAS,YAAY;AACjB,QAAM,YAAY,eAAe;AACjC,YAAU,WAAW;AACrB,YAAU,QAAQ;AAClB,YAAU,gBAAgB;AAC1B,YAAU,eAAe;AACzB,YAAU,oBAAoB;AAC9B,YAAU,wBAAwB;AAClC,YAAU,mBAAmB;AAC7B,YAAU,mBAAmB;AAC7B,YAAU,eAAe;AACzB,YAAU,oBAAoB;AAClC;AACA,SAAS,gBAAgB,OAAO;AAC5B,QAAM,eAAgB,iBAAiB,OAAO,eAAe,YAAY,OAAO,iBAAiB,OAAO,YAAY;AACpH,SAAO,aAAa,OAAO;AAC/B;AAOA,SAAS,mBAAmB;AACxB,SAAO,iBAAiB,OAAO;AACnC;AAUA,SAAS,iBAAiB,OAAO;AAC7B,eACI,UAAU,MACV,yBAAyB,OAAO,eAAe,2CAA2C;AAC9F,eACI,eAAe,OAAO,iBAAiB,OAAO,MAAM,QAAQ,qCAAqC;AACrG,mBAAiB,OAAO,gBAAgB;AAC5C;AAIA,SAAS,mBAAmB;AACxB,QAAM,SAAS,iBAAiB;AAChC,SAAO,SAAS,OAAO,OAAO,OAAO,aAAa;AACtD;AAMA,SAAS,iBAAiB;AACtB,mBAAiB,OAAO,mBAAmB;AAC/C;AAMA,SAAS,oBAAoB;AACzB,mBAAiB,OAAO,mBAAmB;AAC/C;AAOA,SAAS,kBAAkB;AACvB,wBAAsB;AAC1B;AAKA,SAAS,wBAAwB;AAC7B,mBAAiB,OAAO,mBAAmB;AAC/C;AACA,SAAS,eAAe;AACpB,SAAO,iBAAiB,OAAO;AACnC;AACA,IAAI,sBAAsB;AAK1B,SAAS,qBAAqB;AAC1B,SAAO;AACX;AAKA,SAAS,mBAAmB,MAAM;AAC9B,wBAAsB;AAC1B;AAKA,SAAS,eAAe,SAAS,SAAS,MAAM,sBAAsB,MAAM,MAAM;AAC9E,QAAM,WAAW,uCAAuC,SAAS,QAAQ,qBAAqB,IAAI;AAClG,WAAS,4BAA4B;AACrC,SAAO;AACX;AAMA,SAAS,uCAAuC,SAAS,SAAS,MAAM,sBAAsB,MAAM,MAAM,SAAS,oBAAI,IAAI,GAAG;AAC1H,QAAM,YAAY,CAAC,uBAAuB,aAAa,oBAAoB,OAAO,CAAC;AACnF,SAAO,SAAS,OAAO,YAAY,WAAW,SAAY,UAAU,OAAO;AAC3E,SAAO,IAAI,WAAW,WAAW,UAAU,gBAAgB,GAAG,QAAQ,MAAM,MAAM;AACtF;AA0BA,IAAM,WAAN,MAAM,UAAS;AAAA,EACX,OAAO,qBAAqB;AAAA,EAC5B,OAAO,OAAO,IAAI,aAAa;AAAA,EAC/B,OAAO,OAAO,SAAS,QAAQ;AAC3B,QAAI,MAAM,QAAQ,OAAO,GAAG;AACxB,aAAO,eAAe,EAAE,MAAM,GAAG,GAAG,QAAQ,SAAS,EAAE;AAAA,IAC3D,OACK;AACD,YAAM,OAAO,QAAQ,QAAQ;AAC7B,aAAO,eAAe,EAAE,KAAK,GAAG,QAAQ,QAAQ,QAAQ,WAAW,IAAI;AAAA,IAC3E;AAAA,EACJ;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,IAAkD,mBAAmB;AAAA,MACxE,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS,MAAM,SAAS,UAAU;AAAA,IACtC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,oBAAoB;AAC/B;AASA,IAAM,WAAW,IAAI,eAAe,YAAY,kBAAkB,EAAE;AAUpE,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,OAAO,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,OAAO,gBAAgB,CAAC,aAAa;AACzC;AACA,IAAM,yBAAN,cAAqC,WAAW;AAAA,EAC5C;AAAA,EACA,YAAY,QAAQ;AAChB,UAAM;AACN,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,YAAY,KAAK,MAAM;AAAA,EAClC;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,QAAQ,KAAK;AACnB,wBAAoB,OAAO,QAAQ;AACnC,WAAO,MAAM,qBAAqB,OAAO,QAAQ;AAAA,EACrD;AACJ;AACA,SAAS,mBAAmB;AACxB,SAAO,IAAI,uBAAuB,SAAS,CAAC;AAChD;AAiCA,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA,EAIf,WAAW;AAAA,EACX,YAAY,OAAO;AACf,SAAK,SAAS,MAAM,SAAS,KAAK;AAAA,EACtC;AACJ;AAIA,IAAM,qCAAqC,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,2BAA2B,IAAI;AAAA,EACzI,YAAY;AAAA,EACZ,SAAS,MAAM;AAGX,UAAM,WAAW,OAAO,mBAAmB;AAC3C,QAAI;AACJ,WAAO,CAAC,MAAM;AACV,UAAI,SAAS,aAAa,CAAC,kBAAkB;AACzC,mBAAW,MAAM;AACb,gBAAM;AAAA,QACV,CAAC;AAAA,MACL,OACK;AACD,6BAAqB,SAAS,IAAI,YAAY;AAC9C,yBAAiB,YAAY,CAAC;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AACD,IAAM,qCAAqC;AAAA,EACvC,SAAS;AAAA,EACT,UAAU,MAAM,KAAK,OAAO,YAAY;AAAA,EACxC,OAAO;AACX;AACA,IAAM,uBAAuB,IAAI,eAAe,YAAY,yBAAyB,IAAI;AAAA,EACrF,YAAY;AAAA,EACZ,SAAS,MAAM;AACX,QAA2C,OAAc;AACrD;AAAA,IACJ;AACA,UAAM,SAAS,OAAO,QAAQ,EAAE;AAChC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,UAAM,eAAe,OAAO,kCAAkC;AAC9D,UAAM,oBAAoB,CAAC,MAAM;AAC7B,mBAAa,EAAE,MAAM;AACrB,QAAE,eAAe;AAAA,IACrB;AACA,UAAM,gBAAgB,CAAC,MAAM;AACzB,UAAI,EAAE,OAAO;AACT,qBAAa,EAAE,KAAK;AAAA,MACxB,OACK;AACD,qBAAa,IAAI,MAAM,YACjB,sEAAsE,EAAE,OAAO,KAC/E,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AAAA,MAClC;AACA,QAAE,eAAe;AAAA,IACrB;AACA,UAAM,sBAAsB,MAAM;AAC9B,aAAO,iBAAiB,sBAAsB,iBAAiB;AAC/D,aAAO,iBAAiB,SAAS,aAAa;AAAA,IAClD;AAGA,QAAI,OAAO,SAAS,aAAa;AAC7B,WAAK,KAAK,IAAI,mBAAmB;AAAA,IACrC,OACK;AACD,0BAAoB;AAAA,IACxB;AACA,WAAO,UAAU,EAAE,UAAU,MAAM;AAC/B,aAAO,oBAAoB,SAAS,aAAa;AACjD,aAAO,oBAAoB,sBAAsB,iBAAiB;AAAA,IACtE,CAAC;AAAA,EACL;AACJ,CAAC;AAQD,SAAS,qCAAqC;AAC1C,SAAO,yBAAyB;AAAA,IAC5B,8BAA8B,MAAM,KAAK,OAAO,oBAAoB,CAAC;AAAA,EACzE,CAAC;AACL;AAOA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,cAAc,MAAM,MAAM,MAAM;AAC5D;AAMA,SAAS,sBAAsB,OAAO;AAGlC,SAAO;AACX;AAIA,SAAS,OAAO,cAAc,SAAS;AACnC,QAAM,CAAC,KAAK,KAAK,MAAM,IAAI,aAAa,cAAc,SAAS,KAAK;AACpE,QAAM,WAAW;AACjB,QAAM,OAAO,SAAS,MAAM;AAC5B,WAAS,MAAM;AACf,WAAS,SAAS;AAClB,WAAS,aAAa,mBAAmB,KAAK,QAAQ;AACtD,MAAI,WAAW;AACX,aAAS,WAAW,MAAM,YAAY,SAAS,CAAC;AAChD,SAAK,YAAY,SAAS;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,qBAAqB;AAC1B,QAAM,OAAO,KAAK,MAAM;AACxB,MAAI,KAAK,eAAe,QAAW;AAC/B,UAAM,aAAa,MAAM,KAAK;AAC9B,eAAW,MAAM,IAAI;AACrB,SAAK,aAAa;AAAA,EACtB;AACA,SAAO,KAAK;AAChB;AAIA,SAAS,iBAAiB,OAAO;AAC7B,SAAO,SAAS,KAAK,KAAK,OAAO,MAAM,QAAQ;AACnD;AAKA,IAAM,2BAAN,MAA+B;AAC/B;AAEA,IAAM,mBAAmB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,qBAAqB,IAAI,EAAE,YAAY,QAAQ,SAAS,MAAM,MAAM,CAAC;AAEjK,IAAM,oBAAoB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,sBAAsB,IAAI,EAAE,YAAY,QAAQ,SAAS,MAAM,MAAM,CAAC;AACnK,IAAM,8BAA8B,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,uBAAuB,EAAE;AAEhI,IAAM,wBAAwB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,qCAAqC,EAAE;AAUxI,SAAS,2BAA2B,SAAS,cAAc;AAGvD,MAAI,kBAAkB,MAAM,MAAM;AAC9B,UAAM,IAAI,aAAa,MAAmE,aACtF,GAAG,QAAQ,IAAI,sDAAsD,eAAe,IAAI,YAAY,KAAK,EAAE,EAAE;AAAA,EACrH;AACJ;AAEA,IAAM,cAAN,MAAkB;AAAA,EACd;AAAA,EACA;AAAA,EACA,YAAY,MAAM,MAAM;AACpB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,oBAAoB;AAC/B;AACA,SAAS,oBAAoB;AACzB,SAAO,IAAI,YAAY,SAAS,GAAG,gBAAgB,CAAC;AACxD;AAKA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACvB,SAAS;AAAA,EACT,eAAe,oBAAI,IAAI;AAAA,EACvB,YAAY;AAAA,EACZ,cAAc,IAAI,gBAAgB,KAAK;AAAA,EACvC,IAAI,kBAAkB;AAElB,WAAO,KAAK,YAAY,QAAQ,KAAK,YAAY;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,4BAA4B;AAC5B,QAAI,KAAK,WAAW;AAEhB,aAAO,IAAI,WAAW,CAAC,eAAe;AAClC,mBAAW,KAAK,KAAK;AACrB,mBAAW,SAAS;AAAA,MACxB,CAAC;AAAA,IACL;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,MAAM;AAEF,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,WAAW;AAC1C,WAAK,YAAY,KAAK,IAAI;AAAA,IAC9B;AACA,UAAM,SAAS,KAAK;AACpB,SAAK,aAAa,IAAI,MAAM;AAC5B,WAAO;AAAA,EACX;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,aAAa,IAAI,MAAM;AAAA,EACvC;AAAA,EACA,OAAO,QAAQ;AACX,SAAK,aAAa,OAAO,MAAM;AAC/B,QAAI,KAAK,aAAa,SAAS,KAAK,KAAK,iBAAiB;AACtD,WAAK,YAAY,KAAK,KAAK;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,cAAc;AACV,SAAK,aAAa,MAAM;AACxB,QAAI,KAAK,iBAAiB;AACtB,WAAK,YAAY,KAAK,KAAK;AAAA,IAC/B;AAMA,SAAK,YAAY;AACjB,SAAK,YAAY,YAAY;AAAA,EACjC;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,IAAkD,mBAAmB;AAAA,MACxE,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS,MAAM,IAAI,sBAAqB;AAAA,IAC5C,CAAC;AAAA;AACL;AAsBA,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,uBAAuB,OAAO,oBAAoB;AAAA,EAClD,YAAY,OAAO,wBAAwB;AAAA,EAC3C,eAAe,OAAO,kCAAkC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,MAAM;AACF,UAAM,SAAS,KAAK,qBAAqB,IAAI;AAC7C,WAAO,MAAM;AACT,UAAI,CAAC,KAAK,qBAAqB,IAAI,MAAM,GAAG;AAExC;AAAA,MACJ;AAEA,WAAK,UAAU;AAAA,QAAO;AAAA;AAAA,MAA8C;AACpE,WAAK,qBAAqB,OAAO,MAAM;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,IAAI;AACJ,UAAM,aAAa,KAAK,IAAI;AAC5B,OAAG,EAAE,MAAM,KAAK,YAAY,EAAE,QAAQ,UAAU;AAAA,EACpD;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,IAAkD,mBAAmB;AAAA,MACxE,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS,MAAM,IAAI,cAAa;AAAA,IACpC,CAAC;AAAA;AACL;AAEA,SAAS,QAAQ,MAAM;AAEvB;AAKA,IAAM,kBAAN,MAAM,iBAAgB;AAAA;AAAA,EAElB,OAAO;AAAA;AAAA,IAAkD,mBAAmB;AAAA,MACxE,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS,MAAM,IAAI,yBAAyB;AAAA,IAChD,CAAC;AAAA;AACL;AAKA,IAAM,2BAAN,MAA+B;AAAA,EAC3B,mBAAmB;AAAA,EACnB,SAAS,oBAAI,IAAI;AAAA,EACjB,IAAI,QAAQ;AACR,SAAK,QAAQ,MAAM;AACnB,SAAK,SAAS,MAAM;AAAA,EACxB;AAAA,EACA,SAAS,QAAQ;AACb,QAAI,CAAC,OAAO,OAAO;AACf;AAAA,IACJ;AACA,SAAK;AAAA,EACT;AAAA,EACA,OAAO,QAAQ;AACX,UAAM,OAAO,OAAO;AACpB,UAAM,QAAQ,KAAK,OAAO,IAAI,IAAI;AAClC,QAAI,CAAC,MAAM,IAAI,MAAM,GAAG;AACpB;AAAA,IACJ;AACA,UAAM,OAAO,MAAM;AACnB,QAAI,OAAO,OAAO;AACd,WAAK;AAAA,IACT;AAAA,EACJ;AAAA,EACA,QAAQ,QAAQ;AACZ,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,KAAK,OAAO,IAAI,IAAI,GAAG;AACxB,WAAK,OAAO,IAAI,MAAM,oBAAI,IAAI,CAAC;AAAA,IACnC;AACA,UAAM,QAAQ,KAAK,OAAO,IAAI,IAAI;AAClC,QAAI,MAAM,IAAI,MAAM,GAAG;AACnB;AAAA,IACJ;AACA,UAAM,IAAI,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACJ,WAAO,KAAK,mBAAmB,GAAG;AAC9B,UAAI,eAAe;AACnB,iBAAW,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AAErC,YAAI,SAAS,MAAM;AACf,2BAAiB,KAAK,WAAW,KAAK;AAAA,QAC1C,OACK;AACD,2BAAiB,KAAK,IAAI,MAAM,KAAK,WAAW,KAAK,CAAC;AAAA,QAC1D;AAAA,MACJ;AAGA,UAAI,CAAC,cAAc;AACf,aAAK,mBAAmB;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW,OAAO;AACd,QAAI,eAAe;AACnB,eAAW,UAAU,OAAO;AACxB,UAAI,CAAC,OAAO,OAAO;AACf;AAAA,MACJ;AACA,WAAK;AACL,qBAAe;AAEf,aAAO,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACJ;;;AC5zHA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,eAAe,OAAO,cAAc,EAAE,UAAU,KAAK,CAAC;AAAA;AAAA,EAEtD,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AAEV,SAAK,WAAW,UAAU,MAAM;AAC5B,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,IACrB,CAAC;AAAA,EACL;AAAA,EACA,UAAU,UAAU;AAChB,QAAI,KAAK,WAAW;AAChB,YAAM,IAAI,aAAa,KAAiD,aACpE,gGACkD;AAAA,IAC1D;AACA,KAAC,KAAK,cAAc,CAAC,GAAG,KAAK,QAAQ;AACrC,WAAO;AAAA,MACH,aAAa,MAAM;AACf,cAAM,MAAM,KAAK,WAAW,QAAQ,QAAQ;AAC5C,YAAI,QAAQ,UAAa,QAAQ,IAAI;AACjC,eAAK,WAAW,OAAO,KAAK,CAAC;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,KAAK,OAAO;AACR,QAAI,KAAK,WAAW;AAChB,cAAQ,KAAK,mBAAmB,KAAiD,aAC7E,yFACkD,CAAC;AACvD;AAAA,IACJ;AACA,QAAI,KAAK,cAAc,MAAM;AACzB;AAAA,IACJ;AACA,UAAM,mBAAmB,kBAAkB,IAAI;AAC/C,QAAI;AACA,iBAAW,cAAc,KAAK,WAAW;AACrC,YAAI;AACA,qBAAW,KAAK;AAAA,QACpB,SACO,KAAK;AACR,eAAK,cAAc,YAAY,GAAG;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ,UACA;AACI,wBAAkB,gBAAgB;AAAA,IACtC;AAAA,EACJ;AACJ;AAEA,SAAS,oBAAoB,KAAK;AAC9B,SAAO,IAAI;AACf;AAMA,SAASC,WAAU,oBAAoB;AACnC,SAAO,UAAY,kBAAkB;AACzC;AAKA,SAAS,SAAS,aAAa,SAAS;AACpC,QAAM,SAAS,eAAe,aAAa,SAAS,KAAK;AACzD,MAAI,WAAW;AACX,WAAO,WAAW,MAAM,cAAc,OAAO,CAAC;AAC9C,WAAO,MAAM,EAAE,YAAY,SAAS;AAAA,EACxC;AACA,SAAO;AACX;AAEA,IAAM,gBAAN,MAAoB;AAAA,EAChB,CAAC,MAAM;AAAA,EACP,YAAY,MAAM;AACd,SAAK,MAAM,IAAI;AAAA,EACnB;AAAA,EACA,UAAU;AACN,SAAK,MAAM,EAAE,QAAQ;AAAA,EACzB;AACJ;AAmBA,SAAS,OAAO,UAAU,SAAS;AAC/B,eACI,2BAA2B,QAAQ,iHACW;AAClD,MAAI,aAAa,CAAC,SAAS,UAAU;AACjC,6BAAyB,MAAM;AAAA,EACnC;AACA,MAAI,aAAa,SAAS,sBAAsB,QAAW;AACvD,YAAQ,KAAK,uGAAuG;AAAA,EACxH;AACA,QAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,MAAI,aAAa,SAAS,kBAAkB,OAAO,SAAS,IAAI,UAAU,IAAI;AAC9E,MAAI;AACJ,QAAM,cAAc,SAAS,IAAI,aAAa,MAAM,EAAE,UAAU,KAAK,CAAC;AACtE,QAAM,WAAW,SAAS,IAAI,wBAAwB;AACtD,MAAI,gBAAgB,MAAM;AAEtB,WAAO,iBAAiB,YAAY,MAAM,UAAU,QAAQ;AAC5D,QAAI,sBAAsB,0BAA0B,WAAW,WAAW,YAAY,MAAM;AAGxF,mBAAa;AAAA,IACjB;AAAA,EACJ,OACK;AAED,WAAO,iBAAiB,UAAU,SAAS,IAAI,eAAe,GAAG,QAAQ;AAAA,EAC7E;AACA,OAAK,WAAW;AAChB,MAAI,eAAe,MAAM;AAErB,SAAK,cAAc,WAAW,UAAU,MAAM,KAAK,QAAQ,CAAC;AAAA,EAChE;AACA,QAAM,YAAY,IAAI,cAAc,IAAI;AACxC,MAAI,WAAW;AACX,SAAK,YAAY,SAAS,aAAa;AACvC,UAAM,8BAA8B,2BAA2B,EAAE,UAAU,OAAO,KAAK,CAAC;AACxF,QAAI;AACA,6BAAuB,SAAS;AAAA,IACpC,UACA;AACI,iCAA2B,2BAA2B;AAAA,IAC1D;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,oBACW,MAAO,iCACjB,gBADiB;AAAA,EAEpB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,EACb,MAAM;AACF,SAAK,QAAQ;AACb,QAAI,aAAa,sBAAsB,GAAG;AACtC,YAAM,IAAI,MAAM,mEAAmE;AAAA,IACvF;AACA,QAAI,KAAK,UAAU,CAAC,+BAA+B,IAAI,GAAG;AACtD;AAAA,IACJ;AACA,SAAK,SAAS;AACd,UAAM,oBAAoB,CAAC,eAAe,KAAK,eAAe,CAAC,GAAG,KAAK,SAAS;AAChF,UAAM,WAAW,0BAA0B,IAAI;AAG/C,UAAM,sBAAsB,qBAAqB,KAAK;AACtD,QAAI;AACA,WAAK,aAAa;AAClB,WAAK,GAAG,iBAAiB;AAAA,IAC7B,UACA;AACI,2BAAqB,mBAAmB;AACxC,+BAAyB,MAAM,QAAQ;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,eAAe;AACX,QAAI,CAAC,KAAK,YAAY,QAAQ;AAC1B;AAAA,IACJ;AACA,UAAM,eAAe,kBAAkB,IAAI;AAC3C,QAAI;AAIA,aAAO,KAAK,WAAW,QAAQ;AAC3B,aAAK,WAAW,IAAI,EAAE;AAAA,MAC1B;AAAA,IACJ,UACA;AACI,WAAK,aAAa,CAAC;AACnB,wBAAkB,YAAY;AAAA,IAClC;AAAA,EACJ;AACJ,IAAI;AACJ,IAAM,oBACW,MAAO,iCACjB,mBADiB;AAAA,EAEpB,sBAAsB;AAClB,SAAK,UAAU,SAAS,IAAI;AAC5B,SAAK,SAAS;AAAA,MAAO;AAAA;AAAA,IAAsC;AAAA,EAC/D;AAAA,EACA,UAAU;AACN,oBAAgB,IAAI;AACpB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,UAAU,OAAO,IAAI;AAAA,EAC9B;AACJ,IAAI;AACJ,IAAM,oBACW,MAAO,iCACjB,mBADiB;AAAA,EAEpB,sBAAsB;AAClB,SAAK,KAAK,KAAK,KAAK;AACpB,8BAA0B,KAAK,IAAI;AACnC,SAAK,SAAS;AAAA,MAAO;AAAA;AAAA,IAAsC;AAAA,EAC/D;AAAA,EACA,UAAU;AACN,oBAAgB,IAAI;AACpB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,KAAK,OAAO,GAAG,OAAO,IAAI;AAAA,EACnC;AACJ,IAAI;AACJ,SAAS,iBAAiB,MAAM,UAAU,IAAI;AAC1C,QAAM,OAAO,OAAO,OAAO,gBAAgB;AAC3C,OAAK,OAAO;AACZ,OAAK,OAAO,OAAO,SAAS,cAAc,KAAK,UAAU;AACzD,OAAK,WAAW;AAChB,OAAK,KAAK;AACV,OAAK,OAAO,MAAM,oBAAI,IAAI;AAC1B,OAAK,OAAO,EAAE,IAAI,IAAI;AACtB,OAAK,oBAAoB,IAAI;AAC7B,SAAO;AACX;AACA,SAAS,iBAAiB,IAAI,WAAW,UAAU;AAC/C,QAAM,OAAO,OAAO,OAAO,gBAAgB;AAC3C,OAAK,KAAK;AACV,OAAK,YAAY;AACjB,OAAK,WAAW;AAChB,OAAK,OAAO,OAAO,SAAS,cAAc,KAAK,UAAU;AACzD,OAAK,UAAU,IAAI,IAAI;AACvB,OAAK,SAAS;AAAA,IAAO;AAAA;AAAA,EAAsC;AAC3D,SAAO;AACX;AAEA,IAAM,aAAa,CAAC,MAAM;AAC1B,SAAS,aAAa,sBAAsB,SAAS;AACjD,MAAI,OAAO,yBAAyB,YAAY;AAC5C,UAAM,SAAS,mBAAmB,sBAAuB,YAAa,SAAS,KAAK;AACpF,WAAO,0BAA0B,MAAM;AAAA,EAC3C,OACK;AACD,UAAM,SAAS,mBAAmB,qBAAqB,QAAQ,qBAAqB,aAAa,qBAAqB,KAAK;AAC3H,WAAO,0BAA0B,MAAM;AAAA,EAC3C;AACJ;AACA,SAAS,0BAA0B,QAAQ;AACvC,MAAI,WAAW;AACX,WAAO,WAAW,MAAM,kBAAkB,OAAO,CAAC;AAAA,EACtD;AACA,QAAM,OAAO,OAAO,MAAM;AAC1B,QAAM,iBAAiB;AACvB,iBAAe,MAAM,CAAC,aAAa,kBAAkB,MAAM,QAAQ;AACnE,iBAAe,SAAS,CAAC,aAAa,qBAAqB,MAAM,QAAQ;AACzE,iBAAe,aAAa,mBAAmB,KAAK,MAAM;AAC1D,SAAO;AACX;AAOA,IAAM,uCAAuC;AAC7C,SAAS,SAAS,SAAS;AACvB,MAAI,aAAa,CAAC,SAAS,UAAU;AACjC,6BAAyB,QAAQ;AAAA,EACrC;AACA,QAAM,mBAAmB,QAAQ;AACjC,QAAM,SAAU,QAAQ,UAAU,qBAAqB,MAAM;AAC7D,SAAO,IAAI,aAAa,QAAQ,UAAU,OAAO,GAAG,QAAQ,cAAc,QAAQ,QAAQ,eAAe,QAAQ,KAAK,IAAI,QAAW,QAAQ,YAAY,OAAO,QAAQ,GAAG,oCAAoC;AACnN;AAIA,IAAM,uBAAN,MAA2B;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACf,SAAK,QAAQ;AACb,SAAK,MAAM,MAAM,KAAK,IAAI,KAAK,IAAI;AACnC,SAAK,MAAM,SAAS,KAAK,OAAO,KAAK,IAAI;AACzC,SAAK,MAAM,aAAa;AAAA,EAC5B;AAAA,EACA,UAAU,SAAS,MAAM,KAAK,OAAO,MAAM,OAAO;AAAA,EAClD,OAAO,UAAU;AACb,SAAK,IAAI,SAASA,WAAU,KAAK,KAAK,CAAC,CAAC;AAAA,EAC5C;AAAA,EACA,YAAY,SAAS,MAAM,KAAK,OAAO,MAAM,aAAa,KAAK,OAAO,MAAM,WAAW;AAAA,EACvF,WAAW;AAIP,QAAI,KAAK,QAAQ,GAAG;AAChB,aAAO;AAAA,IACX;AACA,WAAO,KAAK,MAAM,MAAM;AAAA,EAC5B;AAAA,EACA,aAAa;AACT,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,eAAN,cAA2B,qBAAqB;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ;AAAA,EACA,YAAY,SAAS,UAAU,cAAc,OAAO,UAAU,uBAAuB,sCAAsC;AACvH;AAAA;AAAA;AAAA,MAGA,SAAS,MAAM;AACX,cAAM,cAAc,KAAK,MAAM,EAAE,SAAS;AAC1C,YAAI,CAAC,aAAa;AACd,iBAAO;AAAA,QACX;AAEA,YAAI,KAAK,MAAM,EAAE,WAAW,aAAa,KAAK,MAAM,GAAG;AACnD,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,WAAW,WAAW,GAAG;AAC1B,cAAI,sBAAsB;AACtB,kBAAM,IAAI,mBAAmB,KAAK,MAAM,CAAC;AAAA,UAC7C,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO,YAAY;AAAA,MACvB,GAAG,EAAE,MAAM,CAAC;AAAA,IAAC;AACb,SAAK,WAAW;AAChB,SAAK,QAAQ;AAEb,SAAK,aAAa,aAAa;AAAA,MAC3B,QAAQ;AAAA,MACR,aAAa,CAACC,cAAa,EAAE,SAAAA,UAAS,QAAQ,EAAE;AAAA,IACpD,CAAC;AAGD,SAAK,QAAQ,aAAa;AAAA;AAAA,MAEtB,QAAQ,KAAK;AAAA;AAAA,MAEb,aAAa,CAAC,YAAY,aAAa;AACnC,cAAM,SAAS,WAAW,YAAY,SAAY,SAAS;AAC3D,YAAI,CAAC,UAAU;AACX,iBAAO;AAAA,YACH;AAAA,YACA;AAAA,YACA,gBAAgB;AAAA,YAChB,QAAQ;AAAA,UACZ;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,YACH;AAAA,YACA;AAAA,YACA,gBAAgB,qBAAqB,SAAS,KAAK;AAAA;AAAA,YAEnD,QAAQ,SAAS,MAAM,WAAW,YAAY,WAAW,UACnD,SAAS,MAAM,SACf;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,SAAK,YAAY,OAAO,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,MAChD;AAAA,MACA,eAAe;AAAA,IACnB,CAAC;AACD,SAAK,eAAe,SAAS,IAAI,YAAY;AAE7C,SAAK,sBAAsB,SAAS,IAAI,UAAU,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AAAA,EACtF;AAAA,EACA,SAAS,SAAS,MAAM,qBAAqB,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1D,QAAQ,SAAS,MAAM;AACnB,UAAM,SAAS,KAAK,MAAM,EAAE,SAAS;AACrC,WAAO,UAAU,CAAC,WAAW,MAAM,IAAI,OAAO,QAAQ;AAAA,EAC1D,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,OAAO;AACP,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,UAAM,QAAQD,WAAU,KAAK,KAAK;AAClC,UAAM,QAAQA,WAAU,KAAK,KAAK;AAClC,QAAI,CAAC,OAAO;AACR,YAAM,UAAUA,WAAU,KAAK,KAAK;AACpC,UAAI,MAAM,WAAW,YAChB,KAAK,QAAQ,KAAK,MAAM,SAAS,KAAK,IAAI,YAAY,QAAQ;AAC/D;AAAA,MACJ;AAAA,IACJ;AAEA,SAAK,MAAM,IAAI;AAAA,MACX,YAAY,MAAM;AAAA,MAClB,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,QAAQ,OAAO,EAAE,MAAM,CAAC;AAAA,IAC5B,CAAC;AAGD,SAAK,oBAAoB;AAAA,EAC7B;AAAA,EACA,SAAS;AAEL,UAAM,EAAE,OAAO,IAAIA,WAAU,KAAK,KAAK;AACvC,QAAI,WAAW,UAAU,WAAW,WAAW;AAC3C,aAAO;AAAA,IACX;AAEA,SAAK,WAAW,OAAO,CAAC,EAAE,SAAS,OAAO,OAAO,EAAE,SAAS,QAAQ,SAAS,EAAE,EAAE;AACjF,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,UAAU,QAAQ;AACvB,SAAK,oBAAoB;AAEzB,SAAK,MAAM,IAAI;AAAA,MACX,YAAY,EAAE,SAAS,QAAW,QAAQ,EAAE;AAAA,MAC5C,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AAAA,EACM,aAAa;AAAA;AACf,YAAM,aAAa,KAAK,WAAW;AAGnC,YAAM,EAAE,QAAQ,eAAe,eAAe,IAAIA,WAAU,KAAK,KAAK;AACtE,UAAI,WAAW,YAAY,QAAW;AAElC;AAAA,MACJ,WACS,kBAAkB,WAAW;AAElC;AAAA,MACJ;AAEA,WAAK,oBAAoB;AAUzB,UAAI,qBAAsB,KAAK,qBAC3B,KAAK,aAAa,IAAI;AAC1B,YAAM,EAAE,QAAQ,YAAY,IAAK,KAAK,oBAAoB,IAAI,gBAAgB;AAC9E,UAAI;AAIA,cAAM,SAAS,MAAMA,WAAU,MAAM;AACjC,iBAAO,KAAK,SAAS;AAAA,YACjB,QAAQ,WAAW;AAAA;AAAA,YAEnB,SAAS,WAAW;AAAA,YACpB;AAAA,YACA,UAAU;AAAA,cACN,QAAQ;AAAA,YACZ;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAGD,YAAI,YAAY,WAAWA,WAAU,KAAK,UAAU,MAAM,YAAY;AAClE;AAAA,QACJ;AACA,aAAK,MAAM,IAAI;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB;AAAA,QACJ,CAAC;AAAA,MACL,SACO,KAAK;AACR,YAAI,YAAY,WAAWA,WAAU,KAAK,UAAU,MAAM,YAAY;AAClE;AAAA,QACJ;AACA,aAAK,MAAM,IAAI;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,QAAQ,OAAO,EAAE,OAAO,yBAAyB,GAAG,EAAE,CAAC;AAAA,QAC3D,CAAC;AAAA,MACL,UACA;AAEI,6BAAqB;AACrB,6BAAqB;AAAA,MACzB;AAAA,IACJ;AAAA;AAAA,EACA,sBAAsB;AAClB,IAAAA,WAAU,MAAM,KAAK,mBAAmB,MAAM,CAAC;AAC/C,SAAK,oBAAoB;AAEzB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAAA,EAC9B;AACJ;AAIA,SAAS,eAAe,OAAO;AAC3B,SAAO,CAAC,GAAG,MAAO,MAAM,UAAa,MAAM,SAAY,MAAM,IAAI,MAAM,GAAG,CAAC;AAC/E;AACA,SAAS,UAAU,SAAS;AACxB,MAAI,2BAA2B,OAAO,GAAG;AACrC,WAAO,QAAQ;AAAA,EACnB;AACA,SAAO,CAAO,WAAW;AACrB,QAAI;AACA,aAAO,OAAO,EAAE,OAAO,MAAM,QAAQ,OAAO,MAAM,EAAE,CAAC;AAAA,IACzD,SACO,KAAK;AACR,aAAO,OAAO,EAAE,OAAO,yBAAyB,GAAG,EAAE,CAAC;AAAA,IAC1D;AAAA,EACJ;AACJ;AACA,SAAS,2BAA2B,SAAS;AACzC,SAAO,CAAC,CAAC,QAAQ;AACrB;AAIA,SAAS,qBAAqB,OAAO;AACjC,UAAQ,MAAM,QAAQ;AAAA,IAClB,KAAK;AACD,aAAO,MAAM,WAAW,WAAW,IAAI,YAAY;AAAA,IACvD,KAAK;AACD,aAAO,WAAW,MAAM,OAAO,CAAC,IAAI,aAAa;AAAA,IACrD;AACI,aAAO,MAAM;AAAA,EACrB;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,MAAM,UAAU;AAC3B;AACA,SAAS,yBAAyB,OAAO;AACrC,MAAI,iBAAiB,OAAO;AACxB,WAAO;AAAA,EACX;AACA,SAAO,IAAI,qBAAqB,KAAK;AACzC;AACA,IAAM,qBAAN,cAAiC,MAAM;AAAA,EACnC,YAAY,OAAO;AACf,UAAM,YACA,0EAA0E,MAAM,OAAO,KACvF,MAAM,SAAS,EAAE,OAAO,MAAM,CAAC;AAAA,EACzC;AACJ;AACA,IAAM,uBAAN,cAAmC,MAAM;AAAA,EACrC,YAAY,OAAO;AACf,UAAM,YACA,4DAA4D,OAAO,KAAK,CAAC,sDACzE,OAAO,KAAK,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,EACzC;AACJ;", "names": ["computed", "injectorProfiler", "effect", "CheckNoChangesMode", "isInI18nBlock", "untracked", "request"]}