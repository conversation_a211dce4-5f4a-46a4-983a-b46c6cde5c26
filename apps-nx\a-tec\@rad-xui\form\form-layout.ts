import { CommonModule } from '@angular/common';
import { Component, computed, inject, input } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FormlyModule } from '@ngx-formly/core';
import { RadFormData } from './form-data';
import { FieldLayout, FieldMode, RadFormConfig } from './form-types';
import { FormBuilder } from './form-builder';

@Component({
    selector: 'rad-form-layout',
    template: `
    <div class="rad-form" ngClass="{'read-only': readOnly}">
        <formly-form [form]="form().form" [model]="data()" [fields]="fields()" [options]="form().options"  />
    </div>
    `,
    styles: [],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        FormlyModule
    ],

})
export class RadFormLayout {


    #builder = inject(FormBuilder);
    form = input<RadFormData | undefined>(undefined)

    layout = input<FieldLayout>('vertical');
    editMode = input<FieldMode>('edit');
    labelWidth = input<string|undefined>('120px');

    model = input<any>(undefined);

    data = computed(() => {
        return this.model() || this.form()?.model();
    });

    items = input<RadFormConfig>([]);

    private _fields = computed(() => {
        const items = this.items();
        const mode = this.editMode();
        const layout = this.layout();
        const labelWidth = this.labelWidth();

        if(!items || items.length === 0) {
            return [];
        }

        return items?.map((field) => {
            field.props = field.props ?? {};
            field.props.layout = layout;
            field.props.infoMode = mode === 'info';
            field.props.labelWidth = field.props.lableWidth ?? labelWidth ;
            return field;
        });
    });

    protected fields = computed(() => {
        const fields = this.#builder.createFields(this._fields());
        return fields;
    });

}