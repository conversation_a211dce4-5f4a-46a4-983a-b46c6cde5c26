{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/hu.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"hu\", [[\"de.\", \"du.\"], u, u], u, [[\"V\", \"H\", \"K\", \"Sz\", \"Cs\", \"P\", \"Sz\"], [\"V\", \"H\", \"K\", \"<PERSON>ze\", \"Cs\", \"P\", \"Szo\"], [\"vasárnap\", \"hétfő\", \"kedd\", \"szerda\", \"csütörtök\", \"péntek\", \"szombat\"], [\"V\", \"H\", \"K\", \"Sze\", \"<PERSON>s\", \"P\", \"<PERSON>zo\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A\", \"<PERSON>z\", \"O\", \"N\", \"D\"], [\"jan.\", \"febr.\", \"márc.\", \"ápr.\", \"máj.\", \"jún.\", \"júl.\", \"aug.\", \"szept.\", \"okt.\", \"nov.\", \"dec.\"], [\"január\", \"február\", \"m<PERSON>rcius\", \"április\", \"május\", \"június\", \"július\", \"augusztus\", \"szeptember\", \"október\", \"november\", \"december\"]], u, [[\"ie.\", \"isz.\"], [\"i. e.\", \"i. sz.\"], [\"Krisztus előtt\", \"időszámításunk szerint\"]], 1, [6, 0], [\"y. MM. dd.\", \"y. MMM d.\", \"y. MMMM d.\", \"y. MMMM d., EEEE\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"HUF\", \"Ft\", \"magyar forint\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"EUR\": [u, \"€\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"HUF\": [\"Ft\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"], \"XCD\": [u, \"$\"] }, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACjB,QAAM,IAAI;AACV,MAAI,MAAM;AACN,WAAO;AACX,SAAO;AACX;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,YAAY,SAAS,QAAQ,UAAU,aAAa,UAAU,SAAS,GAAG,CAAC,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,SAAS,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,WAAW,WAAW,SAAS,UAAU,UAAU,aAAa,cAAc,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,MAAM,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,kBAAkB,wBAAwB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,aAAa,cAAc,kBAAkB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,MAAM,iBAAiB,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,MAAM;", "names": []}