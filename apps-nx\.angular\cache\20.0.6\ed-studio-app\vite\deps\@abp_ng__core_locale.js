import {
  differentLocales
} from "./chunk-MYM5KR2K.js";
import "./chunk-SEP5HN4L.js";
import "./chunk-XYTEREF3.js";
import "./chunk-LDHXDJ6B.js";
import "./chunk-LJG7ZCKC.js";
import "./chunk-GUJAAXQB.js";
import "./chunk-YFKVMALY.js";
import "./chunk-G3WPIMP2.js";
import "./chunk-XJKSSPTD.js";
import {
  isDevMode
} from "./chunk-QQZDB4KQ.js";
import "./chunk-BYBDDJ2C.js";
import "./chunk-NY55TVYV.js";
import "./chunk-2O4VBYCZ.js";
import "./chunk-GJIVGOXW.js";
import "./chunk-K2JRLIHD.js";
import {
  __async,
  __spreadValues
} from "./chunk-QDB2FYN3.js";

// node_modules/@abp/ng.core/fesm2022/abp-ng.core-locale.mjs
var localeMap = {};
function loadLocale(locale) {
  const list = {
    "ar": () => import("./ar-FV5UQVVT.js"),
    "cs": () => import("./cs-YBVMR4OR.js"),
    "en": () => import("./en-KSPBMZ5A.js"),
    "en-GB": () => import("./en-GB-H3FFLAKL.js"),
    "es": () => import("./es-S2AA4CI4.js"),
    "de": () => import("./de-B7UTT5F5.js"),
    "fi": () => import("./fi-WLYTXFFE.js"),
    "fr": () => import("./fr-TVCVMM4Q.js"),
    "hi": () => import("./hi-X4KU7FKX.js"),
    "hu": () => import("./hu-AWJ4FPJ4.js"),
    "is": () => import("./is-LTVEBSOK.js"),
    "it": () => import("./it-CXB5EVA2.js"),
    "pt": () => import("./pt-ZCSU2YCB.js"),
    "tr": () => import("./tr-7D4GY2CM.js"),
    "ru": () => import("./ru-XMVCUFRS.js"),
    "ro": () => import("./ro-ZQK4O6N6.js"),
    "sk": () => import("./sk-AXBNSNL2.js"),
    "sl": () => import("./sl-QT3M5EWZ.js"),
    "zh-Hans": () => import("./zh-Hans-DXG7EVTY.js"),
    "zh-Hant": () => import("./zh-Hant-37PTPWUG.js")
  };
  return list[locale]();
}
function registerLocaleForEsBuild({ cultureNameLocaleFileMap = {}, errorHandlerFn = defaultLocalErrorHandlerFn } = {}) {
  return (locale) => {
    localeMap = __spreadValues(__spreadValues({}, differentLocales), cultureNameLocaleFileMap);
    const l = localeMap[locale] || locale;
    const localeSupportList = "ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant".split("|");
    if (localeSupportList.indexOf(l) == -1) {
      return;
    }
    return new Promise((resolve, reject) => {
      return loadLocale(l).then((val) => {
        let module = val;
        while (module.default) {
          module = module.default;
        }
        resolve({ default: module });
      }).catch((error) => {
        errorHandlerFn({
          resolve,
          reject,
          error,
          locale
        });
      });
    });
  };
}
function registerLocale({ cultureNameLocaleFileMap = {}, errorHandlerFn = defaultLocalErrorHandlerFn } = {}) {
  return (locale) => {
    localeMap = __spreadValues(__spreadValues({}, differentLocales), cultureNameLocaleFileMap);
    const localePath = `/locales/${localeMap[locale] || locale}`;
    return new Promise((resolve, reject) => {
      return import(
        /* webpackMode: "lazy-once" */
        /* webpackChunkName: "locales"*/
        /* webpackInclude: /[/\\](ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant)\.(mjs|js)$/ */
        /* webpackExclude: /[/\\]global|extra/ */
        /* @vite-ignore */
        `@angular/common${localePath}`
      ).then((val) => {
        let module = val;
        while (module.default) {
          module = module.default;
        }
        resolve({ default: module });
      }).catch((error) => {
        errorHandlerFn({
          resolve,
          reject,
          error,
          locale
        });
      });
    });
  };
}
var extraLocales = {};
function storeLocaleData(data, localeId) {
  extraLocales[localeId] = data;
}
function defaultLocalErrorHandlerFn(_0) {
  return __async(this, arguments, function* ({ locale, resolve }) {
    if (extraLocales[locale]) {
      resolve({ default: extraLocales[localeMap[locale] || locale] });
      return;
    }
    if (isDevMode()) {
      console.error(`Cannot find the ${locale} locale file. You can check how can add new culture at https://abp.io/docs/latest/framework/ui/angular/localization#adding-a-new-culture`);
    }
    resolve();
  });
}
export {
  defaultLocalErrorHandlerFn,
  registerLocale,
  registerLocaleForEsBuild,
  storeLocaleData
};
//# sourceMappingURL=@abp_ng__core_locale.js.map
