import { FormGroup } from '@angular/forms';
import { FormlyFormOptions } from '@ngx-formly/core';
import { computed, effect, output, signal, Signal } from '@angular/core';
import { filter } from 'rxjs';
import { IEditForm } from './form-types';




export class RadFormData<T=unknown> implements IEditForm {

  private _form = new FormGroup({});
  get form() {
    return this._form;
  }

  private  _model: Signal<T> = undefined;

  get model() {
    return this._model;
  }



  constructor(model: Signal<T> = undefined, readonly = false) {
    this._model = model;
    this._options.formState.disabled = readonly;
    this._readOnly.set(readonly);

  }

  private _readOnly = signal(false);

  readonly = computed(()=>this._readOnly());

  private _options: FormlyFormOptions = { formState: { disabled: false } };

  get options(){
    return this._options;
  }


  edit(value: boolean){
    this._options.formState.disabled = !value;  
    this._readOnly.set(this._options.formState.disabled);
  }








  validate(){
    //touch all form fields
    this.form.markAllAsTouched();
    return !this.form.invalid;
  }

}


export class RadSignalForm<T=unknown> implements IEditForm {

  private _form = new FormGroup({});
  get form() {
    return this._form;
  }

  private  _model: Signal<T> = undefined;

  get model(): Signal<T> {
    return this._model;
  }



  constructor(model: Signal<T> = undefined, editable: Signal<boolean> = signal(false)) {
    this._model = model;
    this._options.formState.disabled = !editable();

    effect(()=>{
      this._options.formState.disabled = !editable();
    });

    

  }


  private _options: FormlyFormOptions = { formState: { disabled: false } };

  get options(){
    return this._options;
  }


  validate(){
    //touch all form fields
    this.form.markAllAsTouched();
    return !this.form.invalid;
  }

}


class RadEditForm2<T=unknown> implements IEditForm {

  private _form = new FormGroup({});
  get form() {
    return this._form;
  }

  private _data: Signal<T> = undefined;
  private _originalModel: T = undefined;
  private  _model = signal<T>(undefined);


  model = computed(()=> {

    const model = this._model();
    return model;
  });


  changes = this._form.valueChanges.pipe(filter(() => this._form.touched));
  

  private _internalEditing = signal(false);
  private _externalEditing:Signal<boolean> = undefined;
  private _hasExternalEditing = false;
  editing = computed(()=>{
    if(this._hasExternalEditing){
      return this._externalEditing();
    }
    return this._internalEditing();
  });

  constructor(data: Signal<T>, editable?: Signal<boolean>) {

    this._data = data;
    this.setupEditing(editable);


    //const model = structuredClone(data()) as T;
    //this._originalModel = structuredClone(data()) as T;
    //this._model = signal(model);
    //this._options.formState.disabled = !this.editing();
    

    effect(()=> {
      this._options.formState.disabled = !this.editing();
    });

    effect(()=> {
      const data = this._data();
      this._originalModel = structuredClone(data) as T;
      this._model.set(data);
    });

    

  }

  private setupEditing(editing: Signal<boolean>) {
      if(editing){
        this._externalEditing = editing;
        this._hasExternalEditing = true;
      }

  }

  private _options: FormlyFormOptions = { formState: { disabled: false } };

  get options() {
    return this._options;
  }


  validate() {
    //touch all form fields
    this.form.markAllAsTouched();
    return !this.form.invalid;
  }

  updateData(): T {

    const data = this._model();
    this._internalEditing.set(false);
    return structuredClone(data) as T;
  }

  edit() {

    this._internalEditing.set(true);
  }

  cancel() {
    this._internalEditing.set(false);
    this._model.set(this._originalModel);
  }

}