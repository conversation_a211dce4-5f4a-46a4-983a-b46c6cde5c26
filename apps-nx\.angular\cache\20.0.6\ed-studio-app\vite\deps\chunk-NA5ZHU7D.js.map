{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-baseeditableholder.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { input, booleanAttribute, signal, computed, Directive } from '@angular/core';\nimport { BaseModelHolder } from 'primeng/basemodelholder';\nclass BaseEditableHolder extends BaseModelHolder {\n  /**\n   * There must be a value (if set).\n   * @defaultValue false\n   * @group Props\n   */\n  required = input(undefined, {\n    transform: booleanAttribute\n  });\n  /**\n   * When present, it specifies that the component should have invalid state style.\n   * @defaultValue false\n   * @group Props\n   */\n  invalid = input(undefined, {\n    transform: booleanAttribute\n  });\n  /**\n   * When present, it specifies that the component should have disabled state style.\n   * @defaultValue false\n   * @group Props\n   */\n  disabled = input(undefined, {\n    transform: booleanAttribute\n  });\n  /**\n   * When present, it specifies that the name of the input.\n   * @defaultValue undefined\n   * @group Props\n   */\n  name = input();\n  _disabled = signal(false);\n  $disabled = computed(() => this.disabled() || this._disabled());\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  writeDisabledState(value) {\n    this._disabled.set(value);\n  }\n  writeControlValue(value, setModelValue) {\n    // NOOP - this method should be overridden in the derived classes\n  }\n  /**** Angular ControlValueAccessors ****/\n  writeValue(value) {\n    this.writeControlValue(value, this.writeModelValue.bind(this));\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    this.writeDisabledState(val);\n    this.cd.markForCheck();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBaseEditableHolder_BaseFactory;\n    return function BaseEditableHolder_Factory(__ngFactoryType__) {\n      return (ɵBaseEditableHolder_BaseFactory || (ɵBaseEditableHolder_BaseFactory = i0.ɵɵgetInheritedFactory(BaseEditableHolder)))(__ngFactoryType__ || BaseEditableHolder);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseEditableHolder,\n    inputs: {\n      required: [1, \"required\"],\n      invalid: [1, \"invalid\"],\n      disabled: [1, \"disabled\"],\n      name: [1, \"name\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseEditableHolder, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseEditableHolder };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,IAAM,qBAAN,MAAM,4BAA2B,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/C,WAAW,MAAM,QAAW;AAAA,IAC1B,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,MAAM,QAAW;AAAA,IACzB,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,MAAM,QAAW;AAAA,IAC1B,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,MAAM;AAAA,EACb,YAAY,OAAO,KAAK;AAAA,EACxB,YAAY,SAAS,MAAM,KAAK,SAAS,KAAK,KAAK,UAAU,CAAC;AAAA,EAC9D,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,mBAAmB,OAAO;AACxB,SAAK,UAAU,IAAI,KAAK;AAAA,EAC1B;AAAA,EACA,kBAAkB,OAAO,eAAe;AAAA,EAExC;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,SAAK,kBAAkB,OAAO,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,EAC/D;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,iBAAiB,KAAK;AACpB,SAAK,mBAAmB,GAAG;AAC3B,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,MAAM,CAAC,GAAG,MAAM;AAAA,IAClB;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}