{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.core/fesm2022/abp-ng.core-locale.mjs"], "sourcesContent": ["import { differentLocales } from '@abp/ng.core';\nimport { isDevMode } from '@angular/core';\n\nlet localeMap = {};\nfunction loadLocale(locale) {\n    // hard coded list works with esbuild. Source https://github.com/angular/angular-cli/issues/26904#issuecomment-1903596563\n    const list = {\n        'ar': () => import('@angular/common/locales/ar'),\n        'cs': () => import('@angular/common/locales/cs'),\n        'en': () => import('@angular/common/locales/en'),\n        'en-GB': () => import('@angular/common/locales/en-GB'),\n        'es': () => import('@angular/common/locales/es'),\n        'de': () => import('@angular/common/locales/de'),\n        'fi': () => import('@angular/common/locales/fi'),\n        'fr': () => import('@angular/common/locales/fr'),\n        'hi': () => import('@angular/common/locales/hi'),\n        'hu': () => import('@angular/common/locales/hu'),\n        'is': () => import('@angular/common/locales/is'),\n        'it': () => import('@angular/common/locales/it'),\n        'pt': () => import('@angular/common/locales/pt'),\n        'tr': () => import('@angular/common/locales/tr'),\n        'ru': () => import('@angular/common/locales/ru'),\n        'ro': () => import('@angular/common/locales/ro'),\n        'sk': () => import('@angular/common/locales/sk'),\n        'sl': () => import('@angular/common/locales/sl'),\n        'zh-Hans': () => import('@angular/common/locales/zh-Hans'),\n        'zh-Hant': () => import('@angular/common/locales/zh-Hant')\n    };\n    return list[locale]();\n}\nfunction registerLocaleForEsBuild({ cultureNameLocaleFileMap = {}, errorHandlerFn = defaultLocalErrorHandlerFn, } = {}) {\n    return (locale) => {\n        localeMap = { ...differentLocales, ...cultureNameLocaleFileMap };\n        const l = localeMap[locale] || locale;\n        const localeSupportList = \"ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant\".split(\"|\");\n        if (localeSupportList.indexOf(l) == -1) {\n            return;\n        }\n        return new Promise((resolve, reject) => {\n            return loadLocale(l)\n                .then(val => {\n                let module = val;\n                while (module.default) {\n                    module = module.default;\n                }\n                resolve({ default: module });\n            })\n                .catch(error => {\n                errorHandlerFn({\n                    resolve,\n                    reject,\n                    error,\n                    locale,\n                });\n            });\n        });\n    };\n}\nfunction registerLocale({ cultureNameLocaleFileMap = {}, errorHandlerFn = defaultLocalErrorHandlerFn, } = {}) {\n    return (locale) => {\n        localeMap = { ...differentLocales, ...cultureNameLocaleFileMap };\n        const localePath = `/locales/${localeMap[locale] || locale}`;\n        return new Promise((resolve, reject) => {\n            return import(\n            /* webpackMode: \"lazy-once\" */\n            /* webpackChunkName: \"locales\"*/\n            /* webpackInclude: /[/\\\\](ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant)\\.(mjs|js)$/ */\n            /* webpackExclude: /[/\\\\]global|extra/ */\n            /* @vite-ignore */\n            `@angular/common${localePath}`)\n                .then(val => {\n                let module = val;\n                while (module.default) {\n                    module = module.default;\n                }\n                resolve({ default: module });\n            })\n                .catch(error => {\n                errorHandlerFn({\n                    resolve,\n                    reject,\n                    error,\n                    locale,\n                });\n            });\n        });\n    };\n}\nconst extraLocales = {};\nfunction storeLocaleData(data, localeId) {\n    extraLocales[localeId] = data;\n}\nasync function defaultLocalErrorHandlerFn({ locale, resolve }) {\n    if (extraLocales[locale]) {\n        resolve({ default: extraLocales[localeMap[locale] || locale] });\n        return;\n    }\n    if (isDevMode()) {\n        console.error(`Cannot find the ${locale} locale file. You can check how can add new culture at https://abp.io/docs/latest/framework/ui/angular/localization#adding-a-new-culture`);\n    }\n    resolve();\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { defaultLocalErrorHandlerFn, registerLocale, registerLocaleForEsBuild, storeLocaleData };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAI,YAAY,CAAC;AACjB,SAAS,WAAW,QAAQ;AAExB,QAAM,OAAO;AAAA,IACT,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,SAAS,MAAM,OAAO,qBAA+B;AAAA,IACrD,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,WAAW,MAAM,OAAO,uBAAiC;AAAA,IACzD,WAAW,MAAM,OAAO,uBAAiC;AAAA,EAC7D;AACA,SAAO,KAAK,MAAM,EAAE;AACxB;AACA,SAAS,yBAAyB,EAAE,2BAA2B,CAAC,GAAG,iBAAiB,2BAA4B,IAAI,CAAC,GAAG;AACpH,SAAO,CAAC,WAAW;AACf,gBAAY,kCAAK,mBAAqB;AACtC,UAAM,IAAI,UAAU,MAAM,KAAK;AAC/B,UAAM,oBAAoB,2EAA2E,MAAM,GAAG;AAC9G,QAAI,kBAAkB,QAAQ,CAAC,KAAK,IAAI;AACpC;AAAA,IACJ;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,aAAO,WAAW,CAAC,EACd,KAAK,SAAO;AACb,YAAI,SAAS;AACb,eAAO,OAAO,SAAS;AACnB,mBAAS,OAAO;AAAA,QACpB;AACA,gBAAQ,EAAE,SAAS,OAAO,CAAC;AAAA,MAC/B,CAAC,EACI,MAAM,WAAS;AAChB,uBAAe;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;AACA,SAAS,eAAe,EAAE,2BAA2B,CAAC,GAAG,iBAAiB,2BAA4B,IAAI,CAAC,GAAG;AAC1G,SAAO,CAAC,WAAW;AACf,gBAAY,kCAAK,mBAAqB;AACtC,UAAM,aAAa,YAAY,UAAU,MAAM,KAAK,MAAM;AAC1D,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMP,kBAAkB,UAAU;AAAA,QACvB,KAAK,SAAO;AACb,YAAI,SAAS;AACb,eAAO,OAAO,SAAS;AACnB,mBAAS,OAAO;AAAA,QACpB;AACA,gBAAQ,EAAE,SAAS,OAAO,CAAC;AAAA,MAC/B,CAAC,EACI,MAAM,WAAS;AAChB,uBAAe;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;AACA,IAAM,eAAe,CAAC;AACtB,SAAS,gBAAgB,MAAM,UAAU;AACrC,eAAa,QAAQ,IAAI;AAC7B;AACA,SAAe,2BAA2B,IAAqB;AAAA,6CAArB,EAAE,QAAQ,QAAQ,GAAG;AAC3D,QAAI,aAAa,MAAM,GAAG;AACtB,cAAQ,EAAE,SAAS,aAAa,UAAU,MAAM,KAAK,MAAM,EAAE,CAAC;AAC9D;AAAA,IACJ;AACA,QAAI,UAAU,GAAG;AACb,cAAQ,MAAM,mBAAmB,MAAM,0IAA0I;AAAA,IACrL;AACA,YAAQ;AAAA,EACZ;AAAA;", "names": []}