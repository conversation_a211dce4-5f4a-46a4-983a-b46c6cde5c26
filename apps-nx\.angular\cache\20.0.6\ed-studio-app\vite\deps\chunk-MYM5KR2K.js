import {
  ActivatedRoute,
  NavigationCancel,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  PRIMARY_OUTLET,
  Router,
  RouterModule,
  RouterOutlet,
  TitleStrategy
} from "./chunk-SEP5HN4L.js";
import {
  DomSanitizer,
  Title
} from "./chunk-XYTEREF3.js";
import {
  toSignal
} from "./chunk-LJG7ZCKC.js";
import {
  FormGroupDirective,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from "./chunk-GUJAAXQB.js";
import {
  CommonModule,
  DATE_PIPE_DEFAULT_TIMEZONE,
  DatePipe,
  NgComponentOutlet,
  registerLocaleData
} from "./chunk-YFKVMALY.js";
import {
  HTTP_INTERCEPTORS,
  HttpClient,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpHeaders,
  HttpParams,
  provideHttpClient,
  withInterceptorsFromDi,
  withXsrfConfiguration
} from "./chunk-G3WPIMP2.js";
import {
  ApplicationRef,
  ChangeDetectorRef,
  Compiler,
  Component,
  ComponentFactoryResolver$1,
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Injectable,
  Input,
  IterableDiffers,
  LOCALE_ID,
  NgModule,
  NgModuleFactory$1,
  NgZone,
  Optional,
  Output,
  Pipe,
  SecurityContext,
  Self,
  SkipSelf,
  TemplateRef,
  ViewContainerRef,
  isDevMode,
  provideAppInitializer,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵgetInheritedFactory,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresolveWindow,
  ɵɵtemplate
} from "./chunk-QQZDB4KQ.js";
import {
  DOCUMENT,
  InjectionToken,
  Injector,
  computed,
  effect,
  inject,
  makeEnvironmentProviders,
  signal,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵinject
} from "./chunk-BYBDDJ2C.js";
import {
  firstValueFrom,
  fromEvent,
  lastValueFrom
} from "./chunk-2O4VBYCZ.js";
import {
  BehaviorSubject,
  EMPTY,
  Observable,
  ReplaySubject,
  Subject,
  Subscription,
  catchError,
  combineLatest,
  concat,
  debounceTime,
  delay,
  distinctUntilChanged,
  filter,
  finalize,
  from,
  map,
  mapTo,
  of,
  pipe,
  retryWhen,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
  throwError,
  timer
} from "./chunk-GJIVGOXW.js";
import {
  __async,
  __objRest,
  __privateAdd,
  __privateGet,
  __privateMethod,
  __privateSet,
  __spreadProps,
  __spreadValues
} from "./chunk-QDB2FYN3.js";

// node_modules/just-compare/index.mjs
var collectionCompare = compare;
function compare(value1, value2) {
  if (value1 === value2) {
    return true;
  }
  if (value1 !== value1 && value2 !== value2) {
    return true;
  }
  if (typeof value1 != typeof value2 || // primitive != primitive wrapper
  {}.toString.call(value1) != {}.toString.call(value2)) {
    return false;
  }
  if (value1 !== Object(value1)) {
    return false;
  }
  if (!value1) {
    return false;
  }
  if (Array.isArray(value1)) {
    return compareArrays(value1, value2);
  }
  if ({}.toString.call(value1) == "[object Set]") {
    return compareArrays(Array.from(value1), Array.from(value2));
  }
  if ({}.toString.call(value1) == "[object Object]") {
    return compareObjects(value1, value2);
  }
  return compareNativeSubtypes(value1, value2);
}
function compareNativeSubtypes(value1, value2) {
  return value1.toString() === value2.toString();
}
function compareArrays(value1, value2) {
  var len = value1.length;
  if (len != value2.length) {
    return false;
  }
  for (var i = 0; i < len; i++) {
    if (!compare(value1[i], value2[i])) {
      return false;
    }
  }
  return true;
}
function compareObjects(value1, value2) {
  var keys1 = Object.keys(value1);
  var len = keys1.length;
  if (len != Object.keys(value2).length) {
    return false;
  }
  for (var i = 0; i < len; i++) {
    var key1 = keys1[i];
    if (!(value2.hasOwnProperty(key1) && compare(value1[key1], value2[key1]))) {
      return false;
    }
  }
  return true;
}

// node_modules/just-clone/index.mjs
var collectionClone = clone;
function clone(obj) {
  let result = obj;
  var type = {}.toString.call(obj).slice(8, -1);
  if (type == "Set") {
    return new Set([...obj].map((value) => clone(value)));
  }
  if (type == "Map") {
    return new Map([...obj].map((kv) => [clone(kv[0]), clone(kv[1])]));
  }
  if (type == "Date") {
    return new Date(obj.getTime());
  }
  if (type == "RegExp") {
    return RegExp(obj.source, getRegExpFlags(obj));
  }
  if (type == "Array" || type == "Object") {
    result = Array.isArray(obj) ? [] : {};
    for (var key in obj) {
      result[key] = clone(obj[key]);
    }
  }
  return result;
}
function getRegExpFlags(regExp) {
  if (typeof regExp.source.flags == "string") {
    return regExp.source.flags;
  } else {
    var flags = [];
    regExp.global && flags.push("g");
    regExp.ignoreCase && flags.push("i");
    regExp.multiline && flags.push("m");
    regExp.sticky && flags.push("y");
    regExp.unicode && flags.push("u");
    return flags.join("");
  }
}

// node_modules/luxon/src/errors.js
var LuxonError = class extends Error {
};
var InvalidDateTimeError = class extends LuxonError {
  constructor(reason) {
    super(`Invalid DateTime: ${reason.toMessage()}`);
  }
};
var InvalidIntervalError = class extends LuxonError {
  constructor(reason) {
    super(`Invalid Interval: ${reason.toMessage()}`);
  }
};
var InvalidDurationError = class extends LuxonError {
  constructor(reason) {
    super(`Invalid Duration: ${reason.toMessage()}`);
  }
};
var ConflictingSpecificationError = class extends LuxonError {
};
var InvalidUnitError = class extends LuxonError {
  constructor(unit) {
    super(`Invalid unit ${unit}`);
  }
};
var InvalidArgumentError = class extends LuxonError {
};
var ZoneIsAbstractError = class extends LuxonError {
  constructor() {
    super("Zone is an abstract class");
  }
};

// node_modules/luxon/src/impl/formats.js
var n = "numeric";
var s = "short";
var l = "long";
var DATE_SHORT = {
  year: n,
  month: n,
  day: n
};
var DATE_MED = {
  year: n,
  month: s,
  day: n
};
var DATE_MED_WITH_WEEKDAY = {
  year: n,
  month: s,
  day: n,
  weekday: s
};
var DATE_FULL = {
  year: n,
  month: l,
  day: n
};
var DATE_HUGE = {
  year: n,
  month: l,
  day: n,
  weekday: l
};
var TIME_SIMPLE = {
  hour: n,
  minute: n
};
var TIME_WITH_SECONDS = {
  hour: n,
  minute: n,
  second: n
};
var TIME_WITH_SHORT_OFFSET = {
  hour: n,
  minute: n,
  second: n,
  timeZoneName: s
};
var TIME_WITH_LONG_OFFSET = {
  hour: n,
  minute: n,
  second: n,
  timeZoneName: l
};
var TIME_24_SIMPLE = {
  hour: n,
  minute: n,
  hourCycle: "h23"
};
var TIME_24_WITH_SECONDS = {
  hour: n,
  minute: n,
  second: n,
  hourCycle: "h23"
};
var TIME_24_WITH_SHORT_OFFSET = {
  hour: n,
  minute: n,
  second: n,
  hourCycle: "h23",
  timeZoneName: s
};
var TIME_24_WITH_LONG_OFFSET = {
  hour: n,
  minute: n,
  second: n,
  hourCycle: "h23",
  timeZoneName: l
};
var DATETIME_SHORT = {
  year: n,
  month: n,
  day: n,
  hour: n,
  minute: n
};
var DATETIME_SHORT_WITH_SECONDS = {
  year: n,
  month: n,
  day: n,
  hour: n,
  minute: n,
  second: n
};
var DATETIME_MED = {
  year: n,
  month: s,
  day: n,
  hour: n,
  minute: n
};
var DATETIME_MED_WITH_SECONDS = {
  year: n,
  month: s,
  day: n,
  hour: n,
  minute: n,
  second: n
};
var DATETIME_MED_WITH_WEEKDAY = {
  year: n,
  month: s,
  day: n,
  weekday: s,
  hour: n,
  minute: n
};
var DATETIME_FULL = {
  year: n,
  month: l,
  day: n,
  hour: n,
  minute: n,
  timeZoneName: s
};
var DATETIME_FULL_WITH_SECONDS = {
  year: n,
  month: l,
  day: n,
  hour: n,
  minute: n,
  second: n,
  timeZoneName: s
};
var DATETIME_HUGE = {
  year: n,
  month: l,
  day: n,
  weekday: l,
  hour: n,
  minute: n,
  timeZoneName: l
};
var DATETIME_HUGE_WITH_SECONDS = {
  year: n,
  month: l,
  day: n,
  weekday: l,
  hour: n,
  minute: n,
  second: n,
  timeZoneName: l
};

// node_modules/luxon/src/zone.js
var Zone = class {
  /**
   * The type of zone
   * @abstract
   * @type {string}
   */
  get type() {
    throw new ZoneIsAbstractError();
  }
  /**
   * The name of this zone.
   * @abstract
   * @type {string}
   */
  get name() {
    throw new ZoneIsAbstractError();
  }
  /**
   * The IANA name of this zone.
   * Defaults to `name` if not overwritten by a subclass.
   * @abstract
   * @type {string}
   */
  get ianaName() {
    return this.name;
  }
  /**
   * Returns whether the offset is known to be fixed for the whole year.
   * @abstract
   * @type {boolean}
   */
  get isUniversal() {
    throw new ZoneIsAbstractError();
  }
  /**
   * Returns the offset's common name (such as EST) at the specified timestamp
   * @abstract
   * @param {number} ts - Epoch milliseconds for which to get the name
   * @param {Object} opts - Options to affect the format
   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.
   * @param {string} opts.locale - What locale to return the offset name in.
   * @return {string}
   */
  offsetName(ts, opts) {
    throw new ZoneIsAbstractError();
  }
  /**
   * Returns the offset's value as a string
   * @abstract
   * @param {number} ts - Epoch milliseconds for which to get the offset
   * @param {string} format - What style of offset to return.
   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively
   * @return {string}
   */
  formatOffset(ts, format) {
    throw new ZoneIsAbstractError();
  }
  /**
   * Return the offset in minutes for this zone at the specified timestamp.
   * @abstract
   * @param {number} ts - Epoch milliseconds for which to compute the offset
   * @return {number}
   */
  offset(ts) {
    throw new ZoneIsAbstractError();
  }
  /**
   * Return whether this Zone is equal to another zone
   * @abstract
   * @param {Zone} otherZone - the zone to compare
   * @return {boolean}
   */
  equals(otherZone) {
    throw new ZoneIsAbstractError();
  }
  /**
   * Return whether this Zone is valid.
   * @abstract
   * @type {boolean}
   */
  get isValid() {
    throw new ZoneIsAbstractError();
  }
};

// node_modules/luxon/src/zones/systemZone.js
var singleton = null;
var SystemZone = class _SystemZone extends Zone {
  /**
   * Get a singleton instance of the local zone
   * @return {SystemZone}
   */
  static get instance() {
    if (singleton === null) {
      singleton = new _SystemZone();
    }
    return singleton;
  }
  /** @override **/
  get type() {
    return "system";
  }
  /** @override **/
  get name() {
    return new Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
  /** @override **/
  get isUniversal() {
    return false;
  }
  /** @override **/
  offsetName(ts, { format, locale }) {
    return parseZoneInfo(ts, format, locale);
  }
  /** @override **/
  formatOffset(ts, format) {
    return formatOffset(this.offset(ts), format);
  }
  /** @override **/
  offset(ts) {
    return -new Date(ts).getTimezoneOffset();
  }
  /** @override **/
  equals(otherZone) {
    return otherZone.type === "system";
  }
  /** @override **/
  get isValid() {
    return true;
  }
};

// node_modules/luxon/src/zones/IANAZone.js
var dtfCache = /* @__PURE__ */ new Map();
function makeDTF(zoneName) {
  let dtf = dtfCache.get(zoneName);
  if (dtf === void 0) {
    dtf = new Intl.DateTimeFormat("en-US", {
      hour12: false,
      timeZone: zoneName,
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      era: "short"
    });
    dtfCache.set(zoneName, dtf);
  }
  return dtf;
}
var typeToPos = {
  year: 0,
  month: 1,
  day: 2,
  era: 3,
  hour: 4,
  minute: 5,
  second: 6
};
function hackyOffset(dtf, date) {
  const formatted = dtf.format(date).replace(/\u200E/g, ""), parsed = /(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(formatted), [, fMonth, fDay, fYear, fadOrBc, fHour, fMinute, fSecond] = parsed;
  return [fYear, fMonth, fDay, fadOrBc, fHour, fMinute, fSecond];
}
function partsOffset(dtf, date) {
  const formatted = dtf.formatToParts(date);
  const filled = [];
  for (let i = 0; i < formatted.length; i++) {
    const { type, value } = formatted[i];
    const pos = typeToPos[type];
    if (type === "era") {
      filled[pos] = value;
    } else if (!isUndefined(pos)) {
      filled[pos] = parseInt(value, 10);
    }
  }
  return filled;
}
var ianaZoneCache = /* @__PURE__ */ new Map();
var IANAZone = class _IANAZone extends Zone {
  /**
   * @param {string} name - Zone name
   * @return {IANAZone}
   */
  static create(name) {
    let zone = ianaZoneCache.get(name);
    if (zone === void 0) {
      ianaZoneCache.set(name, zone = new _IANAZone(name));
    }
    return zone;
  }
  /**
   * Reset local caches. Should only be necessary in testing scenarios.
   * @return {void}
   */
  static resetCache() {
    ianaZoneCache.clear();
    dtfCache.clear();
  }
  /**
   * Returns whether the provided string is a valid specifier. This only checks the string's format, not that the specifier identifies a known zone; see isValidZone for that.
   * @param {string} s - The string to check validity on
   * @example IANAZone.isValidSpecifier("America/New_York") //=> true
   * @example IANAZone.isValidSpecifier("Sport~~blorp") //=> false
   * @deprecated For backward compatibility, this forwards to isValidZone, better use `isValidZone()` directly instead.
   * @return {boolean}
   */
  static isValidSpecifier(s2) {
    return this.isValidZone(s2);
  }
  /**
   * Returns whether the provided string identifies a real zone
   * @param {string} zone - The string to check
   * @example IANAZone.isValidZone("America/New_York") //=> true
   * @example IANAZone.isValidZone("Fantasia/Castle") //=> false
   * @example IANAZone.isValidZone("Sport~~blorp") //=> false
   * @return {boolean}
   */
  static isValidZone(zone) {
    if (!zone) {
      return false;
    }
    try {
      new Intl.DateTimeFormat("en-US", { timeZone: zone }).format();
      return true;
    } catch (e) {
      return false;
    }
  }
  constructor(name) {
    super();
    this.zoneName = name;
    this.valid = _IANAZone.isValidZone(name);
  }
  /**
   * The type of zone. `iana` for all instances of `IANAZone`.
   * @override
   * @type {string}
   */
  get type() {
    return "iana";
  }
  /**
   * The name of this zone (i.e. the IANA zone name).
   * @override
   * @type {string}
   */
  get name() {
    return this.zoneName;
  }
  /**
   * Returns whether the offset is known to be fixed for the whole year:
   * Always returns false for all IANA zones.
   * @override
   * @type {boolean}
   */
  get isUniversal() {
    return false;
  }
  /**
   * Returns the offset's common name (such as EST) at the specified timestamp
   * @override
   * @param {number} ts - Epoch milliseconds for which to get the name
   * @param {Object} opts - Options to affect the format
   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.
   * @param {string} opts.locale - What locale to return the offset name in.
   * @return {string}
   */
  offsetName(ts, { format, locale }) {
    return parseZoneInfo(ts, format, locale, this.name);
  }
  /**
   * Returns the offset's value as a string
   * @override
   * @param {number} ts - Epoch milliseconds for which to get the offset
   * @param {string} format - What style of offset to return.
   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively
   * @return {string}
   */
  formatOffset(ts, format) {
    return formatOffset(this.offset(ts), format);
  }
  /**
   * Return the offset in minutes for this zone at the specified timestamp.
   * @override
   * @param {number} ts - Epoch milliseconds for which to compute the offset
   * @return {number}
   */
  offset(ts) {
    if (!this.valid) return NaN;
    const date = new Date(ts);
    if (isNaN(date)) return NaN;
    const dtf = makeDTF(this.name);
    let [year, month, day, adOrBc, hour, minute, second] = dtf.formatToParts ? partsOffset(dtf, date) : hackyOffset(dtf, date);
    if (adOrBc === "BC") {
      year = -Math.abs(year) + 1;
    }
    const adjustedHour = hour === 24 ? 0 : hour;
    const asUTC = objToLocalTS({
      year,
      month,
      day,
      hour: adjustedHour,
      minute,
      second,
      millisecond: 0
    });
    let asTS = +date;
    const over = asTS % 1e3;
    asTS -= over >= 0 ? over : 1e3 + over;
    return (asUTC - asTS) / (60 * 1e3);
  }
  /**
   * Return whether this Zone is equal to another zone
   * @override
   * @param {Zone} otherZone - the zone to compare
   * @return {boolean}
   */
  equals(otherZone) {
    return otherZone.type === "iana" && otherZone.name === this.name;
  }
  /**
   * Return whether this Zone is valid.
   * @override
   * @type {boolean}
   */
  get isValid() {
    return this.valid;
  }
};

// node_modules/luxon/src/impl/locale.js
var intlLFCache = {};
function getCachedLF(locString, opts = {}) {
  const key = JSON.stringify([locString, opts]);
  let dtf = intlLFCache[key];
  if (!dtf) {
    dtf = new Intl.ListFormat(locString, opts);
    intlLFCache[key] = dtf;
  }
  return dtf;
}
var intlDTCache = /* @__PURE__ */ new Map();
function getCachedDTF(locString, opts = {}) {
  const key = JSON.stringify([locString, opts]);
  let dtf = intlDTCache.get(key);
  if (dtf === void 0) {
    dtf = new Intl.DateTimeFormat(locString, opts);
    intlDTCache.set(key, dtf);
  }
  return dtf;
}
var intlNumCache = /* @__PURE__ */ new Map();
function getCachedINF(locString, opts = {}) {
  const key = JSON.stringify([locString, opts]);
  let inf = intlNumCache.get(key);
  if (inf === void 0) {
    inf = new Intl.NumberFormat(locString, opts);
    intlNumCache.set(key, inf);
  }
  return inf;
}
var intlRelCache = /* @__PURE__ */ new Map();
function getCachedRTF(locString, opts = {}) {
  const _a = opts, { base } = _a, cacheKeyOpts = __objRest(_a, ["base"]);
  const key = JSON.stringify([locString, cacheKeyOpts]);
  let inf = intlRelCache.get(key);
  if (inf === void 0) {
    inf = new Intl.RelativeTimeFormat(locString, opts);
    intlRelCache.set(key, inf);
  }
  return inf;
}
var sysLocaleCache = null;
function systemLocale() {
  if (sysLocaleCache) {
    return sysLocaleCache;
  } else {
    sysLocaleCache = new Intl.DateTimeFormat().resolvedOptions().locale;
    return sysLocaleCache;
  }
}
var intlResolvedOptionsCache = /* @__PURE__ */ new Map();
function getCachedIntResolvedOptions(locString) {
  let opts = intlResolvedOptionsCache.get(locString);
  if (opts === void 0) {
    opts = new Intl.DateTimeFormat(locString).resolvedOptions();
    intlResolvedOptionsCache.set(locString, opts);
  }
  return opts;
}
var weekInfoCache = /* @__PURE__ */ new Map();
function getCachedWeekInfo(locString) {
  let data = weekInfoCache.get(locString);
  if (!data) {
    const locale = new Intl.Locale(locString);
    data = "getWeekInfo" in locale ? locale.getWeekInfo() : locale.weekInfo;
    if (!("minimalDays" in data)) {
      data = __spreadValues(__spreadValues({}, fallbackWeekSettings), data);
    }
    weekInfoCache.set(locString, data);
  }
  return data;
}
function parseLocaleString(localeStr) {
  const xIndex = localeStr.indexOf("-x-");
  if (xIndex !== -1) {
    localeStr = localeStr.substring(0, xIndex);
  }
  const uIndex = localeStr.indexOf("-u-");
  if (uIndex === -1) {
    return [localeStr];
  } else {
    let options;
    let selectedStr;
    try {
      options = getCachedDTF(localeStr).resolvedOptions();
      selectedStr = localeStr;
    } catch (e) {
      const smaller = localeStr.substring(0, uIndex);
      options = getCachedDTF(smaller).resolvedOptions();
      selectedStr = smaller;
    }
    const { numberingSystem, calendar } = options;
    return [selectedStr, numberingSystem, calendar];
  }
}
function intlConfigString(localeStr, numberingSystem, outputCalendar) {
  if (outputCalendar || numberingSystem) {
    if (!localeStr.includes("-u-")) {
      localeStr += "-u";
    }
    if (outputCalendar) {
      localeStr += `-ca-${outputCalendar}`;
    }
    if (numberingSystem) {
      localeStr += `-nu-${numberingSystem}`;
    }
    return localeStr;
  } else {
    return localeStr;
  }
}
function mapMonths(f) {
  const ms = [];
  for (let i = 1; i <= 12; i++) {
    const dt = DateTime.utc(2009, i, 1);
    ms.push(f(dt));
  }
  return ms;
}
function mapWeekdays(f) {
  const ms = [];
  for (let i = 1; i <= 7; i++) {
    const dt = DateTime.utc(2016, 11, 13 + i);
    ms.push(f(dt));
  }
  return ms;
}
function listStuff(loc, length, englishFn, intlFn) {
  const mode = loc.listingMode();
  if (mode === "error") {
    return null;
  } else if (mode === "en") {
    return englishFn(length);
  } else {
    return intlFn(length);
  }
}
function supportsFastNumbers(loc) {
  if (loc.numberingSystem && loc.numberingSystem !== "latn") {
    return false;
  } else {
    return loc.numberingSystem === "latn" || !loc.locale || loc.locale.startsWith("en") || getCachedIntResolvedOptions(loc.locale).numberingSystem === "latn";
  }
}
var PolyNumberFormatter = class {
  constructor(intl, forceSimple, opts) {
    this.padTo = opts.padTo || 0;
    this.floor = opts.floor || false;
    const _a = opts, { padTo, floor } = _a, otherOpts = __objRest(_a, ["padTo", "floor"]);
    if (!forceSimple || Object.keys(otherOpts).length > 0) {
      const intlOpts = __spreadValues({ useGrouping: false }, opts);
      if (opts.padTo > 0) intlOpts.minimumIntegerDigits = opts.padTo;
      this.inf = getCachedINF(intl, intlOpts);
    }
  }
  format(i) {
    if (this.inf) {
      const fixed = this.floor ? Math.floor(i) : i;
      return this.inf.format(fixed);
    } else {
      const fixed = this.floor ? Math.floor(i) : roundTo(i, 3);
      return padStart(fixed, this.padTo);
    }
  }
};
var PolyDateFormatter = class {
  constructor(dt, intl, opts) {
    this.opts = opts;
    this.originalZone = void 0;
    let z = void 0;
    if (this.opts.timeZone) {
      this.dt = dt;
    } else if (dt.zone.type === "fixed") {
      const gmtOffset = -1 * (dt.offset / 60);
      const offsetZ = gmtOffset >= 0 ? `Etc/GMT+${gmtOffset}` : `Etc/GMT${gmtOffset}`;
      if (dt.offset !== 0 && IANAZone.create(offsetZ).valid) {
        z = offsetZ;
        this.dt = dt;
      } else {
        z = "UTC";
        this.dt = dt.offset === 0 ? dt : dt.setZone("UTC").plus({ minutes: dt.offset });
        this.originalZone = dt.zone;
      }
    } else if (dt.zone.type === "system") {
      this.dt = dt;
    } else if (dt.zone.type === "iana") {
      this.dt = dt;
      z = dt.zone.name;
    } else {
      z = "UTC";
      this.dt = dt.setZone("UTC").plus({ minutes: dt.offset });
      this.originalZone = dt.zone;
    }
    const intlOpts = __spreadValues({}, this.opts);
    intlOpts.timeZone = intlOpts.timeZone || z;
    this.dtf = getCachedDTF(intl, intlOpts);
  }
  format() {
    if (this.originalZone) {
      return this.formatToParts().map(({ value }) => value).join("");
    }
    return this.dtf.format(this.dt.toJSDate());
  }
  formatToParts() {
    const parts = this.dtf.formatToParts(this.dt.toJSDate());
    if (this.originalZone) {
      return parts.map((part) => {
        if (part.type === "timeZoneName") {
          const offsetName = this.originalZone.offsetName(this.dt.ts, {
            locale: this.dt.locale,
            format: this.opts.timeZoneName
          });
          return __spreadProps(__spreadValues({}, part), {
            value: offsetName
          });
        } else {
          return part;
        }
      });
    }
    return parts;
  }
  resolvedOptions() {
    return this.dtf.resolvedOptions();
  }
};
var PolyRelFormatter = class {
  constructor(intl, isEnglish, opts) {
    this.opts = __spreadValues({ style: "long" }, opts);
    if (!isEnglish && hasRelative()) {
      this.rtf = getCachedRTF(intl, opts);
    }
  }
  format(count, unit) {
    if (this.rtf) {
      return this.rtf.format(count, unit);
    } else {
      return formatRelativeTime(unit, count, this.opts.numeric, this.opts.style !== "long");
    }
  }
  formatToParts(count, unit) {
    if (this.rtf) {
      return this.rtf.formatToParts(count, unit);
    } else {
      return [];
    }
  }
};
var fallbackWeekSettings = {
  firstDay: 1,
  minimalDays: 4,
  weekend: [6, 7]
};
var Locale = class _Locale {
  static fromOpts(opts) {
    return _Locale.create(
      opts.locale,
      opts.numberingSystem,
      opts.outputCalendar,
      opts.weekSettings,
      opts.defaultToEN
    );
  }
  static create(locale, numberingSystem, outputCalendar, weekSettings, defaultToEN = false) {
    const specifiedLocale = locale || Settings.defaultLocale;
    const localeR = specifiedLocale || (defaultToEN ? "en-US" : systemLocale());
    const numberingSystemR = numberingSystem || Settings.defaultNumberingSystem;
    const outputCalendarR = outputCalendar || Settings.defaultOutputCalendar;
    const weekSettingsR = validateWeekSettings(weekSettings) || Settings.defaultWeekSettings;
    return new _Locale(localeR, numberingSystemR, outputCalendarR, weekSettingsR, specifiedLocale);
  }
  static resetCache() {
    sysLocaleCache = null;
    intlDTCache.clear();
    intlNumCache.clear();
    intlRelCache.clear();
    intlResolvedOptionsCache.clear();
    weekInfoCache.clear();
  }
  static fromObject({ locale, numberingSystem, outputCalendar, weekSettings } = {}) {
    return _Locale.create(locale, numberingSystem, outputCalendar, weekSettings);
  }
  constructor(locale, numbering, outputCalendar, weekSettings, specifiedLocale) {
    const [parsedLocale, parsedNumberingSystem, parsedOutputCalendar] = parseLocaleString(locale);
    this.locale = parsedLocale;
    this.numberingSystem = numbering || parsedNumberingSystem || null;
    this.outputCalendar = outputCalendar || parsedOutputCalendar || null;
    this.weekSettings = weekSettings;
    this.intl = intlConfigString(this.locale, this.numberingSystem, this.outputCalendar);
    this.weekdaysCache = { format: {}, standalone: {} };
    this.monthsCache = { format: {}, standalone: {} };
    this.meridiemCache = null;
    this.eraCache = {};
    this.specifiedLocale = specifiedLocale;
    this.fastNumbersCached = null;
  }
  get fastNumbers() {
    if (this.fastNumbersCached == null) {
      this.fastNumbersCached = supportsFastNumbers(this);
    }
    return this.fastNumbersCached;
  }
  listingMode() {
    const isActuallyEn = this.isEnglish();
    const hasNoWeirdness = (this.numberingSystem === null || this.numberingSystem === "latn") && (this.outputCalendar === null || this.outputCalendar === "gregory");
    return isActuallyEn && hasNoWeirdness ? "en" : "intl";
  }
  clone(alts) {
    if (!alts || Object.getOwnPropertyNames(alts).length === 0) {
      return this;
    } else {
      return _Locale.create(
        alts.locale || this.specifiedLocale,
        alts.numberingSystem || this.numberingSystem,
        alts.outputCalendar || this.outputCalendar,
        validateWeekSettings(alts.weekSettings) || this.weekSettings,
        alts.defaultToEN || false
      );
    }
  }
  redefaultToEN(alts = {}) {
    return this.clone(__spreadProps(__spreadValues({}, alts), { defaultToEN: true }));
  }
  redefaultToSystem(alts = {}) {
    return this.clone(__spreadProps(__spreadValues({}, alts), { defaultToEN: false }));
  }
  months(length, format = false) {
    return listStuff(this, length, months, () => {
      const monthSpecialCase = this.intl === "ja" || this.intl.startsWith("ja-");
      format &= !monthSpecialCase;
      const intl = format ? { month: length, day: "numeric" } : { month: length }, formatStr = format ? "format" : "standalone";
      if (!this.monthsCache[formatStr][length]) {
        const mapper = !monthSpecialCase ? (dt) => this.extract(dt, intl, "month") : (dt) => this.dtFormatter(dt, intl).format();
        this.monthsCache[formatStr][length] = mapMonths(mapper);
      }
      return this.monthsCache[formatStr][length];
    });
  }
  weekdays(length, format = false) {
    return listStuff(this, length, weekdays, () => {
      const intl = format ? { weekday: length, year: "numeric", month: "long", day: "numeric" } : { weekday: length }, formatStr = format ? "format" : "standalone";
      if (!this.weekdaysCache[formatStr][length]) {
        this.weekdaysCache[formatStr][length] = mapWeekdays(
          (dt) => this.extract(dt, intl, "weekday")
        );
      }
      return this.weekdaysCache[formatStr][length];
    });
  }
  meridiems() {
    return listStuff(
      this,
      void 0,
      () => meridiems,
      () => {
        if (!this.meridiemCache) {
          const intl = { hour: "numeric", hourCycle: "h12" };
          this.meridiemCache = [DateTime.utc(2016, 11, 13, 9), DateTime.utc(2016, 11, 13, 19)].map(
            (dt) => this.extract(dt, intl, "dayperiod")
          );
        }
        return this.meridiemCache;
      }
    );
  }
  eras(length) {
    return listStuff(this, length, eras, () => {
      const intl = { era: length };
      if (!this.eraCache[length]) {
        this.eraCache[length] = [DateTime.utc(-40, 1, 1), DateTime.utc(2017, 1, 1)].map(
          (dt) => this.extract(dt, intl, "era")
        );
      }
      return this.eraCache[length];
    });
  }
  extract(dt, intlOpts, field) {
    const df = this.dtFormatter(dt, intlOpts), results = df.formatToParts(), matching = results.find((m) => m.type.toLowerCase() === field);
    return matching ? matching.value : null;
  }
  numberFormatter(opts = {}) {
    return new PolyNumberFormatter(this.intl, opts.forceSimple || this.fastNumbers, opts);
  }
  dtFormatter(dt, intlOpts = {}) {
    return new PolyDateFormatter(dt, this.intl, intlOpts);
  }
  relFormatter(opts = {}) {
    return new PolyRelFormatter(this.intl, this.isEnglish(), opts);
  }
  listFormatter(opts = {}) {
    return getCachedLF(this.intl, opts);
  }
  isEnglish() {
    return this.locale === "en" || this.locale.toLowerCase() === "en-us" || getCachedIntResolvedOptions(this.intl).locale.startsWith("en-us");
  }
  getWeekSettings() {
    if (this.weekSettings) {
      return this.weekSettings;
    } else if (!hasLocaleWeekInfo()) {
      return fallbackWeekSettings;
    } else {
      return getCachedWeekInfo(this.locale);
    }
  }
  getStartOfWeek() {
    return this.getWeekSettings().firstDay;
  }
  getMinDaysInFirstWeek() {
    return this.getWeekSettings().minimalDays;
  }
  getWeekendDays() {
    return this.getWeekSettings().weekend;
  }
  equals(other) {
    return this.locale === other.locale && this.numberingSystem === other.numberingSystem && this.outputCalendar === other.outputCalendar;
  }
  toString() {
    return `Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`;
  }
};

// node_modules/luxon/src/zones/fixedOffsetZone.js
var singleton2 = null;
var FixedOffsetZone = class _FixedOffsetZone extends Zone {
  /**
   * Get a singleton instance of UTC
   * @return {FixedOffsetZone}
   */
  static get utcInstance() {
    if (singleton2 === null) {
      singleton2 = new _FixedOffsetZone(0);
    }
    return singleton2;
  }
  /**
   * Get an instance with a specified offset
   * @param {number} offset - The offset in minutes
   * @return {FixedOffsetZone}
   */
  static instance(offset2) {
    return offset2 === 0 ? _FixedOffsetZone.utcInstance : new _FixedOffsetZone(offset2);
  }
  /**
   * Get an instance of FixedOffsetZone from a UTC offset string, like "UTC+6"
   * @param {string} s - The offset string to parse
   * @example FixedOffsetZone.parseSpecifier("UTC+6")
   * @example FixedOffsetZone.parseSpecifier("UTC+06")
   * @example FixedOffsetZone.parseSpecifier("UTC-6:00")
   * @return {FixedOffsetZone}
   */
  static parseSpecifier(s2) {
    if (s2) {
      const r = s2.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);
      if (r) {
        return new _FixedOffsetZone(signedOffset(r[1], r[2]));
      }
    }
    return null;
  }
  constructor(offset2) {
    super();
    this.fixed = offset2;
  }
  /**
   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.
   * @override
   * @type {string}
   */
  get type() {
    return "fixed";
  }
  /**
   * The name of this zone.
   * All fixed zones' names always start with "UTC" (plus optional offset)
   * @override
   * @type {string}
   */
  get name() {
    return this.fixed === 0 ? "UTC" : `UTC${formatOffset(this.fixed, "narrow")}`;
  }
  /**
   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`
   *
   * @override
   * @type {string}
   */
  get ianaName() {
    if (this.fixed === 0) {
      return "Etc/UTC";
    } else {
      return `Etc/GMT${formatOffset(-this.fixed, "narrow")}`;
    }
  }
  /**
   * Returns the offset's common name at the specified timestamp.
   *
   * For fixed offset zones this equals to the zone name.
   * @override
   */
  offsetName() {
    return this.name;
  }
  /**
   * Returns the offset's value as a string
   * @override
   * @param {number} ts - Epoch milliseconds for which to get the offset
   * @param {string} format - What style of offset to return.
   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively
   * @return {string}
   */
  formatOffset(ts, format) {
    return formatOffset(this.fixed, format);
  }
  /**
   * Returns whether the offset is known to be fixed for the whole year:
   * Always returns true for all fixed offset zones.
   * @override
   * @type {boolean}
   */
  get isUniversal() {
    return true;
  }
  /**
   * Return the offset in minutes for this zone at the specified timestamp.
   *
   * For fixed offset zones, this is constant and does not depend on a timestamp.
   * @override
   * @return {number}
   */
  offset() {
    return this.fixed;
  }
  /**
   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)
   * @override
   * @param {Zone} otherZone - the zone to compare
   * @return {boolean}
   */
  equals(otherZone) {
    return otherZone.type === "fixed" && otherZone.fixed === this.fixed;
  }
  /**
   * Return whether this Zone is valid:
   * All fixed offset zones are valid.
   * @override
   * @type {boolean}
   */
  get isValid() {
    return true;
  }
};

// node_modules/luxon/src/zones/invalidZone.js
var InvalidZone = class extends Zone {
  constructor(zoneName) {
    super();
    this.zoneName = zoneName;
  }
  /** @override **/
  get type() {
    return "invalid";
  }
  /** @override **/
  get name() {
    return this.zoneName;
  }
  /** @override **/
  get isUniversal() {
    return false;
  }
  /** @override **/
  offsetName() {
    return null;
  }
  /** @override **/
  formatOffset() {
    return "";
  }
  /** @override **/
  offset() {
    return NaN;
  }
  /** @override **/
  equals() {
    return false;
  }
  /** @override **/
  get isValid() {
    return false;
  }
};

// node_modules/luxon/src/impl/zoneUtil.js
function normalizeZone(input, defaultZone2) {
  let offset2;
  if (isUndefined(input) || input === null) {
    return defaultZone2;
  } else if (input instanceof Zone) {
    return input;
  } else if (isString(input)) {
    const lowered = input.toLowerCase();
    if (lowered === "default") return defaultZone2;
    else if (lowered === "local" || lowered === "system") return SystemZone.instance;
    else if (lowered === "utc" || lowered === "gmt") return FixedOffsetZone.utcInstance;
    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);
  } else if (isNumber(input)) {
    return FixedOffsetZone.instance(input);
  } else if (typeof input === "object" && "offset" in input && typeof input.offset === "function") {
    return input;
  } else {
    return new InvalidZone(input);
  }
}

// node_modules/luxon/src/impl/digits.js
var numberingSystems = {
  arab: "[٠-٩]",
  arabext: "[۰-۹]",
  bali: "[᭐-᭙]",
  beng: "[০-৯]",
  deva: "[०-९]",
  fullwide: "[０-９]",
  gujr: "[૦-૯]",
  hanidec: "[〇|一|二|三|四|五|六|七|八|九]",
  khmr: "[០-៩]",
  knda: "[೦-೯]",
  laoo: "[໐-໙]",
  limb: "[᥆-᥏]",
  mlym: "[൦-൯]",
  mong: "[᠐-᠙]",
  mymr: "[၀-၉]",
  orya: "[୦-୯]",
  tamldec: "[௦-௯]",
  telu: "[౦-౯]",
  thai: "[๐-๙]",
  tibt: "[༠-༩]",
  latn: "\\d"
};
var numberingSystemsUTF16 = {
  arab: [1632, 1641],
  arabext: [1776, 1785],
  bali: [6992, 7001],
  beng: [2534, 2543],
  deva: [2406, 2415],
  fullwide: [65296, 65303],
  gujr: [2790, 2799],
  khmr: [6112, 6121],
  knda: [3302, 3311],
  laoo: [3792, 3801],
  limb: [6470, 6479],
  mlym: [3430, 3439],
  mong: [6160, 6169],
  mymr: [4160, 4169],
  orya: [2918, 2927],
  tamldec: [3046, 3055],
  telu: [3174, 3183],
  thai: [3664, 3673],
  tibt: [3872, 3881]
};
var hanidecChars = numberingSystems.hanidec.replace(/[\[|\]]/g, "").split("");
function parseDigits(str) {
  let value = parseInt(str, 10);
  if (isNaN(value)) {
    value = "";
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (str[i].search(numberingSystems.hanidec) !== -1) {
        value += hanidecChars.indexOf(str[i]);
      } else {
        for (const key in numberingSystemsUTF16) {
          const [min, max] = numberingSystemsUTF16[key];
          if (code >= min && code <= max) {
            value += code - min;
          }
        }
      }
    }
    return parseInt(value, 10);
  } else {
    return value;
  }
}
var digitRegexCache = /* @__PURE__ */ new Map();
function resetDigitRegexCache() {
  digitRegexCache.clear();
}
function digitRegex({ numberingSystem }, append = "") {
  const ns = numberingSystem || "latn";
  let appendCache = digitRegexCache.get(ns);
  if (appendCache === void 0) {
    appendCache = /* @__PURE__ */ new Map();
    digitRegexCache.set(ns, appendCache);
  }
  let regex = appendCache.get(append);
  if (regex === void 0) {
    regex = new RegExp(`${numberingSystems[ns]}${append}`);
    appendCache.set(append, regex);
  }
  return regex;
}

// node_modules/luxon/src/settings.js
var now = () => Date.now();
var defaultZone = "system";
var defaultLocale = null;
var defaultNumberingSystem = null;
var defaultOutputCalendar = null;
var twoDigitCutoffYear = 60;
var throwOnInvalid;
var defaultWeekSettings = null;
var Settings = class {
  /**
   * Get the callback for returning the current timestamp.
   * @type {function}
   */
  static get now() {
    return now;
  }
  /**
   * Set the callback for returning the current timestamp.
   * The function should return a number, which will be interpreted as an Epoch millisecond count
   * @type {function}
   * @example Settings.now = () => Date.now() + 3000 // pretend it is 3 seconds in the future
   * @example Settings.now = () => 0 // always pretend it's Jan 1, 1970 at midnight in UTC time
   */
  static set now(n2) {
    now = n2;
  }
  /**
   * Set the default time zone to create DateTimes in. Does not affect existing instances.
   * Use the value "system" to reset this value to the system's time zone.
   * @type {string}
   */
  static set defaultZone(zone) {
    defaultZone = zone;
  }
  /**
   * Get the default time zone object currently used to create DateTimes. Does not affect existing instances.
   * The default value is the system's time zone (the one set on the machine that runs this code).
   * @type {Zone}
   */
  static get defaultZone() {
    return normalizeZone(defaultZone, SystemZone.instance);
  }
  /**
   * Get the default locale to create DateTimes with. Does not affect existing instances.
   * @type {string}
   */
  static get defaultLocale() {
    return defaultLocale;
  }
  /**
   * Set the default locale to create DateTimes with. Does not affect existing instances.
   * @type {string}
   */
  static set defaultLocale(locale) {
    defaultLocale = locale;
  }
  /**
   * Get the default numbering system to create DateTimes with. Does not affect existing instances.
   * @type {string}
   */
  static get defaultNumberingSystem() {
    return defaultNumberingSystem;
  }
  /**
   * Set the default numbering system to create DateTimes with. Does not affect existing instances.
   * @type {string}
   */
  static set defaultNumberingSystem(numberingSystem) {
    defaultNumberingSystem = numberingSystem;
  }
  /**
   * Get the default output calendar to create DateTimes with. Does not affect existing instances.
   * @type {string}
   */
  static get defaultOutputCalendar() {
    return defaultOutputCalendar;
  }
  /**
   * Set the default output calendar to create DateTimes with. Does not affect existing instances.
   * @type {string}
   */
  static set defaultOutputCalendar(outputCalendar) {
    defaultOutputCalendar = outputCalendar;
  }
  /**
   * @typedef {Object} WeekSettings
   * @property {number} firstDay
   * @property {number} minimalDays
   * @property {number[]} weekend
   */
  /**
   * @return {WeekSettings|null}
   */
  static get defaultWeekSettings() {
    return defaultWeekSettings;
  }
  /**
   * Allows overriding the default locale week settings, i.e. the start of the week, the weekend and
   * how many days are required in the first week of a year.
   * Does not affect existing instances.
   *
   * @param {WeekSettings|null} weekSettings
   */
  static set defaultWeekSettings(weekSettings) {
    defaultWeekSettings = validateWeekSettings(weekSettings);
  }
  /**
   * Get the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.
   * @type {number}
   */
  static get twoDigitCutoffYear() {
    return twoDigitCutoffYear;
  }
  /**
   * Set the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.
   * @type {number}
   * @example Settings.twoDigitCutoffYear = 0 // all 'yy' are interpreted as 20th century
   * @example Settings.twoDigitCutoffYear = 99 // all 'yy' are interpreted as 21st century
   * @example Settings.twoDigitCutoffYear = 50 // '49' -> 2049; '50' -> 1950
   * @example Settings.twoDigitCutoffYear = 1950 // interpreted as 50
   * @example Settings.twoDigitCutoffYear = 2050 // ALSO interpreted as 50
   */
  static set twoDigitCutoffYear(cutoffYear) {
    twoDigitCutoffYear = cutoffYear % 100;
  }
  /**
   * Get whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals
   * @type {boolean}
   */
  static get throwOnInvalid() {
    return throwOnInvalid;
  }
  /**
   * Set whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals
   * @type {boolean}
   */
  static set throwOnInvalid(t) {
    throwOnInvalid = t;
  }
  /**
   * Reset Luxon's global caches. Should only be necessary in testing scenarios.
   * @return {void}
   */
  static resetCaches() {
    Locale.resetCache();
    IANAZone.resetCache();
    DateTime.resetCache();
    resetDigitRegexCache();
  }
};

// node_modules/luxon/src/impl/invalid.js
var Invalid = class {
  constructor(reason, explanation) {
    this.reason = reason;
    this.explanation = explanation;
  }
  toMessage() {
    if (this.explanation) {
      return `${this.reason}: ${this.explanation}`;
    } else {
      return this.reason;
    }
  }
};

// node_modules/luxon/src/impl/conversions.js
var nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];
var leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];
function unitOutOfRange(unit, value) {
  return new Invalid(
    "unit out of range",
    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`
  );
}
function dayOfWeek(year, month, day) {
  const d = new Date(Date.UTC(year, month - 1, day));
  if (year < 100 && year >= 0) {
    d.setUTCFullYear(d.getUTCFullYear() - 1900);
  }
  const js = d.getUTCDay();
  return js === 0 ? 7 : js;
}
function computeOrdinal(year, month, day) {
  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];
}
function uncomputeOrdinal(year, ordinal) {
  const table = isLeapYear(year) ? leapLadder : nonLeapLadder, month0 = table.findIndex((i) => i < ordinal), day = ordinal - table[month0];
  return { month: month0 + 1, day };
}
function isoWeekdayToLocal(isoWeekday, startOfWeek) {
  return (isoWeekday - startOfWeek + 7) % 7 + 1;
}
function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {
  const { year, month, day } = gregObj, ordinal = computeOrdinal(year, month, day), weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);
  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7), weekYear;
  if (weekNumber < 1) {
    weekYear = year - 1;
    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);
  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {
    weekYear = year + 1;
    weekNumber = 1;
  } else {
    weekYear = year;
  }
  return __spreadValues({ weekYear, weekNumber, weekday }, timeObject(gregObj));
}
function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {
  const { weekYear, weekNumber, weekday } = weekData, weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek), yearInDays = daysInYear(weekYear);
  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek, year;
  if (ordinal < 1) {
    year = weekYear - 1;
    ordinal += daysInYear(year);
  } else if (ordinal > yearInDays) {
    year = weekYear + 1;
    ordinal -= daysInYear(weekYear);
  } else {
    year = weekYear;
  }
  const { month, day } = uncomputeOrdinal(year, ordinal);
  return __spreadValues({ year, month, day }, timeObject(weekData));
}
function gregorianToOrdinal(gregData) {
  const { year, month, day } = gregData;
  const ordinal = computeOrdinal(year, month, day);
  return __spreadValues({ year, ordinal }, timeObject(gregData));
}
function ordinalToGregorian(ordinalData) {
  const { year, ordinal } = ordinalData;
  const { month, day } = uncomputeOrdinal(year, ordinal);
  return __spreadValues({ year, month, day }, timeObject(ordinalData));
}
function usesLocalWeekValues(obj, loc) {
  const hasLocaleWeekData = !isUndefined(obj.localWeekday) || !isUndefined(obj.localWeekNumber) || !isUndefined(obj.localWeekYear);
  if (hasLocaleWeekData) {
    const hasIsoWeekData = !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);
    if (hasIsoWeekData) {
      throw new ConflictingSpecificationError(
        "Cannot mix locale-based week fields with ISO-based week fields"
      );
    }
    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;
    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;
    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;
    delete obj.localWeekday;
    delete obj.localWeekNumber;
    delete obj.localWeekYear;
    return {
      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),
      startOfWeek: loc.getStartOfWeek()
    };
  } else {
    return { minDaysInFirstWeek: 4, startOfWeek: 1 };
  }
}
function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {
  const validYear = isInteger(obj.weekYear), validWeek = integerBetween(
    obj.weekNumber,
    1,
    weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)
  ), validWeekday = integerBetween(obj.weekday, 1, 7);
  if (!validYear) {
    return unitOutOfRange("weekYear", obj.weekYear);
  } else if (!validWeek) {
    return unitOutOfRange("week", obj.weekNumber);
  } else if (!validWeekday) {
    return unitOutOfRange("weekday", obj.weekday);
  } else return false;
}
function hasInvalidOrdinalData(obj) {
  const validYear = isInteger(obj.year), validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));
  if (!validYear) {
    return unitOutOfRange("year", obj.year);
  } else if (!validOrdinal) {
    return unitOutOfRange("ordinal", obj.ordinal);
  } else return false;
}
function hasInvalidGregorianData(obj) {
  const validYear = isInteger(obj.year), validMonth = integerBetween(obj.month, 1, 12), validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));
  if (!validYear) {
    return unitOutOfRange("year", obj.year);
  } else if (!validMonth) {
    return unitOutOfRange("month", obj.month);
  } else if (!validDay) {
    return unitOutOfRange("day", obj.day);
  } else return false;
}
function hasInvalidTimeData(obj) {
  const { hour, minute, second, millisecond } = obj;
  const validHour = integerBetween(hour, 0, 23) || hour === 24 && minute === 0 && second === 0 && millisecond === 0, validMinute = integerBetween(minute, 0, 59), validSecond = integerBetween(second, 0, 59), validMillisecond = integerBetween(millisecond, 0, 999);
  if (!validHour) {
    return unitOutOfRange("hour", hour);
  } else if (!validMinute) {
    return unitOutOfRange("minute", minute);
  } else if (!validSecond) {
    return unitOutOfRange("second", second);
  } else if (!validMillisecond) {
    return unitOutOfRange("millisecond", millisecond);
  } else return false;
}

// node_modules/luxon/src/impl/util.js
function isUndefined(o) {
  return typeof o === "undefined";
}
function isNumber(o) {
  return typeof o === "number";
}
function isInteger(o) {
  return typeof o === "number" && o % 1 === 0;
}
function isString(o) {
  return typeof o === "string";
}
function isDate(o) {
  return Object.prototype.toString.call(o) === "[object Date]";
}
function hasRelative() {
  try {
    return typeof Intl !== "undefined" && !!Intl.RelativeTimeFormat;
  } catch (e) {
    return false;
  }
}
function hasLocaleWeekInfo() {
  try {
    return typeof Intl !== "undefined" && !!Intl.Locale && ("weekInfo" in Intl.Locale.prototype || "getWeekInfo" in Intl.Locale.prototype);
  } catch (e) {
    return false;
  }
}
function maybeArray(thing) {
  return Array.isArray(thing) ? thing : [thing];
}
function bestBy(arr, by, compare2) {
  if (arr.length === 0) {
    return void 0;
  }
  return arr.reduce((best, next) => {
    const pair = [by(next), next];
    if (!best) {
      return pair;
    } else if (compare2(best[0], pair[0]) === best[0]) {
      return best;
    } else {
      return pair;
    }
  }, null)[1];
}
function pick(obj, keys) {
  return keys.reduce((a, k) => {
    a[k] = obj[k];
    return a;
  }, {});
}
function hasOwnProperty(obj, prop) {
  return Object.prototype.hasOwnProperty.call(obj, prop);
}
function validateWeekSettings(settings) {
  if (settings == null) {
    return null;
  } else if (typeof settings !== "object") {
    throw new InvalidArgumentError("Week settings must be an object");
  } else {
    if (!integerBetween(settings.firstDay, 1, 7) || !integerBetween(settings.minimalDays, 1, 7) || !Array.isArray(settings.weekend) || settings.weekend.some((v) => !integerBetween(v, 1, 7))) {
      throw new InvalidArgumentError("Invalid week settings");
    }
    return {
      firstDay: settings.firstDay,
      minimalDays: settings.minimalDays,
      weekend: Array.from(settings.weekend)
    };
  }
}
function integerBetween(thing, bottom, top) {
  return isInteger(thing) && thing >= bottom && thing <= top;
}
function floorMod(x, n2) {
  return x - n2 * Math.floor(x / n2);
}
function padStart(input, n2 = 2) {
  const isNeg = input < 0;
  let padded;
  if (isNeg) {
    padded = "-" + ("" + -input).padStart(n2, "0");
  } else {
    padded = ("" + input).padStart(n2, "0");
  }
  return padded;
}
function parseInteger(string) {
  if (isUndefined(string) || string === null || string === "") {
    return void 0;
  } else {
    return parseInt(string, 10);
  }
}
function parseFloating(string) {
  if (isUndefined(string) || string === null || string === "") {
    return void 0;
  } else {
    return parseFloat(string);
  }
}
function parseMillis(fraction) {
  if (isUndefined(fraction) || fraction === null || fraction === "") {
    return void 0;
  } else {
    const f = parseFloat("0." + fraction) * 1e3;
    return Math.floor(f);
  }
}
function roundTo(number, digits, rounding = "round") {
  const factor = 10 ** digits;
  switch (rounding) {
    case "expand":
      return number > 0 ? Math.ceil(number * factor) / factor : Math.floor(number * factor) / factor;
    case "trunc":
      return Math.trunc(number * factor) / factor;
    case "round":
      return Math.round(number * factor) / factor;
    case "floor":
      return Math.floor(number * factor) / factor;
    case "ceil":
      return Math.ceil(number * factor) / factor;
    default:
      throw new RangeError(`Value rounding ${rounding} is out of range`);
  }
}
function isLeapYear(year) {
  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
}
function daysInYear(year) {
  return isLeapYear(year) ? 366 : 365;
}
function daysInMonth(year, month) {
  const modMonth = floorMod(month - 1, 12) + 1, modYear = year + (month - modMonth) / 12;
  if (modMonth === 2) {
    return isLeapYear(modYear) ? 29 : 28;
  } else {
    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];
  }
}
function objToLocalTS(obj) {
  let d = Date.UTC(
    obj.year,
    obj.month - 1,
    obj.day,
    obj.hour,
    obj.minute,
    obj.second,
    obj.millisecond
  );
  if (obj.year < 100 && obj.year >= 0) {
    d = new Date(d);
    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);
  }
  return +d;
}
function firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {
  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);
  return -fwdlw + minDaysInFirstWeek - 1;
}
function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {
  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);
  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);
  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;
}
function untruncateYear(year) {
  if (year > 99) {
    return year;
  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2e3 + year;
}
function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {
  const date = new Date(ts), intlOpts = {
    hourCycle: "h23",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  };
  if (timeZone) {
    intlOpts.timeZone = timeZone;
  }
  const modified = __spreadValues({ timeZoneName: offsetFormat }, intlOpts);
  const parsed = new Intl.DateTimeFormat(locale, modified).formatToParts(date).find((m) => m.type.toLowerCase() === "timezonename");
  return parsed ? parsed.value : null;
}
function signedOffset(offHourStr, offMinuteStr) {
  let offHour = parseInt(offHourStr, 10);
  if (Number.isNaN(offHour)) {
    offHour = 0;
  }
  const offMin = parseInt(offMinuteStr, 10) || 0, offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;
  return offHour * 60 + offMinSigned;
}
function asNumber(value) {
  const numericValue = Number(value);
  if (typeof value === "boolean" || value === "" || !Number.isFinite(numericValue))
    throw new InvalidArgumentError(`Invalid unit value ${value}`);
  return numericValue;
}
function normalizeObject(obj, normalizer) {
  const normalized = {};
  for (const u in obj) {
    if (hasOwnProperty(obj, u)) {
      const v = obj[u];
      if (v === void 0 || v === null) continue;
      normalized[normalizer(u)] = asNumber(v);
    }
  }
  return normalized;
}
function formatOffset(offset2, format) {
  const hours = Math.trunc(Math.abs(offset2 / 60)), minutes = Math.trunc(Math.abs(offset2 % 60)), sign = offset2 >= 0 ? "+" : "-";
  switch (format) {
    case "short":
      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;
    case "narrow":
      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : ""}`;
    case "techie":
      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;
    default:
      throw new RangeError(`Value format ${format} is out of range for property format`);
  }
}
function timeObject(obj) {
  return pick(obj, ["hour", "minute", "second", "millisecond"]);
}

// node_modules/luxon/src/impl/english.js
var monthsLong = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December"
];
var monthsShort = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec"
];
var monthsNarrow = ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"];
function months(length) {
  switch (length) {
    case "narrow":
      return [...monthsNarrow];
    case "short":
      return [...monthsShort];
    case "long":
      return [...monthsLong];
    case "numeric":
      return ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"];
    case "2-digit":
      return ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"];
    default:
      return null;
  }
}
var weekdaysLong = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday"
];
var weekdaysShort = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
var weekdaysNarrow = ["M", "T", "W", "T", "F", "S", "S"];
function weekdays(length) {
  switch (length) {
    case "narrow":
      return [...weekdaysNarrow];
    case "short":
      return [...weekdaysShort];
    case "long":
      return [...weekdaysLong];
    case "numeric":
      return ["1", "2", "3", "4", "5", "6", "7"];
    default:
      return null;
  }
}
var meridiems = ["AM", "PM"];
var erasLong = ["Before Christ", "Anno Domini"];
var erasShort = ["BC", "AD"];
var erasNarrow = ["B", "A"];
function eras(length) {
  switch (length) {
    case "narrow":
      return [...erasNarrow];
    case "short":
      return [...erasShort];
    case "long":
      return [...erasLong];
    default:
      return null;
  }
}
function meridiemForDateTime(dt) {
  return meridiems[dt.hour < 12 ? 0 : 1];
}
function weekdayForDateTime(dt, length) {
  return weekdays(length)[dt.weekday - 1];
}
function monthForDateTime(dt, length) {
  return months(length)[dt.month - 1];
}
function eraForDateTime(dt, length) {
  return eras(length)[dt.year < 0 ? 0 : 1];
}
function formatRelativeTime(unit, count, numeric = "always", narrow = false) {
  const units = {
    years: ["year", "yr."],
    quarters: ["quarter", "qtr."],
    months: ["month", "mo."],
    weeks: ["week", "wk."],
    days: ["day", "day", "days"],
    hours: ["hour", "hr."],
    minutes: ["minute", "min."],
    seconds: ["second", "sec."]
  };
  const lastable = ["hours", "minutes", "seconds"].indexOf(unit) === -1;
  if (numeric === "auto" && lastable) {
    const isDay = unit === "days";
    switch (count) {
      case 1:
        return isDay ? "tomorrow" : `next ${units[unit][0]}`;
      case -1:
        return isDay ? "yesterday" : `last ${units[unit][0]}`;
      case 0:
        return isDay ? "today" : `this ${units[unit][0]}`;
      default:
    }
  }
  const isInPast = Object.is(count, -0) || count < 0, fmtValue = Math.abs(count), singular = fmtValue === 1, lilUnits = units[unit], fmtUnit = narrow ? singular ? lilUnits[1] : lilUnits[2] || lilUnits[1] : singular ? units[unit][0] : unit;
  return isInPast ? `${fmtValue} ${fmtUnit} ago` : `in ${fmtValue} ${fmtUnit}`;
}

// node_modules/luxon/src/impl/formatter.js
function stringifyTokens(splits, tokenToString) {
  let s2 = "";
  for (const token of splits) {
    if (token.literal) {
      s2 += token.val;
    } else {
      s2 += tokenToString(token.val);
    }
  }
  return s2;
}
var macroTokenToFormatOpts = {
  D: DATE_SHORT,
  DD: DATE_MED,
  DDD: DATE_FULL,
  DDDD: DATE_HUGE,
  t: TIME_SIMPLE,
  tt: TIME_WITH_SECONDS,
  ttt: TIME_WITH_SHORT_OFFSET,
  tttt: TIME_WITH_LONG_OFFSET,
  T: TIME_24_SIMPLE,
  TT: TIME_24_WITH_SECONDS,
  TTT: TIME_24_WITH_SHORT_OFFSET,
  TTTT: TIME_24_WITH_LONG_OFFSET,
  f: DATETIME_SHORT,
  ff: DATETIME_MED,
  fff: DATETIME_FULL,
  ffff: DATETIME_HUGE,
  F: DATETIME_SHORT_WITH_SECONDS,
  FF: DATETIME_MED_WITH_SECONDS,
  FFF: DATETIME_FULL_WITH_SECONDS,
  FFFF: DATETIME_HUGE_WITH_SECONDS
};
var Formatter = class _Formatter {
  static create(locale, opts = {}) {
    return new _Formatter(locale, opts);
  }
  static parseFormat(fmt) {
    let current = null, currentFull = "", bracketed = false;
    const splits = [];
    for (let i = 0; i < fmt.length; i++) {
      const c = fmt.charAt(i);
      if (c === "'") {
        if (currentFull.length > 0 || bracketed) {
          splits.push({
            literal: bracketed || /^\s+$/.test(currentFull),
            val: currentFull === "" ? "'" : currentFull
          });
        }
        current = null;
        currentFull = "";
        bracketed = !bracketed;
      } else if (bracketed) {
        currentFull += c;
      } else if (c === current) {
        currentFull += c;
      } else {
        if (currentFull.length > 0) {
          splits.push({ literal: /^\s+$/.test(currentFull), val: currentFull });
        }
        currentFull = c;
        current = c;
      }
    }
    if (currentFull.length > 0) {
      splits.push({ literal: bracketed || /^\s+$/.test(currentFull), val: currentFull });
    }
    return splits;
  }
  static macroTokenToFormatOpts(token) {
    return macroTokenToFormatOpts[token];
  }
  constructor(locale, formatOpts) {
    this.opts = formatOpts;
    this.loc = locale;
    this.systemLoc = null;
  }
  formatWithSystemDefault(dt, opts) {
    if (this.systemLoc === null) {
      this.systemLoc = this.loc.redefaultToSystem();
    }
    const df = this.systemLoc.dtFormatter(dt, __spreadValues(__spreadValues({}, this.opts), opts));
    return df.format();
  }
  dtFormatter(dt, opts = {}) {
    return this.loc.dtFormatter(dt, __spreadValues(__spreadValues({}, this.opts), opts));
  }
  formatDateTime(dt, opts) {
    return this.dtFormatter(dt, opts).format();
  }
  formatDateTimeParts(dt, opts) {
    return this.dtFormatter(dt, opts).formatToParts();
  }
  formatInterval(interval, opts) {
    const df = this.dtFormatter(interval.start, opts);
    return df.dtf.formatRange(interval.start.toJSDate(), interval.end.toJSDate());
  }
  resolvedOptions(dt, opts) {
    return this.dtFormatter(dt, opts).resolvedOptions();
  }
  num(n2, p = 0, signDisplay = void 0) {
    if (this.opts.forceSimple) {
      return padStart(n2, p);
    }
    const opts = __spreadValues({}, this.opts);
    if (p > 0) {
      opts.padTo = p;
    }
    if (signDisplay) {
      opts.signDisplay = signDisplay;
    }
    return this.loc.numberFormatter(opts).format(n2);
  }
  formatDateTimeFromString(dt, fmt) {
    const knownEnglish = this.loc.listingMode() === "en", useDateTimeFormatter = this.loc.outputCalendar && this.loc.outputCalendar !== "gregory", string = (opts, extract) => this.loc.extract(dt, opts, extract), formatOffset2 = (opts) => {
      if (dt.isOffsetFixed && dt.offset === 0 && opts.allowZ) {
        return "Z";
      }
      return dt.isValid ? dt.zone.formatOffset(dt.ts, opts.format) : "";
    }, meridiem = () => knownEnglish ? meridiemForDateTime(dt) : string({ hour: "numeric", hourCycle: "h12" }, "dayperiod"), month = (length, standalone) => knownEnglish ? monthForDateTime(dt, length) : string(standalone ? { month: length } : { month: length, day: "numeric" }, "month"), weekday = (length, standalone) => knownEnglish ? weekdayForDateTime(dt, length) : string(
      standalone ? { weekday: length } : { weekday: length, month: "long", day: "numeric" },
      "weekday"
    ), maybeMacro = (token) => {
      const formatOpts = _Formatter.macroTokenToFormatOpts(token);
      if (formatOpts) {
        return this.formatWithSystemDefault(dt, formatOpts);
      } else {
        return token;
      }
    }, era = (length) => knownEnglish ? eraForDateTime(dt, length) : string({ era: length }, "era"), tokenToString = (token) => {
      switch (token) {
        // ms
        case "S":
          return this.num(dt.millisecond);
        case "u":
        // falls through
        case "SSS":
          return this.num(dt.millisecond, 3);
        // seconds
        case "s":
          return this.num(dt.second);
        case "ss":
          return this.num(dt.second, 2);
        // fractional seconds
        case "uu":
          return this.num(Math.floor(dt.millisecond / 10), 2);
        case "uuu":
          return this.num(Math.floor(dt.millisecond / 100));
        // minutes
        case "m":
          return this.num(dt.minute);
        case "mm":
          return this.num(dt.minute, 2);
        // hours
        case "h":
          return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12);
        case "hh":
          return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12, 2);
        case "H":
          return this.num(dt.hour);
        case "HH":
          return this.num(dt.hour, 2);
        // offset
        case "Z":
          return formatOffset2({ format: "narrow", allowZ: this.opts.allowZ });
        case "ZZ":
          return formatOffset2({ format: "short", allowZ: this.opts.allowZ });
        case "ZZZ":
          return formatOffset2({ format: "techie", allowZ: this.opts.allowZ });
        case "ZZZZ":
          return dt.zone.offsetName(dt.ts, { format: "short", locale: this.loc.locale });
        case "ZZZZZ":
          return dt.zone.offsetName(dt.ts, { format: "long", locale: this.loc.locale });
        // zone
        case "z":
          return dt.zoneName;
        // meridiems
        case "a":
          return meridiem();
        // dates
        case "d":
          return useDateTimeFormatter ? string({ day: "numeric" }, "day") : this.num(dt.day);
        case "dd":
          return useDateTimeFormatter ? string({ day: "2-digit" }, "day") : this.num(dt.day, 2);
        // weekdays - standalone
        case "c":
          return this.num(dt.weekday);
        case "ccc":
          return weekday("short", true);
        case "cccc":
          return weekday("long", true);
        case "ccccc":
          return weekday("narrow", true);
        // weekdays - format
        case "E":
          return this.num(dt.weekday);
        case "EEE":
          return weekday("short", false);
        case "EEEE":
          return weekday("long", false);
        case "EEEEE":
          return weekday("narrow", false);
        // months - standalone
        case "L":
          return useDateTimeFormatter ? string({ month: "numeric", day: "numeric" }, "month") : this.num(dt.month);
        case "LL":
          return useDateTimeFormatter ? string({ month: "2-digit", day: "numeric" }, "month") : this.num(dt.month, 2);
        case "LLL":
          return month("short", true);
        case "LLLL":
          return month("long", true);
        case "LLLLL":
          return month("narrow", true);
        // months - format
        case "M":
          return useDateTimeFormatter ? string({ month: "numeric" }, "month") : this.num(dt.month);
        case "MM":
          return useDateTimeFormatter ? string({ month: "2-digit" }, "month") : this.num(dt.month, 2);
        case "MMM":
          return month("short", false);
        case "MMMM":
          return month("long", false);
        case "MMMMM":
          return month("narrow", false);
        // years
        case "y":
          return useDateTimeFormatter ? string({ year: "numeric" }, "year") : this.num(dt.year);
        case "yy":
          return useDateTimeFormatter ? string({ year: "2-digit" }, "year") : this.num(dt.year.toString().slice(-2), 2);
        case "yyyy":
          return useDateTimeFormatter ? string({ year: "numeric" }, "year") : this.num(dt.year, 4);
        case "yyyyyy":
          return useDateTimeFormatter ? string({ year: "numeric" }, "year") : this.num(dt.year, 6);
        // eras
        case "G":
          return era("short");
        case "GG":
          return era("long");
        case "GGGGG":
          return era("narrow");
        case "kk":
          return this.num(dt.weekYear.toString().slice(-2), 2);
        case "kkkk":
          return this.num(dt.weekYear, 4);
        case "W":
          return this.num(dt.weekNumber);
        case "WW":
          return this.num(dt.weekNumber, 2);
        case "n":
          return this.num(dt.localWeekNumber);
        case "nn":
          return this.num(dt.localWeekNumber, 2);
        case "ii":
          return this.num(dt.localWeekYear.toString().slice(-2), 2);
        case "iiii":
          return this.num(dt.localWeekYear, 4);
        case "o":
          return this.num(dt.ordinal);
        case "ooo":
          return this.num(dt.ordinal, 3);
        case "q":
          return this.num(dt.quarter);
        case "qq":
          return this.num(dt.quarter, 2);
        case "X":
          return this.num(Math.floor(dt.ts / 1e3));
        case "x":
          return this.num(dt.ts);
        default:
          return maybeMacro(token);
      }
    };
    return stringifyTokens(_Formatter.parseFormat(fmt), tokenToString);
  }
  formatDurationFromString(dur, fmt) {
    const invertLargest = this.opts.signMode === "negativeLargestOnly" ? -1 : 1;
    const tokenToField = (token) => {
      switch (token[0]) {
        case "S":
          return "milliseconds";
        case "s":
          return "seconds";
        case "m":
          return "minutes";
        case "h":
          return "hours";
        case "d":
          return "days";
        case "w":
          return "weeks";
        case "M":
          return "months";
        case "y":
          return "years";
        default:
          return null;
      }
    }, tokenToString = (lildur, info) => (token) => {
      const mapped = tokenToField(token);
      if (mapped) {
        const inversionFactor = info.isNegativeDuration && mapped !== info.largestUnit ? invertLargest : 1;
        let signDisplay;
        if (this.opts.signMode === "negativeLargestOnly" && mapped !== info.largestUnit) {
          signDisplay = "never";
        } else if (this.opts.signMode === "all") {
          signDisplay = "always";
        } else {
          signDisplay = "auto";
        }
        return this.num(lildur.get(mapped) * inversionFactor, token.length, signDisplay);
      } else {
        return token;
      }
    }, tokens = _Formatter.parseFormat(fmt), realTokens = tokens.reduce(
      (found, { literal, val }) => literal ? found : found.concat(val),
      []
    ), collapsed = dur.shiftTo(...realTokens.map(tokenToField).filter((t) => t)), durationInfo = {
      isNegativeDuration: collapsed < 0,
      // this relies on "collapsed" being based on "shiftTo", which builds up the object
      // in order
      largestUnit: Object.keys(collapsed.values)[0]
    };
    return stringifyTokens(tokens, tokenToString(collapsed, durationInfo));
  }
};

// node_modules/luxon/src/impl/regexParser.js
var ianaRegex = /[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;
function combineRegexes(...regexes) {
  const full = regexes.reduce((f, r) => f + r.source, "");
  return RegExp(`^${full}$`);
}
function combineExtractors(...extractors) {
  return (m) => extractors.reduce(
    ([mergedVals, mergedZone, cursor], ex) => {
      const [val, zone, next] = ex(m, cursor);
      return [__spreadValues(__spreadValues({}, mergedVals), val), zone || mergedZone, next];
    },
    [{}, null, 1]
  ).slice(0, 2);
}
function parse(s2, ...patterns) {
  if (s2 == null) {
    return [null, null];
  }
  for (const [regex, extractor] of patterns) {
    const m = regex.exec(s2);
    if (m) {
      return extractor(m);
    }
  }
  return [null, null];
}
function simpleParse(...keys) {
  return (match2, cursor) => {
    const ret = {};
    let i;
    for (i = 0; i < keys.length; i++) {
      ret[keys[i]] = parseInteger(match2[cursor + i]);
    }
    return [ret, null, cursor + i];
  };
}
var offsetRegex = /(?:([Zz])|([+-]\d\d)(?::?(\d\d))?)/;
var isoExtendedZone = `(?:${offsetRegex.source}?(?:\\[(${ianaRegex.source})\\])?)?`;
var isoTimeBaseRegex = /(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/;
var isoTimeRegex = RegExp(`${isoTimeBaseRegex.source}${isoExtendedZone}`);
var isoTimeExtensionRegex = RegExp(`(?:[Tt]${isoTimeRegex.source})?`);
var isoYmdRegex = /([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/;
var isoWeekRegex = /(\d{4})-?W(\d\d)(?:-?(\d))?/;
var isoOrdinalRegex = /(\d{4})-?(\d{3})/;
var extractISOWeekData = simpleParse("weekYear", "weekNumber", "weekDay");
var extractISOOrdinalData = simpleParse("year", "ordinal");
var sqlYmdRegex = /(\d{4})-(\d\d)-(\d\d)/;
var sqlTimeRegex = RegExp(
  `${isoTimeBaseRegex.source} ?(?:${offsetRegex.source}|(${ianaRegex.source}))?`
);
var sqlTimeExtensionRegex = RegExp(`(?: ${sqlTimeRegex.source})?`);
function int(match2, pos, fallback) {
  const m = match2[pos];
  return isUndefined(m) ? fallback : parseInteger(m);
}
function extractISOYmd(match2, cursor) {
  const item = {
    year: int(match2, cursor),
    month: int(match2, cursor + 1, 1),
    day: int(match2, cursor + 2, 1)
  };
  return [item, null, cursor + 3];
}
function extractISOTime(match2, cursor) {
  const item = {
    hours: int(match2, cursor, 0),
    minutes: int(match2, cursor + 1, 0),
    seconds: int(match2, cursor + 2, 0),
    milliseconds: parseMillis(match2[cursor + 3])
  };
  return [item, null, cursor + 4];
}
function extractISOOffset(match2, cursor) {
  const local = !match2[cursor] && !match2[cursor + 1], fullOffset = signedOffset(match2[cursor + 1], match2[cursor + 2]), zone = local ? null : FixedOffsetZone.instance(fullOffset);
  return [{}, zone, cursor + 3];
}
function extractIANAZone(match2, cursor) {
  const zone = match2[cursor] ? IANAZone.create(match2[cursor]) : null;
  return [{}, zone, cursor + 1];
}
var isoTimeOnly = RegExp(`^T?${isoTimeBaseRegex.source}$`);
var isoDuration = /^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;
function extractISODuration(match2) {
  const [s2, yearStr, monthStr, weekStr, dayStr, hourStr, minuteStr, secondStr, millisecondsStr] = match2;
  const hasNegativePrefix = s2[0] === "-";
  const negativeSeconds = secondStr && secondStr[0] === "-";
  const maybeNegate = (num, force = false) => num !== void 0 && (force || num && hasNegativePrefix) ? -num : num;
  return [
    {
      years: maybeNegate(parseFloating(yearStr)),
      months: maybeNegate(parseFloating(monthStr)),
      weeks: maybeNegate(parseFloating(weekStr)),
      days: maybeNegate(parseFloating(dayStr)),
      hours: maybeNegate(parseFloating(hourStr)),
      minutes: maybeNegate(parseFloating(minuteStr)),
      seconds: maybeNegate(parseFloating(secondStr), secondStr === "-0"),
      milliseconds: maybeNegate(parseMillis(millisecondsStr), negativeSeconds)
    }
  ];
}
var obsOffsets = {
  GMT: 0,
  EDT: -4 * 60,
  EST: -5 * 60,
  CDT: -5 * 60,
  CST: -6 * 60,
  MDT: -6 * 60,
  MST: -7 * 60,
  PDT: -7 * 60,
  PST: -8 * 60
};
function fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {
  const result = {
    year: yearStr.length === 2 ? untruncateYear(parseInteger(yearStr)) : parseInteger(yearStr),
    month: monthsShort.indexOf(monthStr) + 1,
    day: parseInteger(dayStr),
    hour: parseInteger(hourStr),
    minute: parseInteger(minuteStr)
  };
  if (secondStr) result.second = parseInteger(secondStr);
  if (weekdayStr) {
    result.weekday = weekdayStr.length > 3 ? weekdaysLong.indexOf(weekdayStr) + 1 : weekdaysShort.indexOf(weekdayStr) + 1;
  }
  return result;
}
var rfc2822 = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;
function extractRFC2822(match2) {
  const [
    ,
    weekdayStr,
    dayStr,
    monthStr,
    yearStr,
    hourStr,
    minuteStr,
    secondStr,
    obsOffset,
    milOffset,
    offHourStr,
    offMinuteStr
  ] = match2, result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);
  let offset2;
  if (obsOffset) {
    offset2 = obsOffsets[obsOffset];
  } else if (milOffset) {
    offset2 = 0;
  } else {
    offset2 = signedOffset(offHourStr, offMinuteStr);
  }
  return [result, new FixedOffsetZone(offset2)];
}
function preprocessRFC2822(s2) {
  return s2.replace(/\([^()]*\)|[\n\t]/g, " ").replace(/(\s\s+)/g, " ").trim();
}
var rfc1123 = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/;
var rfc850 = /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/;
var ascii = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;
function extractRFC1123Or850(match2) {
  const [, weekdayStr, dayStr, monthStr, yearStr, hourStr, minuteStr, secondStr] = match2, result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);
  return [result, FixedOffsetZone.utcInstance];
}
function extractASCII(match2) {
  const [, weekdayStr, monthStr, dayStr, hourStr, minuteStr, secondStr, yearStr] = match2, result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);
  return [result, FixedOffsetZone.utcInstance];
}
var isoYmdWithTimeExtensionRegex = combineRegexes(isoYmdRegex, isoTimeExtensionRegex);
var isoWeekWithTimeExtensionRegex = combineRegexes(isoWeekRegex, isoTimeExtensionRegex);
var isoOrdinalWithTimeExtensionRegex = combineRegexes(isoOrdinalRegex, isoTimeExtensionRegex);
var isoTimeCombinedRegex = combineRegexes(isoTimeRegex);
var extractISOYmdTimeAndOffset = combineExtractors(
  extractISOYmd,
  extractISOTime,
  extractISOOffset,
  extractIANAZone
);
var extractISOWeekTimeAndOffset = combineExtractors(
  extractISOWeekData,
  extractISOTime,
  extractISOOffset,
  extractIANAZone
);
var extractISOOrdinalDateAndTime = combineExtractors(
  extractISOOrdinalData,
  extractISOTime,
  extractISOOffset,
  extractIANAZone
);
var extractISOTimeAndOffset = combineExtractors(
  extractISOTime,
  extractISOOffset,
  extractIANAZone
);
function parseISODate(s2) {
  return parse(
    s2,
    [isoYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],
    [isoWeekWithTimeExtensionRegex, extractISOWeekTimeAndOffset],
    [isoOrdinalWithTimeExtensionRegex, extractISOOrdinalDateAndTime],
    [isoTimeCombinedRegex, extractISOTimeAndOffset]
  );
}
function parseRFC2822Date(s2) {
  return parse(preprocessRFC2822(s2), [rfc2822, extractRFC2822]);
}
function parseHTTPDate(s2) {
  return parse(
    s2,
    [rfc1123, extractRFC1123Or850],
    [rfc850, extractRFC1123Or850],
    [ascii, extractASCII]
  );
}
function parseISODuration(s2) {
  return parse(s2, [isoDuration, extractISODuration]);
}
var extractISOTimeOnly = combineExtractors(extractISOTime);
function parseISOTimeOnly(s2) {
  return parse(s2, [isoTimeOnly, extractISOTimeOnly]);
}
var sqlYmdWithTimeExtensionRegex = combineRegexes(sqlYmdRegex, sqlTimeExtensionRegex);
var sqlTimeCombinedRegex = combineRegexes(sqlTimeRegex);
var extractISOTimeOffsetAndIANAZone = combineExtractors(
  extractISOTime,
  extractISOOffset,
  extractIANAZone
);
function parseSQL(s2) {
  return parse(
    s2,
    [sqlYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],
    [sqlTimeCombinedRegex, extractISOTimeOffsetAndIANAZone]
  );
}

// node_modules/luxon/src/duration.js
var INVALID = "Invalid Duration";
var lowOrderMatrix = {
  weeks: {
    days: 7,
    hours: 7 * 24,
    minutes: 7 * 24 * 60,
    seconds: 7 * 24 * 60 * 60,
    milliseconds: 7 * 24 * 60 * 60 * 1e3
  },
  days: {
    hours: 24,
    minutes: 24 * 60,
    seconds: 24 * 60 * 60,
    milliseconds: 24 * 60 * 60 * 1e3
  },
  hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1e3 },
  minutes: { seconds: 60, milliseconds: 60 * 1e3 },
  seconds: { milliseconds: 1e3 }
};
var casualMatrix = __spreadValues({
  years: {
    quarters: 4,
    months: 12,
    weeks: 52,
    days: 365,
    hours: 365 * 24,
    minutes: 365 * 24 * 60,
    seconds: 365 * 24 * 60 * 60,
    milliseconds: 365 * 24 * 60 * 60 * 1e3
  },
  quarters: {
    months: 3,
    weeks: 13,
    days: 91,
    hours: 91 * 24,
    minutes: 91 * 24 * 60,
    seconds: 91 * 24 * 60 * 60,
    milliseconds: 91 * 24 * 60 * 60 * 1e3
  },
  months: {
    weeks: 4,
    days: 30,
    hours: 30 * 24,
    minutes: 30 * 24 * 60,
    seconds: 30 * 24 * 60 * 60,
    milliseconds: 30 * 24 * 60 * 60 * 1e3
  }
}, lowOrderMatrix);
var daysInYearAccurate = 146097 / 400;
var daysInMonthAccurate = 146097 / 4800;
var accurateMatrix = __spreadValues({
  years: {
    quarters: 4,
    months: 12,
    weeks: daysInYearAccurate / 7,
    days: daysInYearAccurate,
    hours: daysInYearAccurate * 24,
    minutes: daysInYearAccurate * 24 * 60,
    seconds: daysInYearAccurate * 24 * 60 * 60,
    milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1e3
  },
  quarters: {
    months: 3,
    weeks: daysInYearAccurate / 28,
    days: daysInYearAccurate / 4,
    hours: daysInYearAccurate * 24 / 4,
    minutes: daysInYearAccurate * 24 * 60 / 4,
    seconds: daysInYearAccurate * 24 * 60 * 60 / 4,
    milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1e3 / 4
  },
  months: {
    weeks: daysInMonthAccurate / 7,
    days: daysInMonthAccurate,
    hours: daysInMonthAccurate * 24,
    minutes: daysInMonthAccurate * 24 * 60,
    seconds: daysInMonthAccurate * 24 * 60 * 60,
    milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1e3
  }
}, lowOrderMatrix);
var orderedUnits = [
  "years",
  "quarters",
  "months",
  "weeks",
  "days",
  "hours",
  "minutes",
  "seconds",
  "milliseconds"
];
var reverseUnits = orderedUnits.slice(0).reverse();
function clone2(dur, alts, clear = false) {
  const conf = {
    values: clear ? alts.values : __spreadValues(__spreadValues({}, dur.values), alts.values || {}),
    loc: dur.loc.clone(alts.loc),
    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,
    matrix: alts.matrix || dur.matrix
  };
  return new Duration(conf);
}
function durationToMillis(matrix, vals) {
  let sum = vals.milliseconds ?? 0;
  for (const unit of reverseUnits.slice(1)) {
    if (vals[unit]) {
      sum += vals[unit] * matrix[unit]["milliseconds"];
    }
  }
  return sum;
}
function normalizeValues(matrix, vals) {
  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;
  orderedUnits.reduceRight((previous, current) => {
    if (!isUndefined(vals[current])) {
      if (previous) {
        const previousVal = vals[previous] * factor;
        const conv = matrix[current][previous];
        const rollUp = Math.floor(previousVal / conv);
        vals[current] += rollUp * factor;
        vals[previous] -= rollUp * conv * factor;
      }
      return current;
    } else {
      return previous;
    }
  }, null);
  orderedUnits.reduce((previous, current) => {
    if (!isUndefined(vals[current])) {
      if (previous) {
        const fraction = vals[previous] % 1;
        vals[previous] -= fraction;
        vals[current] += fraction * matrix[previous][current];
      }
      return current;
    } else {
      return previous;
    }
  }, null);
}
function removeZeroes(vals) {
  const newVals = {};
  for (const [key, value] of Object.entries(vals)) {
    if (value !== 0) {
      newVals[key] = value;
    }
  }
  return newVals;
}
var Duration = class _Duration {
  /**
   * @private
   */
  constructor(config) {
    const accurate = config.conversionAccuracy === "longterm" || false;
    let matrix = accurate ? accurateMatrix : casualMatrix;
    if (config.matrix) {
      matrix = config.matrix;
    }
    this.values = config.values;
    this.loc = config.loc || Locale.create();
    this.conversionAccuracy = accurate ? "longterm" : "casual";
    this.invalid = config.invalid || null;
    this.matrix = matrix;
    this.isLuxonDuration = true;
  }
  /**
   * Create Duration from a number of milliseconds.
   * @param {number} count of milliseconds
   * @param {Object} opts - options for parsing
   * @param {string} [opts.locale='en-US'] - the locale to use
   * @param {string} opts.numberingSystem - the numbering system to use
   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use
   * @return {Duration}
   */
  static fromMillis(count, opts) {
    return _Duration.fromObject({ milliseconds: count }, opts);
  }
  /**
   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.
   * If this object is empty then a zero milliseconds duration is returned.
   * @param {Object} obj - the object to create the DateTime from
   * @param {number} obj.years
   * @param {number} obj.quarters
   * @param {number} obj.months
   * @param {number} obj.weeks
   * @param {number} obj.days
   * @param {number} obj.hours
   * @param {number} obj.minutes
   * @param {number} obj.seconds
   * @param {number} obj.milliseconds
   * @param {Object} [opts=[]] - options for creating this Duration
   * @param {string} [opts.locale='en-US'] - the locale to use
   * @param {string} opts.numberingSystem - the numbering system to use
   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use
   * @param {string} [opts.matrix=Object] - the custom conversion system to use
   * @return {Duration}
   */
  static fromObject(obj, opts = {}) {
    if (obj == null || typeof obj !== "object") {
      throw new InvalidArgumentError(
        `Duration.fromObject: argument expected to be an object, got ${obj === null ? "null" : typeof obj}`
      );
    }
    return new _Duration({
      values: normalizeObject(obj, _Duration.normalizeUnit),
      loc: Locale.fromObject(opts),
      conversionAccuracy: opts.conversionAccuracy,
      matrix: opts.matrix
    });
  }
  /**
   * Create a Duration from DurationLike.
   *
   * @param {Object | number | Duration} durationLike
   * One of:
   * - object with keys like 'years' and 'hours'.
   * - number representing milliseconds
   * - Duration instance
   * @return {Duration}
   */
  static fromDurationLike(durationLike) {
    if (isNumber(durationLike)) {
      return _Duration.fromMillis(durationLike);
    } else if (_Duration.isDuration(durationLike)) {
      return durationLike;
    } else if (typeof durationLike === "object") {
      return _Duration.fromObject(durationLike);
    } else {
      throw new InvalidArgumentError(
        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`
      );
    }
  }
  /**
   * Create a Duration from an ISO 8601 duration string.
   * @param {string} text - text to parse
   * @param {Object} opts - options for parsing
   * @param {string} [opts.locale='en-US'] - the locale to use
   * @param {string} opts.numberingSystem - the numbering system to use
   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use
   * @param {string} [opts.matrix=Object] - the preset conversion system to use
   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations
   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }
   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }
   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }
   * @return {Duration}
   */
  static fromISO(text, opts) {
    const [parsed] = parseISODuration(text);
    if (parsed) {
      return _Duration.fromObject(parsed, opts);
    } else {
      return _Duration.invalid("unparsable", `the input "${text}" can't be parsed as ISO 8601`);
    }
  }
  /**
   * Create a Duration from an ISO 8601 time string.
   * @param {string} text - text to parse
   * @param {Object} opts - options for parsing
   * @param {string} [opts.locale='en-US'] - the locale to use
   * @param {string} opts.numberingSystem - the numbering system to use
   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use
   * @param {string} [opts.matrix=Object] - the conversion system to use
   * @see https://en.wikipedia.org/wiki/ISO_8601#Times
   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }
   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }
   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }
   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }
   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }
   * @return {Duration}
   */
  static fromISOTime(text, opts) {
    const [parsed] = parseISOTimeOnly(text);
    if (parsed) {
      return _Duration.fromObject(parsed, opts);
    } else {
      return _Duration.invalid("unparsable", `the input "${text}" can't be parsed as ISO 8601`);
    }
  }
  /**
   * Create an invalid Duration.
   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent
   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information
   * @return {Duration}
   */
  static invalid(reason, explanation = null) {
    if (!reason) {
      throw new InvalidArgumentError("need to specify a reason the Duration is invalid");
    }
    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);
    if (Settings.throwOnInvalid) {
      throw new InvalidDurationError(invalid);
    } else {
      return new _Duration({ invalid });
    }
  }
  /**
   * @private
   */
  static normalizeUnit(unit) {
    const normalized = {
      year: "years",
      years: "years",
      quarter: "quarters",
      quarters: "quarters",
      month: "months",
      months: "months",
      week: "weeks",
      weeks: "weeks",
      day: "days",
      days: "days",
      hour: "hours",
      hours: "hours",
      minute: "minutes",
      minutes: "minutes",
      second: "seconds",
      seconds: "seconds",
      millisecond: "milliseconds",
      milliseconds: "milliseconds"
    }[unit ? unit.toLowerCase() : unit];
    if (!normalized) throw new InvalidUnitError(unit);
    return normalized;
  }
  /**
   * Check if an object is a Duration. Works across context boundaries
   * @param {object} o
   * @return {boolean}
   */
  static isDuration(o) {
    return o && o.isLuxonDuration || false;
  }
  /**
   * Get  the locale of a Duration, such 'en-GB'
   * @type {string}
   */
  get locale() {
    return this.isValid ? this.loc.locale : null;
  }
  /**
   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration
   *
   * @type {string}
   */
  get numberingSystem() {
    return this.isValid ? this.loc.numberingSystem : null;
  }
  /**
   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:
   * * `S` for milliseconds
   * * `s` for seconds
   * * `m` for minutes
   * * `h` for hours
   * * `d` for days
   * * `w` for weeks
   * * `M` for months
   * * `y` for years
   * Notes:
   * * Add padding by repeating the token, e.g. "yy" pads the years to two digits, "hhhh" pads the hours out to four digits
   * * Tokens can be escaped by wrapping with single quotes.
   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.
   * @param {string} fmt - the format string
   * @param {Object} opts - options
   * @param {boolean} [opts.floor=true] - floor numerical values
   * @param {'negative'|'all'|'negativeLargestOnly'} [opts.signMode=negative] - How to handle signs
   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat("y d s") //=> "1 6 2"
   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat("yy dd sss") //=> "01 06 002"
   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat("M S") //=> "12 518402000"
   * @example Duration.fromObject({ days: 6, seconds: 2 }).toFormat("d s", { signMode: "all" }) //=> "+6 +2"
   * @example Duration.fromObject({ days: -6, seconds: -2 }).toFormat("d s", { signMode: "all" }) //=> "-6 -2"
   * @example Duration.fromObject({ days: -6, seconds: -2 }).toFormat("d s", { signMode: "negativeLargestOnly" }) //=> "-6 2"
   * @return {string}
   */
  toFormat(fmt, opts = {}) {
    const fmtOpts = __spreadProps(__spreadValues({}, opts), {
      floor: opts.round !== false && opts.floor !== false
    });
    return this.isValid ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt) : INVALID;
  }
  /**
   * Returns a string representation of a Duration with all units included.
   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options
   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.
   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.
   * @param {boolean} [opts.showZeros=true] - Show all units previously used by the duration even if they are zero
   * @example
   * ```js
   * var dur = Duration.fromObject({ months: 1, weeks: 0, hours: 5, minutes: 6 })
   * dur.toHuman() //=> '1 month, 0 weeks, 5 hours, 6 minutes'
   * dur.toHuman({ listStyle: "long" }) //=> '1 month, 0 weeks, 5 hours, and 6 minutes'
   * dur.toHuman({ unitDisplay: "short" }) //=> '1 mth, 0 wks, 5 hr, 6 min'
   * dur.toHuman({ showZeros: false }) //=> '1 month, 5 hours, 6 minutes'
   * ```
   */
  toHuman(opts = {}) {
    if (!this.isValid) return INVALID;
    const showZeros = opts.showZeros !== false;
    const l2 = orderedUnits.map((unit) => {
      const val = this.values[unit];
      if (isUndefined(val) || val === 0 && !showZeros) {
        return null;
      }
      return this.loc.numberFormatter(__spreadProps(__spreadValues({ style: "unit", unitDisplay: "long" }, opts), { unit: unit.slice(0, -1) })).format(val);
    }).filter((n2) => n2);
    return this.loc.listFormatter(__spreadValues({ type: "conjunction", style: opts.listStyle || "narrow" }, opts)).format(l2);
  }
  /**
   * Returns a JavaScript object with this Duration's values.
   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }
   * @return {Object}
   */
  toObject() {
    if (!this.isValid) return {};
    return __spreadValues({}, this.values);
  }
  /**
   * Returns an ISO 8601-compliant string representation of this Duration.
   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations
   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'
   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'
   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'
   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'
   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'
   * @return {string}
   */
  toISO() {
    if (!this.isValid) return null;
    let s2 = "P";
    if (this.years !== 0) s2 += this.years + "Y";
    if (this.months !== 0 || this.quarters !== 0) s2 += this.months + this.quarters * 3 + "M";
    if (this.weeks !== 0) s2 += this.weeks + "W";
    if (this.days !== 0) s2 += this.days + "D";
    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)
      s2 += "T";
    if (this.hours !== 0) s2 += this.hours + "H";
    if (this.minutes !== 0) s2 += this.minutes + "M";
    if (this.seconds !== 0 || this.milliseconds !== 0)
      s2 += roundTo(this.seconds + this.milliseconds / 1e3, 3) + "S";
    if (s2 === "P") s2 += "T0S";
    return s2;
  }
  /**
   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.
   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.
   * @see https://en.wikipedia.org/wiki/ISO_8601#Times
   * @param {Object} opts - options
   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0
   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0
   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix
   * @param {string} [opts.format='extended'] - choose between the basic and extended format
   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'
   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'
   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'
   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'
   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'
   * @return {string}
   */
  toISOTime(opts = {}) {
    if (!this.isValid) return null;
    const millis = this.toMillis();
    if (millis < 0 || millis >= 864e5) return null;
    opts = __spreadProps(__spreadValues({
      suppressMilliseconds: false,
      suppressSeconds: false,
      includePrefix: false,
      format: "extended"
    }, opts), {
      includeOffset: false
    });
    const dateTime = DateTime.fromMillis(millis, { zone: "UTC" });
    return dateTime.toISOTime(opts);
  }
  /**
   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.
   * @return {string}
   */
  toJSON() {
    return this.toISO();
  }
  /**
   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.
   * @return {string}
   */
  toString() {
    return this.toISO();
  }
  /**
   * Returns a string representation of this Duration appropriate for the REPL.
   * @return {string}
   */
  [Symbol.for("nodejs.util.inspect.custom")]() {
    if (this.isValid) {
      return `Duration { values: ${JSON.stringify(this.values)} }`;
    } else {
      return `Duration { Invalid, reason: ${this.invalidReason} }`;
    }
  }
  /**
   * Returns an milliseconds value of this Duration.
   * @return {number}
   */
  toMillis() {
    if (!this.isValid) return NaN;
    return durationToMillis(this.matrix, this.values);
  }
  /**
   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}
   * @return {number}
   */
  valueOf() {
    return this.toMillis();
  }
  /**
   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.
   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()
   * @return {Duration}
   */
  plus(duration) {
    if (!this.isValid) return this;
    const dur = _Duration.fromDurationLike(duration), result = {};
    for (const k of orderedUnits) {
      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {
        result[k] = dur.get(k) + this.get(k);
      }
    }
    return clone2(this, { values: result }, true);
  }
  /**
   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.
   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()
   * @return {Duration}
   */
  minus(duration) {
    if (!this.isValid) return this;
    const dur = _Duration.fromDurationLike(duration);
    return this.plus(dur.negate());
  }
  /**
   * Scale this Duration by the specified amount. Return a newly-constructed Duration.
   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.
   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }
   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === "hours" ? x * 2 : x) //=> { hours: 2, minutes: 30 }
   * @return {Duration}
   */
  mapUnits(fn) {
    if (!this.isValid) return this;
    const result = {};
    for (const k of Object.keys(this.values)) {
      result[k] = asNumber(fn(this.values[k], k));
    }
    return clone2(this, { values: result }, true);
  }
  /**
   * Get the value of unit.
   * @param {string} unit - a unit such as 'minute' or 'day'
   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2
   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0
   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3
   * @return {number}
   */
  get(unit) {
    return this[_Duration.normalizeUnit(unit)];
  }
  /**
   * "Set" the values of specified units. Return a newly-constructed Duration.
   * @param {Object} values - a mapping of units to numbers
   * @example dur.set({ years: 2017 })
   * @example dur.set({ hours: 8, minutes: 30 })
   * @return {Duration}
   */
  set(values) {
    if (!this.isValid) return this;
    const mixed = __spreadValues(__spreadValues({}, this.values), normalizeObject(values, _Duration.normalizeUnit));
    return clone2(this, { values: mixed });
  }
  /**
   * "Set" the locale and/or numberingSystem.  Returns a newly-constructed Duration.
   * @example dur.reconfigure({ locale: 'en-GB' })
   * @return {Duration}
   */
  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {
    const loc = this.loc.clone({ locale, numberingSystem });
    const opts = { loc, matrix, conversionAccuracy };
    return clone2(this, opts);
  }
  /**
   * Return the length of the duration in the specified unit.
   * @param {string} unit - a unit such as 'minutes' or 'days'
   * @example Duration.fromObject({years: 1}).as('days') //=> 365
   * @example Duration.fromObject({years: 1}).as('months') //=> 12
   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5
   * @return {number}
   */
  as(unit) {
    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;
  }
  /**
   * Reduce this Duration to its canonical representation in its current units.
   * Assuming the overall value of the Duration is positive, this means:
   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)
   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise
   *   the overall value would be negative, see third example)
   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)
   *
   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.
   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }
   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }
   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }
   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }
   * @return {Duration}
   */
  normalize() {
    if (!this.isValid) return this;
    const vals = this.toObject();
    normalizeValues(this.matrix, vals);
    return clone2(this, { values: vals }, true);
  }
  /**
   * Rescale units to its largest representation
   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }
   * @return {Duration}
   */
  rescale() {
    if (!this.isValid) return this;
    const vals = removeZeroes(this.normalize().shiftToAll().toObject());
    return clone2(this, { values: vals }, true);
  }
  /**
   * Convert this Duration into its representation in a different set of units.
   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }
   * @return {Duration}
   */
  shiftTo(...units) {
    if (!this.isValid) return this;
    if (units.length === 0) {
      return this;
    }
    units = units.map((u) => _Duration.normalizeUnit(u));
    const built = {}, accumulated = {}, vals = this.toObject();
    let lastUnit;
    for (const k of orderedUnits) {
      if (units.indexOf(k) >= 0) {
        lastUnit = k;
        let own = 0;
        for (const ak in accumulated) {
          own += this.matrix[ak][k] * accumulated[ak];
          accumulated[ak] = 0;
        }
        if (isNumber(vals[k])) {
          own += vals[k];
        }
        const i = Math.trunc(own);
        built[k] = i;
        accumulated[k] = (own * 1e3 - i * 1e3) / 1e3;
      } else if (isNumber(vals[k])) {
        accumulated[k] = vals[k];
      }
    }
    for (const key in accumulated) {
      if (accumulated[key] !== 0) {
        built[lastUnit] += key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];
      }
    }
    normalizeValues(this.matrix, built);
    return clone2(this, { values: built }, true);
  }
  /**
   * Shift this Duration to all available units.
   * Same as shiftTo("years", "months", "weeks", "days", "hours", "minutes", "seconds", "milliseconds")
   * @return {Duration}
   */
  shiftToAll() {
    if (!this.isValid) return this;
    return this.shiftTo(
      "years",
      "months",
      "weeks",
      "days",
      "hours",
      "minutes",
      "seconds",
      "milliseconds"
    );
  }
  /**
   * Return the negative of this Duration.
   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }
   * @return {Duration}
   */
  negate() {
    if (!this.isValid) return this;
    const negated = {};
    for (const k of Object.keys(this.values)) {
      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];
    }
    return clone2(this, { values: negated }, true);
  }
  /**
   * Removes all units with values equal to 0 from this Duration.
   * @example Duration.fromObject({ years: 2, days: 0, hours: 0, minutes: 0 }).removeZeros().toObject() //=> { years: 2 }
   * @return {Duration}
   */
  removeZeros() {
    if (!this.isValid) return this;
    const vals = removeZeroes(this.values);
    return clone2(this, { values: vals }, true);
  }
  /**
   * Get the years.
   * @type {number}
   */
  get years() {
    return this.isValid ? this.values.years || 0 : NaN;
  }
  /**
   * Get the quarters.
   * @type {number}
   */
  get quarters() {
    return this.isValid ? this.values.quarters || 0 : NaN;
  }
  /**
   * Get the months.
   * @type {number}
   */
  get months() {
    return this.isValid ? this.values.months || 0 : NaN;
  }
  /**
   * Get the weeks
   * @type {number}
   */
  get weeks() {
    return this.isValid ? this.values.weeks || 0 : NaN;
  }
  /**
   * Get the days.
   * @type {number}
   */
  get days() {
    return this.isValid ? this.values.days || 0 : NaN;
  }
  /**
   * Get the hours.
   * @type {number}
   */
  get hours() {
    return this.isValid ? this.values.hours || 0 : NaN;
  }
  /**
   * Get the minutes.
   * @type {number}
   */
  get minutes() {
    return this.isValid ? this.values.minutes || 0 : NaN;
  }
  /**
   * Get the seconds.
   * @return {number}
   */
  get seconds() {
    return this.isValid ? this.values.seconds || 0 : NaN;
  }
  /**
   * Get the milliseconds.
   * @return {number}
   */
  get milliseconds() {
    return this.isValid ? this.values.milliseconds || 0 : NaN;
  }
  /**
   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations
   * on invalid DateTimes or Intervals.
   * @return {boolean}
   */
  get isValid() {
    return this.invalid === null;
  }
  /**
   * Returns an error code if this Duration became invalid, or null if the Duration is valid
   * @return {string}
   */
  get invalidReason() {
    return this.invalid ? this.invalid.reason : null;
  }
  /**
   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid
   * @type {string}
   */
  get invalidExplanation() {
    return this.invalid ? this.invalid.explanation : null;
  }
  /**
   * Equality check
   * Two Durations are equal iff they have the same units and the same values for each unit.
   * @param {Duration} other
   * @return {boolean}
   */
  equals(other) {
    if (!this.isValid || !other.isValid) {
      return false;
    }
    if (!this.loc.equals(other.loc)) {
      return false;
    }
    function eq(v1, v2) {
      if (v1 === void 0 || v1 === 0) return v2 === void 0 || v2 === 0;
      return v1 === v2;
    }
    for (const u of orderedUnits) {
      if (!eq(this.values[u], other.values[u])) {
        return false;
      }
    }
    return true;
  }
};

// node_modules/luxon/src/interval.js
var INVALID2 = "Invalid Interval";
function validateStartEnd(start, end) {
  if (!start || !start.isValid) {
    return Interval.invalid("missing or invalid start");
  } else if (!end || !end.isValid) {
    return Interval.invalid("missing or invalid end");
  } else if (end < start) {
    return Interval.invalid(
      "end before start",
      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`
    );
  } else {
    return null;
  }
}
var Interval = class _Interval {
  /**
   * @private
   */
  constructor(config) {
    this.s = config.start;
    this.e = config.end;
    this.invalid = config.invalid || null;
    this.isLuxonInterval = true;
  }
  /**
   * Create an invalid Interval.
   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent
   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information
   * @return {Interval}
   */
  static invalid(reason, explanation = null) {
    if (!reason) {
      throw new InvalidArgumentError("need to specify a reason the Interval is invalid");
    }
    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);
    if (Settings.throwOnInvalid) {
      throw new InvalidIntervalError(invalid);
    } else {
      return new _Interval({ invalid });
    }
  }
  /**
   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.
   * @param {DateTime|Date|Object} start
   * @param {DateTime|Date|Object} end
   * @return {Interval}
   */
  static fromDateTimes(start, end) {
    const builtStart = friendlyDateTime(start), builtEnd = friendlyDateTime(end);
    const validateError = validateStartEnd(builtStart, builtEnd);
    if (validateError == null) {
      return new _Interval({
        start: builtStart,
        end: builtEnd
      });
    } else {
      return validateError;
    }
  }
  /**
   * Create an Interval from a start DateTime and a Duration to extend to.
   * @param {DateTime|Date|Object} start
   * @param {Duration|Object|number} duration - the length of the Interval.
   * @return {Interval}
   */
  static after(start, duration) {
    const dur = Duration.fromDurationLike(duration), dt = friendlyDateTime(start);
    return _Interval.fromDateTimes(dt, dt.plus(dur));
  }
  /**
   * Create an Interval from an end DateTime and a Duration to extend backwards to.
   * @param {DateTime|Date|Object} end
   * @param {Duration|Object|number} duration - the length of the Interval.
   * @return {Interval}
   */
  static before(end, duration) {
    const dur = Duration.fromDurationLike(duration), dt = friendlyDateTime(end);
    return _Interval.fromDateTimes(dt.minus(dur), dt);
  }
  /**
   * Create an Interval from an ISO 8601 string.
   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.
   * @param {string} text - the ISO string to parse
   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}
   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals
   * @return {Interval}
   */
  static fromISO(text, opts) {
    const [s2, e] = (text || "").split("/", 2);
    if (s2 && e) {
      let start, startIsValid;
      try {
        start = DateTime.fromISO(s2, opts);
        startIsValid = start.isValid;
      } catch (e2) {
        startIsValid = false;
      }
      let end, endIsValid;
      try {
        end = DateTime.fromISO(e, opts);
        endIsValid = end.isValid;
      } catch (e2) {
        endIsValid = false;
      }
      if (startIsValid && endIsValid) {
        return _Interval.fromDateTimes(start, end);
      }
      if (startIsValid) {
        const dur = Duration.fromISO(e, opts);
        if (dur.isValid) {
          return _Interval.after(start, dur);
        }
      } else if (endIsValid) {
        const dur = Duration.fromISO(s2, opts);
        if (dur.isValid) {
          return _Interval.before(end, dur);
        }
      }
    }
    return _Interval.invalid("unparsable", `the input "${text}" can't be parsed as ISO 8601`);
  }
  /**
   * Check if an object is an Interval. Works across context boundaries
   * @param {object} o
   * @return {boolean}
   */
  static isInterval(o) {
    return o && o.isLuxonInterval || false;
  }
  /**
   * Returns the start of the Interval
   * @type {DateTime}
   */
  get start() {
    return this.isValid ? this.s : null;
  }
  /**
   * Returns the end of the Interval. This is the first instant which is not part of the interval
   * (Interval is half-open).
   * @type {DateTime}
   */
  get end() {
    return this.isValid ? this.e : null;
  }
  /**
   * Returns the last DateTime included in the interval (since end is not part of the interval)
   * @type {DateTime}
   */
  get lastDateTime() {
    return this.isValid ? this.e ? this.e.minus(1) : null : null;
  }
  /**
   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.
   * @type {boolean}
   */
  get isValid() {
    return this.invalidReason === null;
  }
  /**
   * Returns an error code if this Interval is invalid, or null if the Interval is valid
   * @type {string}
   */
  get invalidReason() {
    return this.invalid ? this.invalid.reason : null;
  }
  /**
   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid
   * @type {string}
   */
  get invalidExplanation() {
    return this.invalid ? this.invalid.explanation : null;
  }
  /**
   * Returns the length of the Interval in the specified unit.
   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.
   * @return {number}
   */
  length(unit = "milliseconds") {
    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;
  }
  /**
   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.
   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'
   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'
   * @param {string} [unit='milliseconds'] - the unit of time to count.
   * @param {Object} opts - options
   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime
   * @return {number}
   */
  count(unit = "milliseconds", opts) {
    if (!this.isValid) return NaN;
    const start = this.start.startOf(unit, opts);
    let end;
    if (opts?.useLocaleWeeks) {
      end = this.end.reconfigure({ locale: start.locale });
    } else {
      end = this.end;
    }
    end = end.startOf(unit, opts);
    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());
  }
  /**
   * Returns whether this Interval's start and end are both in the same unit of time
   * @param {string} unit - the unit of time to check sameness on
   * @return {boolean}
   */
  hasSame(unit) {
    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;
  }
  /**
   * Return whether this Interval has the same start and end DateTimes.
   * @return {boolean}
   */
  isEmpty() {
    return this.s.valueOf() === this.e.valueOf();
  }
  /**
   * Return whether this Interval's start is after the specified DateTime.
   * @param {DateTime} dateTime
   * @return {boolean}
   */
  isAfter(dateTime) {
    if (!this.isValid) return false;
    return this.s > dateTime;
  }
  /**
   * Return whether this Interval's end is before the specified DateTime.
   * @param {DateTime} dateTime
   * @return {boolean}
   */
  isBefore(dateTime) {
    if (!this.isValid) return false;
    return this.e <= dateTime;
  }
  /**
   * Return whether this Interval contains the specified DateTime.
   * @param {DateTime} dateTime
   * @return {boolean}
   */
  contains(dateTime) {
    if (!this.isValid) return false;
    return this.s <= dateTime && this.e > dateTime;
  }
  /**
   * "Sets" the start and/or end dates. Returns a newly-constructed Interval.
   * @param {Object} values - the values to set
   * @param {DateTime} values.start - the starting DateTime
   * @param {DateTime} values.end - the ending DateTime
   * @return {Interval}
   */
  set({ start, end } = {}) {
    if (!this.isValid) return this;
    return _Interval.fromDateTimes(start || this.s, end || this.e);
  }
  /**
   * Split this Interval at each of the specified DateTimes
   * @param {...DateTime} dateTimes - the unit of time to count.
   * @return {Array}
   */
  splitAt(...dateTimes) {
    if (!this.isValid) return [];
    const sorted = dateTimes.map(friendlyDateTime).filter((d) => this.contains(d)).sort((a, b) => a.toMillis() - b.toMillis()), results = [];
    let { s: s2 } = this, i = 0;
    while (s2 < this.e) {
      const added = sorted[i] || this.e, next = +added > +this.e ? this.e : added;
      results.push(_Interval.fromDateTimes(s2, next));
      s2 = next;
      i += 1;
    }
    return results;
  }
  /**
   * Split this Interval into smaller Intervals, each of the specified length.
   * Left over time is grouped into a smaller interval
   * @param {Duration|Object|number} duration - The length of each resulting interval.
   * @return {Array}
   */
  splitBy(duration) {
    const dur = Duration.fromDurationLike(duration);
    if (!this.isValid || !dur.isValid || dur.as("milliseconds") === 0) {
      return [];
    }
    let { s: s2 } = this, idx = 1, next;
    const results = [];
    while (s2 < this.e) {
      const added = this.start.plus(dur.mapUnits((x) => x * idx));
      next = +added > +this.e ? this.e : added;
      results.push(_Interval.fromDateTimes(s2, next));
      s2 = next;
      idx += 1;
    }
    return results;
  }
  /**
   * Split this Interval into the specified number of smaller intervals.
   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.
   * @return {Array}
   */
  divideEqually(numberOfParts) {
    if (!this.isValid) return [];
    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);
  }
  /**
   * Return whether this Interval overlaps with the specified Interval
   * @param {Interval} other
   * @return {boolean}
   */
  overlaps(other) {
    return this.e > other.s && this.s < other.e;
  }
  /**
   * Return whether this Interval's end is adjacent to the specified Interval's start.
   * @param {Interval} other
   * @return {boolean}
   */
  abutsStart(other) {
    if (!this.isValid) return false;
    return +this.e === +other.s;
  }
  /**
   * Return whether this Interval's start is adjacent to the specified Interval's end.
   * @param {Interval} other
   * @return {boolean}
   */
  abutsEnd(other) {
    if (!this.isValid) return false;
    return +other.e === +this.s;
  }
  /**
   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.
   * @param {Interval} other
   * @return {boolean}
   */
  engulfs(other) {
    if (!this.isValid) return false;
    return this.s <= other.s && this.e >= other.e;
  }
  /**
   * Return whether this Interval has the same start and end as the specified Interval.
   * @param {Interval} other
   * @return {boolean}
   */
  equals(other) {
    if (!this.isValid || !other.isValid) {
      return false;
    }
    return this.s.equals(other.s) && this.e.equals(other.e);
  }
  /**
   * Return an Interval representing the intersection of this Interval and the specified Interval.
   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.
   * Returns null if the intersection is empty, meaning, the intervals don't intersect.
   * @param {Interval} other
   * @return {Interval}
   */
  intersection(other) {
    if (!this.isValid) return this;
    const s2 = this.s > other.s ? this.s : other.s, e = this.e < other.e ? this.e : other.e;
    if (s2 >= e) {
      return null;
    } else {
      return _Interval.fromDateTimes(s2, e);
    }
  }
  /**
   * Return an Interval representing the union of this Interval and the specified Interval.
   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.
   * @param {Interval} other
   * @return {Interval}
   */
  union(other) {
    if (!this.isValid) return this;
    const s2 = this.s < other.s ? this.s : other.s, e = this.e > other.e ? this.e : other.e;
    return _Interval.fromDateTimes(s2, e);
  }
  /**
   * Merge an array of Intervals into an equivalent minimal set of Intervals.
   * Combines overlapping and adjacent Intervals.
   * The resulting array will contain the Intervals in ascending order, that is, starting with the earliest Interval
   * and ending with the latest.
   *
   * @param {Array} intervals
   * @return {Array}
   */
  static merge(intervals) {
    const [found, final] = intervals.sort((a, b) => a.s - b.s).reduce(
      ([sofar, current], item) => {
        if (!current) {
          return [sofar, item];
        } else if (current.overlaps(item) || current.abutsStart(item)) {
          return [sofar, current.union(item)];
        } else {
          return [sofar.concat([current]), item];
        }
      },
      [[], null]
    );
    if (final) {
      found.push(final);
    }
    return found;
  }
  /**
   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.
   * @param {Array} intervals
   * @return {Array}
   */
  static xor(intervals) {
    let start = null, currentCount = 0;
    const results = [], ends = intervals.map((i) => [
      { time: i.s, type: "s" },
      { time: i.e, type: "e" }
    ]), flattened = Array.prototype.concat(...ends), arr = flattened.sort((a, b) => a.time - b.time);
    for (const i of arr) {
      currentCount += i.type === "s" ? 1 : -1;
      if (currentCount === 1) {
        start = i.time;
      } else {
        if (start && +start !== +i.time) {
          results.push(_Interval.fromDateTimes(start, i.time));
        }
        start = null;
      }
    }
    return _Interval.merge(results);
  }
  /**
   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.
   * @param {...Interval} intervals
   * @return {Array}
   */
  difference(...intervals) {
    return _Interval.xor([this].concat(intervals)).map((i) => this.intersection(i)).filter((i) => i && !i.isEmpty());
  }
  /**
   * Returns a string representation of this Interval appropriate for debugging.
   * @return {string}
   */
  toString() {
    if (!this.isValid) return INVALID2;
    return `[${this.s.toISO()} – ${this.e.toISO()})`;
  }
  /**
   * Returns a string representation of this Interval appropriate for the REPL.
   * @return {string}
   */
  [Symbol.for("nodejs.util.inspect.custom")]() {
    if (this.isValid) {
      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;
    } else {
      return `Interval { Invalid, reason: ${this.invalidReason} }`;
    }
  }
  /**
   * Returns a localized string representing this Interval. Accepts the same options as the
   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as
   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method
   * is browser-specific, but in general it will return an appropriate representation of the
   * Interval in the assigned locale. Defaults to the system's locale if no locale has been
   * specified.
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat
   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or
   * Intl.DateTimeFormat constructor options.
   * @param {Object} opts - Options to override the configuration of the start DateTime.
   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022
   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022
   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022
   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM
   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p
   * @return {string}
   */
  toLocaleString(formatOpts = DATE_SHORT, opts = {}) {
    return this.isValid ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this) : INVALID2;
  }
  /**
   * Returns an ISO 8601-compliant string representation of this Interval.
   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals
   * @param {Object} opts - The same options as {@link DateTime#toISO}
   * @return {string}
   */
  toISO(opts) {
    if (!this.isValid) return INVALID2;
    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;
  }
  /**
   * Returns an ISO 8601-compliant string representation of date of this Interval.
   * The time components are ignored.
   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals
   * @return {string}
   */
  toISODate() {
    if (!this.isValid) return INVALID2;
    return `${this.s.toISODate()}/${this.e.toISODate()}`;
  }
  /**
   * Returns an ISO 8601-compliant string representation of time of this Interval.
   * The date components are ignored.
   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals
   * @param {Object} opts - The same options as {@link DateTime#toISO}
   * @return {string}
   */
  toISOTime(opts) {
    if (!this.isValid) return INVALID2;
    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;
  }
  /**
   * Returns a string representation of this Interval formatted according to the specified format
   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible
   * formatting tool.
   * @param {string} dateFormat - The format string. This string formats the start and end time.
   * See {@link DateTime#toFormat} for details.
   * @param {Object} opts - Options.
   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end
   * representations.
   * @return {string}
   */
  toFormat(dateFormat, { separator = " – " } = {}) {
    if (!this.isValid) return INVALID2;
    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;
  }
  /**
   * Return a Duration representing the time spanned by this interval.
   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.
   * @param {Object} opts - options that affect the creation of the Duration
   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use
   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }
   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }
   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }
   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }
   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }
   * @return {Duration}
   */
  toDuration(unit, opts) {
    if (!this.isValid) {
      return Duration.invalid(this.invalidReason);
    }
    return this.e.diff(this.s, unit, opts);
  }
  /**
   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes
   * @param {function} mapFn
   * @return {Interval}
   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())
   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))
   */
  mapEndpoints(mapFn) {
    return _Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));
  }
};

// node_modules/luxon/src/info.js
var Info = class {
  /**
   * Return whether the specified zone contains a DST.
   * @param {string|Zone} [zone='local'] - Zone to check. Defaults to the environment's local zone.
   * @return {boolean}
   */
  static hasDST(zone = Settings.defaultZone) {
    const proto = DateTime.now().setZone(zone).set({ month: 12 });
    return !zone.isUniversal && proto.offset !== proto.set({ month: 6 }).offset;
  }
  /**
   * Return whether the specified zone is a valid IANA specifier.
   * @param {string} zone - Zone to check
   * @return {boolean}
   */
  static isValidIANAZone(zone) {
    return IANAZone.isValidZone(zone);
  }
  /**
   * Converts the input into a {@link Zone} instance.
   *
   * * If `input` is already a Zone instance, it is returned unchanged.
   * * If `input` is a string containing a valid time zone name, a Zone instance
   *   with that name is returned.
   * * If `input` is a string that doesn't refer to a known time zone, a Zone
   *   instance with {@link Zone#isValid} == false is returned.
   * * If `input is a number, a Zone instance with the specified fixed offset
   *   in minutes is returned.
   * * If `input` is `null` or `undefined`, the default zone is returned.
   * @param {string|Zone|number} [input] - the value to be converted
   * @return {Zone}
   */
  static normalizeZone(input) {
    return normalizeZone(input, Settings.defaultZone);
  }
  /**
   * Get the weekday on which the week starts according to the given locale.
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @returns {number} the start of the week, 1 for Monday through 7 for Sunday
   */
  static getStartOfWeek({ locale = null, locObj = null } = {}) {
    return (locObj || Locale.create(locale)).getStartOfWeek();
  }
  /**
   * Get the minimum number of days necessary in a week before it is considered part of the next year according
   * to the given locale.
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @returns {number}
   */
  static getMinimumDaysInFirstWeek({ locale = null, locObj = null } = {}) {
    return (locObj || Locale.create(locale)).getMinDaysInFirstWeek();
  }
  /**
   * Get the weekdays, which are considered the weekend according to the given locale
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @returns {number[]} an array of weekdays, 1 for Monday through 7 for Sunday
   */
  static getWeekendWeekdays({ locale = null, locObj = null } = {}) {
    return (locObj || Locale.create(locale)).getWeekendDays().slice();
  }
  /**
   * Return an array of standalone month names.
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat
   * @param {string} [length='long'] - the length of the month representation, such as "numeric", "2-digit", "narrow", "short", "long"
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @param {string} [opts.numberingSystem=null] - the numbering system
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @param {string} [opts.outputCalendar='gregory'] - the calendar
   * @example Info.months()[0] //=> 'January'
   * @example Info.months('short')[0] //=> 'Jan'
   * @example Info.months('numeric')[0] //=> '1'
   * @example Info.months('short', { locale: 'fr-CA' } )[0] //=> 'janv.'
   * @example Info.months('numeric', { locale: 'ar' })[0] //=> '١'
   * @example Info.months('long', { outputCalendar: 'islamic' })[0] //=> 'Rabiʻ I'
   * @return {Array}
   */
  static months(length = "long", { locale = null, numberingSystem = null, locObj = null, outputCalendar = "gregory" } = {}) {
    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length);
  }
  /**
   * Return an array of format month names.
   * Format months differ from standalone months in that they're meant to appear next to the day of the month. In some languages, that
   * changes the string.
   * See {@link Info#months}
   * @param {string} [length='long'] - the length of the month representation, such as "numeric", "2-digit", "narrow", "short", "long"
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @param {string} [opts.numberingSystem=null] - the numbering system
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @param {string} [opts.outputCalendar='gregory'] - the calendar
   * @return {Array}
   */
  static monthsFormat(length = "long", { locale = null, numberingSystem = null, locObj = null, outputCalendar = "gregory" } = {}) {
    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length, true);
  }
  /**
   * Return an array of standalone week names.
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat
   * @param {string} [length='long'] - the length of the weekday representation, such as "narrow", "short", "long".
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @param {string} [opts.numberingSystem=null] - the numbering system
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @example Info.weekdays()[0] //=> 'Monday'
   * @example Info.weekdays('short')[0] //=> 'Mon'
   * @example Info.weekdays('short', { locale: 'fr-CA' })[0] //=> 'lun.'
   * @example Info.weekdays('short', { locale: 'ar' })[0] //=> 'الاثنين'
   * @return {Array}
   */
  static weekdays(length = "long", { locale = null, numberingSystem = null, locObj = null } = {}) {
    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length);
  }
  /**
   * Return an array of format week names.
   * Format weekdays differ from standalone weekdays in that they're meant to appear next to more date information. In some languages, that
   * changes the string.
   * See {@link Info#weekdays}
   * @param {string} [length='long'] - the length of the month representation, such as "narrow", "short", "long".
   * @param {Object} opts - options
   * @param {string} [opts.locale=null] - the locale code
   * @param {string} [opts.numberingSystem=null] - the numbering system
   * @param {string} [opts.locObj=null] - an existing locale object to use
   * @return {Array}
   */
  static weekdaysFormat(length = "long", { locale = null, numberingSystem = null, locObj = null } = {}) {
    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length, true);
  }
  /**
   * Return an array of meridiems.
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @example Info.meridiems() //=> [ 'AM', 'PM' ]
   * @example Info.meridiems({ locale: 'my' }) //=> [ 'နံနက်', 'ညနေ' ]
   * @return {Array}
   */
  static meridiems({ locale = null } = {}) {
    return Locale.create(locale).meridiems();
  }
  /**
   * Return an array of eras, such as ['BC', 'AD']. The locale can be specified, but the calendar system is always Gregorian.
   * @param {string} [length='short'] - the length of the era representation, such as "short" or "long".
   * @param {Object} opts - options
   * @param {string} [opts.locale] - the locale code
   * @example Info.eras() //=> [ 'BC', 'AD' ]
   * @example Info.eras('long') //=> [ 'Before Christ', 'Anno Domini' ]
   * @example Info.eras('long', { locale: 'fr' }) //=> [ 'avant Jésus-Christ', 'après Jésus-Christ' ]
   * @return {Array}
   */
  static eras(length = "short", { locale = null } = {}) {
    return Locale.create(locale, null, "gregory").eras(length);
  }
  /**
   * Return the set of available features in this environment.
   * Some features of Luxon are not available in all environments. For example, on older browsers, relative time formatting support is not available. Use this function to figure out if that's the case.
   * Keys:
   * * `relative`: whether this environment supports relative time formatting
   * * `localeWeek`: whether this environment supports different weekdays for the start of the week based on the locale
   * @example Info.features() //=> { relative: false, localeWeek: true }
   * @return {Object}
   */
  static features() {
    return { relative: hasRelative(), localeWeek: hasLocaleWeekInfo() };
  }
};

// node_modules/luxon/src/impl/diff.js
function dayDiff(earlier, later) {
  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf("day").valueOf(), ms = utcDayStart(later) - utcDayStart(earlier);
  return Math.floor(Duration.fromMillis(ms).as("days"));
}
function highOrderDiffs(cursor, later, units) {
  const differs = [
    ["years", (a, b) => b.year - a.year],
    ["quarters", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],
    ["months", (a, b) => b.month - a.month + (b.year - a.year) * 12],
    [
      "weeks",
      (a, b) => {
        const days = dayDiff(a, b);
        return (days - days % 7) / 7;
      }
    ],
    ["days", dayDiff]
  ];
  const results = {};
  const earlier = cursor;
  let lowestOrder, highWater;
  for (const [unit, differ] of differs) {
    if (units.indexOf(unit) >= 0) {
      lowestOrder = unit;
      results[unit] = differ(cursor, later);
      highWater = earlier.plus(results);
      if (highWater > later) {
        results[unit]--;
        cursor = earlier.plus(results);
        if (cursor > later) {
          highWater = cursor;
          results[unit]--;
          cursor = earlier.plus(results);
        }
      } else {
        cursor = highWater;
      }
    }
  }
  return [cursor, results, highWater, lowestOrder];
}
function diff_default(earlier, later, units, opts) {
  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);
  const remainingMillis = later - cursor;
  const lowerOrderUnits = units.filter(
    (u) => ["hours", "minutes", "seconds", "milliseconds"].indexOf(u) >= 0
  );
  if (lowerOrderUnits.length === 0) {
    if (highWater < later) {
      highWater = cursor.plus({ [lowestOrder]: 1 });
    }
    if (highWater !== cursor) {
      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);
    }
  }
  const duration = Duration.fromObject(results, opts);
  if (lowerOrderUnits.length > 0) {
    return Duration.fromMillis(remainingMillis, opts).shiftTo(...lowerOrderUnits).plus(duration);
  } else {
    return duration;
  }
}

// node_modules/luxon/src/impl/tokenParser.js
var MISSING_FTP = "missing Intl.DateTimeFormat.formatToParts support";
function intUnit(regex, post = (i) => i) {
  return { regex, deser: ([s2]) => post(parseDigits(s2)) };
}
var NBSP = String.fromCharCode(160);
var spaceOrNBSP = `[ ${NBSP}]`;
var spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, "g");
function fixListRegex(s2) {
  return s2.replace(/\./g, "\\.?").replace(spaceOrNBSPRegExp, spaceOrNBSP);
}
function stripInsensitivities(s2) {
  return s2.replace(/\./g, "").replace(spaceOrNBSPRegExp, " ").toLowerCase();
}
function oneOf(strings, startIndex) {
  if (strings === null) {
    return null;
  } else {
    return {
      regex: RegExp(strings.map(fixListRegex).join("|")),
      deser: ([s2]) => strings.findIndex((i) => stripInsensitivities(s2) === stripInsensitivities(i)) + startIndex
    };
  }
}
function offset(regex, groups) {
  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };
}
function simple(regex) {
  return { regex, deser: ([s2]) => s2 };
}
function escapeToken(value) {
  return value.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, "\\$&");
}
function unitForToken(token, loc) {
  const one = digitRegex(loc), two = digitRegex(loc, "{2}"), three = digitRegex(loc, "{3}"), four = digitRegex(loc, "{4}"), six = digitRegex(loc, "{6}"), oneOrTwo = digitRegex(loc, "{1,2}"), oneToThree = digitRegex(loc, "{1,3}"), oneToSix = digitRegex(loc, "{1,6}"), oneToNine = digitRegex(loc, "{1,9}"), twoToFour = digitRegex(loc, "{2,4}"), fourToSix = digitRegex(loc, "{4,6}"), literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s2]) => s2, literal: true }), unitate = (t) => {
    if (token.literal) {
      return literal(t);
    }
    switch (t.val) {
      // era
      case "G":
        return oneOf(loc.eras("short"), 0);
      case "GG":
        return oneOf(loc.eras("long"), 0);
      // years
      case "y":
        return intUnit(oneToSix);
      case "yy":
        return intUnit(twoToFour, untruncateYear);
      case "yyyy":
        return intUnit(four);
      case "yyyyy":
        return intUnit(fourToSix);
      case "yyyyyy":
        return intUnit(six);
      // months
      case "M":
        return intUnit(oneOrTwo);
      case "MM":
        return intUnit(two);
      case "MMM":
        return oneOf(loc.months("short", true), 1);
      case "MMMM":
        return oneOf(loc.months("long", true), 1);
      case "L":
        return intUnit(oneOrTwo);
      case "LL":
        return intUnit(two);
      case "LLL":
        return oneOf(loc.months("short", false), 1);
      case "LLLL":
        return oneOf(loc.months("long", false), 1);
      // dates
      case "d":
        return intUnit(oneOrTwo);
      case "dd":
        return intUnit(two);
      // ordinals
      case "o":
        return intUnit(oneToThree);
      case "ooo":
        return intUnit(three);
      // time
      case "HH":
        return intUnit(two);
      case "H":
        return intUnit(oneOrTwo);
      case "hh":
        return intUnit(two);
      case "h":
        return intUnit(oneOrTwo);
      case "mm":
        return intUnit(two);
      case "m":
        return intUnit(oneOrTwo);
      case "q":
        return intUnit(oneOrTwo);
      case "qq":
        return intUnit(two);
      case "s":
        return intUnit(oneOrTwo);
      case "ss":
        return intUnit(two);
      case "S":
        return intUnit(oneToThree);
      case "SSS":
        return intUnit(three);
      case "u":
        return simple(oneToNine);
      case "uu":
        return simple(oneOrTwo);
      case "uuu":
        return intUnit(one);
      // meridiem
      case "a":
        return oneOf(loc.meridiems(), 0);
      // weekYear (k)
      case "kkkk":
        return intUnit(four);
      case "kk":
        return intUnit(twoToFour, untruncateYear);
      // weekNumber (W)
      case "W":
        return intUnit(oneOrTwo);
      case "WW":
        return intUnit(two);
      // weekdays
      case "E":
      case "c":
        return intUnit(one);
      case "EEE":
        return oneOf(loc.weekdays("short", false), 1);
      case "EEEE":
        return oneOf(loc.weekdays("long", false), 1);
      case "ccc":
        return oneOf(loc.weekdays("short", true), 1);
      case "cccc":
        return oneOf(loc.weekdays("long", true), 1);
      // offset/zone
      case "Z":
      case "ZZ":
        return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);
      case "ZZZ":
        return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);
      // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing
      // because we don't have any way to figure out what they are
      case "z":
        return simple(/[a-z_+-/]{1,256}?/i);
      // this special-case "token" represents a place where a macro-token expanded into a white-space literal
      // in this case we accept any non-newline white-space
      case " ":
        return simple(/[^\S\n\r]/);
      default:
        return literal(t);
    }
  };
  const unit = unitate(token) || {
    invalidReason: MISSING_FTP
  };
  unit.token = token;
  return unit;
}
var partTypeStyleToTokenVal = {
  year: {
    "2-digit": "yy",
    numeric: "yyyyy"
  },
  month: {
    numeric: "M",
    "2-digit": "MM",
    short: "MMM",
    long: "MMMM"
  },
  day: {
    numeric: "d",
    "2-digit": "dd"
  },
  weekday: {
    short: "EEE",
    long: "EEEE"
  },
  dayperiod: "a",
  dayPeriod: "a",
  hour12: {
    numeric: "h",
    "2-digit": "hh"
  },
  hour24: {
    numeric: "H",
    "2-digit": "HH"
  },
  minute: {
    numeric: "m",
    "2-digit": "mm"
  },
  second: {
    numeric: "s",
    "2-digit": "ss"
  },
  timeZoneName: {
    long: "ZZZZZ",
    short: "ZZZ"
  }
};
function tokenForPart(part, formatOpts, resolvedOpts) {
  const { type, value } = part;
  if (type === "literal") {
    const isSpace = /^\s+$/.test(value);
    return {
      literal: !isSpace,
      val: isSpace ? " " : value
    };
  }
  const style = formatOpts[type];
  let actualType = type;
  if (type === "hour") {
    if (formatOpts.hour12 != null) {
      actualType = formatOpts.hour12 ? "hour12" : "hour24";
    } else if (formatOpts.hourCycle != null) {
      if (formatOpts.hourCycle === "h11" || formatOpts.hourCycle === "h12") {
        actualType = "hour12";
      } else {
        actualType = "hour24";
      }
    } else {
      actualType = resolvedOpts.hour12 ? "hour12" : "hour24";
    }
  }
  let val = partTypeStyleToTokenVal[actualType];
  if (typeof val === "object") {
    val = val[style];
  }
  if (val) {
    return {
      literal: false,
      val
    };
  }
  return void 0;
}
function buildRegex(units) {
  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, "");
  return [`^${re}$`, units];
}
function match(input, regex, handlers) {
  const matches = input.match(regex);
  if (matches) {
    const all = {};
    let matchIndex = 1;
    for (const i in handlers) {
      if (hasOwnProperty(handlers, i)) {
        const h = handlers[i], groups = h.groups ? h.groups + 1 : 1;
        if (!h.literal && h.token) {
          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));
        }
        matchIndex += groups;
      }
    }
    return [matches, all];
  } else {
    return [matches, {}];
  }
}
function dateTimeFromMatches(matches) {
  const toField = (token) => {
    switch (token) {
      case "S":
        return "millisecond";
      case "s":
        return "second";
      case "m":
        return "minute";
      case "h":
      case "H":
        return "hour";
      case "d":
        return "day";
      case "o":
        return "ordinal";
      case "L":
      case "M":
        return "month";
      case "y":
        return "year";
      case "E":
      case "c":
        return "weekday";
      case "W":
        return "weekNumber";
      case "k":
        return "weekYear";
      case "q":
        return "quarter";
      default:
        return null;
    }
  };
  let zone = null;
  let specificOffset;
  if (!isUndefined(matches.z)) {
    zone = IANAZone.create(matches.z);
  }
  if (!isUndefined(matches.Z)) {
    if (!zone) {
      zone = new FixedOffsetZone(matches.Z);
    }
    specificOffset = matches.Z;
  }
  if (!isUndefined(matches.q)) {
    matches.M = (matches.q - 1) * 3 + 1;
  }
  if (!isUndefined(matches.h)) {
    if (matches.h < 12 && matches.a === 1) {
      matches.h += 12;
    } else if (matches.h === 12 && matches.a === 0) {
      matches.h = 0;
    }
  }
  if (matches.G === 0 && matches.y) {
    matches.y = -matches.y;
  }
  if (!isUndefined(matches.u)) {
    matches.S = parseMillis(matches.u);
  }
  const vals = Object.keys(matches).reduce((r, k) => {
    const f = toField(k);
    if (f) {
      r[f] = matches[k];
    }
    return r;
  }, {});
  return [vals, zone, specificOffset];
}
var dummyDateTimeCache = null;
function getDummyDateTime() {
  if (!dummyDateTimeCache) {
    dummyDateTimeCache = DateTime.fromMillis(1555555555555);
  }
  return dummyDateTimeCache;
}
function maybeExpandMacroToken(token, locale) {
  if (token.literal) {
    return token;
  }
  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);
  const tokens = formatOptsToTokens(formatOpts, locale);
  if (tokens == null || tokens.includes(void 0)) {
    return token;
  }
  return tokens;
}
function expandMacroTokens(tokens, locale) {
  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));
}
var TokenParser = class {
  constructor(locale, format) {
    this.locale = locale;
    this.format = format;
    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);
    this.units = this.tokens.map((t) => unitForToken(t, locale));
    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);
    if (!this.disqualifyingUnit) {
      const [regexString, handlers] = buildRegex(this.units);
      this.regex = RegExp(regexString, "i");
      this.handlers = handlers;
    }
  }
  explainFromTokens(input) {
    if (!this.isValid) {
      return { input, tokens: this.tokens, invalidReason: this.invalidReason };
    } else {
      const [rawMatches, matches] = match(input, this.regex, this.handlers), [result, zone, specificOffset] = matches ? dateTimeFromMatches(matches) : [null, null, void 0];
      if (hasOwnProperty(matches, "a") && hasOwnProperty(matches, "H")) {
        throw new ConflictingSpecificationError(
          "Can't include meridiem when specifying 24-hour format"
        );
      }
      return {
        input,
        tokens: this.tokens,
        regex: this.regex,
        rawMatches,
        matches,
        result,
        zone,
        specificOffset
      };
    }
  }
  get isValid() {
    return !this.disqualifyingUnit;
  }
  get invalidReason() {
    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;
  }
};
function explainFromTokens(locale, input, format) {
  const parser = new TokenParser(locale, format);
  return parser.explainFromTokens(input);
}
function parseFromTokens(locale, input, format) {
  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);
  return [result, zone, specificOffset, invalidReason];
}
function formatOptsToTokens(formatOpts, locale) {
  if (!formatOpts) {
    return null;
  }
  const formatter = Formatter.create(locale, formatOpts);
  const df = formatter.dtFormatter(getDummyDateTime());
  const parts = df.formatToParts();
  const resolvedOpts = df.resolvedOptions();
  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));
}

// node_modules/luxon/src/datetime.js
var INVALID3 = "Invalid DateTime";
var MAX_DATE = 864e13;
function unsupportedZone(zone) {
  return new Invalid("unsupported zone", `the zone "${zone.name}" is not supported`);
}
function possiblyCachedWeekData(dt) {
  if (dt.weekData === null) {
    dt.weekData = gregorianToWeek(dt.c);
  }
  return dt.weekData;
}
function possiblyCachedLocalWeekData(dt) {
  if (dt.localWeekData === null) {
    dt.localWeekData = gregorianToWeek(
      dt.c,
      dt.loc.getMinDaysInFirstWeek(),
      dt.loc.getStartOfWeek()
    );
  }
  return dt.localWeekData;
}
function clone3(inst, alts) {
  const current = {
    ts: inst.ts,
    zone: inst.zone,
    c: inst.c,
    o: inst.o,
    loc: inst.loc,
    invalid: inst.invalid
  };
  return new DateTime(__spreadProps(__spreadValues(__spreadValues({}, current), alts), { old: current }));
}
function fixOffset(localTS, o, tz) {
  let utcGuess = localTS - o * 60 * 1e3;
  const o2 = tz.offset(utcGuess);
  if (o === o2) {
    return [utcGuess, o];
  }
  utcGuess -= (o2 - o) * 60 * 1e3;
  const o3 = tz.offset(utcGuess);
  if (o2 === o3) {
    return [utcGuess, o2];
  }
  return [localTS - Math.min(o2, o3) * 60 * 1e3, Math.max(o2, o3)];
}
function tsToObj(ts, offset2) {
  ts += offset2 * 60 * 1e3;
  const d = new Date(ts);
  return {
    year: d.getUTCFullYear(),
    month: d.getUTCMonth() + 1,
    day: d.getUTCDate(),
    hour: d.getUTCHours(),
    minute: d.getUTCMinutes(),
    second: d.getUTCSeconds(),
    millisecond: d.getUTCMilliseconds()
  };
}
function objToTS(obj, offset2, zone) {
  return fixOffset(objToLocalTS(obj), offset2, zone);
}
function adjustTime(inst, dur) {
  const oPre = inst.o, year = inst.c.year + Math.trunc(dur.years), month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3, c = __spreadProps(__spreadValues({}, inst.c), {
    year,
    month,
    day: Math.min(inst.c.day, daysInMonth(year, month)) + Math.trunc(dur.days) + Math.trunc(dur.weeks) * 7
  }), millisToAdd = Duration.fromObject({
    years: dur.years - Math.trunc(dur.years),
    quarters: dur.quarters - Math.trunc(dur.quarters),
    months: dur.months - Math.trunc(dur.months),
    weeks: dur.weeks - Math.trunc(dur.weeks),
    days: dur.days - Math.trunc(dur.days),
    hours: dur.hours,
    minutes: dur.minutes,
    seconds: dur.seconds,
    milliseconds: dur.milliseconds
  }).as("milliseconds"), localTS = objToLocalTS(c);
  let [ts, o] = fixOffset(localTS, oPre, inst.zone);
  if (millisToAdd !== 0) {
    ts += millisToAdd;
    o = inst.zone.offset(ts);
  }
  return { ts, o };
}
function parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {
  const { setZone, zone } = opts;
  if (parsed && Object.keys(parsed).length !== 0 || parsedZone) {
    const interpretationZone = parsedZone || zone, inst = DateTime.fromObject(parsed, __spreadProps(__spreadValues({}, opts), {
      zone: interpretationZone,
      specificOffset
    }));
    return setZone ? inst : inst.setZone(zone);
  } else {
    return DateTime.invalid(
      new Invalid("unparsable", `the input "${text}" can't be parsed as ${format}`)
    );
  }
}
function toTechFormat(dt, format, allowZ = true) {
  return dt.isValid ? Formatter.create(Locale.create("en-US"), {
    allowZ,
    forceSimple: true
  }).formatDateTimeFromString(dt, format) : null;
}
function toISODate(o, extended, precision) {
  const longFormat = o.c.year > 9999 || o.c.year < 0;
  let c = "";
  if (longFormat && o.c.year >= 0) c += "+";
  c += padStart(o.c.year, longFormat ? 6 : 4);
  if (precision === "year") return c;
  if (extended) {
    c += "-";
    c += padStart(o.c.month);
    if (precision === "month") return c;
    c += "-";
  } else {
    c += padStart(o.c.month);
    if (precision === "month") return c;
  }
  c += padStart(o.c.day);
  return c;
}
function toISOTime(o, extended, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone, precision) {
  let showSeconds = !suppressSeconds || o.c.millisecond !== 0 || o.c.second !== 0, c = "";
  switch (precision) {
    case "day":
    case "month":
    case "year":
      break;
    default:
      c += padStart(o.c.hour);
      if (precision === "hour") break;
      if (extended) {
        c += ":";
        c += padStart(o.c.minute);
        if (precision === "minute") break;
        if (showSeconds) {
          c += ":";
          c += padStart(o.c.second);
        }
      } else {
        c += padStart(o.c.minute);
        if (precision === "minute") break;
        if (showSeconds) {
          c += padStart(o.c.second);
        }
      }
      if (precision === "second") break;
      if (showSeconds && (!suppressMilliseconds || o.c.millisecond !== 0)) {
        c += ".";
        c += padStart(o.c.millisecond, 3);
      }
  }
  if (includeOffset) {
    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {
      c += "Z";
    } else if (o.o < 0) {
      c += "-";
      c += padStart(Math.trunc(-o.o / 60));
      c += ":";
      c += padStart(Math.trunc(-o.o % 60));
    } else {
      c += "+";
      c += padStart(Math.trunc(o.o / 60));
      c += ":";
      c += padStart(Math.trunc(o.o % 60));
    }
  }
  if (extendedZone) {
    c += "[" + o.zone.ianaName + "]";
  }
  return c;
}
var defaultUnitValues = {
  month: 1,
  day: 1,
  hour: 0,
  minute: 0,
  second: 0,
  millisecond: 0
};
var defaultWeekUnitValues = {
  weekNumber: 1,
  weekday: 1,
  hour: 0,
  minute: 0,
  second: 0,
  millisecond: 0
};
var defaultOrdinalUnitValues = {
  ordinal: 1,
  hour: 0,
  minute: 0,
  second: 0,
  millisecond: 0
};
var orderedUnits2 = ["year", "month", "day", "hour", "minute", "second", "millisecond"];
var orderedWeekUnits = [
  "weekYear",
  "weekNumber",
  "weekday",
  "hour",
  "minute",
  "second",
  "millisecond"
];
var orderedOrdinalUnits = ["year", "ordinal", "hour", "minute", "second", "millisecond"];
function normalizeUnit(unit) {
  const normalized = {
    year: "year",
    years: "year",
    month: "month",
    months: "month",
    day: "day",
    days: "day",
    hour: "hour",
    hours: "hour",
    minute: "minute",
    minutes: "minute",
    quarter: "quarter",
    quarters: "quarter",
    second: "second",
    seconds: "second",
    millisecond: "millisecond",
    milliseconds: "millisecond",
    weekday: "weekday",
    weekdays: "weekday",
    weeknumber: "weekNumber",
    weeksnumber: "weekNumber",
    weeknumbers: "weekNumber",
    weekyear: "weekYear",
    weekyears: "weekYear",
    ordinal: "ordinal"
  }[unit.toLowerCase()];
  if (!normalized) throw new InvalidUnitError(unit);
  return normalized;
}
function normalizeUnitWithLocalWeeks(unit) {
  switch (unit.toLowerCase()) {
    case "localweekday":
    case "localweekdays":
      return "localWeekday";
    case "localweeknumber":
    case "localweeknumbers":
      return "localWeekNumber";
    case "localweekyear":
    case "localweekyears":
      return "localWeekYear";
    default:
      return normalizeUnit(unit);
  }
}
function guessOffsetForZone(zone) {
  if (zoneOffsetTs === void 0) {
    zoneOffsetTs = Settings.now();
  }
  if (zone.type !== "iana") {
    return zone.offset(zoneOffsetTs);
  }
  const zoneName = zone.name;
  let offsetGuess = zoneOffsetGuessCache.get(zoneName);
  if (offsetGuess === void 0) {
    offsetGuess = zone.offset(zoneOffsetTs);
    zoneOffsetGuessCache.set(zoneName, offsetGuess);
  }
  return offsetGuess;
}
function quickDT(obj, opts) {
  const zone = normalizeZone(opts.zone, Settings.defaultZone);
  if (!zone.isValid) {
    return DateTime.invalid(unsupportedZone(zone));
  }
  const loc = Locale.fromObject(opts);
  let ts, o;
  if (!isUndefined(obj.year)) {
    for (const u of orderedUnits2) {
      if (isUndefined(obj[u])) {
        obj[u] = defaultUnitValues[u];
      }
    }
    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);
    if (invalid) {
      return DateTime.invalid(invalid);
    }
    const offsetProvis = guessOffsetForZone(zone);
    [ts, o] = objToTS(obj, offsetProvis, zone);
  } else {
    ts = Settings.now();
  }
  return new DateTime({ ts, zone, loc, o });
}
function diffRelative(start, end, opts) {
  const round = isUndefined(opts.round) ? true : opts.round, rounding = isUndefined(opts.rounding) ? "trunc" : opts.rounding, format = (c, unit) => {
    c = roundTo(c, round || opts.calendary ? 0 : 2, opts.calendary ? "round" : rounding);
    const formatter = end.loc.clone(opts).relFormatter(opts);
    return formatter.format(c, unit);
  }, differ = (unit) => {
    if (opts.calendary) {
      if (!end.hasSame(start, unit)) {
        return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);
      } else return 0;
    } else {
      return end.diff(start, unit).get(unit);
    }
  };
  if (opts.unit) {
    return format(differ(opts.unit), opts.unit);
  }
  for (const unit of opts.units) {
    const count = differ(unit);
    if (Math.abs(count) >= 1) {
      return format(count, unit);
    }
  }
  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);
}
function lastOpts(argList) {
  let opts = {}, args;
  if (argList.length > 0 && typeof argList[argList.length - 1] === "object") {
    opts = argList[argList.length - 1];
    args = Array.from(argList).slice(0, argList.length - 1);
  } else {
    args = Array.from(argList);
  }
  return [opts, args];
}
var zoneOffsetTs;
var zoneOffsetGuessCache = /* @__PURE__ */ new Map();
var DateTime = class _DateTime {
  /**
   * @access private
   */
  constructor(config) {
    const zone = config.zone || Settings.defaultZone;
    let invalid = config.invalid || (Number.isNaN(config.ts) ? new Invalid("invalid input") : null) || (!zone.isValid ? unsupportedZone(zone) : null);
    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;
    let c = null, o = null;
    if (!invalid) {
      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);
      if (unchanged) {
        [c, o] = [config.old.c, config.old.o];
      } else {
        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);
        c = tsToObj(this.ts, ot);
        invalid = Number.isNaN(c.year) ? new Invalid("invalid input") : null;
        c = invalid ? null : c;
        o = invalid ? null : ot;
      }
    }
    this._zone = zone;
    this.loc = config.loc || Locale.create();
    this.invalid = invalid;
    this.weekData = null;
    this.localWeekData = null;
    this.c = c;
    this.o = o;
    this.isLuxonDateTime = true;
  }
  // CONSTRUCT
  /**
   * Create a DateTime for the current instant, in the system's time zone.
   *
   * Use Settings to override these default values if needed.
   * @example DateTime.now().toISO() //~> now in the ISO format
   * @return {DateTime}
   */
  static now() {
    return new _DateTime({});
  }
  /**
   * Create a local DateTime
   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used
   * @param {number} [month=1] - The month, 1-indexed
   * @param {number} [day=1] - The day of the month, 1-indexed
   * @param {number} [hour=0] - The hour of the day, in 24-hour time
   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59
   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59
   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999
   * @example DateTime.local()                                  //~> now
   * @example DateTime.local({ zone: "America/New_York" })      //~> now, in US east coast time
   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00
   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00
   * @example DateTime.local(2017, 3, 12, { locale: "fr" })     //~> 2017-03-12T00:00:00, with a French locale
   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00
   * @example DateTime.local(2017, 3, 12, 5, { zone: "utc" })   //~> 2017-03-12T05:00:00, in UTC
   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00
   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10
   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765
   * @return {DateTime}
   */
  static local() {
    const [opts, args] = lastOpts(arguments), [year, month, day, hour, minute, second, millisecond] = args;
    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);
  }
  /**
   * Create a DateTime in UTC
   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used
   * @param {number} [month=1] - The month, 1-indexed
   * @param {number} [day=1] - The day of the month
   * @param {number} [hour=0] - The hour of the day, in 24-hour time
   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59
   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59
   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999
   * @param {Object} options - configuration options for the DateTime
   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance
   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance
   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance
   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance
   * @example DateTime.utc()                                              //~> now
   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z
   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z
   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z
   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z
   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z
   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: "fr" })          //~> 2017-03-12T05:45:00Z with a French locale
   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z
   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: "fr" }) //~> 2017-03-12T05:45:10.765Z with a French locale
   * @return {DateTime}
   */
  static utc() {
    const [opts, args] = lastOpts(arguments), [year, month, day, hour, minute, second, millisecond] = args;
    opts.zone = FixedOffsetZone.utcInstance;
    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);
  }
  /**
   * Create a DateTime from a JavaScript Date object. Uses the default zone.
   * @param {Date} date - a JavaScript Date object
   * @param {Object} options - configuration options for the DateTime
   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into
   * @return {DateTime}
   */
  static fromJSDate(date, options = {}) {
    const ts = isDate(date) ? date.valueOf() : NaN;
    if (Number.isNaN(ts)) {
      return _DateTime.invalid("invalid input");
    }
    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);
    if (!zoneToUse.isValid) {
      return _DateTime.invalid(unsupportedZone(zoneToUse));
    }
    return new _DateTime({
      ts,
      zone: zoneToUse,
      loc: Locale.fromObject(options)
    });
  }
  /**
   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.
   * @param {number} milliseconds - a number of milliseconds since 1970 UTC
   * @param {Object} options - configuration options for the DateTime
   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into
   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance
   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance
   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance
   * @return {DateTime}
   */
  static fromMillis(milliseconds, options = {}) {
    if (!isNumber(milliseconds)) {
      throw new InvalidArgumentError(
        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`
      );
    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {
      return _DateTime.invalid("Timestamp out of range");
    } else {
      return new _DateTime({
        ts: milliseconds,
        zone: normalizeZone(options.zone, Settings.defaultZone),
        loc: Locale.fromObject(options)
      });
    }
  }
  /**
   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.
   * @param {number} seconds - a number of seconds since 1970 UTC
   * @param {Object} options - configuration options for the DateTime
   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into
   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance
   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance
   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance
   * @return {DateTime}
   */
  static fromSeconds(seconds, options = {}) {
    if (!isNumber(seconds)) {
      throw new InvalidArgumentError("fromSeconds requires a numerical input");
    } else {
      return new _DateTime({
        ts: seconds * 1e3,
        zone: normalizeZone(options.zone, Settings.defaultZone),
        loc: Locale.fromObject(options)
      });
    }
  }
  /**
   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.
   * @param {Object} obj - the object to create the DateTime from
   * @param {number} obj.year - a year, such as 1987
   * @param {number} obj.month - a month, 1-12
   * @param {number} obj.day - a day of the month, 1-31, depending on the month
   * @param {number} obj.ordinal - day of the year, 1-365 or 366
   * @param {number} obj.weekYear - an ISO week year
   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year
   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday
   * @param {number} obj.localWeekYear - a week year, according to the locale
   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale
   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale
   * @param {number} obj.hour - hour of the day, 0-23
   * @param {number} obj.minute - minute of the hour, 0-59
   * @param {number} obj.second - second of the minute, 0-59
   * @param {number} obj.millisecond - millisecond of the second, 0-999
   * @param {Object} opts - options for creating this DateTime
   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()
   * @param {string} [opts.locale='system\'s locale'] - a locale to set on the resulting DateTime instance
   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance
   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance
   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'
   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'
   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06
   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),
   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })
   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })
   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'
   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: "en-US" }).toISODate() //=> '2021-12-26'
   * @return {DateTime}
   */
  static fromObject(obj, opts = {}) {
    obj = obj || {};
    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);
    if (!zoneToUse.isValid) {
      return _DateTime.invalid(unsupportedZone(zoneToUse));
    }
    const loc = Locale.fromObject(opts);
    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);
    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);
    const tsNow = Settings.now(), offsetProvis = !isUndefined(opts.specificOffset) ? opts.specificOffset : zoneToUse.offset(tsNow), containsOrdinal = !isUndefined(normalized.ordinal), containsGregorYear = !isUndefined(normalized.year), containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day), containsGregor = containsGregorYear || containsGregorMD, definiteWeekDef = normalized.weekYear || normalized.weekNumber;
    if ((containsGregor || containsOrdinal) && definiteWeekDef) {
      throw new ConflictingSpecificationError(
        "Can't mix weekYear/weekNumber units with year/month/day or ordinals"
      );
    }
    if (containsGregorMD && containsOrdinal) {
      throw new ConflictingSpecificationError("Can't mix ordinal dates with month/day");
    }
    const useWeekData = definiteWeekDef || normalized.weekday && !containsGregor;
    let units, defaultValues, objNow = tsToObj(tsNow, offsetProvis);
    if (useWeekData) {
      units = orderedWeekUnits;
      defaultValues = defaultWeekUnitValues;
      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);
    } else if (containsOrdinal) {
      units = orderedOrdinalUnits;
      defaultValues = defaultOrdinalUnitValues;
      objNow = gregorianToOrdinal(objNow);
    } else {
      units = orderedUnits2;
      defaultValues = defaultUnitValues;
    }
    let foundFirst = false;
    for (const u of units) {
      const v = normalized[u];
      if (!isUndefined(v)) {
        foundFirst = true;
      } else if (foundFirst) {
        normalized[u] = defaultValues[u];
      } else {
        normalized[u] = objNow[u];
      }
    }
    const higherOrderInvalid = useWeekData ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek) : containsOrdinal ? hasInvalidOrdinalData(normalized) : hasInvalidGregorianData(normalized), invalid = higherOrderInvalid || hasInvalidTimeData(normalized);
    if (invalid) {
      return _DateTime.invalid(invalid);
    }
    const gregorian = useWeekData ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek) : containsOrdinal ? ordinalToGregorian(normalized) : normalized, [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse), inst = new _DateTime({
      ts: tsFinal,
      zone: zoneToUse,
      o: offsetFinal,
      loc
    });
    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {
      return _DateTime.invalid(
        "mismatched weekday",
        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`
      );
    }
    if (!inst.isValid) {
      return _DateTime.invalid(inst.invalid);
    }
    return inst;
  }
  /**
   * Create a DateTime from an ISO 8601 string
   * @param {string} text - the ISO string
   * @param {Object} opts - options to affect the creation
   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone
   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one
   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance
   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance
   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance
   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance
   * @example DateTime.fromISO('2016-05-25T09:08:34.123')
   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')
   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})
   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})
   * @example DateTime.fromISO('2016-W05-4')
   * @return {DateTime}
   */
  static fromISO(text, opts = {}) {
    const [vals, parsedZone] = parseISODate(text);
    return parseDataToDateTime(vals, parsedZone, opts, "ISO 8601", text);
  }
  /**
   * Create a DateTime from an RFC 2822 string
   * @param {string} text - the RFC 2822 string
   * @param {Object} opts - options to affect the creation
   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.
   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one
   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance
   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance
   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance
   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')
   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')
   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')
   * @return {DateTime}
   */
  static fromRFC2822(text, opts = {}) {
    const [vals, parsedZone] = parseRFC2822Date(text);
    return parseDataToDateTime(vals, parsedZone, opts, "RFC 2822", text);
  }
  /**
   * Create a DateTime from an HTTP header date
   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1
   * @param {string} text - the HTTP header date
   * @param {Object} opts - options to affect the creation
   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.
   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.
   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance
   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance
   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance
   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')
   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')
   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')
   * @return {DateTime}
   */
  static fromHTTP(text, opts = {}) {
    const [vals, parsedZone] = parseHTTPDate(text);
    return parseDataToDateTime(vals, parsedZone, opts, "HTTP", opts);
  }
  /**
   * Create a DateTime from an input string and format string.
   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).
   * @param {string} text - the string to parse
   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)
   * @param {Object} opts - options to affect the creation
   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone
   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one
   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale
   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system
   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance
   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @return {DateTime}
   */
  static fromFormat(text, fmt, opts = {}) {
    if (isUndefined(text) || isUndefined(fmt)) {
      throw new InvalidArgumentError("fromFormat requires an input string and a format");
    }
    const { locale = null, numberingSystem = null } = opts, localeToUse = Locale.fromOpts({
      locale,
      numberingSystem,
      defaultToEN: true
    }), [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);
    if (invalid) {
      return _DateTime.invalid(invalid);
    } else {
      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);
    }
  }
  /**
   * @deprecated use fromFormat instead
   */
  static fromString(text, fmt, opts = {}) {
    return _DateTime.fromFormat(text, fmt, opts);
  }
  /**
   * Create a DateTime from a SQL date, time, or datetime
   * Defaults to en-US if no locale has been specified, regardless of the system's locale
   * @param {string} text - the string to parse
   * @param {Object} opts - options to affect the creation
   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone
   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one
   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale
   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system
   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance
   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance
   * @example DateTime.fromSQL('2017-05-15')
   * @example DateTime.fromSQL('2017-05-15 09:12:34')
   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')
   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')
   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')
   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })
   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })
   * @example DateTime.fromSQL('09:12:34.342')
   * @return {DateTime}
   */
  static fromSQL(text, opts = {}) {
    const [vals, parsedZone] = parseSQL(text);
    return parseDataToDateTime(vals, parsedZone, opts, "SQL", text);
  }
  /**
   * Create an invalid DateTime.
   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.
   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information
   * @return {DateTime}
   */
  static invalid(reason, explanation = null) {
    if (!reason) {
      throw new InvalidArgumentError("need to specify a reason the DateTime is invalid");
    }
    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);
    if (Settings.throwOnInvalid) {
      throw new InvalidDateTimeError(invalid);
    } else {
      return new _DateTime({ invalid });
    }
  }
  /**
   * Check if an object is an instance of DateTime. Works across context boundaries
   * @param {object} o
   * @return {boolean}
   */
  static isDateTime(o) {
    return o && o.isLuxonDateTime || false;
  }
  /**
   * Produce the format string for a set of options
   * @param formatOpts
   * @param localeOpts
   * @returns {string}
   */
  static parseFormatForOpts(formatOpts, localeOpts = {}) {
    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));
    return !tokenList ? null : tokenList.map((t) => t ? t.val : null).join("");
  }
  /**
   * Produce the the fully expanded format token for the locale
   * Does NOT quote characters, so quoted tokens will not round trip correctly
   * @param fmt
   * @param localeOpts
   * @returns {string}
   */
  static expandFormat(fmt, localeOpts = {}) {
    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));
    return expanded.map((t) => t.val).join("");
  }
  static resetCache() {
    zoneOffsetTs = void 0;
    zoneOffsetGuessCache.clear();
  }
  // INFO
  /**
   * Get the value of unit.
   * @param {string} unit - a unit such as 'minute' or 'day'
   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7
   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4
   * @return {number}
   */
  get(unit) {
    return this[unit];
  }
  /**
   * Returns whether the DateTime is valid. Invalid DateTimes occur when:
   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30
   * * The DateTime was created by an operation on another invalid date
   * @type {boolean}
   */
  get isValid() {
    return this.invalid === null;
  }
  /**
   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid
   * @type {string}
   */
  get invalidReason() {
    return this.invalid ? this.invalid.reason : null;
  }
  /**
   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid
   * @type {string}
   */
  get invalidExplanation() {
    return this.invalid ? this.invalid.explanation : null;
  }
  /**
   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime
   *
   * @type {string}
   */
  get locale() {
    return this.isValid ? this.loc.locale : null;
  }
  /**
   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime
   *
   * @type {string}
   */
  get numberingSystem() {
    return this.isValid ? this.loc.numberingSystem : null;
  }
  /**
   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime
   *
   * @type {string}
   */
  get outputCalendar() {
    return this.isValid ? this.loc.outputCalendar : null;
  }
  /**
   * Get the time zone associated with this DateTime.
   * @type {Zone}
   */
  get zone() {
    return this._zone;
  }
  /**
   * Get the name of the time zone.
   * @type {string}
   */
  get zoneName() {
    return this.isValid ? this.zone.name : null;
  }
  /**
   * Get the year
   * @example DateTime.local(2017, 5, 25).year //=> 2017
   * @type {number}
   */
  get year() {
    return this.isValid ? this.c.year : NaN;
  }
  /**
   * Get the quarter
   * @example DateTime.local(2017, 5, 25).quarter //=> 2
   * @type {number}
   */
  get quarter() {
    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;
  }
  /**
   * Get the month (1-12).
   * @example DateTime.local(2017, 5, 25).month //=> 5
   * @type {number}
   */
  get month() {
    return this.isValid ? this.c.month : NaN;
  }
  /**
   * Get the day of the month (1-30ish).
   * @example DateTime.local(2017, 5, 25).day //=> 25
   * @type {number}
   */
  get day() {
    return this.isValid ? this.c.day : NaN;
  }
  /**
   * Get the hour of the day (0-23).
   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9
   * @type {number}
   */
  get hour() {
    return this.isValid ? this.c.hour : NaN;
  }
  /**
   * Get the minute of the hour (0-59).
   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30
   * @type {number}
   */
  get minute() {
    return this.isValid ? this.c.minute : NaN;
  }
  /**
   * Get the second of the minute (0-59).
   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52
   * @type {number}
   */
  get second() {
    return this.isValid ? this.c.second : NaN;
  }
  /**
   * Get the millisecond of the second (0-999).
   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654
   * @type {number}
   */
  get millisecond() {
    return this.isValid ? this.c.millisecond : NaN;
  }
  /**
   * Get the week year
   * @see https://en.wikipedia.org/wiki/ISO_week_date
   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015
   * @type {number}
   */
  get weekYear() {
    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;
  }
  /**
   * Get the week number of the week year (1-52ish).
   * @see https://en.wikipedia.org/wiki/ISO_week_date
   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21
   * @type {number}
   */
  get weekNumber() {
    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;
  }
  /**
   * Get the day of the week.
   * 1 is Monday and 7 is Sunday
   * @see https://en.wikipedia.org/wiki/ISO_week_date
   * @example DateTime.local(2014, 11, 31).weekday //=> 4
   * @type {number}
   */
  get weekday() {
    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;
  }
  /**
   * Returns true if this date is on a weekend according to the locale, false otherwise
   * @returns {boolean}
   */
  get isWeekend() {
    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);
  }
  /**
   * Get the day of the week according to the locale.
   * 1 is the first day of the week and 7 is the last day of the week.
   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,
   * @returns {number}
   */
  get localWeekday() {
    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;
  }
  /**
   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,
   * because the week can start on different days of the week (see localWeekday) and because a different number of days
   * is required for a week to count as the first week of a year.
   * @returns {number}
   */
  get localWeekNumber() {
    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;
  }
  /**
   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)
   * differently, see localWeekNumber.
   * @returns {number}
   */
  get localWeekYear() {
    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;
  }
  /**
   * Get the ordinal (meaning the day of the year)
   * @example DateTime.local(2017, 5, 25).ordinal //=> 145
   * @type {number|DateTime}
   */
  get ordinal() {
    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;
  }
  /**
   * Get the human readable short month name, such as 'Oct'.
   * Defaults to the system's locale if no locale has been specified
   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct
   * @type {string}
   */
  get monthShort() {
    return this.isValid ? Info.months("short", { locObj: this.loc })[this.month - 1] : null;
  }
  /**
   * Get the human readable long month name, such as 'October'.
   * Defaults to the system's locale if no locale has been specified
   * @example DateTime.local(2017, 10, 30).monthLong //=> October
   * @type {string}
   */
  get monthLong() {
    return this.isValid ? Info.months("long", { locObj: this.loc })[this.month - 1] : null;
  }
  /**
   * Get the human readable short weekday, such as 'Mon'.
   * Defaults to the system's locale if no locale has been specified
   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon
   * @type {string}
   */
  get weekdayShort() {
    return this.isValid ? Info.weekdays("short", { locObj: this.loc })[this.weekday - 1] : null;
  }
  /**
   * Get the human readable long weekday, such as 'Monday'.
   * Defaults to the system's locale if no locale has been specified
   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday
   * @type {string}
   */
  get weekdayLong() {
    return this.isValid ? Info.weekdays("long", { locObj: this.loc })[this.weekday - 1] : null;
  }
  /**
   * Get the UTC offset of this DateTime in minutes
   * @example DateTime.now().offset //=> -240
   * @example DateTime.utc().offset //=> 0
   * @type {number}
   */
  get offset() {
    return this.isValid ? +this.o : NaN;
  }
  /**
   * Get the short human name for the zone's current offset, for example "EST" or "EDT".
   * Defaults to the system's locale if no locale has been specified
   * @type {string}
   */
  get offsetNameShort() {
    if (this.isValid) {
      return this.zone.offsetName(this.ts, {
        format: "short",
        locale: this.locale
      });
    } else {
      return null;
    }
  }
  /**
   * Get the long human name for the zone's current offset, for example "Eastern Standard Time" or "Eastern Daylight Time".
   * Defaults to the system's locale if no locale has been specified
   * @type {string}
   */
  get offsetNameLong() {
    if (this.isValid) {
      return this.zone.offsetName(this.ts, {
        format: "long",
        locale: this.locale
      });
    } else {
      return null;
    }
  }
  /**
   * Get whether this zone's offset ever changes, as in a DST.
   * @type {boolean}
   */
  get isOffsetFixed() {
    return this.isValid ? this.zone.isUniversal : null;
  }
  /**
   * Get whether the DateTime is in a DST.
   * @type {boolean}
   */
  get isInDST() {
    if (this.isOffsetFixed) {
      return false;
    } else {
      return this.offset > this.set({ month: 1, day: 1 }).offset || this.offset > this.set({ month: 5 }).offset;
    }
  }
  /**
   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC
   * in this DateTime's zone. During DST changes local time can be ambiguous, for example
   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.
   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.
   * @returns {DateTime[]}
   */
  getPossibleOffsets() {
    if (!this.isValid || this.isOffsetFixed) {
      return [this];
    }
    const dayMs = 864e5;
    const minuteMs = 6e4;
    const localTS = objToLocalTS(this.c);
    const oEarlier = this.zone.offset(localTS - dayMs);
    const oLater = this.zone.offset(localTS + dayMs);
    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);
    const o2 = this.zone.offset(localTS - oLater * minuteMs);
    if (o1 === o2) {
      return [this];
    }
    const ts1 = localTS - o1 * minuteMs;
    const ts2 = localTS - o2 * minuteMs;
    const c1 = tsToObj(ts1, o1);
    const c2 = tsToObj(ts2, o2);
    if (c1.hour === c2.hour && c1.minute === c2.minute && c1.second === c2.second && c1.millisecond === c2.millisecond) {
      return [clone3(this, { ts: ts1 }), clone3(this, { ts: ts2 })];
    }
    return [this];
  }
  /**
   * Returns true if this DateTime is in a leap year, false otherwise
   * @example DateTime.local(2016).isInLeapYear //=> true
   * @example DateTime.local(2013).isInLeapYear //=> false
   * @type {boolean}
   */
  get isInLeapYear() {
    return isLeapYear(this.year);
  }
  /**
   * Returns the number of days in this DateTime's month
   * @example DateTime.local(2016, 2).daysInMonth //=> 29
   * @example DateTime.local(2016, 3).daysInMonth //=> 31
   * @type {number}
   */
  get daysInMonth() {
    return daysInMonth(this.year, this.month);
  }
  /**
   * Returns the number of days in this DateTime's year
   * @example DateTime.local(2016).daysInYear //=> 366
   * @example DateTime.local(2013).daysInYear //=> 365
   * @type {number}
   */
  get daysInYear() {
    return this.isValid ? daysInYear(this.year) : NaN;
  }
  /**
   * Returns the number of weeks in this DateTime's year
   * @see https://en.wikipedia.org/wiki/ISO_week_date
   * @example DateTime.local(2004).weeksInWeekYear //=> 53
   * @example DateTime.local(2013).weeksInWeekYear //=> 52
   * @type {number}
   */
  get weeksInWeekYear() {
    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;
  }
  /**
   * Returns the number of weeks in this DateTime's local week year
   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52
   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53
   * @type {number}
   */
  get weeksInLocalWeekYear() {
    return this.isValid ? weeksInWeekYear(
      this.localWeekYear,
      this.loc.getMinDaysInFirstWeek(),
      this.loc.getStartOfWeek()
    ) : NaN;
  }
  /**
   * Returns the resolved Intl options for this DateTime.
   * This is useful in understanding the behavior of formatting methods
   * @param {Object} opts - the same options as toLocaleString
   * @return {Object}
   */
  resolvedLocaleOptions(opts = {}) {
    const { locale, numberingSystem, calendar } = Formatter.create(
      this.loc.clone(opts),
      opts
    ).resolvedOptions(this);
    return { locale, numberingSystem, outputCalendar: calendar };
  }
  // TRANSFORM
  /**
   * "Set" the DateTime's zone to UTC. Returns a newly-constructed DateTime.
   *
   * Equivalent to {@link DateTime#setZone}('utc')
   * @param {number} [offset=0] - optionally, an offset from UTC in minutes
   * @param {Object} [opts={}] - options to pass to `setZone()`
   * @return {DateTime}
   */
  toUTC(offset2 = 0, opts = {}) {
    return this.setZone(FixedOffsetZone.instance(offset2), opts);
  }
  /**
   * "Set" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.
   *
   * Equivalent to `setZone('local')`
   * @return {DateTime}
   */
  toLocal() {
    return this.setZone(Settings.defaultZone);
  }
  /**
   * "Set" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.
   *
   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.
   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.
   * @param {Object} opts - options
   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.
   * @return {DateTime}
   */
  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {
    zone = normalizeZone(zone, Settings.defaultZone);
    if (zone.equals(this.zone)) {
      return this;
    } else if (!zone.isValid) {
      return _DateTime.invalid(unsupportedZone(zone));
    } else {
      let newTS = this.ts;
      if (keepLocalTime || keepCalendarTime) {
        const offsetGuess = zone.offset(this.ts);
        const asObj = this.toObject();
        [newTS] = objToTS(asObj, offsetGuess, zone);
      }
      return clone3(this, { ts: newTS, zone });
    }
  }
  /**
   * "Set" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.
   * @param {Object} properties - the properties to set
   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })
   * @return {DateTime}
   */
  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {
    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });
    return clone3(this, { loc });
  }
  /**
   * "Set" the locale. Returns a newly-constructed DateTime.
   * Just a convenient alias for reconfigure({ locale })
   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')
   * @return {DateTime}
   */
  setLocale(locale) {
    return this.reconfigure({ locale });
  }
  /**
   * "Set" the values of specified units. Returns a newly-constructed DateTime.
   * You can only set units with this method; for "setting" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.
   *
   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.
   * They cannot be mixed with ISO-week units like `weekday`.
   * @param {Object} values - a mapping of units to numbers
   * @example dt.set({ year: 2017 })
   * @example dt.set({ hour: 8, minute: 30 })
   * @example dt.set({ weekday: 5 })
   * @example dt.set({ year: 2005, ordinal: 234 })
   * @return {DateTime}
   */
  set(values) {
    if (!this.isValid) return this;
    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);
    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);
    const settingWeekStuff = !isUndefined(normalized.weekYear) || !isUndefined(normalized.weekNumber) || !isUndefined(normalized.weekday), containsOrdinal = !isUndefined(normalized.ordinal), containsGregorYear = !isUndefined(normalized.year), containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day), containsGregor = containsGregorYear || containsGregorMD, definiteWeekDef = normalized.weekYear || normalized.weekNumber;
    if ((containsGregor || containsOrdinal) && definiteWeekDef) {
      throw new ConflictingSpecificationError(
        "Can't mix weekYear/weekNumber units with year/month/day or ordinals"
      );
    }
    if (containsGregorMD && containsOrdinal) {
      throw new ConflictingSpecificationError("Can't mix ordinal dates with month/day");
    }
    let mixed;
    if (settingWeekStuff) {
      mixed = weekToGregorian(
        __spreadValues(__spreadValues({}, gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek)), normalized),
        minDaysInFirstWeek,
        startOfWeek
      );
    } else if (!isUndefined(normalized.ordinal)) {
      mixed = ordinalToGregorian(__spreadValues(__spreadValues({}, gregorianToOrdinal(this.c)), normalized));
    } else {
      mixed = __spreadValues(__spreadValues({}, this.toObject()), normalized);
      if (isUndefined(normalized.day)) {
        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);
      }
    }
    const [ts, o] = objToTS(mixed, this.o, this.zone);
    return clone3(this, { ts, o });
  }
  /**
   * Add a period of time to this DateTime and return the resulting DateTime
   *
   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.
   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()
   * @example DateTime.now().plus(123) //~> in 123 milliseconds
   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes
   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow
   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday
   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min
   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min
   * @return {DateTime}
   */
  plus(duration) {
    if (!this.isValid) return this;
    const dur = Duration.fromDurationLike(duration);
    return clone3(this, adjustTime(this, dur));
  }
  /**
   * Subtract a period of time to this DateTime and return the resulting DateTime
   * See {@link DateTime#plus}
   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()
   @return {DateTime}
   */
  minus(duration) {
    if (!this.isValid) return this;
    const dur = Duration.fromDurationLike(duration).negate();
    return clone3(this, adjustTime(this, dur));
  }
  /**
   * "Set" this DateTime to the beginning of a unit of time.
   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.
   * @param {Object} opts - options
   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week
   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'
   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'
   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays
   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'
   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'
   * @return {DateTime}
   */
  startOf(unit, { useLocaleWeeks = false } = {}) {
    if (!this.isValid) return this;
    const o = {}, normalizedUnit = Duration.normalizeUnit(unit);
    switch (normalizedUnit) {
      case "years":
        o.month = 1;
      // falls through
      case "quarters":
      case "months":
        o.day = 1;
      // falls through
      case "weeks":
      case "days":
        o.hour = 0;
      // falls through
      case "hours":
        o.minute = 0;
      // falls through
      case "minutes":
        o.second = 0;
      // falls through
      case "seconds":
        o.millisecond = 0;
        break;
      case "milliseconds":
        break;
    }
    if (normalizedUnit === "weeks") {
      if (useLocaleWeeks) {
        const startOfWeek = this.loc.getStartOfWeek();
        const { weekday } = this;
        if (weekday < startOfWeek) {
          o.weekNumber = this.weekNumber - 1;
        }
        o.weekday = startOfWeek;
      } else {
        o.weekday = 1;
      }
    }
    if (normalizedUnit === "quarters") {
      const q = Math.ceil(this.month / 3);
      o.month = (q - 1) * 3 + 1;
    }
    return this.set(o);
  }
  /**
   * "Set" this DateTime to the end (meaning the last millisecond) of a unit of time
   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.
   * @param {Object} opts - options
   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week
   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'
   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'
   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays
   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'
   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'
   * @return {DateTime}
   */
  endOf(unit, opts) {
    return this.isValid ? this.plus({ [unit]: 1 }).startOf(unit, opts).minus(1) : this;
  }
  // OUTPUT
  /**
   * Returns a string representation of this DateTime formatted according to the specified format string.
   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).
   * Defaults to en-US if no locale has been specified, regardless of the system's locale.
   * @param {string} fmt - the format string
   * @param {Object} opts - opts to override the configuration options on this DateTime
   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'
   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'
   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: "fr" }) //=> '2017 avr. 22'
   * @example DateTime.now().toFormat("HH 'hours and' mm 'minutes'") //=> '20 hours and 55 minutes'
   * @return {string}
   */
  toFormat(fmt, opts = {}) {
    return this.isValid ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt) : INVALID3;
  }
  /**
   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.
   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation
   * of the DateTime in the assigned locale.
   * Defaults to the system's locale if no locale has been specified
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat
   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options
   * @param {Object} opts - opts to override the configuration options on this DateTime
   * @example DateTime.now().toLocaleString(); //=> 4/20/2017
   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'
   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'
   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'
   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'
   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'
   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'
   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'
   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'
   * @return {string}
   */
  toLocaleString(formatOpts = DATE_SHORT, opts = {}) {
    return this.isValid ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this) : INVALID3;
  }
  /**
   * Returns an array of format "parts", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.
   * Defaults to the system's locale if no locale has been specified
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts
   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.
   * @example DateTime.now().toLocaleParts(); //=> [
   *                                   //=>   { type: 'day', value: '25' },
   *                                   //=>   { type: 'literal', value: '/' },
   *                                   //=>   { type: 'month', value: '05' },
   *                                   //=>   { type: 'literal', value: '/' },
   *                                   //=>   { type: 'year', value: '1982' }
   *                                   //=> ]
   */
  toLocaleParts(opts = {}) {
    return this.isValid ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this) : [];
  }
  /**
   * Returns an ISO 8601-compliant string representation of this DateTime
   * @param {Object} opts - options
   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0
   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0
   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'
   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension
   * @param {string} [opts.format='extended'] - choose between the basic and extended format
   * @param {string} [opts.precision='milliseconds'] - truncate output to desired presicion: 'years', 'months', 'days', 'hours', 'minutes', 'seconds' or 'milliseconds'. When precision and suppressSeconds or suppressMilliseconds are used together, precision sets the maximum unit shown in the output, however seconds or milliseconds will still be suppressed if they are 0.
   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'
   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'
   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'
   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'
   * @example DateTime.now().toISO({ precision: 'day' }) //=> '2017-04-22Z'
   * @example DateTime.now().toISO({ precision: 'minute' }) //=> '2017-04-22T20:47Z'
   * @return {string|null}
   */
  toISO({
    format = "extended",
    suppressSeconds = false,
    suppressMilliseconds = false,
    includeOffset = true,
    extendedZone = false,
    precision = "milliseconds"
  } = {}) {
    if (!this.isValid) {
      return null;
    }
    precision = normalizeUnit(precision);
    const ext = format === "extended";
    let c = toISODate(this, ext, precision);
    if (orderedUnits2.indexOf(precision) >= 3) c += "T";
    c += toISOTime(
      this,
      ext,
      suppressSeconds,
      suppressMilliseconds,
      includeOffset,
      extendedZone,
      precision
    );
    return c;
  }
  /**
   * Returns an ISO 8601-compliant string representation of this DateTime's date component
   * @param {Object} opts - options
   * @param {string} [opts.format='extended'] - choose between the basic and extended format
   * @param {string} [opts.precision='day'] - truncate output to desired precision: 'years', 'months', or 'days'.
   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'
   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'
   * @example DateTime.utc(1982, 5, 25).toISODate({ precision: 'month' }) //=> '1982-05'
   * @return {string|null}
   */
  toISODate({ format = "extended", precision = "day" } = {}) {
    if (!this.isValid) {
      return null;
    }
    return toISODate(this, format === "extended", normalizeUnit(precision));
  }
  /**
   * Returns an ISO 8601-compliant string representation of this DateTime's week date
   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'
   * @return {string}
   */
  toISOWeekDate() {
    return toTechFormat(this, "kkkk-'W'WW-c");
  }
  /**
   * Returns an ISO 8601-compliant string representation of this DateTime's time component
   * @param {Object} opts - options
   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0
   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0
   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'
   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension
   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix
   * @param {string} [opts.format='extended'] - choose between the basic and extended format
   * @param {string} [opts.precision='milliseconds'] - truncate output to desired presicion: 'hours', 'minutes', 'seconds' or 'milliseconds'. When precision and suppressSeconds or suppressMilliseconds are used together, precision sets the maximum unit shown in the output, however seconds or milliseconds will still be suppressed if they are 0.
   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'
   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'
   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'
   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'
   * @example DateTime.utc().set({ hour: 7, minute: 34, second: 56 }).toISOTime({ precision: 'minute' }) //=> '07:34Z'
   * @return {string}
   */
  toISOTime({
    suppressMilliseconds = false,
    suppressSeconds = false,
    includeOffset = true,
    includePrefix = false,
    extendedZone = false,
    format = "extended",
    precision = "milliseconds"
  } = {}) {
    if (!this.isValid) {
      return null;
    }
    precision = normalizeUnit(precision);
    let c = includePrefix && orderedUnits2.indexOf(precision) >= 3 ? "T" : "";
    return c + toISOTime(
      this,
      format === "extended",
      suppressSeconds,
      suppressMilliseconds,
      includeOffset,
      extendedZone,
      precision
    );
  }
  /**
   * Returns an RFC 2822-compatible string representation of this DateTime
   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'
   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'
   * @return {string}
   */
  toRFC2822() {
    return toTechFormat(this, "EEE, dd LLL yyyy HH:mm:ss ZZZ", false);
  }
  /**
   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.
   * Specifically, the string conforms to RFC 1123.
   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1
   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'
   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'
   * @return {string}
   */
  toHTTP() {
    return toTechFormat(this.toUTC(), "EEE, dd LLL yyyy HH:mm:ss 'GMT'");
  }
  /**
   * Returns a string representation of this DateTime appropriate for use in SQL Date
   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'
   * @return {string|null}
   */
  toSQLDate() {
    if (!this.isValid) {
      return null;
    }
    return toISODate(this, true);
  }
  /**
   * Returns a string representation of this DateTime appropriate for use in SQL Time
   * @param {Object} opts - options
   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.
   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'
   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'
   * @example DateTime.utc().toSQL() //=> '05:15:16.345'
   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'
   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'
   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'
   * @return {string}
   */
  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {
    let fmt = "HH:mm:ss.SSS";
    if (includeZone || includeOffset) {
      if (includeOffsetSpace) {
        fmt += " ";
      }
      if (includeZone) {
        fmt += "z";
      } else if (includeOffset) {
        fmt += "ZZ";
      }
    }
    return toTechFormat(this, fmt, true);
  }
  /**
   * Returns a string representation of this DateTime appropriate for use in SQL DateTime
   * @param {Object} opts - options
   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.
   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'
   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'
   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'
   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'
   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'
   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'
   * @return {string}
   */
  toSQL(opts = {}) {
    if (!this.isValid) {
      return null;
    }
    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;
  }
  /**
   * Returns a string representation of this DateTime appropriate for debugging
   * @return {string}
   */
  toString() {
    return this.isValid ? this.toISO() : INVALID3;
  }
  /**
   * Returns a string representation of this DateTime appropriate for the REPL.
   * @return {string}
   */
  [Symbol.for("nodejs.util.inspect.custom")]() {
    if (this.isValid) {
      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;
    } else {
      return `DateTime { Invalid, reason: ${this.invalidReason} }`;
    }
  }
  /**
   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}
   * @return {number}
   */
  valueOf() {
    return this.toMillis();
  }
  /**
   * Returns the epoch milliseconds of this DateTime.
   * @return {number}
   */
  toMillis() {
    return this.isValid ? this.ts : NaN;
  }
  /**
   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.
   * @return {number}
   */
  toSeconds() {
    return this.isValid ? this.ts / 1e3 : NaN;
  }
  /**
   * Returns the epoch seconds (as a whole number) of this DateTime.
   * @return {number}
   */
  toUnixInteger() {
    return this.isValid ? Math.floor(this.ts / 1e3) : NaN;
  }
  /**
   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.
   * @return {string}
   */
  toJSON() {
    return this.toISO();
  }
  /**
   * Returns a BSON serializable equivalent to this DateTime.
   * @return {Date}
   */
  toBSON() {
    return this.toJSDate();
  }
  /**
   * Returns a JavaScript object with this DateTime's year, month, day, and so on.
   * @param opts - options for generating the object
   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output
   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }
   * @return {Object}
   */
  toObject(opts = {}) {
    if (!this.isValid) return {};
    const base = __spreadValues({}, this.c);
    if (opts.includeConfig) {
      base.outputCalendar = this.outputCalendar;
      base.numberingSystem = this.loc.numberingSystem;
      base.locale = this.loc.locale;
    }
    return base;
  }
  /**
   * Returns a JavaScript Date equivalent to this DateTime.
   * @return {Date}
   */
  toJSDate() {
    return new Date(this.isValid ? this.ts : NaN);
  }
  // COMPARE
  /**
   * Return the difference between two DateTimes as a Duration.
   * @param {DateTime} otherDateTime - the DateTime to compare this one to
   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.
   * @param {Object} opts - options that affect the creation of the Duration
   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use
   * @example
   * var i1 = DateTime.fromISO('1982-05-25T09:45'),
   *     i2 = DateTime.fromISO('1983-10-14T10:30');
   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }
   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }
   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }
   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }
   * @return {Duration}
   */
  diff(otherDateTime, unit = "milliseconds", opts = {}) {
    if (!this.isValid || !otherDateTime.isValid) {
      return Duration.invalid("created by diffing an invalid DateTime");
    }
    const durOpts = __spreadValues({ locale: this.locale, numberingSystem: this.numberingSystem }, opts);
    const units = maybeArray(unit).map(Duration.normalizeUnit), otherIsLater = otherDateTime.valueOf() > this.valueOf(), earlier = otherIsLater ? this : otherDateTime, later = otherIsLater ? otherDateTime : this, diffed = diff_default(earlier, later, units, durOpts);
    return otherIsLater ? diffed.negate() : diffed;
  }
  /**
   * Return the difference between this DateTime and right now.
   * See {@link DateTime#diff}
   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration
   * @param {Object} opts - options that affect the creation of the Duration
   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use
   * @return {Duration}
   */
  diffNow(unit = "milliseconds", opts = {}) {
    return this.diff(_DateTime.now(), unit, opts);
  }
  /**
   * Return an Interval spanning between this DateTime and another DateTime
   * @param {DateTime} otherDateTime - the other end point of the Interval
   * @return {Interval|DateTime}
   */
  until(otherDateTime) {
    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;
  }
  /**
   * Return whether this DateTime is in the same unit of time as another DateTime.
   * Higher-order units must also be identical for this function to return `true`.
   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.
   * @param {DateTime} otherDateTime - the other DateTime
   * @param {string} unit - the unit of time to check sameness on
   * @param {Object} opts - options
   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used
   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day
   * @return {boolean}
   */
  hasSame(otherDateTime, unit, opts) {
    if (!this.isValid) return false;
    const inputMs = otherDateTime.valueOf();
    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });
    return adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts);
  }
  /**
   * Equality check
   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.
   * To compare just the millisecond values, use `+dt1 === +dt2`.
   * @param {DateTime} other - the other DateTime
   * @return {boolean}
   */
  equals(other) {
    return this.isValid && other.isValid && this.valueOf() === other.valueOf() && this.zone.equals(other.zone) && this.loc.equals(other.loc);
  }
  /**
   * Returns a string representation of a this time relative to now, such as "in two days". Can only internationalize if your
   * platform supports Intl.RelativeTimeFormat. Rounds towards zero by default.
   * @param {Object} options - options that affect the output
   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.
   * @param {string} [options.style="long"] - the style of units, must be "long", "short", or "narrow"
   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of "years", "quarters", "months", "weeks", "days", "hours", "minutes", or "seconds"
   * @param {boolean} [options.round=true] - whether to round the numbers in the output.
   * @param {string} [options.rounding="trunc"] - rounding method to use when rounding the numbers in the output. Can be "trunc" (toward zero), "expand" (away from zero), "round", "floor", or "ceil".
   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.
   * @param {string} options.locale - override the locale of this DateTime
   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this
   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> "in 1 day"
   * @example DateTime.now().setLocale("es").toRelative({ days: 1 }) //=> "dentro de 1 día"
   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: "fr" }) //=> "dans 23 heures"
   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> "2 days ago"
   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: "hours" }) //=> "48 hours ago"
   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> "1.5 days ago"
   */
  toRelative(options = {}) {
    if (!this.isValid) return null;
    const base = options.base || _DateTime.fromObject({}, { zone: this.zone }), padding = options.padding ? this < base ? -options.padding : options.padding : 0;
    let units = ["years", "months", "days", "hours", "minutes", "seconds"];
    let unit = options.unit;
    if (Array.isArray(options.unit)) {
      units = options.unit;
      unit = void 0;
    }
    return diffRelative(base, this.plus(padding), __spreadProps(__spreadValues({}, options), {
      numeric: "always",
      units,
      unit
    }));
  }
  /**
   * Returns a string representation of this date relative to today, such as "yesterday" or "next month".
   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.
   * @param {Object} options - options that affect the output
   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.
   * @param {string} options.locale - override the locale of this DateTime
   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of "years", "quarters", "months", "weeks", or "days"
   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this
   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> "tomorrow"
   * @example DateTime.now().setLocale("es").plus({ days: 1 }).toRelative() //=> ""mañana"
   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: "fr" }) //=> "demain"
   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> "2 days ago"
   */
  toRelativeCalendar(options = {}) {
    if (!this.isValid) return null;
    return diffRelative(options.base || _DateTime.fromObject({}, { zone: this.zone }), this, __spreadProps(__spreadValues({}, options), {
      numeric: "auto",
      units: ["years", "months", "days"],
      calendary: true
    }));
  }
  /**
   * Return the min of several date times
   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum
   * @return {DateTime} the min DateTime, or undefined if called with no argument
   */
  static min(...dateTimes) {
    if (!dateTimes.every(_DateTime.isDateTime)) {
      throw new InvalidArgumentError("min requires all arguments be DateTimes");
    }
    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);
  }
  /**
   * Return the max of several date times
   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum
   * @return {DateTime} the max DateTime, or undefined if called with no argument
   */
  static max(...dateTimes) {
    if (!dateTimes.every(_DateTime.isDateTime)) {
      throw new InvalidArgumentError("max requires all arguments be DateTimes");
    }
    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);
  }
  // MISC
  /**
   * Explain how a string would be parsed by fromFormat()
   * @param {string} text - the string to parse
   * @param {string} fmt - the format the string is expected to be in (see description)
   * @param {Object} options - options taken by fromFormat()
   * @return {Object}
   */
  static fromFormatExplain(text, fmt, options = {}) {
    const { locale = null, numberingSystem = null } = options, localeToUse = Locale.fromOpts({
      locale,
      numberingSystem,
      defaultToEN: true
    });
    return explainFromTokens(localeToUse, text, fmt);
  }
  /**
   * @deprecated use fromFormatExplain instead
   */
  static fromStringExplain(text, fmt, options = {}) {
    return _DateTime.fromFormatExplain(text, fmt, options);
  }
  /**
   * Build a parser for `fmt` using the given locale. This parser can be passed
   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This
   * can be used to optimize cases where many dates need to be parsed in a
   * specific format.
   *
   * @param {String} fmt - the format the string is expected to be in (see
   * description)
   * @param {Object} options - options used to set locale and numberingSystem
   * for parser
   * @returns {TokenParser} - opaque object to be used
   */
  static buildFormatParser(fmt, options = {}) {
    const { locale = null, numberingSystem = null } = options, localeToUse = Locale.fromOpts({
      locale,
      numberingSystem,
      defaultToEN: true
    });
    return new TokenParser(localeToUse, fmt);
  }
  /**
   * Create a DateTime from an input string and format parser.
   *
   * The format parser must have been created with the same locale as this call.
   *
   * @param {String} text - the string to parse
   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}
   * @param {Object} opts - options taken by fromFormat()
   * @returns {DateTime}
   */
  static fromFormatParser(text, formatParser, opts = {}) {
    if (isUndefined(text) || isUndefined(formatParser)) {
      throw new InvalidArgumentError(
        "fromFormatParser requires an input string and a format parser"
      );
    }
    const { locale = null, numberingSystem = null } = opts, localeToUse = Locale.fromOpts({
      locale,
      numberingSystem,
      defaultToEN: true
    });
    if (!localeToUse.equals(formatParser.locale)) {
      throw new InvalidArgumentError(
        `fromFormatParser called with a locale of ${localeToUse}, but the format parser was created for ${formatParser.locale}`
      );
    }
    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);
    if (invalidReason) {
      return _DateTime.invalid(invalidReason);
    } else {
      return parseDataToDateTime(
        result,
        zone,
        opts,
        `format ${formatParser.format}`,
        text,
        specificOffset
      );
    }
  }
  // FORMAT PRESETS
  /**
   * {@link DateTime#toLocaleString} format like 10/14/1983
   * @type {Object}
   */
  static get DATE_SHORT() {
    return DATE_SHORT;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'
   * @type {Object}
   */
  static get DATE_MED() {
    return DATE_MED;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'
   * @type {Object}
   */
  static get DATE_MED_WITH_WEEKDAY() {
    return DATE_MED_WITH_WEEKDAY;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'October 14, 1983'
   * @type {Object}
   */
  static get DATE_FULL() {
    return DATE_FULL;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'
   * @type {Object}
   */
  static get DATE_HUGE() {
    return DATE_HUGE;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get TIME_SIMPLE() {
    return TIME_SIMPLE;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get TIME_WITH_SECONDS() {
    return TIME_WITH_SECONDS;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get TIME_WITH_SHORT_OFFSET() {
    return TIME_WITH_SHORT_OFFSET;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get TIME_WITH_LONG_OFFSET() {
    return TIME_WITH_LONG_OFFSET;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.
   * @type {Object}
   */
  static get TIME_24_SIMPLE() {
    return TIME_24_SIMPLE;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.
   * @type {Object}
   */
  static get TIME_24_WITH_SECONDS() {
    return TIME_24_WITH_SECONDS;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.
   * @type {Object}
   */
  static get TIME_24_WITH_SHORT_OFFSET() {
    return TIME_24_WITH_SHORT_OFFSET;
  }
  /**
   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.
   * @type {Object}
   */
  static get TIME_24_WITH_LONG_OFFSET() {
    return TIME_24_WITH_LONG_OFFSET;
  }
  /**
   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_SHORT() {
    return DATETIME_SHORT;
  }
  /**
   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_SHORT_WITH_SECONDS() {
    return DATETIME_SHORT_WITH_SECONDS;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_MED() {
    return DATETIME_MED;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_MED_WITH_SECONDS() {
    return DATETIME_MED_WITH_SECONDS;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_MED_WITH_WEEKDAY() {
    return DATETIME_MED_WITH_WEEKDAY;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_FULL() {
    return DATETIME_FULL;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_FULL_WITH_SECONDS() {
    return DATETIME_FULL_WITH_SECONDS;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_HUGE() {
    return DATETIME_HUGE;
  }
  /**
   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.
   * @type {Object}
   */
  static get DATETIME_HUGE_WITH_SECONDS() {
    return DATETIME_HUGE_WITH_SECONDS;
  }
};
function friendlyDateTime(dateTimeish) {
  if (DateTime.isDateTime(dateTimeish)) {
    return dateTimeish;
  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {
    return DateTime.fromJSDate(dateTimeish);
  } else if (dateTimeish && typeof dateTimeish === "object") {
    return DateTime.fromObject(dateTimeish);
  } else {
    throw new InvalidArgumentError(
      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`
    );
  }
}

// node_modules/@abp/ng.core/fesm2022/abp-ng.core.mjs
function DynamicLayoutComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0, 0);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngComponentOutlet", ctx_r0.layout);
  }
}
function ReplaceableRouteContainerComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
var _AbstractNgModelComponent = class _AbstractNgModelComponent {
  constructor() {
    this.cdRef = inject(ChangeDetectorRef);
    this.valueFn = (value) => value;
    this.valueLimitFn = (value) => false;
  }
  set value(value) {
    value = this.valueFn(value, this._value);
    if (this.valueLimitFn(value, this._value) !== false || this.readonly) return;
    this._value = value;
    this.notifyValueChange();
  }
  get value() {
    return this._value || this.defaultValue;
  }
  get defaultValue() {
    return this._value;
  }
  notifyValueChange() {
    if (this.onChange) {
      this.onChange(this.value);
    }
  }
  writeValue(value) {
    this._value = this.valueLimitFn(value, this._value) || value;
    this.cdRef.markForCheck();
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled) {
    this.disabled = isDisabled;
  }
};
_AbstractNgModelComponent.ɵfac = function AbstractNgModelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbstractNgModelComponent)();
};
_AbstractNgModelComponent.ɵcmp = ɵɵdefineComponent({
  type: _AbstractNgModelComponent,
  selectors: [["ng-component"]],
  inputs: {
    disabled: "disabled",
    readonly: "readonly",
    valueFn: "valueFn",
    valueLimitFn: "valueLimitFn",
    value: "value"
  },
  standalone: false,
  decls: 0,
  vars: 0,
  template: function AbstractNgModelComponent_Template(rf, ctx) {
  },
  encapsulation: 2
});
var AbstractNgModelComponent = _AbstractNgModelComponent;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbstractNgModelComponent, [{
    type: Component,
    args: [{
      standalone: false,
      template: ""
    }]
  }], null, {
    disabled: [{
      type: Input
    }],
    readonly: [{
      type: Input
    }],
    valueFn: [{
      type: Input
    }],
    valueLimitFn: [{
      type: Input
    }],
    value: [{
      type: Input
    }]
  });
})();
var _AuthGuard = class _AuthGuard {
  canActivate() {
    console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
    return false;
  }
};
_AuthGuard.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AuthGuard)();
};
_AuthGuard.ɵprov = ɵɵdefineInjectable({
  token: _AuthGuard,
  factory: _AuthGuard.ɵfac,
  providedIn: "root"
});
var AuthGuard = _AuthGuard;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var authGuard = () => {
  console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
  return false;
};
var _AuthService = class _AuthService {
  warningMessage() {
    console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
  }
  get oidc() {
    this.warningMessage();
    return false;
  }
  set oidc(value) {
    this.warningMessage();
  }
  init() {
    this.warningMessage();
    return Promise.resolve(void 0);
  }
  login(params) {
    this.warningMessage();
    return of(void 0);
  }
  logout(queryParams) {
    this.warningMessage();
    return of(void 0);
  }
  navigateToLogin(queryParams) {
  }
  get isInternalAuth() {
    throw new Error("not implemented");
  }
  get isAuthenticated() {
    this.warningMessage();
    return false;
  }
  loginUsingGrant(grantType, parameters, headers) {
    console.log({
      grantType,
      parameters,
      headers
    });
    return Promise.reject(new Error("not implemented"));
  }
  getAccessTokenExpiration() {
    this.warningMessage();
    return 0;
  }
  getRefreshToken() {
    this.warningMessage();
    return "";
  }
  getAccessToken() {
    this.warningMessage();
    return "";
  }
  refreshToken() {
    this.warningMessage();
    return Promise.resolve(void 0);
  }
};
_AuthService.ɵfac = function AuthService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AuthService)();
};
_AuthService.ɵprov = ɵɵdefineInjectable({
  token: _AuthService,
  factory: _AuthService.ɵfac,
  providedIn: "root"
});
var AuthService = _AuthService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var AbstractAuthErrorFilter = class {
};
var AuthErrorFilterService = class extends AbstractAuthErrorFilter {
  warningMessage() {
    console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
  }
  get(id) {
    this.warningMessage();
    throw new Error("not implemented");
  }
  add(filter2) {
    this.warningMessage();
  }
  patch(item) {
    this.warningMessage();
  }
  remove(id) {
    this.warningMessage();
  }
  run(event) {
    this.warningMessage();
    throw new Error("not implemented");
  }
};
var LOCALIZATIONS = new InjectionToken("LOCALIZATIONS");
function localizationContributor(localizations) {
  if (localizations) {
    localizations$.next([...localizations$.value, ...localizations]);
  }
}
var localizations$ = new BehaviorSubject([]);
var CORE_OPTIONS = new InjectionToken("CORE_OPTIONS");
function coreOptionsFactory(_a) {
  var options = __objRest(_a, []);
  return __spreadValues({}, options);
}
function getLocaleDirection(locale) {
  return /^(ar(-[A-Z]{2})?|ckb(-IR)?|fa(-AF)?|he|ks|lrc(-IQ)?|mzn|pa-Arab|ps(-PK)?|sd|ug|ur(-IN)?|uz-Arab|yi)$/.test(locale) ? "rtl" : "ltr";
}
function createLocalizer(localization) {
  return (resourceName, key, defaultValue) => {
    if (resourceName === "_") return key;
    const resource = localization?.values?.[resourceName];
    if (!resource) return defaultValue;
    return resource[key] || defaultValue;
  };
}
function createLocalizerWithFallback(localization) {
  const findLocalization = createLocalizationFinder(localization);
  return (resourceNames, keys, defaultValue) => {
    const {
      localized
    } = findLocalization(resourceNames, keys);
    return localized || defaultValue;
  };
}
function createLocalizationPipeKeyGenerator(localization) {
  const findLocalization = createLocalizationFinder(localization);
  return (resourceNames, keys, defaultKey) => {
    const {
      resourceName,
      key
    } = findLocalization(resourceNames, keys);
    return !resourceName ? defaultKey : resourceName === "_" ? key : `${resourceName}::${key}`;
  };
}
function createLocalizationFinder(localization) {
  const localize = createLocalizer(localization);
  return (resourceNames, keys) => {
    resourceNames = resourceNames.concat(localization.defaultResourceName || "").filter(Boolean);
    const resourceCount = resourceNames.length;
    const keyCount = keys.length;
    for (let i = 0; i < resourceCount; i++) {
      const resourceName = resourceNames[i];
      for (let j = 0; j < keyCount; j++) {
        const key = keys[j];
        const localized = localize(resourceName, key, null);
        if (localized) return {
          resourceName,
          key,
          localized
        };
      }
    }
    return {
      resourceName: void 0,
      key: void 0,
      localized: void 0
    };
  };
}
function createTokenParser(format) {
  return (str) => {
    const tokens = [];
    const regex = format.replace(/\./g, "\\.").replace(/\{\s?([0-9a-zA-Z]+)\s?\}/g, (_, token) => {
      tokens.push(token);
      return "(.+)";
    });
    const matches = (str.match(regex) || []).slice(1);
    return matches.reduce((acc, v, i) => {
      const key = tokens[i];
      acc[key] = [...acc[key] || [], v].filter(Boolean);
      return acc;
    }, {});
  };
}
function interpolate(text, params) {
  return text.replace(/(['"])?\{\s*(\d+)\s*\}\1/g, (_, quote, digit) => (quote ? quote : "") + (params[digit] ?? `{${digit}}`) + (quote ? quote : "")).replace(/\s+/g, " ");
}
function escapeHtmlChars(value) {
  return value && typeof value === "string" ? value.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;") : value;
}
var _ContentProjectionService = class _ContentProjectionService {
  constructor(injector) {
    this.injector = injector;
  }
  projectContent(projectionStrategy, injector = this.injector) {
    return projectionStrategy.injectContent(injector);
  }
};
_ContentProjectionService.ɵfac = function ContentProjectionService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ContentProjectionService)(ɵɵinject(Injector));
};
_ContentProjectionService.ɵprov = ɵɵdefineInjectable({
  token: _ContentProjectionService,
  factory: _ContentProjectionService.ɵfac,
  providedIn: "root"
});
var ContentProjectionService = _ContentProjectionService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ContentProjectionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: Injector
  }], null);
})();
function pushValueTo(array) {
  return (element) => {
    array.push(element);
    return array;
  };
}
function noop() {
  const fn = function() {
  };
  return fn;
}
function isUndefinedOrEmptyString(value) {
  return value === void 0 || value === "";
}
function isNullOrUndefined(obj) {
  return obj === null || obj === void 0;
}
function isNullOrEmpty(obj) {
  return obj === null || obj === void 0 || obj === "";
}
function exists(obj) {
  return !isNullOrUndefined(obj);
}
function isObject(obj) {
  return obj instanceof Object;
}
function isArray(obj) {
  return Array.isArray(obj);
}
function isObjectAndNotArray(obj) {
  return isObject(obj) && !isArray(obj);
}
function isNode(obj) {
  return obj instanceof Node;
}
function isObjectAndNotArrayNotNode(obj) {
  return isObjectAndNotArray(obj) && !isNode(obj);
}
function checkHasProp(object, key) {
  return Object.prototype.hasOwnProperty.call(object, key);
}
function getShortDateFormat(configStateService) {
  const dateTimeFormat = configStateService.getDeep("localization.currentCulture.dateTimeFormat");
  return dateTimeFormat.shortDatePattern;
}
function getShortTimeFormat(configStateService) {
  const dateTimeFormat = configStateService.getDeep("localization.currentCulture.dateTimeFormat");
  return dateTimeFormat?.shortTimePattern?.replace("tt", "a");
}
function getShortDateShortTimeFormat(configStateService) {
  const dateTimeFormat = configStateService.getDeep("localization.currentCulture.dateTimeFormat");
  return `${dateTimeFormat.shortDatePattern} ${dateTimeFormat?.shortTimePattern?.replace("tt", "a")}`;
}
function deepMerge(target, source) {
  if (isObjectAndNotArrayNotNode(target) && isObjectAndNotArrayNotNode(source)) {
    return deepMergeRecursively(target, source);
  } else if (isNullOrUndefined(target) && isNullOrUndefined(source)) {
    return {};
  } else {
    return exists(source) ? source : target;
  }
}
function deepMergeRecursively(target, source) {
  const shouldNotRecurse = isNullOrUndefined(target) || isNullOrUndefined(source) || // at least one not defined
  isArray(target) || isArray(source) || // at least one array
  !isObject(target) || !isObject(source) || // at least one not an object
  isNode(target) || isNode(source);
  if (shouldNotRecurse) {
    return exists(source) ? source : target;
  }
  const keysOfTarget = Object.keys(target);
  const keysOfSource = Object.keys(source);
  const uniqueKeys = new Set(keysOfTarget.concat(keysOfSource));
  return [...uniqueKeys].reduce((retVal, key) => {
    retVal[key] = deepMergeRecursively(target[key], source[key]);
    return retVal;
  }, {});
}
var InternalStore = class {
  get state() {
    return this.state$.value;
  }
  constructor(initialState) {
    this.initialState = initialState;
    this.state$ = new BehaviorSubject(this.initialState);
    this.update$ = new Subject();
    this.sliceState = (selector, compareFn = collectionCompare) => this.state$.pipe(map(selector), distinctUntilChanged(compareFn));
    this.sliceUpdate = (selector, filterFn = (x) => x !== void 0) => this.update$.pipe(map(selector), filter(filterFn));
  }
  patch(state) {
    let patchedState = state;
    if (typeof state === "object" && !Array.isArray(state)) {
      patchedState = __spreadValues(__spreadValues({}, this.state), state);
    }
    this.state$.next(patchedState);
    this.update$.next(patchedState);
  }
  deepPatch(state) {
    this.state$.next(deepMerge(this.state, state));
    this.update$.next(state);
  }
  set(state) {
    this.state$.next(state);
    this.update$.next(state);
  }
  reset() {
    this.set(this.initialState);
  }
};
var mapToApiUrl = (key) => (apis) => (key && apis[key] || apis.default).url || apis.default.url;
var mapToIssuer = (issuer) => {
  if (!issuer) {
    return issuer;
  }
  return issuer.endsWith("/") ? issuer : issuer + "/";
};
var _EnvironmentService = class _EnvironmentService {
  constructor() {
    this.store = new InternalStore({});
  }
  get createOnUpdateStream() {
    return this.store.sliceUpdate;
  }
  getEnvironment$() {
    return this.store.sliceState((state) => state);
  }
  getEnvironment() {
    return this.store.state;
  }
  getApiUrl(key) {
    return mapToApiUrl(key)(this.store.state?.apis);
  }
  getApiUrl$(key) {
    return this.store.sliceState((state) => state.apis).pipe(map(mapToApiUrl(key)));
  }
  setState(environment) {
    this.store.set(environment);
  }
  getIssuer() {
    const issuer = this.store.state?.oAuthConfig?.issuer;
    return mapToIssuer(issuer);
  }
  getIssuer$() {
    return this.store.sliceState((state) => state?.oAuthConfig?.issuer).pipe(map(mapToIssuer));
  }
  getImpersonation() {
    return this.store.state?.oAuthConfig?.impersonation || {};
  }
  getImpersonation$() {
    return this.store.sliceState((state) => state?.oAuthConfig?.impersonation || {});
  }
};
_EnvironmentService.ɵfac = function EnvironmentService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _EnvironmentService)();
};
_EnvironmentService.ɵprov = ɵɵdefineInjectable({
  token: _EnvironmentService,
  factory: _EnvironmentService.ɵfac,
  providedIn: "root"
});
var EnvironmentService = _EnvironmentService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnvironmentService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _HttpErrorReporterService = class _HttpErrorReporterService {
  constructor() {
    this._reporter$ = new Subject();
    this._errors$ = new BehaviorSubject([]);
  }
  get reporter$() {
    return this._reporter$.asObservable();
  }
  get errors$() {
    return this._errors$.asObservable();
  }
  get errors() {
    return this._errors$.value;
  }
  reportError(error) {
    this._reporter$.next(error);
    this._errors$.next([...this.errors, error]);
  }
};
_HttpErrorReporterService.ɵfac = function HttpErrorReporterService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _HttpErrorReporterService)();
};
_HttpErrorReporterService.ɵprov = ɵɵdefineInjectable({
  token: _HttpErrorReporterService,
  factory: _HttpErrorReporterService.ɵfac,
  providedIn: "root"
});
var HttpErrorReporterService = _HttpErrorReporterService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HttpErrorReporterService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
function getRemoteEnv(injector, environment) {
  const environmentService = injector.get(EnvironmentService);
  const {
    remoteEnv
  } = environment;
  const {
    headers = {},
    method = "GET",
    url
  } = remoteEnv || {};
  if (!url) return Promise.resolve();
  const http = injector.get(HttpClient);
  const httpErrorReporter = injector.get(HttpErrorReporterService);
  return http.request(method, url, {
    headers
  }).pipe(
    catchError((err) => {
      httpErrorReporter.reportError(err);
      return of(null);
    }),
    // TODO: Consider get handle function from a provider
    tap((env) => environmentService.setState(mergeEnvironments(environment, env || {}, remoteEnv)))
  ).toPromise();
}
function mergeEnvironments(local, remote, config) {
  switch (config.mergeStrategy) {
    case "deepmerge":
      return deepMerge(local, remote);
    case "overwrite":
    case null:
    case void 0:
      return remote;
    default:
      return config.mergeStrategy(local, remote);
  }
}
var LazyModuleFactory = class extends NgModuleFactory$1 {
  get moduleType() {
    return this.moduleWithProviders.ngModule;
  }
  constructor(moduleWithProviders) {
    super();
    this.moduleWithProviders = moduleWithProviders;
  }
  create(parentInjector) {
    const injector = Injector.create(__spreadProps(__spreadValues({}, parentInjector && {
      parent: parentInjector
    }), {
      providers: this.moduleWithProviders.providers
    }));
    const compiler = injector.get(Compiler);
    const factory = compiler.compileModuleSync(this.moduleType);
    return factory.create(injector);
  }
};
function featuresFactory(configState, featureKeys, mapFn = (features) => features) {
  return configState.getFeatures$(featureKeys).pipe(filter(Boolean), map(mapFn));
}
function downloadBlob(blob, filename) {
  const blobUrl = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = blobUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.dispatchEvent(new MouseEvent("click", {
    bubbles: true,
    cancelable: true,
    view: window
  }));
  document.body.removeChild(link);
}
function isNumber2(value) {
  return value == Number(value);
}
function mapEnumToOptions(_enum) {
  const options = [];
  for (const member in _enum) if (!isNumber2(member)) options.push({
    key: member,
    value: _enum[member]
  });
  return options;
}
function uuid(a) {
  return a ? (a ^ Math.random() * 16 >> a / 4).toString(16) : ("10000000-1000-4000-8000" + -1e11).replace(/[018]/g, uuid);
}
function generateHash(value) {
  let hashed = 0;
  let charCode;
  for (let i = 0; i < value.length; i++) {
    charCode = value.charCodeAt(i);
    hashed = (hashed << 5) - hashed + charCode;
    hashed |= 0;
  }
  return hashed;
}
function generatePassword(injector, length = 8) {
  if (injector) {
    length = getRequiredPasswordLength(injector);
  }
  length = Math.min(Math.max(4, length), 128);
  const lowers = "abcdefghjkmnpqrstuvwxyz";
  const uppers = "ABCDEFGHJKMNPQRSTUVWXYZ";
  const numbers = "23456789";
  const specials = "!*_#/+-.";
  const all = lowers + uppers + numbers + specials;
  const getRandom = (chrSet) => chrSet[Math.floor(Math.random() * chrSet.length)];
  const password = Array({
    length
  });
  password[0] = getRandom(lowers);
  password[1] = getRandom(uppers);
  password[2] = getRandom(numbers);
  password[3] = getRandom(specials);
  for (let i = 4; i < length; i++) {
    password[i] = getRandom(all);
  }
  return password.sort(() => 0.5 - Math.random()).join("");
}
function getRequiredPasswordLength(injector) {
  const configState = injector.get(ConfigStateService);
  const passwordRules = configState.getSettings("Identity.Password");
  return Number(passwordRules["Abp.Identity.Password.RequiredLength"]) || 8;
}
function getPathName(url) {
  const {
    pathname
  } = new URL(url, window.location.origin);
  return pathname;
}
var WebHttpUrlEncodingCodec = class {
  encodeKey(k) {
    return encodeURIComponent(k);
  }
  encodeValue(v) {
    return encodeURIComponent(v);
  }
  decodeKey(k) {
    return decodeURIComponent(k);
  }
  decodeValue(v) {
    return decodeURIComponent(v);
  }
};
var _AbpLocalStorageService = class _AbpLocalStorageService {
  constructor() {
  }
  get length() {
    return localStorage.length;
  }
  clear() {
    localStorage.clear();
  }
  getItem(key) {
    return localStorage.getItem(key);
  }
  key(index2) {
    return localStorage.key(index2);
  }
  removeItem(key) {
    localStorage.removeItem(key);
  }
  setItem(key, value) {
    localStorage.setItem(key, value);
  }
};
_AbpLocalStorageService.ɵfac = function AbpLocalStorageService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpLocalStorageService)();
};
_AbpLocalStorageService.ɵprov = ɵɵdefineInjectable({
  token: _AbpLocalStorageService,
  factory: _AbpLocalStorageService.ɵfac,
  providedIn: "root"
});
var AbpLocalStorageService = _AbpLocalStorageService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpLocalStorageService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var _SessionStateService = class _SessionStateService {
  constructor(configState, localStorageService) {
    this.configState = configState;
    this.localStorageService = localStorageService;
    this.store = new InternalStore({});
    this.document = inject(DOCUMENT);
    this.updateLocalStorage = () => {
      this.localStorageService.setItem("abpSession", JSON.stringify(this.store.state));
    };
    this.init();
    this.setInitialLanguage();
  }
  init() {
    const session = this.localStorageService.getItem("abpSession");
    if (session) {
      this.store.set(JSON.parse(session));
    }
    this.store.sliceUpdate((state) => state).subscribe(this.updateLocalStorage);
  }
  setInitialLanguage() {
    const appLanguage = this.getLanguage();
    this.configState.getDeep$("localization.currentCulture.cultureName").pipe(filter((cultureName) => !!cultureName), take(1)).subscribe((lang) => {
      if (lang.includes(";")) {
        lang = lang.split(";")[0];
      }
      this.setLanguage(lang);
    });
  }
  onLanguageChange$() {
    return this.store.sliceUpdate((state) => state.language);
  }
  onTenantChange$() {
    return this.store.sliceUpdate((state) => state.tenant);
  }
  getLanguage() {
    return this.store.state.language;
  }
  getLanguage$() {
    return this.store.sliceState((state) => state.language);
  }
  getTenant() {
    return this.store.state.tenant;
  }
  getTenant$() {
    return this.store.sliceState((state) => state.tenant);
  }
  setTenant(tenant) {
    if (collectionCompare(tenant, this.store.state.tenant)) return;
    this.store.set(__spreadProps(__spreadValues({}, this.store.state), {
      tenant
    }));
  }
  setLanguage(language) {
    const currentLanguage = this.store.state.language;
    if (language !== currentLanguage) {
      this.store.patch({
        language
      });
    }
    const currentAttribute = this.document.documentElement.getAttribute("lang");
    if (language !== currentAttribute) {
      this.document.documentElement.setAttribute("lang", language);
    }
  }
};
_SessionStateService.ɵfac = function SessionStateService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _SessionStateService)(ɵɵinject(ConfigStateService), ɵɵinject(AbpLocalStorageService));
};
_SessionStateService.ɵprov = ɵɵdefineInjectable({
  token: _SessionStateService,
  factory: _SessionStateService.ɵfac,
  providedIn: "root"
});
var SessionStateService = _SessionStateService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SessionStateService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: AbpLocalStorageService
  }], null);
})();
var APP_INIT_ERROR_HANDLERS = new InjectionToken("APP_INIT_ERROR_HANDLERS");
var _AbpTenantService = class _AbpTenantService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.findTenantById = (id, config) => this.restService.request({
      method: "GET",
      url: `/api/abp/multi-tenancy/tenants/by-id/${id}`
    }, __spreadValues({
      apiName: this.apiName
    }, config));
    this.findTenantByName = (name, config) => this.restService.request({
      method: "GET",
      url: `/api/abp/multi-tenancy/tenants/by-name/${name}`
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
};
_AbpTenantService.ɵfac = function AbpTenantService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpTenantService)(ɵɵinject(RestService));
};
_AbpTenantService.ɵprov = ɵɵdefineInjectable({
  token: _AbpTenantService,
  factory: _AbpTenantService.ɵfac,
  providedIn: "root"
});
var AbpTenantService = _AbpTenantService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpTenantService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var TENANT_KEY = new InjectionToken("TENANT_KEY");
var IS_EXTERNAL_REQUEST = new HttpContextToken(() => false);
var _ExternalHttpClient_instances, setPlaceholderContext_fn;
var _ExternalHttpClient = class _ExternalHttpClient extends HttpClient {
  constructor() {
    super(...arguments);
    __privateAdd(this, _ExternalHttpClient_instances);
  }
  request(first, url, options = {}) {
    if (typeof first === "string") {
      __privateMethod(this, _ExternalHttpClient_instances, setPlaceholderContext_fn).call(this, options);
      return super.request(first, url || "", options);
    }
    __privateMethod(this, _ExternalHttpClient_instances, setPlaceholderContext_fn).call(this, first);
    return super.request(first);
  }
};
_ExternalHttpClient_instances = new WeakSet();
setPlaceholderContext_fn = function(optionsOrRequest) {
  optionsOrRequest.context ??= new HttpContext();
  optionsOrRequest.context.set(IS_EXTERNAL_REQUEST, true);
};
_ExternalHttpClient.ɵfac = /* @__PURE__ */ (() => {
  let ɵExternalHttpClient_BaseFactory;
  return function ExternalHttpClient_Factory(__ngFactoryType__) {
    return (ɵExternalHttpClient_BaseFactory || (ɵExternalHttpClient_BaseFactory = ɵɵgetInheritedFactory(_ExternalHttpClient)))(__ngFactoryType__ || _ExternalHttpClient);
  };
})();
_ExternalHttpClient.ɵprov = ɵɵdefineInjectable({
  token: _ExternalHttpClient,
  factory: _ExternalHttpClient.ɵfac,
  providedIn: "root"
});
var ExternalHttpClient = _ExternalHttpClient;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExternalHttpClient, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _RestService = class _RestService {
  constructor(options, http, externalHttp, environment, httpErrorReporter) {
    this.options = options;
    this.http = http;
    this.externalHttp = externalHttp;
    this.environment = environment;
    this.httpErrorReporter = httpErrorReporter;
  }
  getApiFromStore(apiName) {
    return this.environment.getApiUrl(apiName);
  }
  handleError(err) {
    this.httpErrorReporter.reportError(err);
    return throwError(() => err);
  }
  request(request, config, api) {
    config = config || {};
    api = api || this.getApiFromStore(config.apiName);
    const _a = request, {
      method,
      params
    } = _a, options = __objRest(_a, [
      "method",
      "params"
    ]);
    const {
      observe = "body",
      skipHandleError
    } = config;
    const url = this.removeDuplicateSlashes(api + request.url);
    const httpClient = this.getHttpClient(config.skipAddingHeader);
    return httpClient.request(method, url, __spreadValues(__spreadValues({
      observe
    }, params && {
      params: this.getParams(params, config.httpParamEncoder)
    }), options)).pipe(catchError((err) => skipHandleError ? throwError(() => err) : this.handleError(err)));
  }
  getHttpClient(isExternal) {
    return isExternal ? this.externalHttp : this.http;
  }
  getParams(params, encoder) {
    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      if (isUndefinedOrEmptyString(value)) return acc;
      if (value === null && !this.options.sendNullsAsQueryParam) return acc;
      acc[key] = value;
      return acc;
    }, {});
    return encoder ? new HttpParams({
      encoder,
      fromObject: filteredParams
    }) : new HttpParams({
      fromObject: filteredParams
    });
  }
  removeDuplicateSlashes(url) {
    return url.replace(/([^:]\/)\/+/g, "$1");
  }
};
_RestService.ɵfac = function RestService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _RestService)(ɵɵinject(CORE_OPTIONS), ɵɵinject(HttpClient), ɵɵinject(ExternalHttpClient), ɵɵinject(EnvironmentService), ɵɵinject(HttpErrorReporterService));
};
_RestService.ɵprov = ɵɵdefineInjectable({
  token: _RestService,
  factory: _RestService.ɵfac,
  providedIn: "root"
});
var RestService = _RestService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RestService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: void 0,
    decorators: [{
      type: Inject,
      args: [CORE_OPTIONS]
    }]
  }, {
    type: HttpClient
  }, {
    type: ExternalHttpClient
  }, {
    type: EnvironmentService
  }, {
    type: HttpErrorReporterService
  }], null);
})();
var _MultiTenancyService = class _MultiTenancyService {
  constructor(restService, sessionState, tenantService, configStateService, tenantKey) {
    this.restService = restService;
    this.sessionState = sessionState;
    this.tenantService = tenantService;
    this.configStateService = configStateService;
    this.tenantKey = tenantKey;
    this.domainTenant = null;
    this.isTenantBoxVisible = true;
    this.apiName = "abp";
    this.setTenantToState = (tenant) => {
      this.sessionState.setTenant({
        id: tenant.tenantId,
        name: tenant.name,
        isAvailable: true
      });
      return this.configStateService.refreshAppState().pipe(map((_) => tenant));
    };
  }
  setTenantByName(tenantName) {
    return this.tenantService.findTenantByName(tenantName).pipe(switchMap(this.setTenantToState));
  }
  setTenantById(tenantId) {
    return this.tenantService.findTenantById(tenantId).pipe(switchMap(this.setTenantToState));
  }
};
_MultiTenancyService.ɵfac = function MultiTenancyService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _MultiTenancyService)(ɵɵinject(RestService), ɵɵinject(SessionStateService), ɵɵinject(AbpTenantService), ɵɵinject(ConfigStateService), ɵɵinject(TENANT_KEY));
};
_MultiTenancyService.ɵprov = ɵɵdefineInjectable({
  token: _MultiTenancyService,
  factory: _MultiTenancyService.ɵfac,
  providedIn: "root"
});
var MultiTenancyService = _MultiTenancyService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MultiTenancyService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }, {
    type: SessionStateService
  }, {
    type: AbpTenantService
  }, {
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TENANT_KEY]
    }]
  }], null);
})();
var TENANT_NOT_FOUND_BY_NAME = new InjectionToken("TENANT_NOT_FOUND_BY_NAME");
var tenancyPlaceholder = "{0}";
function getCurrentTenancyName(appBaseUrl) {
  if (appBaseUrl.charAt(appBaseUrl.length - 1) !== "/") appBaseUrl += "/";
  const parseTokens = createTokenParser(appBaseUrl);
  const token = tenancyPlaceholder.replace(/[}{]/g, "");
  return parseTokens(window.location.href)[token]?.[0];
}
function getCurrentTenancyNameFromUrl(tenantKey) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(tenantKey);
}
function parseTenantFromUrl(injector) {
  return __async(this, null, function* () {
    const environmentService = injector.get(EnvironmentService);
    const multiTenancyService = injector.get(MultiTenancyService);
    const tenantNotFoundHandler = injector.get(TENANT_NOT_FOUND_BY_NAME, null);
    const baseUrl = environmentService.getEnvironment()?.application?.baseUrl || "";
    const tenancyName = getCurrentTenancyName(baseUrl);
    const hideTenantBox = () => {
      multiTenancyService.isTenantBoxVisible = false;
    };
    const setDomainTenant = (tenant) => {
      multiTenancyService.domainTenant = {
        id: tenant.tenantId,
        name: tenant.name,
        isAvailable: true
      };
    };
    const setEnvironmentWithDomainTenant = (tenant) => {
      hideTenantBox();
      setDomainTenant(tenant);
    };
    if (tenancyName) {
      replaceTenantNameWithinEnvironment(injector, tenancyName);
      const tenant$ = multiTenancyService.setTenantByName(tenancyName);
      try {
        const result = yield firstValueFrom(tenant$);
        setEnvironmentWithDomainTenant(result);
        return Promise.resolve(result);
      } catch (httpError) {
        if (httpError instanceof HttpErrorResponse && httpError.status === 404 && tenantNotFoundHandler) {
          tenantNotFoundHandler(httpError);
        }
        return Promise.reject();
      }
    }
    replaceTenantNameWithinEnvironment(injector, "", tenancyPlaceholder + ".");
    const tenantIdFromQueryParams = getCurrentTenancyNameFromUrl(multiTenancyService.tenantKey);
    if (tenantIdFromQueryParams) {
      const tenantById$ = multiTenancyService.setTenantById(tenantIdFromQueryParams);
      return firstValueFrom(tenantById$);
    }
    return Promise.resolve();
  });
}
function replaceTenantNameWithinEnvironment(injector, tenancyName, placeholder = tenancyPlaceholder) {
  const environmentService = injector.get(EnvironmentService);
  const environment = collectionClone(environmentService.getEnvironment());
  if (environment.application.baseUrl) {
    environment.application.baseUrl = environment.application.baseUrl.replace(placeholder, tenancyName);
  }
  if (environment.oAuthConfig?.redirectUri) {
    environment.oAuthConfig.redirectUri = environment.oAuthConfig.redirectUri.replace(placeholder, tenancyName);
  }
  if (!environment.oAuthConfig) {
    environment.oAuthConfig = {};
  }
  environment.oAuthConfig.issuer = (environment.oAuthConfig.issuer || "").replace(placeholder, tenancyName);
  Object.keys(environment.apis).forEach((api) => {
    Object.keys(environment.apis[api]).forEach((key) => {
      environment.apis[api][key] = (environment.apis[api][key] || "").replace(placeholder, tenancyName);
    });
  });
  return environmentService.setState(environment);
}
var CHECK_AUTHENTICATION_STATE_FN_KEY = new InjectionToken("CHECK_AUTHENTICATION_STATE_FN_KEY");
function getInitialData() {
  return __async(this, null, function* () {
    const injector = inject(Injector);
    const environmentService = injector.get(EnvironmentService);
    const configState = injector.get(ConfigStateService);
    const options = injector.get(CORE_OPTIONS);
    environmentService.setState(options.environment);
    yield getRemoteEnv(injector, options.environment);
    yield parseTenantFromUrl(injector);
    const authService = injector.get(AuthService, void 0, {
      optional: true
    });
    const checkAuthenticationState = injector.get(CHECK_AUTHENTICATION_STATE_FN_KEY, noop, {
      optional: true
    });
    if (!options.skipInitAuthService && authService) {
      yield authService.init();
    }
    if (options.skipGetAppConfiguration) return;
    const result$ = configState.refreshAppState().pipe(tap(() => checkAuthenticationState(injector)), tap(() => {
      const currentTenant = configState.getOne("currentTenant");
      injector.get(SessionStateService).setTenant(currentTenant);
    }), catchError((error) => {
      const appInitErrorHandlers = injector.get(APP_INIT_ERROR_HANDLERS, null);
      if (appInitErrorHandlers && appInitErrorHandlers.length) {
        appInitErrorHandlers.forEach((func) => func(error));
      }
      return throwError(() => error);
    }));
    yield lastValueFrom(result$);
  });
}
function localeInitializer() {
  const injector = inject(Injector);
  const sessionState = injector.get(SessionStateService);
  const {
    registerLocaleFn
  } = injector.get(CORE_OPTIONS);
  const lang = sessionState.getLanguage() || "en";
  return new Promise((resolve, reject) => {
    registerLocaleFn(lang).then((module) => {
      if (module?.default) registerLocaleData(module.default);
      return resolve("resolved");
    }, reject);
  });
}
var CrossOriginStrategy = class {
  constructor(crossorigin, integrity) {
    this.crossorigin = crossorigin;
    this.integrity = integrity;
  }
  setCrossOrigin(element) {
    if (this.integrity) element.setAttribute("integrity", this.integrity);
    if (this.crossorigin) {
      element.setAttribute("crossorigin", this.crossorigin);
    }
  }
};
var NoCrossOriginStrategy = class extends CrossOriginStrategy {
  setCrossOrigin() {
  }
};
var CROSS_ORIGIN_STRATEGY = {
  Anonymous(integrity) {
    return new CrossOriginStrategy("anonymous", integrity);
  },
  UseCredentials(integrity) {
    return new CrossOriginStrategy("use-credentials", integrity);
  },
  None() {
    return new NoCrossOriginStrategy(null);
  }
};
var DomStrategy = class {
  constructor(target = document.head, position = "beforeend") {
    this.target = target;
    this.position = position;
  }
  insertElement(element) {
    this.target.insertAdjacentElement(this.position, element);
  }
};
var DOM_STRATEGY = {
  AfterElement(element) {
    return new DomStrategy(element, "afterend");
  },
  AppendToBody() {
    return new DomStrategy(document.body, "beforeend");
  },
  AppendToHead() {
    return new DomStrategy(document.head, "beforeend");
  },
  BeforeElement(element) {
    return new DomStrategy(element, "beforebegin");
  },
  PrependToHead() {
    return new DomStrategy(document.head, "afterbegin");
  }
};
function fromLazyLoad(element, domStrategy = DOM_STRATEGY.AppendToHead(), crossOriginStrategy = CROSS_ORIGIN_STRATEGY.Anonymous()) {
  crossOriginStrategy.setCrossOrigin(element);
  domStrategy.insertElement(element);
  return new Observable((observer) => {
    element.onload = (event) => {
      clearCallbacks(element);
      observer.next(event);
      observer.complete();
    };
    const handleError = createErrorHandler(observer, element);
    element.onerror = handleError;
    element.onabort = handleError;
    element.onemptied = handleError;
    element.onstalled = handleError;
    element.onsuspend = handleError;
    return () => {
      clearCallbacks(element);
      observer.complete();
    };
  });
}
function createErrorHandler(observer, element) {
  return function(event) {
    clearCallbacks(element);
    element.parentNode?.removeChild(element);
    observer.error(event);
  };
}
function clearCallbacks(element) {
  element.onload = null;
  element.onerror = null;
  element.onabort = null;
  element.onemptied = null;
  element.onstalled = null;
  element.onsuspend = null;
}
var DefaultQueueManager = class {
  constructor() {
    this.queue = [];
    this.isRunning = false;
    this.stack = 0;
    this.interval = 0;
    this.stackSize = 100;
  }
  init(interval, stackSize) {
    this.interval = interval;
    this.stackSize = stackSize;
  }
  add(fn) {
    this.queue.push(fn);
    this.run();
  }
  run() {
    if (this.isRunning) return;
    this.stack++;
    this.isRunning = true;
    const fn = this.queue.shift();
    if (!fn) {
      this.isRunning = false;
      return;
    }
    fn();
    if (this.stack > this.stackSize) {
      setTimeout(() => {
        this.isRunning = false;
        this.run();
        this.stack = 0;
      }, this.interval);
    } else {
      this.isRunning = false;
      this.run();
    }
  }
};
function findRoute(routesService, path) {
  const node = routesService.find((route) => route.path === path);
  return node || path === "/" ? node : findRoute(routesService, path.split("/").slice(0, -1).join("/") || "/");
}
function getRoutePath(router, url = router.url) {
  const emptyGroup = {
    segments: []
  };
  const primaryGroup = router.parseUrl(url).root.children[PRIMARY_OUTLET];
  return "/" + (primaryGroup || emptyGroup).segments.map(({
    path
  }) => path).join("/");
}
function reloadRoute(router, ngZone) {
  const {
    shouldReuseRoute
  } = router.routeReuseStrategy;
  const setRouteReuse = (reuse) => {
    router.routeReuseStrategy.shouldReuseRoute = reuse;
  };
  setRouteReuse(() => false);
  router.navigated = false;
  ngZone.run(() => __async(null, null, function* () {
    yield router.navigateByUrl(router.url).catch(noop);
    setRouteReuse(shouldReuseRoute);
  }));
}
var BaseTreeNode = class _BaseTreeNode {
  constructor(props) {
    this.children = [];
    this.isLeaf = true;
    Object.assign(this, props);
  }
  static create(props) {
    return new _BaseTreeNode(props);
  }
};
function createTreeFromList(list, keySelector, parentKeySelector, valueMapper) {
  const map2 = createMapFromList(list, keySelector, valueMapper);
  const tree = [];
  list.forEach((row) => {
    const id = keySelector(row);
    const parentId = parentKeySelector(row);
    const node = map2.get(id);
    if (!node) return;
    if (parentId) {
      const parent = map2.get(parentId);
      if (!parent) return;
      parent.children.push(node);
      parent.isLeaf = false;
      node.parent = parent;
    } else {
      tree.push(node);
    }
  });
  return tree;
}
function createMapFromList(list, keySelector, valueMapper) {
  const map2 = /* @__PURE__ */ new Map();
  list.forEach((row) => map2.set(keySelector(row), valueMapper(row)));
  return map2;
}
function createTreeNodeFilterCreator(key, mapperFn) {
  return (search) => {
    const regex = new RegExp(".*" + search + ".*", "i");
    return function collectNodes(nodes, matches = []) {
      for (const node of nodes) {
        if (regex.test(mapperFn(node[key]))) matches.push(node);
        if (node.children.length) collectNodes(node.children, matches);
      }
      return matches;
    };
  };
}
function createGroupMap(list, othersGroupKey) {
  if (!isArray(list) || !list.some((node) => Boolean(node.group))) return void 0;
  const mapGroup = /* @__PURE__ */ new Map();
  for (const node of list) {
    const group = node?.group || othersGroupKey;
    if (typeof group !== "string") {
      throw new Error(`Invalid group: ${group}`);
    }
    const items = mapGroup.get(group) || [];
    items.push(node);
    mapGroup.set(group, items);
  }
  return mapGroup;
}
var _DomInsertionService = class _DomInsertionService {
  constructor() {
    this.inserted = /* @__PURE__ */ new Set();
  }
  insertContent(contentStrategy) {
    const hash = generateHash(contentStrategy.content);
    if (this.inserted.has(hash)) return;
    const element = contentStrategy.insertElement();
    this.inserted.add(hash);
    return element;
  }
  removeContent(element) {
    if (element.textContent) {
      const hash = generateHash(element.textContent);
      this.inserted.delete(hash);
      element.parentNode?.removeChild(element);
    }
  }
  has(content) {
    const hash = generateHash(content);
    return this.inserted.has(hash);
  }
};
_DomInsertionService.ɵfac = function DomInsertionService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _DomInsertionService)();
};
_DomInsertionService.ɵprov = ɵɵdefineInjectable({
  token: _DomInsertionService,
  factory: _DomInsertionService.ɵfac,
  providedIn: "root"
});
var DomInsertionService = _DomInsertionService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DomInsertionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var LOADER_DELAY = new InjectionToken("LOADER_DELAY");
var _HttpWaitService = class _HttpWaitService {
  constructor(injector) {
    this.store = new InternalStore({
      requests: [],
      filteredRequests: []
    });
    this.destroy$ = new Subject();
    this.delay = injector.get(LOADER_DELAY, 500);
  }
  getLoading() {
    return !!this.applyFilter(this.store.state.requests).length;
  }
  getLoading$() {
    return this.store.sliceState(({
      requests
    }) => requests).pipe(map((requests) => !!this.applyFilter(requests).length), switchMap((condition) => condition ? this.delay === 0 ? of(true) : timer(this.delay).pipe(mapTo(true), takeUntil(this.destroy$)) : of(false)), tap(() => this.destroy$.next()));
  }
  updateLoading$() {
    return this.store.sliceUpdate(({
      requests
    }) => !!this.applyFilter(requests).length);
  }
  clearLoading() {
    this.store.patch({
      requests: []
    });
  }
  addRequest(request) {
    this.store.patch({
      requests: [...this.store.state.requests, request]
    });
  }
  deleteRequest(request) {
    const requests = this.store.state.requests.filter((r) => r !== request);
    this.store.patch({
      requests
    });
  }
  addFilter(request) {
    const requests = Array.isArray(request) ? request : [request];
    const filteredRequests = [...this.store.state.filteredRequests.filter((f) => !requests.some((r) => this.isSameRequest(f, r))), ...requests];
    this.store.patch({
      filteredRequests
    });
  }
  removeFilter(request) {
    const requests = Array.isArray(request) ? request : [request];
    const filteredRequests = this.store.state.filteredRequests.filter((f) => !requests.some((r) => this.isSameRequest(f, r)));
    this.store.patch({
      filteredRequests
    });
  }
  applyFilter(requests) {
    if (!requests) {
      return [];
    }
    const {
      filteredRequests
    } = this.store.state;
    return requests.filter(({
      method,
      url
    }) => !filteredRequests.find((filteredRequest) => this.isSameRequest(filteredRequest, {
      method,
      endpoint: getPathName(url)
    })));
  }
  isSameRequest(filteredRequest, request) {
    const {
      method,
      endpoint
    } = filteredRequest;
    return endpoint === request.endpoint && method === request.method;
  }
};
_HttpWaitService.ɵfac = function HttpWaitService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _HttpWaitService)(ɵɵinject(Injector));
};
_HttpWaitService.ɵprov = ɵɵdefineInjectable({
  token: _HttpWaitService,
  factory: _HttpWaitService.ɵfac,
  providedIn: "root"
});
var HttpWaitService = _HttpWaitService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HttpWaitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: Injector
  }], null);
})();
var _ResourceWaitService = class _ResourceWaitService {
  constructor() {
    this.store = new InternalStore({
      resources: /* @__PURE__ */ new Set()
    });
  }
  getLoading() {
    return !!this.store.state.resources.size;
  }
  getLoading$() {
    return this.store.sliceState(({
      resources
    }) => !!resources.size);
  }
  updateLoading$() {
    return this.store.sliceUpdate(({
      resources
    }) => !!resources?.size);
  }
  clearLoading() {
    this.store.patch({
      resources: /* @__PURE__ */ new Set()
    });
  }
  addResource(resource) {
    const resources = this.store.state.resources;
    resources.add(resource);
    this.store.patch({
      resources
    });
  }
  deleteResource(resource) {
    const resources = this.store.state.resources;
    resources.delete(resource);
    this.store.patch({
      resources
    });
  }
};
_ResourceWaitService.ɵfac = function ResourceWaitService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ResourceWaitService)();
};
_ResourceWaitService.ɵprov = ɵɵdefineInjectable({
  token: _ResourceWaitService,
  factory: _ResourceWaitService.ɵfac,
  providedIn: "root"
});
var ResourceWaitService = _ResourceWaitService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ResourceWaitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _LazyLoadService = class _LazyLoadService {
  constructor(resourceWaitService) {
    this.resourceWaitService = resourceWaitService;
    this.loaded = /* @__PURE__ */ new Map();
  }
  load(strategy, retryTimes, retryDelay) {
    if (this.loaded.has(strategy.path)) return of(new CustomEvent("load"));
    this.resourceWaitService.addResource(strategy.path);
    const delayOperator = retryDelay ? pipe(delay(retryDelay)) : pipe();
    const takeOp = retryTimes ? pipe(take(retryTimes)) : pipe();
    return strategy.createStream().pipe(retryWhen((error$) => concat(error$.pipe(delayOperator, takeOp), throwError(() => new CustomEvent("error")))), tap(() => {
      this.loaded.set(strategy.path, strategy.element);
      this.resourceWaitService.deleteResource(strategy.path);
    }), delay(100), shareReplay({
      bufferSize: 1,
      refCount: true
    }));
  }
  remove(path) {
    const element = this.loaded.get(path);
    if (!element) return false;
    element.parentNode?.removeChild(element);
    this.loaded.delete(path);
    return true;
  }
};
_LazyLoadService.ɵfac = function LazyLoadService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _LazyLoadService)(ɵɵinject(ResourceWaitService));
};
_LazyLoadService.ɵprov = ɵɵdefineInjectable({
  token: _LazyLoadService,
  factory: _LazyLoadService.ɵfac,
  providedIn: "root"
});
var LazyLoadService = _LazyLoadService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LazyLoadService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: ResourceWaitService
  }], null);
})();
var LIST_QUERY_DEBOUNCE_TIME = new InjectionToken("LIST_QUERY_DEBOUNCE_TIME");
var _ListService = class _ListService {
  set filter(value) {
    this._filter = value;
    this.get();
  }
  get filter() {
    return this._filter;
  }
  set maxResultCount(value) {
    this._maxResultCount = value;
    this.get();
  }
  get maxResultCount() {
    return this._maxResultCount;
  }
  set page(value) {
    if (value === this._page) return;
    this._page = value;
    this.get();
  }
  get page() {
    return this._page;
  }
  set totalCount(value) {
    if (value === this._totalCount) return;
    this._totalCount = value;
    this.get();
  }
  get totalCount() {
    return this._totalCount;
  }
  set sortKey(value) {
    this._sortKey = value;
    this.get();
  }
  get sortKey() {
    return this._sortKey;
  }
  set sortOrder(value) {
    this._sortOrder = value;
    this.get();
  }
  get sortOrder() {
    return this._sortOrder;
  }
  get query$() {
    return this._query$.asObservable().pipe(this.delay, shareReplay({
      bufferSize: 1,
      refCount: true
    }));
  }
  /**
   * @deprecated Use `requestStatus$` instead.
   */
  get isLoading$() {
    return this._isLoading$.asObservable().pipe(takeUntil(this.destroy$));
  }
  get requestStatus$() {
    return this._requestStatus.asObservable().pipe(takeUntil(this.destroy$));
  }
  constructor(injector) {
    this._filter = "";
    this._maxResultCount = 10;
    this._page = 0;
    this._totalCount = 0;
    this._sortKey = "";
    this._sortOrder = "";
    this._query$ = new ReplaySubject(1);
    this._isLoading$ = new BehaviorSubject(false);
    this._requestStatus = new BehaviorSubject("idle");
    this.destroy$ = new Subject();
    this.get = () => {
      this.resetPageWhenUnchanged();
      this.next();
    };
    this.getWithoutPageReset = () => {
      this.next();
    };
    const delay2 = injector.get(LIST_QUERY_DEBOUNCE_TIME, 300);
    this.delay = delay2 ? debounceTime(delay2) : tap();
    this.get();
  }
  hookToQuery(streamCreatorCallback) {
    return this.query$.pipe(tap(() => this._isLoading$.next(true)), tap(() => this._requestStatus.next("loading")), switchMap((query) => streamCreatorCallback(query).pipe(catchError(() => {
      this._requestStatus.next("error");
      return EMPTY;
    }), tap(() => this._requestStatus.next("success")), finalize(() => {
      this._isLoading$.next(false);
      this._requestStatus.next("idle");
    }))), filter(Boolean), shareReplay({
      bufferSize: 1,
      refCount: true
    }), takeUntil(this.destroy$));
  }
  ngOnDestroy() {
    this.destroy$.next();
  }
  resetPageWhenUnchanged() {
    const maxPage = Number(Number(this.totalCount / this._maxResultCount).toFixed());
    const skipCount = this._page * this._maxResultCount;
    if (skipCount !== this._totalCount) {
      return;
    }
    if (this.page === maxPage && this.page > 0) {
      this.page = this.page - 1;
    }
  }
  next() {
    this._query$.next({
      filter: this._filter || void 0,
      maxResultCount: this._maxResultCount,
      skipCount: this._page * this._maxResultCount,
      sorting: this._sortOrder ? `${this._sortKey} ${this._sortOrder}` : void 0
    });
  }
};
_ListService.ɵfac = function ListService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ListService)(ɵɵinject(Injector));
};
_ListService.ɵprov = ɵɵdefineInjectable({
  token: _ListService,
  factory: _ListService.ɵfac
});
var ListService = _ListService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ListService, [{
    type: Injectable
  }], () => [{
    type: Injector
  }], null);
})();
var _PermissionService = class _PermissionService {
  constructor(configState) {
    this.configState = configState;
  }
  getGrantedPolicy$(key) {
    return this.getStream().pipe(map((grantedPolicies) => this.isPolicyGranted(key, grantedPolicies)));
  }
  getGrantedPolicy(key) {
    const policies = this.getSnapshot();
    return this.isPolicyGranted(key, policies);
  }
  filterItemsByPolicy(items) {
    const policies = this.getSnapshot();
    return items.filter((item) => !item.requiredPolicy || this.isPolicyGranted(item.requiredPolicy, policies));
  }
  filterItemsByPolicy$(items) {
    return this.getStream().pipe(map((policies) => items.filter((item) => !item.requiredPolicy || this.isPolicyGranted(item.requiredPolicy, policies))));
  }
  isPolicyGranted(key, grantedPolicies) {
    if (!key) return true;
    const orRegexp = /\|\|/g;
    const andRegexp = /&&/g;
    if (orRegexp.test(key)) {
      const keys = key.split("||").filter(Boolean);
      if (keys.length < 2) return false;
      return keys.some((k) => this.getPolicy(k.trim(), grantedPolicies));
    } else if (andRegexp.test(key)) {
      const keys = key.split("&&").filter(Boolean);
      if (keys.length < 2) return false;
      return keys.every((k) => this.getPolicy(k.trim(), grantedPolicies));
    }
    return this.getPolicy(key, grantedPolicies);
  }
  getStream() {
    return this.configState.getAll$().pipe(map(this.mapToPolicies));
  }
  getSnapshot() {
    return this.mapToPolicies(this.configState.getAll());
  }
  mapToPolicies(applicationConfiguration) {
    return applicationConfiguration?.auth?.grantedPolicies || {};
  }
  getPolicy(key, grantedPolicies) {
    return grantedPolicies[key] || false;
  }
};
_PermissionService.ɵfac = function PermissionService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _PermissionService)(ɵɵinject(ConfigStateService));
};
_PermissionService.ɵprov = ɵɵdefineInjectable({
  token: _PermissionService,
  factory: _PermissionService.ɵfac,
  providedIn: "root"
});
var PermissionService = _PermissionService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: ConfigStateService
  }], null);
})();
var _ReplaceableComponentsService = class _ReplaceableComponentsService {
  get replaceableComponents$() {
    return this.store.sliceState((state) => state);
  }
  get replaceableComponents() {
    return this.store.state;
  }
  get onUpdate$() {
    return this.store.sliceUpdate((state) => state);
  }
  constructor(ngZone, router) {
    this.ngZone = ngZone;
    this.router = router;
    this.store = new InternalStore([]);
  }
  add(replaceableComponent, reload) {
    const replaceableComponents = [...this.store.state];
    const index2 = replaceableComponents.findIndex((component) => component.key === replaceableComponent.key);
    if (index2 > -1) {
      replaceableComponents[index2] = replaceableComponent;
    } else {
      replaceableComponents.push(replaceableComponent);
    }
    this.store.set(replaceableComponents);
    if (reload) reloadRoute(this.router, this.ngZone);
  }
  get(replaceableComponentKey) {
    return this.replaceableComponents.find((component) => component.key === replaceableComponentKey);
  }
  get$(replaceableComponentKey) {
    return this.replaceableComponents$.pipe(map((components) => components.find((component) => component.key === replaceableComponentKey)));
  }
};
_ReplaceableComponentsService.ɵfac = function ReplaceableComponentsService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ReplaceableComponentsService)(ɵɵinject(NgZone), ɵɵinject(Router));
};
_ReplaceableComponentsService.ɵprov = ɵɵdefineInjectable({
  token: _ReplaceableComponentsService,
  factory: _ReplaceableComponentsService.ɵfac,
  providedIn: "root"
});
var ReplaceableComponentsService = _ReplaceableComponentsService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplaceableComponentsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: NgZone
  }, {
    type: Router
  }], null);
})();
var NavigationEvent = {
  Cancel: NavigationCancel,
  End: NavigationEnd,
  Error: NavigationError,
  Start: NavigationStart
};
var _previousNavigation, _currentNavigation;
var _RouterEvents = class _RouterEvents {
  constructor() {
    __privateAdd(this, _previousNavigation);
    __privateAdd(this, _currentNavigation);
    this.router = inject(Router);
    __privateSet(this, _previousNavigation, signal(void 0));
    this.previousNavigation = __privateGet(this, _previousNavigation).asReadonly();
    __privateSet(this, _currentNavigation, signal(void 0));
    this.currentNavigation = __privateGet(this, _currentNavigation).asReadonly();
    this.listenToNavigation();
  }
  listenToNavigation() {
    const routerEvent$ = this.router.events.pipe(filter((e) => e instanceof NavigationEvent.End && !e.url.includes("error")));
    routerEvent$.subscribe((event) => {
      __privateGet(this, _previousNavigation).set(this.currentNavigation());
      __privateGet(this, _currentNavigation).set(event.url);
    });
  }
  getEvents(...eventTypes) {
    const filterRouterEvents = (event) => eventTypes.some((type) => event instanceof type);
    return this.router.events.pipe(filter(filterRouterEvents));
  }
  getNavigationEvents(...navigationEventKeys) {
    const filterNavigationEvents = (event) => navigationEventKeys.some((key) => event instanceof NavigationEvent[key]);
    return this.router.events.pipe(filter(filterNavigationEvents));
  }
  getAllEvents() {
    return this.router.events;
  }
  getAllNavigationEvents() {
    const keys = Object.keys(NavigationEvent);
    return this.getNavigationEvents(...keys);
  }
};
_previousNavigation = new WeakMap();
_currentNavigation = new WeakMap();
_RouterEvents.ɵfac = function RouterEvents_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _RouterEvents)();
};
_RouterEvents.ɵprov = ɵɵdefineInjectable({
  token: _RouterEvents,
  factory: _RouterEvents.ɵfac,
  providedIn: "root"
});
var RouterEvents = _RouterEvents;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RouterEvents, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var _RouterWaitService = class _RouterWaitService {
  constructor(routerEvents, injector) {
    this.routerEvents = routerEvents;
    this.store = new InternalStore({
      loading: false
    });
    this.destroy$ = new Subject();
    this.delay = injector.get(LOADER_DELAY, 500);
    this.updateLoadingStatusOnNavigationEvents();
  }
  updateLoadingStatusOnNavigationEvents() {
    this.routerEvents.getAllNavigationEvents().pipe(map((event) => event instanceof NavigationStart), switchMap((condition) => condition ? this.delay === 0 ? of(true) : timer(this.delay || 0).pipe(mapTo(true), takeUntil(this.destroy$)) : of(false)), tap(() => this.destroy$.next())).subscribe((status) => {
      this.setLoading(status);
    });
  }
  getLoading() {
    return this.store.state.loading;
  }
  getLoading$() {
    return this.store.sliceState(({
      loading
    }) => loading);
  }
  updateLoading$() {
    return this.store.sliceUpdate(({
      loading
    }) => loading);
  }
  setLoading(loading) {
    this.store.patch({
      loading
    });
  }
};
_RouterWaitService.ɵfac = function RouterWaitService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _RouterWaitService)(ɵɵinject(RouterEvents), ɵɵinject(Injector));
};
_RouterWaitService.ɵprov = ɵɵdefineInjectable({
  token: _RouterWaitService,
  factory: _RouterWaitService.ɵfac,
  providedIn: "root"
});
var RouterWaitService = _RouterWaitService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RouterWaitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RouterEvents
  }, {
    type: Injector
  }], null);
})();
var COOKIE_LANGUAGE_KEY = new InjectionToken("COOKIE_LANGUAGE_KEY", {
  factory: () => ".AspNetCore.Culture"
});
var NAVIGATE_TO_MANAGE_PROFILE = new InjectionToken("NAVIGATE_TO_MANAGE_PROFILE");
var QUEUE_MANAGER = new InjectionToken("QUEUE_MANAGER");
var INCUDE_LOCALIZATION_RESOURCES_TOKEN = new InjectionToken("INCUDE_LOCALIZATION_RESOURCES_TOKEN");
var PIPE_TO_LOGIN_FN_KEY = new InjectionToken("PIPE_TO_LOGIN_FN_KEY");
var SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY = new InjectionToken("SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY");
var OTHERS_GROUP = new InjectionToken("OTHERS_GROUP");
var SORT_COMPARE_FUNC = new InjectionToken("SORT_COMPARE_FUNC");
function compareFuncFactory() {
  const localizationService = inject(LocalizationService);
  const fn = (a, b) => {
    const aNumber = a.order;
    const bNumber = b.order;
    if (aNumber > bNumber) return 1;
    if (aNumber < bNumber) return -1;
    if (a.id > b.id) return 1;
    if (a.id < b.id) return -1;
    if (!Number.isInteger(aNumber)) return 1;
    if (!Number.isInteger(bNumber)) return -1;
    const aName = localizationService.instant(a.name);
    const bName = localizationService.instant(b.name);
    if (aName > bName) return 1;
    if (aName < bName) return -1;
    return 0;
  };
  return fn;
}
var DYNAMIC_LAYOUTS_TOKEN = new InjectionToken("DYNAMIC_LAYOUTS");
var DISABLE_PROJECT_NAME = new InjectionToken("DISABLE_APP_NAME");
var AbstractTreeService = class {
  constructor() {
    this._flat$ = new BehaviorSubject([]);
    this._tree$ = new BehaviorSubject([]);
    this._visible$ = new BehaviorSubject([]);
    this.shouldSingularizeRoutes = true;
  }
  get flat() {
    return this._flat$.value;
  }
  get flat$() {
    return this._flat$.asObservable();
  }
  get tree() {
    return this._tree$.value;
  }
  get tree$() {
    return this._tree$.asObservable();
  }
  get visible() {
    return this._visible$.value;
  }
  get visible$() {
    return this._visible$.asObservable();
  }
  filterWith(setOrMap) {
    return this._flat$.value.filter((item) => !setOrMap.has(item[this.id]));
  }
  findItemsToRemove(set) {
    return this._flat$.value.reduce((acc, item) => {
      if (!acc.has(item[this.parentId])) {
        return acc;
      }
      const childSet = /* @__PURE__ */ new Set([item[this.id]]);
      const children = this.findItemsToRemove(childSet);
      return /* @__PURE__ */ new Set([...acc, ...children]);
    }, set);
  }
  publish(flatItems) {
    this._flat$.next(flatItems);
    this._tree$.next(this.createTree(flatItems));
    this._visible$.next(this.createTree(flatItems.filter((item) => !this.hide(item))));
    return flatItems;
  }
  createTree(items) {
    return createTreeFromList(items, (item) => item[this.id], (item) => item[this.parentId], (item) => BaseTreeNode.create(item));
  }
  createGroupedTree(list) {
    const map2 = createGroupMap(list, this.othersGroup);
    if (!map2) {
      return void 0;
    }
    return Array.from(map2, ([key, items]) => ({
      group: key,
      items
    }));
  }
  add(items) {
    let flatItems = [];
    if (!this.shouldSingularizeRoutes) {
      flatItems = [...this.flat, ...items];
    }
    if (this.shouldSingularizeRoutes) {
      const map2 = /* @__PURE__ */ new Map();
      items.forEach((item) => map2.set(item[this.id], item));
      flatItems = this.filterWith(map2);
      map2.forEach(pushValueTo(flatItems));
    }
    flatItems.sort(this.sort);
    return this.publish(flatItems);
  }
  find(predicate, tree = this.tree) {
    return tree.reduce((acc, node) => {
      if (acc) {
        return acc;
      }
      if (predicate(node)) {
        return node;
      }
      return this.find(predicate, node.children);
    }, null);
  }
  patch(identifier, props) {
    const flatItems = this._flat$.value;
    const index2 = flatItems.findIndex((item) => item[this.id] === identifier);
    if (index2 < 0) {
      return false;
    }
    flatItems[index2] = __spreadValues(__spreadValues({}, flatItems[index2]), props);
    flatItems.sort(this.sort);
    return this.publish(flatItems);
  }
  refresh() {
    return this.add([]);
  }
  remove(identifiers) {
    const set = /* @__PURE__ */ new Set();
    identifiers.forEach((id) => set.add(id));
    const setToRemove = this.findItemsToRemove(set);
    const flatItems = this.filterWith(setToRemove);
    return this.publish(flatItems);
  }
  removeByParam(params) {
    if (!params) {
      return null;
    }
    const keys = Object.keys(params);
    if (keys.length === 0) {
      return null;
    }
    const excludedList = this.flat.filter((item) => keys.every((key) => item[key] === params[key]));
    if (!excludedList?.length) {
      return null;
    }
    for (const item of excludedList) {
      this.removeByParam({
        [this.parentId]: item[this.id]
      });
    }
    const flatItems = this.flat.filter((item) => !excludedList.includes(item));
    return this.publish(flatItems);
  }
  search(params, tree = this.tree) {
    const searchKeys = Object.keys(params);
    return tree.reduce((acc, node) => {
      if (acc) {
        return acc;
      }
      if (searchKeys.every((key) => node[key] === params[key])) {
        return node;
      }
      return this.search(params, node.children);
    }, null);
  }
  setSingularizeStatus(singularize = true) {
    this.shouldSingularizeRoutes = singularize;
  }
};
var _AbstractNavTreeService = class _AbstractNavTreeService extends AbstractTreeService {
  constructor(injector) {
    super();
    this.injector = injector;
    this.id = "name";
    this.parentId = "parentName";
    this.hide = (item) => item.invisible || !this.isGranted(item);
    this.sort = (a, b) => {
      return this.compareFunc(a, b);
    };
    const configState = this.injector.get(ConfigStateService);
    this.subscription = configState.createOnUpdateStream((state) => state).subscribe(() => this.refresh());
    this.permissionService = injector.get(PermissionService);
    this.othersGroup = injector.get(OTHERS_GROUP);
    this.compareFunc = injector.get(SORT_COMPARE_FUNC);
  }
  isGranted({
    requiredPolicy
  }) {
    return this.permissionService.getGrantedPolicy(requiredPolicy);
  }
  hasChildren(identifier) {
    const node = this.find((item) => item[this.id] === identifier);
    return Boolean(node?.children?.length);
  }
  hasInvisibleChild(identifier) {
    const node = this.find((item) => item[this.id] === identifier);
    return node?.children?.some((child) => child.invisible) || false;
  }
  /* istanbul ignore next */
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
};
_AbstractNavTreeService.ɵfac = function AbstractNavTreeService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbstractNavTreeService)(ɵɵinject(Injector));
};
_AbstractNavTreeService.ɵprov = ɵɵdefineInjectable({
  token: _AbstractNavTreeService,
  factory: _AbstractNavTreeService.ɵfac
});
var AbstractNavTreeService = _AbstractNavTreeService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbstractNavTreeService, [{
    type: Injectable
  }], () => [{
    type: Injector
  }], null);
})();
var _RoutesService = class _RoutesService extends AbstractNavTreeService {
  hasPathOrChild(item) {
    return Boolean(item.path) || this.hasChildren(item.name);
  }
  get groupedVisible() {
    return this.createGroupedTree(this.visible.filter((item) => this.hasPathOrChild(item)));
  }
  get groupedVisible$() {
    return this.visible$.pipe(map((items) => items.filter((item) => this.hasPathOrChild(item))), map((visible) => this.createGroupedTree(visible)));
  }
};
_RoutesService.ɵfac = /* @__PURE__ */ (() => {
  let ɵRoutesService_BaseFactory;
  return function RoutesService_Factory(__ngFactoryType__) {
    return (ɵRoutesService_BaseFactory || (ɵRoutesService_BaseFactory = ɵɵgetInheritedFactory(_RoutesService)))(__ngFactoryType__ || _RoutesService);
  };
})();
_RoutesService.ɵprov = ɵɵdefineInjectable({
  token: _RoutesService,
  factory: _RoutesService.ɵfac,
  providedIn: "root"
});
var RoutesService = _RoutesService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoutesService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _SubscriptionService = class _SubscriptionService {
  constructor() {
    this.subscription = new Subscription();
  }
  get isClosed() {
    return this.subscription.closed;
  }
  addOne(source$, nextOrObserver, error) {
    const subscription = source$.subscribe(nextOrObserver, error);
    this.subscription.add(subscription);
    return subscription;
  }
  closeAll() {
    this.subscription.unsubscribe();
  }
  closeOne(subscription) {
    this.removeOne(subscription);
    if (subscription) {
      subscription.unsubscribe();
    }
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  removeOne(subscription) {
    if (!subscription) return;
    this.subscription.remove(subscription);
  }
  reset() {
    this.subscription.unsubscribe();
    this.subscription = new Subscription();
  }
};
_SubscriptionService.ɵfac = function SubscriptionService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _SubscriptionService)();
};
_SubscriptionService.ɵprov = ɵɵdefineInjectable({
  token: _SubscriptionService,
  factory: _SubscriptionService.ɵfac
});
var SubscriptionService = _SubscriptionService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SubscriptionService, [{
    type: Injectable
  }], null, null);
})();
var trackBy = (key) => (_, item) => item[key];
var trackByDeep = (...keys) => (_, item) => keys.reduce((acc, key) => acc[key], item);
var _TrackByService = class _TrackByService {
  constructor() {
    this.by = trackBy;
    this.byDeep = trackByDeep;
  }
};
_TrackByService.ɵfac = function TrackByService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _TrackByService)();
};
_TrackByService.ɵprov = ɵɵdefineInjectable({
  token: _TrackByService,
  factory: _TrackByService.ɵfac,
  providedIn: "root"
});
var TrackByService = _TrackByService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TrackByService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _AbpWindowService = class _AbpWindowService {
  constructor() {
    this.document = inject(DOCUMENT);
    this.window = this.document.defaultView;
    this.navigator = this.window.navigator;
  }
  copyToClipboard(text) {
    return this.navigator.clipboard.writeText(text);
  }
  open(url, target, features) {
    return this.window.open(url, target, features);
  }
  reloadPage() {
    this.window.location.reload();
  }
  downloadBlob(blob, fileName) {
    const blobUrl = this.window.URL.createObjectURL(blob);
    const a = this.document.createElement("a");
    a.style.display = "none";
    a.href = blobUrl;
    a.download = fileName;
    this.document.body.appendChild(a);
    a.dispatchEvent(new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
      view: this.window
    }));
    this.window.URL.revokeObjectURL(blobUrl);
    this.document.body.removeChild(a);
  }
};
_AbpWindowService.ɵfac = function AbpWindowService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpWindowService)();
};
_AbpWindowService.ɵprov = ɵɵdefineInjectable({
  token: _AbpWindowService,
  factory: _AbpWindowService.ɵfac,
  providedIn: "root"
});
var AbpWindowService = _AbpWindowService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpWindowService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _InternetConnectionService = class _InternetConnectionService {
  constructor() {
    this.document = inject(DOCUMENT);
    this.window = this.document.defaultView;
    this.navigator = this.window.navigator;
    this.status$ = new BehaviorSubject(this.navigator.onLine);
    this.status = signal(this.navigator.onLine);
    this.networkStatus = computed(() => this.status());
    this.window.addEventListener("offline", () => this.setStatus(false));
    this.window.addEventListener("online", () => this.setStatus(true));
  }
  setStatus(val) {
    this.status.set(val);
    this.status$.next(val);
  }
  get networkStatus$() {
    return this.status$.asObservable();
  }
};
_InternetConnectionService.ɵfac = function InternetConnectionService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _InternetConnectionService)();
};
_InternetConnectionService.ɵprov = ɵɵdefineInjectable({
  token: _InternetConnectionService,
  factory: _InternetConnectionService.ɵfac,
  providedIn: "root"
});
var InternetConnectionService = _InternetConnectionService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InternetConnectionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var _LocalStorageListenerService = class _LocalStorageListenerService {
  constructor() {
    this.window = inject(DOCUMENT).defaultView;
    this.window.addEventListener("storage", (event) => {
      if (event.key === "access_token") {
        const tokenRemoved = event.newValue === null;
        const tokenAdded = event.oldValue === null && event.newValue !== null;
        if (tokenRemoved || tokenAdded) {
          this.window.location.assign("/");
        }
      }
    });
  }
};
_LocalStorageListenerService.ɵfac = function LocalStorageListenerService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _LocalStorageListenerService)();
};
_LocalStorageListenerService.ɵprov = ɵɵdefineInjectable({
  token: _LocalStorageListenerService,
  factory: _LocalStorageListenerService.ɵfac,
  providedIn: "root"
});
var LocalStorageListenerService = _LocalStorageListenerService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalStorageListenerService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var _AbpTitleStrategy = class _AbpTitleStrategy extends TitleStrategy {
  constructor() {
    super();
    this.title = inject(Title);
    this.localizationService = inject(LocalizationService);
    this.disableProjectName = inject(DISABLE_PROJECT_NAME, {
      optional: true
    });
    this.langugageChange = toSignal(this.localizationService.languageChange$);
    effect(() => {
      if (this.langugageChange()) {
        this.updateTitle(this.routerState);
      }
    });
  }
  updateTitle(routerState) {
    this.routerState = routerState;
    const title = this.buildTitle(routerState);
    const projectName = this.localizationService.instant({
      key: "::AppName",
      defaultValue: "MyProjectName"
    });
    if (!title) {
      return this.title.setTitle(projectName);
    }
    let localizedText = this.localizationService.instant({
      key: title,
      defaultValue: title
    });
    if (!this.disableProjectName) {
      localizedText += ` | ${projectName}`;
    }
    this.title.setTitle(localizedText);
  }
};
_AbpTitleStrategy.ɵfac = function AbpTitleStrategy_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpTitleStrategy)();
};
_AbpTitleStrategy.ɵprov = ɵɵdefineInjectable({
  token: _AbpTitleStrategy,
  factory: _AbpTitleStrategy.ɵfac,
  providedIn: "root"
});
var AbpTitleStrategy = _AbpTitleStrategy;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpTitleStrategy, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var _TimezoneService = class _TimezoneService {
  constructor() {
    this.configState = inject(ConfigStateService);
    this.document = inject(DOCUMENT);
    this.cookieKey = "__timezone";
    this.configState.getOne$("setting").subscribe((settings) => {
      this.timeZoneNameFromSettings = settings?.values?.["Abp.Timing.TimeZone"];
    });
    this.configState.getOne$("clock").subscribe((clock) => {
      this.isUtcClockEnabled = clock?.kind === "Utc";
    });
  }
  /**
   * Returns the effective timezone to be used across the application.
   *
   * This value is determined based on the clock kind setting in the configuration:
   * - If clock kind is not equal to Utc, the browser's local timezone is returned.
   * - If clock kind is equal to Utc, the configured timezone (`timeZoneNameFromSettings`) is returned if available;
   *   otherwise, the browser's timezone is used as a fallback.
   *
   * @returns The IANA timezone name (e.g., 'Europe/Istanbul', 'America/New_York').
   */
  get timezone() {
    if (!this.isUtcClockEnabled) {
      return this.getBrowserTimezone();
    }
    return this.timeZoneNameFromSettings || this.getBrowserTimezone();
  }
  /**
   * Retrieves the browser's local timezone based on the user's system settings.
   *
   * @returns The IANA timezone name (e.g., 'Europe/Istanbul', 'America/New_York').
   */
  getBrowserTimezone() {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
  /**
   * Sets the application's timezone in a cookie to persist the user's selected timezone.
   *
   * This method sets the cookie only if the clock kind setting is set to UTC.
   * The cookie is stored using the key defined by `this.cookieKey` and applied to the root path (`/`).
   *
   * @param timezone - The IANA timezone name to be stored (e.g., 'Europe/Istanbul').
   */
  setTimezone(timezone) {
    if (this.isUtcClockEnabled) {
      this.document.cookie = `${this.cookieKey}=${timezone}; path=/`;
    }
  }
};
_TimezoneService.ɵfac = function TimezoneService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _TimezoneService)();
};
_TimezoneService.ɵprov = ɵɵdefineInjectable({
  token: _TimezoneService,
  factory: _TimezoneService.ɵfac,
  providedIn: "root"
});
var TimezoneService = _TimezoneService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TimezoneService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var _TimeService = class _TimeService {
  constructor() {
    this.locale = inject(LOCALE_ID);
  }
  /**
   * Returns the current date and time in the specified timezone.
   *
   * @param zone - An IANA timezone name (e.g., 'Europe/Istanbul', 'UTC'); defaults to the system's local timezone.
   * @returns A Luxon DateTime instance representing the current time in the given timezone.
   */
  now(zone = "local") {
    return DateTime.now().setZone(zone);
  }
  /**
   * Converts the input date to the specified timezone, applying any timezone and daylight saving time (DST) adjustments.
   *
   * This method:
   * 1. Parses the input value into a Luxon DateTime object.
   * 2. Applies the specified IANA timezone, including any DST shifts based on the given date.
   *
   * @param value - The ISO string or Date object to convert.
   * @param zone - An IANA timezone name (e.g., 'America/New_York').
   * @returns A Luxon DateTime instance adjusted to the specified timezone and DST rules.
   */
  toZone(value, zone) {
    return DateTime.fromISO(value instanceof Date ? value.toISOString() : value, {
      zone
    });
  }
  /**
   * Formats the input date by applying timezone and daylight saving time (DST) adjustments.
   *
   * This method:
   * 1. Converts the input date to the specified timezone.
   * 2. Formats the result using the given format and locale, reflecting any timezone or DST shifts.
   *
   * @param value - The ISO string or Date object to format.
   * @param format - The format string (default: 'ff').
   * @param zone - Optional IANA timezone name (e.g., 'America/New_York'); defaults to the system's local timezone.
   * @returns A formatted date string adjusted for the given timezone and DST rules.
   */
  format(value, format = "ff", zone = "local") {
    return this.toZone(value, zone).setLocale(this.locale).toFormat(format);
  }
  /**
   * Formats a date using the standard time offset (ignoring daylight saving time) for the specified timezone.
   *
   * This method:
   * 1. Converts the input date to UTC.
   * 2. Calculates the standard UTC offset for the given timezone (based on January 1st to avoid DST).
   * 3. Applies the standard offset manually to the UTC time.
   * 4. Formats the result using the specified format and locale, without applying additional timezone shifts.
   *
   * @param value - The ISO string or Date object to format.
   * @param format - The Luxon format string (default: 'ff').
   * @param zone - Optional IANA timezone name (e.g., 'America/New_York'); if omitted, system local timezone is used.
   * @returns A formatted date string adjusted by standard time (non-DST).
   */
  formatDateWithStandardOffset(value, format = "ff", zone) {
    const utcDate = typeof value === "string" ? DateTime.fromISO(value, {
      zone: "UTC"
    }) : DateTime.fromJSDate(value, {
      zone: "UTC"
    });
    if (!utcDate.isValid) return "";
    const targetZone = zone ?? DateTime.local().zoneName;
    const januaryDate = DateTime.fromObject({
      year: utcDate.year,
      month: 1,
      day: 1
    }, {
      zone: targetZone
    });
    const standardOffset = januaryDate.offset;
    const dateWithStandardOffset = utcDate.plus({
      minutes: standardOffset
    });
    return dateWithStandardOffset.setZone("UTC").setLocale(this.locale).toFormat(format);
  }
  /**
   * Formats the input date using its original clock time, without converting based on timezone or DST
   *
   * This method:
   * 1. Converts the input date to ISO string.
   * 2. Calculates the date time in UTC, keeping the local time.
   * 3. Formats the result using the specified format and locale, without shifting timezones.
   *
   * @param value - The ISO string or Date object to format.
   * @param format - The format string (default: 'ff').
   * @returns A formatted date string without applying timezone.
   */
  formatWithoutTimeZone(value, format = "ff") {
    const isoString = value instanceof Date ? value.toISOString() : value;
    const dateTime = DateTime.fromISO(isoString).setZone("utc", {
      keepLocalTime: true
    }).setLocale(this.locale);
    return dateTime.toFormat(format);
  }
};
_TimeService.ɵfac = function TimeService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _TimeService)();
};
_TimeService.ɵprov = ɵɵdefineInjectable({
  token: _TimeService,
  factory: _TimeService.ɵfac,
  providedIn: "root"
});
var TimeService = _TimeService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TimeService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var _AbpApplicationConfigurationService = class _AbpApplicationConfigurationService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.get = (options, config) => this.restService.request({
      method: "GET",
      url: "/api/abp/application-configuration",
      params: {
        includeLocalizationResources: options.includeLocalizationResources
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
};
_AbpApplicationConfigurationService.ɵfac = function AbpApplicationConfigurationService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpApplicationConfigurationService)(ɵɵinject(RestService));
};
_AbpApplicationConfigurationService.ɵprov = ɵɵdefineInjectable({
  token: _AbpApplicationConfigurationService,
  factory: _AbpApplicationConfigurationService.ɵfac,
  providedIn: "root"
});
var AbpApplicationConfigurationService = _AbpApplicationConfigurationService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpApplicationConfigurationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var _AbpApplicationLocalizationService = class _AbpApplicationLocalizationService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.get = (input, config) => this.restService.request({
      method: "GET",
      url: "/api/abp/application-localization",
      params: {
        cultureName: input.cultureName,
        onlyDynamics: input.onlyDynamics
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
};
_AbpApplicationLocalizationService.ɵfac = function AbpApplicationLocalizationService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpApplicationLocalizationService)(ɵɵinject(RestService));
};
_AbpApplicationLocalizationService.ɵprov = ɵɵdefineInjectable({
  token: _AbpApplicationLocalizationService,
  factory: _AbpApplicationLocalizationService.ɵfac,
  providedIn: "root"
});
var AbpApplicationLocalizationService = _AbpApplicationLocalizationService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpApplicationLocalizationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var _ConfigStateService = class _ConfigStateService {
  setState(config) {
    this.store.set(config);
  }
  get createOnUpdateStream() {
    return this.store.sliceUpdate;
  }
  constructor(abpConfigService, abpApplicationLocalizationService, includeLocalizationResources) {
    this.abpConfigService = abpConfigService;
    this.abpApplicationLocalizationService = abpApplicationLocalizationService;
    this.includeLocalizationResources = includeLocalizationResources;
    this.updateSubject = new Subject();
    this.store = new InternalStore({});
    this.initUpdateStream();
  }
  initUpdateStream() {
    this.updateSubject.pipe(switchMap(() => this.abpConfigService.get({
      includeLocalizationResources: !!this.includeLocalizationResources
    }))).pipe(switchMap((appState) => this.getLocalizationAndCombineWithAppState(appState))).subscribe((res) => this.store.set(res));
  }
  getLocalizationAndCombineWithAppState(appState) {
    if (!appState.localization.currentCulture.cultureName) {
      throw new Error("culture name should defined");
    }
    const cultureName = this.uiCultureFromAuthCodeFlow ?? appState.localization.currentCulture.cultureName;
    return this.getlocalizationResource(cultureName).pipe(map((result) => __spreadProps(__spreadValues({}, appState), {
      localization: __spreadValues(__spreadValues({}, appState.localization), result)
    })), tap(() => this.uiCultureFromAuthCodeFlow = void 0));
  }
  getlocalizationResource(cultureName) {
    return this.abpApplicationLocalizationService.get({
      cultureName,
      onlyDynamics: false
    });
  }
  refreshAppState() {
    this.updateSubject.next();
    return this.createOnUpdateStream((state) => state).pipe(take(1));
  }
  refreshLocalization(lang) {
    if (this.includeLocalizationResources) {
      return this.refreshAppState().pipe(map(() => null));
    }
    return this.getlocalizationResource(lang).pipe(tap((result) => this.store.patch({
      localization: __spreadValues(__spreadValues({}, this.store.state.localization), result)
    }))).pipe(map(() => null));
  }
  getOne$(key) {
    return this.store.sliceState((state) => state[key]);
  }
  getOne(key) {
    return this.store.state[key];
  }
  getAll$() {
    return this.store.sliceState((state) => state);
  }
  getAll() {
    return this.store.state;
  }
  getDeep$(keys) {
    keys = splitKeys(keys);
    return this.store.sliceState((state) => state).pipe(map((state) => {
      return keys.reduce((acc, val) => {
        if (acc) {
          return acc[val];
        }
        return void 0;
      }, state);
    }));
  }
  getDeep(keys) {
    keys = splitKeys(keys);
    return keys.reduce((acc, val) => {
      if (acc) {
        return acc[val];
      }
      return void 0;
    }, this.store.state);
  }
  getFeature(key) {
    return this.store.state.features?.values?.[key];
  }
  getFeature$(key) {
    return this.store.sliceState((state) => state.features?.values?.[key]);
  }
  getFeatures(keys) {
    const {
      features
    } = this.store.state;
    if (!features) return;
    return keys.reduce((acc, key) => __spreadProps(__spreadValues({}, acc), {
      [key]: features.values[key]
    }), {});
  }
  getFeatures$(keys) {
    return this.store.sliceState(({
      features
    }) => {
      if (!features?.values) return;
      return keys.reduce((acc, key) => __spreadProps(__spreadValues({}, acc), {
        [key]: features.values[key]
      }), {});
    });
  }
  isFeatureEnabled(key, features) {
    return features.values[key] === "true";
  }
  getFeatureIsEnabled(key) {
    return this.isFeatureEnabled(key, this.store.state.features);
  }
  getFeatureIsEnabled$(key) {
    return this.store.sliceState((state) => this.isFeatureEnabled(key, state.features));
  }
  getSetting(key) {
    return this.store.state.setting?.values?.[key];
  }
  getSetting$(key) {
    return this.store.sliceState((state) => state.setting?.values?.[key]);
  }
  getSettings(keyword) {
    const settings = this.store.state.setting?.values || {};
    if (!keyword) return settings;
    const keysFound = Object.keys(settings).filter((key) => key.indexOf(keyword) > -1);
    return keysFound.reduce((acc, key) => {
      acc[key] = settings[key];
      return acc;
    }, {});
  }
  getSettings$(keyword) {
    return this.store.sliceState((state) => state.setting?.values).pipe(map((settings = {}) => {
      if (!keyword) return settings;
      const keysFound = Object.keys(settings).filter((key) => key.indexOf(keyword) > -1);
      return keysFound.reduce((acc, key) => {
        acc[key] = settings[key];
        return acc;
      }, {});
    }));
  }
  getGlobalFeatures() {
    return this.store.state.globalFeatures;
  }
  getGlobalFeatures$() {
    return this.store.sliceState((state) => state.globalFeatures);
  }
  isGlobalFeatureEnabled(key, globalFeatures) {
    const features = globalFeatures.enabledFeatures || [];
    return features.some((f) => key === f);
  }
  getGlobalFeatureIsEnabled(key) {
    return this.isGlobalFeatureEnabled(key, this.store.state.globalFeatures);
  }
  getGlobalFeatureIsEnabled$(key) {
    return this.store.sliceState((state) => this.isGlobalFeatureEnabled(key, state.globalFeatures));
  }
};
_ConfigStateService.ɵfac = function ConfigStateService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ConfigStateService)(ɵɵinject(AbpApplicationConfigurationService), ɵɵinject(AbpApplicationLocalizationService), ɵɵinject(INCUDE_LOCALIZATION_RESOURCES_TOKEN, 8));
};
_ConfigStateService.ɵprov = ɵɵdefineInjectable({
  token: _ConfigStateService,
  factory: _ConfigStateService.ɵfac,
  providedIn: "root"
});
var ConfigStateService = _ConfigStateService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ConfigStateService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: AbpApplicationConfigurationService
  }, {
    type: AbpApplicationLocalizationService
  }, {
    type: void 0,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [INCUDE_LOCALIZATION_RESOURCES_TOKEN]
    }]
  }], null);
})();
function splitKeys(keys) {
  if (typeof keys === "string") {
    keys = keys.split(".");
  }
  if (!Array.isArray(keys)) {
    throw new Error("The argument must be a dot string or an string array.");
  }
  return keys;
}
var _LocalizationService = class _LocalizationService {
  /**
   * Returns currently selected language
   * Even though this looks like it's redundant to return the same value as `getLanguage()`,
   * it's actually not. This could be invoked any time, and the latestLang could be different from the
   * sessionState.getLanguage() value.
   */
  get currentLang() {
    return this.latestLang || this.sessionState.getLanguage();
  }
  get currentLang$() {
    return this.sessionState.getLanguage$();
  }
  get languageChange$() {
    return this._languageChange$.asObservable();
  }
  constructor(sessionState, injector, otherInstance, configState) {
    this.sessionState = sessionState;
    this.injector = injector;
    this.configState = configState;
    this.latestLang = this.sessionState.getLanguage();
    this._languageChange$ = new Subject();
    this.uiLocalizations$ = new BehaviorSubject(/* @__PURE__ */ new Map());
    this.localizations$ = new BehaviorSubject(/* @__PURE__ */ new Map());
    if (otherInstance) throw new Error("LocalizationService should have only one instance.");
    this.listenToSetLanguage();
    this.initLocalizationValues();
  }
  initLocalizationValues() {
    localizations$.subscribe((val) => this.addLocalization(val));
    const legacyResources$ = this.configState.getDeep$("localization.values");
    const remoteLocalizations$ = this.configState.getDeep$("localization.resources");
    const currentLanguage$ = this.sessionState.getLanguage$();
    const uiLocalizations$ = combineLatest([currentLanguage$, this.uiLocalizations$]).pipe(map(([currentLang, localizations]) => localizations.get(currentLang)));
    combineLatest([legacyResources$, remoteLocalizations$, uiLocalizations$]).pipe(map(([legacy, resource, local]) => {
      if (!resource) {
        return;
      }
      const remote = combineLegacyandNewResources(legacy || {}, resource);
      if (remote) {
        if (!local) {
          local = /* @__PURE__ */ new Map();
        }
        Object.entries(remote).forEach((entry) => {
          const resourceName = entry[0];
          const remoteTexts = entry[1];
          let resource2 = local?.get(resourceName) || {};
          resource2 = __spreadValues(__spreadValues({}, resource2), remoteTexts);
          local?.set(resourceName, resource2);
        });
      }
      return local;
    }), filter(Boolean)).subscribe((val) => this.localizations$.next(val));
  }
  addLocalization(localizations) {
    if (!localizations) return;
    const localizationMap = this.uiLocalizations$.value;
    localizations.forEach((loc) => {
      const cultureMap = localizationMap.get(loc.culture) || /* @__PURE__ */ new Map();
      loc.resources.forEach((res) => {
        let resource = cultureMap.get(res.resourceName) || {};
        resource = __spreadValues(__spreadValues({}, resource), res.texts);
        cultureMap.set(res.resourceName, resource);
      });
      localizationMap.set(loc.culture, cultureMap);
    });
    this.uiLocalizations$.next(localizationMap);
  }
  listenToSetLanguage() {
    this.sessionState.onLanguageChange$().pipe(filter((lang) => this.configState.getDeep("localization.currentCulture.cultureName") !== lang), switchMap((lang) => this.configState.refreshLocalization(lang).pipe(map(() => lang))), filter(Boolean), switchMap((lang) => from(this.registerLocale(lang).then(() => lang)))).subscribe((lang) => this._languageChange$.next(lang));
  }
  registerLocale(locale) {
    const {
      registerLocaleFn
    } = this.injector.get(CORE_OPTIONS);
    return registerLocaleFn(locale).then((module) => {
      if (module?.default) registerLocaleData(module.default);
      this.latestLang = locale;
    });
  }
  /**
   * Returns an observable localized text with the given interpolation parameters in current language.
   * @param key Localizaton key to replace with localized text
   * @param interpolateParams Values to interpolate
   */
  get(key, ...interpolateParams) {
    return this.configState.getAll$().pipe(map((state) => this.getLocalization(state, key, ...interpolateParams)));
  }
  getResource(resourceName) {
    return this.localizations$.value.get(resourceName);
  }
  getResource$(resourceName) {
    return this.localizations$.pipe(map((res) => res.get(resourceName)));
  }
  /**
   * Returns localized text with the given interpolation parameters in current language.
   * @param key Localization key to replace with localized text
   * @param interpolateParams Values to intepolate.
   */
  instant(key, ...interpolateParams) {
    return this.getLocalization(this.configState.getAll(), key, ...interpolateParams);
  }
  localize(resourceName, key, defaultValue) {
    return this.configState.getOne$("localization").pipe(map(createLocalizer), map((localize) => localize(resourceName, key, defaultValue)));
  }
  localizeSync(resourceName, key, defaultValue) {
    const localization = this.configState.getOne("localization");
    return createLocalizer(localization)(resourceName, key, defaultValue);
  }
  localizeWithFallback(resourceNames, keys, defaultValue) {
    return this.configState.getOne$("localization").pipe(map(createLocalizerWithFallback), map((localizeWithFallback) => localizeWithFallback(resourceNames, keys, defaultValue)));
  }
  localizeWithFallbackSync(resourceNames, keys, defaultValue) {
    const localization = this.configState.getOne("localization");
    return createLocalizerWithFallback(localization)(resourceNames, keys, defaultValue);
  }
  getLocalization(state, key, ...interpolateParams) {
    let defaultValue = "";
    if (!key) {
      return defaultValue;
    }
    if (typeof key !== "string") {
      defaultValue = key.defaultValue;
      key = key.key;
    }
    const keys = key.split("::");
    const warn = (message) => {
      if (isDevMode()) console.warn(message);
    };
    if (keys.length < 2) {
      warn("The localization source separator (::) not found.");
      return defaultValue || key;
    }
    if (!state.localization) return defaultValue || keys[1];
    const sourceName = keys[0] || state.localization.defaultResourceName;
    const sourceKey = keys[1];
    if (sourceName === "_") {
      return defaultValue || sourceKey;
    }
    if (!sourceName) {
      warn("Localization source name is not specified and the defaultResourceName was not defined!");
      return defaultValue || sourceKey;
    }
    const source = this.localizations$.value.get(sourceName);
    if (!source) {
      warn("Could not find localization source: " + sourceName);
      return defaultValue || sourceKey;
    }
    let localization = source[sourceKey];
    if (typeof localization === "undefined") {
      return defaultValue || sourceKey;
    }
    interpolateParams = interpolateParams.filter((params) => params != null);
    if (localization) localization = interpolate(localization, interpolateParams);
    if (typeof localization !== "string") localization = "";
    return localization || defaultValue || key;
  }
};
_LocalizationService.ɵfac = function LocalizationService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _LocalizationService)(ɵɵinject(SessionStateService), ɵɵinject(Injector), ɵɵinject(_LocalizationService, 12), ɵɵinject(ConfigStateService));
};
_LocalizationService.ɵprov = ɵɵdefineInjectable({
  token: _LocalizationService,
  factory: _LocalizationService.ɵfac,
  providedIn: "root"
});
var LocalizationService = _LocalizationService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalizationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: SessionStateService
  }, {
    type: Injector
  }, {
    type: LocalizationService,
    decorators: [{
      type: Optional
    }, {
      type: SkipSelf
    }]
  }, {
    type: ConfigStateService
  }], null);
})();
function recursivelyMergeBaseResources(baseResourceName, source) {
  const item = source[baseResourceName];
  if (item.baseResources.length === 0) {
    return item;
  }
  return item.baseResources.reduce((acc, baseResource) => {
    const baseItem = recursivelyMergeBaseResources(baseResource, source);
    const texts = __spreadValues(__spreadValues({}, baseItem.texts), item.texts);
    return __spreadProps(__spreadValues({}, acc), {
      texts
    });
  }, item);
}
function mergeResourcesWithBaseResource(resource) {
  const entities = Object.keys(resource).map((key) => {
    const newValue = recursivelyMergeBaseResources(key, resource);
    return [key, newValue];
  });
  return entities.reduce((acc, [key, value]) => __spreadProps(__spreadValues({}, acc), {
    [key]: value
  }), {});
}
function combineLegacyandNewResources(legacy, resource) {
  const mergedResource = mergeResourcesWithBaseResource(resource);
  return Object.entries(mergedResource).reduce((acc, [key, value]) => {
    return __spreadProps(__spreadValues({}, acc), {
      [key]: value.texts
    });
  }, legacy);
}
var _DynamicLayoutComponent = class _DynamicLayoutComponent {
  constructor(dynamicLayoutComponent) {
    this.layouts = inject(DYNAMIC_LAYOUTS_TOKEN);
    this.isLayoutVisible = true;
    this.router = inject(Router);
    this.route = inject(ActivatedRoute);
    this.routes = inject(RoutesService);
    this.localizationService = inject(LocalizationService);
    this.replaceableComponents = inject(ReplaceableComponentsService);
    this.subscription = inject(SubscriptionService);
    this.routerEvents = inject(RouterEvents);
    this.environment = inject(EnvironmentService);
    if (dynamicLayoutComponent) {
      if (isDevMode()) console.warn("DynamicLayoutComponent must be used only in AppComponent.");
      return;
    }
    this.checkLayoutOnNavigationEnd();
    this.listenToLanguageChange();
  }
  ngOnInit() {
    if (this.layout) {
      return;
    }
    const {
      oAuthConfig
    } = this.environment.getEnvironment();
    if (oAuthConfig.responseType === "code") {
      this.getLayout();
    }
  }
  checkLayoutOnNavigationEnd() {
    const navigationEnd$ = this.routerEvents.getNavigationEvents("End");
    this.subscription.addOne(navigationEnd$, () => this.getLayout());
  }
  getLayout() {
    let expectedLayout = this.getExtractedLayout();
    if (!expectedLayout) expectedLayout = "empty";
    if (this.layoutKey === expectedLayout) return;
    const key = this.layouts.get(expectedLayout);
    if (key) {
      this.layout = this.getComponent(key)?.component;
      this.layoutKey = expectedLayout;
    }
    if (!this.layout) {
      this.showLayoutNotFoundError(expectedLayout);
    }
  }
  getExtractedLayout() {
    const routeData = this.route.snapshot.data || {};
    let expectedLayout = routeData["layout"];
    let node = findRoute(this.routes, getRoutePath(this.router));
    node = {
      parent: node
    };
    while (node.parent) {
      node = node.parent;
      if (node.layout) {
        expectedLayout = node.layout;
        break;
      }
    }
    return expectedLayout;
  }
  showLayoutNotFoundError(layoutName) {
    let message = `Layout ${layoutName} not found.`;
    if (layoutName === "account") {
      message = 'Account layout not found. Please check your configuration. If you are using LeptonX, please make sure you have added "AccountLayoutModule.forRoot()" to your app.module configuration.';
    }
    console.warn(message);
  }
  listenToLanguageChange() {
    this.subscription.addOne(this.localizationService.languageChange$, () => {
      this.isLayoutVisible = false;
      setTimeout(() => this.isLayoutVisible = true, 0);
    });
  }
  getComponent(key) {
    return this.replaceableComponents.get(key);
  }
};
_DynamicLayoutComponent.ɵfac = function DynamicLayoutComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _DynamicLayoutComponent)(ɵɵdirectiveInject(_DynamicLayoutComponent, 12));
};
_DynamicLayoutComponent.ɵcmp = ɵɵdefineComponent({
  type: _DynamicLayoutComponent,
  selectors: [["abp-dynamic-layout"]],
  standalone: false,
  features: [ɵɵProvidersFeature([SubscriptionService])],
  decls: 1,
  vars: 1,
  consts: [[3, "ngComponentOutlet"]],
  template: function DynamicLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵconditionalCreate(0, DynamicLayoutComponent_Conditional_0_Template, 1, 1, "ng-container", 0);
    }
    if (rf & 2) {
      ɵɵconditional(ctx.isLayoutVisible ? 0 : -1);
    }
  },
  dependencies: [NgComponentOutlet],
  encapsulation: 2
});
var DynamicLayoutComponent = _DynamicLayoutComponent;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DynamicLayoutComponent, [{
    type: Component,
    args: [{
      standalone: false,
      selector: "abp-dynamic-layout",
      template: `
    @if (isLayoutVisible) {
      <ng-container [ngComponentOutlet]="layout"></ng-container>
    }
  `,
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: DynamicLayoutComponent,
    decorators: [{
      type: Optional
    }, {
      type: SkipSelf
    }]
  }], null);
})();
var _ReplaceableRouteContainerComponent = class _ReplaceableRouteContainerComponent {
  constructor(route, replaceableComponents, subscription) {
    this.route = route;
    this.replaceableComponents = replaceableComponents;
    this.subscription = subscription;
  }
  ngOnInit() {
    this.defaultComponent = this.route.snapshot.data.replaceableComponent.defaultComponent;
    this.componentKey = this.route.snapshot.data.replaceableComponent.key;
    const component$ = this.replaceableComponents.get$(this.componentKey).pipe(distinctUntilChanged());
    this.subscription.addOne(component$, (res = {}) => {
      this.externalComponent = res.component;
    });
  }
};
_ReplaceableRouteContainerComponent.ɵfac = function ReplaceableRouteContainerComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ReplaceableRouteContainerComponent)(ɵɵdirectiveInject(ActivatedRoute), ɵɵdirectiveInject(ReplaceableComponentsService), ɵɵdirectiveInject(SubscriptionService));
};
_ReplaceableRouteContainerComponent.ɵcmp = ɵɵdefineComponent({
  type: _ReplaceableRouteContainerComponent,
  selectors: [["abp-replaceable-route-container"]],
  standalone: false,
  features: [ɵɵProvidersFeature([SubscriptionService])],
  decls: 1,
  vars: 1,
  consts: [[4, "ngComponentOutlet"]],
  template: function ReplaceableRouteContainerComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵtemplate(0, ReplaceableRouteContainerComponent_ng_container_0_Template, 1, 0, "ng-container", 0);
    }
    if (rf & 2) {
      ɵɵproperty("ngComponentOutlet", ctx.externalComponent || ctx.defaultComponent);
    }
  },
  dependencies: [NgComponentOutlet],
  encapsulation: 2
});
var ReplaceableRouteContainerComponent = _ReplaceableRouteContainerComponent;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplaceableRouteContainerComponent, [{
    type: Component,
    args: [{
      standalone: false,
      selector: "abp-replaceable-route-container",
      template: `
    <ng-container *ngComponentOutlet="externalComponent || defaultComponent"></ng-container>
  `,
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: ActivatedRoute
  }, {
    type: ReplaceableComponentsService
  }, {
    type: SubscriptionService
  }], null);
})();
var _RouterOutletComponent = class _RouterOutletComponent {
};
_RouterOutletComponent.ɵfac = function RouterOutletComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _RouterOutletComponent)();
};
_RouterOutletComponent.ɵcmp = ɵɵdefineComponent({
  type: _RouterOutletComponent,
  selectors: [["abp-router-outlet"]],
  standalone: false,
  decls: 1,
  vars: 0,
  template: function RouterOutletComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵelement(0, "router-outlet");
    }
  },
  dependencies: [RouterOutlet],
  encapsulation: 2
});
var RouterOutletComponent = _RouterOutletComponent;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RouterOutletComponent, [{
    type: Component,
    args: [{
      standalone: false,
      selector: "abp-router-outlet",
      template: ` <router-outlet></router-outlet> `
    }]
  }], null, null);
})();
var differentLocales = {
  aa: "en",
  "aa-DJ": "en",
  "aa-ER": "en",
  "aa-ET": "en",
  "af-ZA": "af",
  "agq-CM": "agq",
  "ak-GH": "ak",
  "am-ET": "am",
  "ar-001": "ar",
  arn: "en",
  "arn-CL": "en",
  "as-IN": "as",
  "asa-TZ": "asa",
  "ast-ES": "ast",
  "az-Cyrl-AZ": "az-Cyrl",
  "az-Latn-AZ": "az-Latn",
  ba: "ru",
  "ba-RU": "ru",
  "bas-CM": "bas",
  "be-BY": "be",
  "bem-ZM": "bem",
  "bez-TZ": "bez",
  "bg-BG": "bg",
  bin: "en",
  "bin-NG": "en",
  "bm-Latn": "bm",
  "bm-Latn-ML": "bm",
  "bn-BD": "bn",
  "bo-CN": "bo",
  "br-FR": "br",
  "brx-IN": "brx",
  "bs-Cyrl-BA": "bs-Cyrl",
  "bs-Latn-BA": "bs-Latn",
  byn: "en",
  "byn-ER": "en",
  "ca-ES": "ca",
  "ca-ES-valencia": "ca-ES-VALENCIA",
  "ce-RU": "ce",
  "cgg-UG": "cgg",
  "chr-Cher": "chr",
  "chr-Cher-US": "chr",
  co: "en",
  "co-FR": "fr",
  "cs-CZ": "cs",
  "cu-RU": "cu",
  "cy-GB": "cy",
  "da-DK": "da",
  "dav-KE": "dav",
  "de-DE": "de",
  "dje-NE": "dje",
  "dsb-DE": "dsb",
  "dua-CM": "dua",
  dv: "en",
  "dv-MV": "en",
  "dyo-SN": "dyo",
  "dz-BT": "dz",
  "ebu-KE": "ebu",
  "ee-GH": "ee",
  "el-GR": "el",
  "en-029": "en",
  "en-ID": "en",
  "en-US": "en",
  "eo-001": "en",
  "es-ES": "es",
  "et-EE": "et",
  "eu-ES": "eu",
  "ewo-CM": "ewo",
  "fa-IR": "fa",
  "ff-Latn-SN": "ff-Latn",
  "ff-NG": "ff",
  "fi-FI": "fi",
  "fil-PH": "fil",
  "fo-FO": "fo",
  "fr-029": "fr",
  "fr-FR": "fr",
  "fur-IT": "fur",
  "fy-NL": "fy",
  "ga-IE": "ga",
  "gd-GB": "gd",
  "gl-ES": "gl",
  gn: "en",
  "gn-PY": "en",
  "gsw-CH": "gsw",
  "gu-IN": "gu",
  "guz-KE": "guz",
  "gv-IM": "gv",
  "ha-Latn": "ha",
  "ha-Latn-GH": "ha-GH",
  "ha-Latn-NE": "ha-NE",
  "ha-Latn-NG": "ha",
  "haw-US": "haw",
  "he-IL": "he",
  "hi-IN": "hi",
  "hr-HR": "hr",
  "hsb-DE": "hsb",
  "hu-HU": "hu",
  "hy-AM": "hy",
  "ia-001": "ia",
  "ia-FR": "ia",
  ibb: "en",
  "ibb-NG": "en",
  "id-ID": "id",
  "ig-NG": "ig",
  "ii-CN": "ii",
  "is-IS": "is",
  "it-IT": "it",
  iu: "en",
  "iu-Cans": "en",
  "iu-Cans-CA": "en",
  "iu-Latn": "en",
  "iu-Latn-CA": "en",
  "ja-JP": "ja",
  "jgo-CM": "jgo",
  "jmc-TZ": "jmc",
  "jv-Java": "jv",
  "jv-Java-ID": "jv",
  "jv-Latn": "jv",
  "jv-Latn-ID": "jv",
  "ka-GE": "ka",
  "kab-DZ": "kab",
  "kam-KE": "kam",
  "kde-TZ": "kde",
  "kea-CV": "kea",
  "khq-ML": "khq",
  "ki-KE": "ki",
  "kk-KZ": "kk",
  "kkj-CM": "kkj",
  "kl-GL": "kl",
  "kln-KE": "kln",
  "km-KH": "km",
  "kn-IN": "kn",
  "ko-KR": "ko",
  "kok-IN": "kok",
  kr: "en",
  "kr-NG": "en",
  "ks-Arab": "ks",
  "ks-Arab-IN": "ks",
  "ks-Deva": "ks",
  "ks-Deva-IN": "ks",
  "ksb-TZ": "ksb",
  "ksf-CM": "ksf",
  "ksh-DE": "ksh",
  "ku-Arab": "ku",
  "ku-Arab-IQ": "ku",
  "ku-Arab-IR": "ku",
  "kw-GB": "kw",
  "ky-KG": "ky",
  la: "en",
  "la-001": "en",
  "lag-TZ": "lag",
  "lb-LU": "lb",
  "lg-UG": "lg",
  "lkt-US": "lkt",
  "ln-CD": "ln",
  "lo-LA": "lo",
  "lrc-IR": "lrc",
  "lt-LT": "lt",
  "lu-CD": "lu",
  "luo-KE": "luo",
  "luy-KE": "luy",
  "lv-LV": "lv",
  "mas-KE": "mas",
  "mer-KE": "mer",
  "mfe-MU": "mfe",
  "mg-MG": "mg",
  "mgh-MZ": "mgh",
  "mgo-CM": "mgo",
  "mi-NZ": "mi",
  "mk-MK": "mk",
  "ml-IN": "ml",
  "mn-Cyrl": "mn",
  "mn-MN": "mn",
  "mn-Mong": "mn",
  "mn-Mong-CN": "mn",
  "mn-Mong-MN": "mn",
  mni: "en",
  "mni-IN": "en",
  moh: "en",
  "moh-CA": "en",
  "mr-IN": "mr",
  "ms-MY": "ms",
  "mt-MT": "mt",
  "mua-CM": "mua",
  "my-MM": "my",
  "mzn-IR": "mzn",
  "naq-NA": "naq",
  "nb-NO": "nb",
  "nd-ZW": "nd",
  "ne-NP": "ne",
  "nl-NL": "nl",
  "nmg-CM": "ngm",
  "nn-NO": "nn",
  "nnh-CM": "nnh",
  no: "en",
  nqo: "en",
  "nqo-GN": "en",
  nr: "en",
  "nr-ZA": "en",
  nso: "en",
  "nso-ZA": "en",
  "nus-SS": "nus",
  "nyn-UG": "nyn",
  oc: "en",
  "oc-FR": "fr",
  "om-ET": "om",
  "or-IN": "or",
  "os-GE": "os",
  "pa-Arab-PK": "pa-Arab",
  "pa-IN": "pa",
  pap: "en",
  "pap-029": "en",
  "pl-PL": "pl",
  "prg-001": "prg",
  prs: "en",
  "prs-AF": "en",
  "ps-AF": "ps",
  "pt-BR": "pt",
  quc: "en",
  "quc-Latn": "en",
  "quc-Latn-GT": "en",
  quz: "en",
  "quz-BO": "en",
  "quz-EC": "en",
  "quz-PE": "en",
  "rm-CH": "rm",
  "rn-BI": "rn",
  "ro-RO": "ro",
  "rof-TZ": "rof",
  "ru-RU": "ru",
  "rw-RW": "rw",
  "rwk-TZ": "rwk",
  sa: "en",
  "sa-IN": "en",
  "sah-RU": "sah",
  "saq-KE": "saq",
  "sbp-TZ": "en",
  "sd-Arab": "sd",
  "sd-Arab-PK": "sd",
  "sd-Deva": "sd",
  "sd-Deva-IN": "sd",
  "se-NO": "se",
  "seh-MZ": "seh",
  "ses-ML": "ses",
  "sg-CF": "sg",
  "shi-Latn-MA": "shi-Latn",
  "shi-Tfng-MA": "shi-Tfng",
  "si-LK": "si",
  "sk-SK": "sk",
  "sl-SI": "sl",
  sma: "en",
  "sma-NO": "en",
  "sma-SE": "en",
  smj: "en",
  "smj-NO": "en",
  "smj-SE": "en",
  "smn-FI": "en",
  sms: "en",
  "sms-FI": "en",
  "sn-Latn": "sn",
  "sn-Latn-ZW": "sn",
  "so-SO": "so",
  "sq-AL": "so",
  "sr-Cyrl-RS": "sr-Cryl",
  "sr-Latn-RS": "sr-Latn",
  ss: "en",
  "ss-SZ": "en",
  "ss-ZA": "en",
  ssy: "en",
  "ssy-ER": "en",
  st: "en",
  "st-LS": "en",
  "st-ZA": "en",
  "sv-SE": "sv",
  "sw-TZ": "sw",
  syr: "en",
  "syr-SY": "en",
  "ta-IN": "ta",
  "te-IN": "te",
  "teo-UG": "teo",
  "tg-Cyrl": "tg",
  "tg-Cyrl-TJ": "tg",
  "th-TH": "th",
  "ti-ET": "ti",
  tig: "en",
  "tig-ER": "en",
  "tk-TM": "tk",
  tn: "en",
  "tn-BW": "en",
  "tn-ZA": "en",
  "to-TO": "to",
  "tr-TR": "tr",
  ts: "en",
  "ts-ZA": "en",
  "tt-RU": "tt",
  "twq-NE": "twq",
  "tzm-Arab": "tzm",
  "tzm-Arab-MA": "tzm",
  "tzm-Latn": "tzm",
  "tzm-Latn-DZ": "tzm",
  "tzm-Latn-MA": "tzm",
  "tzm-Tfng": "tzm",
  "tzm-Tfng-MA": "tzm",
  "ug-CN": "ug",
  "uk-UA": "uk",
  "ur-PK": "ur",
  "uz-Arab-AF": "uz-Arab",
  "uz-Cyrl-UZ": "uz-Cyrl",
  "uz-Latn-UZ": "uz-Latn",
  "vai-Latn-LR": "vai-Latn",
  "vai-Vaii-LR": "vai-Vaii",
  ve: "en",
  "ve-ZA": "en",
  "vi-VN": "vi",
  "vo-001": "vo",
  "vun-TZ": "vun",
  "wae-CH": "wae",
  wal: "en",
  "wal-ET": "en",
  "wo-SN": "wo",
  "xh-ZA": "xh",
  "xog-UG": "xog",
  "yav-CM": "yav",
  "yi-001": "yi",
  "yo-NG": "yo",
  "zgh-Tfng": "zgh",
  "zgh-Tfng-MA": "zgh",
  "zh-CN": "zh",
  "zh-HK": "zh",
  "zh-MO": "zh",
  "zh-SG": "zh",
  "zh-TW": "zh",
  "zu-ZA": "zu"
};
var DEFAULT_DYNAMIC_LAYOUTS = /* @__PURE__ */ new Map([[
  "application",
  "Theme.ApplicationLayoutComponent"
  /* eThemeSharedComponents.ApplicationLayoutComponent */
], [
  "account",
  "Theme.AccountLayoutComponent"
  /* eThemeSharedComponents.AccountLayoutComponent */
], [
  "empty",
  "Theme.EmptyLayoutComponent"
  /* eThemeSharedComponents.EmptyLayoutComponent */
]]);
var _AutofocusDirective = class _AutofocusDirective {
  set delay(val) {
    this._delay = Number(val) || 0;
  }
  get delay() {
    return this._delay;
  }
  constructor(elRef) {
    this.elRef = elRef;
    this._delay = 0;
  }
  ngAfterViewInit() {
    setTimeout(() => this.elRef.nativeElement.focus(), this.delay);
  }
};
_AutofocusDirective.ɵfac = function AutofocusDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AutofocusDirective)(ɵɵdirectiveInject(ElementRef));
};
_AutofocusDirective.ɵdir = ɵɵdefineDirective({
  type: _AutofocusDirective,
  selectors: [["", "autofocus", ""]],
  inputs: {
    delay: [0, "autofocus", "delay"]
  }
});
var AutofocusDirective = _AutofocusDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AutofocusDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[autofocus]"
    }]
  }], () => [{
    type: ElementRef
  }], {
    delay: [{
      type: Input,
      args: ["autofocus"]
    }]
  });
})();
var _InputEventDebounceDirective = class _InputEventDebounceDirective {
  constructor(el, subscription) {
    this.el = el;
    this.subscription = subscription;
    this.debounce = 300;
    this.debounceEvent = new EventEmitter();
  }
  ngOnInit() {
    const input$ = fromEvent(this.el.nativeElement, "input").pipe(debounceTime(this.debounce));
    this.subscription.addOne(input$, (event) => {
      this.debounceEvent.emit(event);
    });
  }
};
_InputEventDebounceDirective.ɵfac = function InputEventDebounceDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _InputEventDebounceDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(SubscriptionService));
};
_InputEventDebounceDirective.ɵdir = ɵɵdefineDirective({
  type: _InputEventDebounceDirective,
  selectors: [["", "input.debounce", ""]],
  inputs: {
    debounce: "debounce"
  },
  outputs: {
    debounceEvent: "input.debounce"
  },
  features: [ɵɵProvidersFeature([SubscriptionService])]
});
var InputEventDebounceDirective = _InputEventDebounceDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InputEventDebounceDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[input.debounce]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: SubscriptionService
  }], {
    debounce: [{
      type: Input
    }],
    debounceEvent: [{
      type: Output,
      args: ["input.debounce"]
    }]
  });
})();
var AbpForContext = class {
  constructor($implicit, index2, count, list) {
    this.$implicit = $implicit;
    this.index = index2;
    this.count = count;
    this.list = list;
  }
};
var RecordView = class {
  constructor(record, view) {
    this.record = record;
    this.view = view;
  }
};
var _ForDirective = class _ForDirective {
  get compareFn() {
    return this.compareBy || collectionCompare;
  }
  get trackByFn() {
    return this.trackBy || ((index2, item) => item.id || index2);
  }
  constructor(tempRef, vcRef, differs) {
    this.tempRef = tempRef;
    this.vcRef = vcRef;
    this.differs = differs;
  }
  iterateOverAppliedOperations(changes) {
    const rw = [];
    changes.forEachOperation((record, previousIndex, currentIndex) => {
      if (record.previousIndex == null) {
        const view = this.vcRef.createEmbeddedView(this.tempRef, new AbpForContext(null, -1, -1, this.items), currentIndex || 0);
        rw.push(new RecordView(record, view));
      } else if (currentIndex == null && previousIndex !== null) {
        this.vcRef.remove(previousIndex);
      } else {
        if (previousIndex !== null) {
          const view = this.vcRef.get(previousIndex);
          if (view && currentIndex !== null) {
            this.vcRef.move(view, currentIndex);
            rw.push(new RecordView(record, view));
          }
        }
      }
    });
    for (let i = 0, l2 = rw.length; i < l2; i++) {
      rw[i].view.context.$implicit = rw[i].record.item;
    }
  }
  iterateOverAttachedViews(changes) {
    for (let i = 0, l2 = this.vcRef.length; i < l2; i++) {
      const viewRef = this.vcRef.get(i);
      viewRef.context.index = i;
      viewRef.context.count = l2;
      viewRef.context.list = this.items;
    }
    changes.forEachIdentityChange((record) => {
      if (record.currentIndex !== null) {
        const viewRef = this.vcRef.get(record.currentIndex);
        viewRef.context.$implicit = record.item;
      }
    });
  }
  projectItems(items) {
    if (!items.length && this.emptyRef) {
      this.vcRef.clear();
      this.vcRef.createEmbeddedView(this.emptyRef).rootNodes;
      this.isShowEmptyRef = true;
      this.differ = null;
      return;
    }
    if (this.emptyRef && this.isShowEmptyRef) {
      this.vcRef.clear();
      this.isShowEmptyRef = false;
    }
    if (!this.differ && items) {
      this.differ = this.differs.find(items).create(this.trackByFn);
    }
    if (this.differ) {
      const changes = this.differ.diff(items);
      if (changes) {
        this.iterateOverAppliedOperations(changes);
        this.iterateOverAttachedViews(changes);
      }
    }
  }
  sortItems(items) {
    const orderBy = this.orderBy;
    if (orderBy) {
      items.sort((a, b) => a[orderBy] > b[orderBy] ? 1 : a[orderBy] < b[orderBy] ? -1 : 0);
    } else {
      items.sort();
    }
  }
  ngOnChanges() {
    let items = collectionClone(this.items);
    if (!Array.isArray(items)) return;
    const compareFn = this.compareFn;
    const filterBy = this.filterBy;
    if (typeof filterBy !== "undefined" && typeof this.filterVal !== "undefined" && this.filterVal !== "") {
      items = items.filter((item) => compareFn(item[filterBy], this.filterVal));
    }
    switch (this.orderDir) {
      case "ASC":
        this.sortItems(items);
        this.projectItems(items);
        break;
      case "DESC":
        this.sortItems(items);
        items.reverse();
        this.projectItems(items);
        break;
      default:
        this.projectItems(items);
    }
  }
};
_ForDirective.ɵfac = function ForDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ForDirective)(ɵɵdirectiveInject(TemplateRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(IterableDiffers));
};
_ForDirective.ɵdir = ɵɵdefineDirective({
  type: _ForDirective,
  selectors: [["", "abpFor", ""]],
  inputs: {
    items: [0, "abpForOf", "items"],
    orderBy: [0, "abpForOrderBy", "orderBy"],
    orderDir: [0, "abpForOrderDir", "orderDir"],
    filterBy: [0, "abpForFilterBy", "filterBy"],
    filterVal: [0, "abpForFilterVal", "filterVal"],
    trackBy: [0, "abpForTrackBy", "trackBy"],
    compareBy: [0, "abpForCompareBy", "compareBy"],
    emptyRef: [0, "abpForEmptyRef", "emptyRef"]
  },
  features: [ɵɵNgOnChangesFeature]
});
var ForDirective = _ForDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ForDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpFor]"
    }]
  }], () => [{
    type: TemplateRef
  }, {
    type: ViewContainerRef
  }, {
    type: IterableDiffers
  }], {
    items: [{
      type: Input,
      args: ["abpForOf"]
    }],
    orderBy: [{
      type: Input,
      args: ["abpForOrderBy"]
    }],
    orderDir: [{
      type: Input,
      args: ["abpForOrderDir"]
    }],
    filterBy: [{
      type: Input,
      args: ["abpForFilterBy"]
    }],
    filterVal: [{
      type: Input,
      args: ["abpForFilterVal"]
    }],
    trackBy: [{
      type: Input,
      args: ["abpForTrackBy"]
    }],
    compareBy: [{
      type: Input,
      args: ["abpForCompareBy"]
    }],
    emptyRef: [{
      type: Input,
      args: ["abpForEmptyRef"]
    }]
  });
})();
var _FormSubmitDirective = class _FormSubmitDirective {
  constructor(formGroupDirective, host, cdRef, subscription) {
    this.formGroupDirective = formGroupDirective;
    this.host = host;
    this.cdRef = cdRef;
    this.subscription = subscription;
    this.debounce = 200;
    this.markAsDirtyWhenSubmit = true;
    this.ngSubmit = new EventEmitter();
    this.executedNgSubmit = false;
  }
  ngOnInit() {
    this.subscription.addOne(this.formGroupDirective.ngSubmit, () => {
      if (this.markAsDirtyWhenSubmit) {
        this.markAsDirty();
      }
      this.executedNgSubmit = true;
    });
    const keyup$ = fromEvent(this.host.nativeElement, "keyup").pipe(debounceTime(this.debounce), filter((event) => !(event.target instanceof HTMLTextAreaElement)), filter((event) => event && event.key === "Enter"));
    this.subscription.addOne(keyup$, () => {
      if (!this.executedNgSubmit) {
        this.host.nativeElement.dispatchEvent(new Event("submit", {
          bubbles: true,
          cancelable: true
        }));
      }
      this.executedNgSubmit = false;
    });
  }
  markAsDirty() {
    const {
      form
    } = this.formGroupDirective;
    setDirty(form.controls);
    form.markAsDirty();
    this.cdRef.detectChanges();
  }
};
_FormSubmitDirective.ɵfac = function FormSubmitDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _FormSubmitDirective)(ɵɵdirectiveInject(FormGroupDirective, 2), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(SubscriptionService));
};
_FormSubmitDirective.ɵdir = ɵɵdefineDirective({
  type: _FormSubmitDirective,
  selectors: [["form", "ngSubmit", "", "formGroup", ""]],
  inputs: {
    debounce: "debounce",
    notValidateOnSubmit: "notValidateOnSubmit",
    markAsDirtyWhenSubmit: "markAsDirtyWhenSubmit"
  },
  outputs: {
    ngSubmit: "ngSubmit"
  },
  features: [ɵɵProvidersFeature([SubscriptionService])]
});
var FormSubmitDirective = _FormSubmitDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FormSubmitDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "form[ngSubmit][formGroup]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: FormGroupDirective,
    decorators: [{
      type: Self
    }]
  }, {
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: SubscriptionService
  }], {
    debounce: [{
      type: Input
    }],
    notValidateOnSubmit: [{
      type: Input
    }],
    markAsDirtyWhenSubmit: [{
      type: Input
    }],
    ngSubmit: [{
      type: Output
    }]
  });
})();
function setDirty(controls) {
  if (Array.isArray(controls)) {
    controls.forEach((group) => {
      setDirty(group.controls);
    });
    return;
  }
  Object.keys(controls).forEach((key) => {
    controls[key].markAsDirty();
    controls[key].updateValueAndValidity();
  });
}
var _InitDirective = class _InitDirective {
  constructor(elRef) {
    this.elRef = elRef;
    this.init = new EventEmitter();
  }
  ngAfterViewInit() {
    this.init.emit(this.elRef);
  }
};
_InitDirective.ɵfac = function InitDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _InitDirective)(ɵɵdirectiveInject(ElementRef));
};
_InitDirective.ɵdir = ɵɵdefineDirective({
  type: _InitDirective,
  selectors: [["", "abpInit", ""]],
  outputs: {
    init: "abpInit"
  }
});
var InitDirective = _InitDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InitDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpInit]"
    }]
  }], () => [{
    type: ElementRef
  }], {
    init: [{
      type: Output,
      args: ["abpInit"]
    }]
  });
})();
var _PermissionDirective = class _PermissionDirective {
  constructor(templateRef, vcRef, permissionService, cdRef, queue) {
    this.templateRef = templateRef;
    this.vcRef = vcRef;
    this.permissionService = permissionService;
    this.cdRef = cdRef;
    this.queue = queue;
    this.runChangeDetection = true;
    this.cdrSubject = new ReplaySubject();
    this.rendered = false;
  }
  check() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.subscription = this.permissionService.getGrantedPolicy$(this.condition || "").pipe(distinctUntilChanged()).subscribe((isGranted) => {
      this.vcRef.clear();
      if (isGranted) this.vcRef.createEmbeddedView(this.templateRef);
      if (this.runChangeDetection) {
        if (!this.rendered) {
          this.cdrSubject.next();
        } else {
          this.cdRef.detectChanges();
        }
      } else {
        this.cdRef.markForCheck();
      }
    });
  }
  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe();
  }
  ngOnChanges() {
    this.check();
  }
  ngAfterViewInit() {
    this.cdrSubject.pipe(take(1)).subscribe(() => this.queue.add(() => this.cdRef.detectChanges()));
    this.rendered = true;
  }
};
_PermissionDirective.ɵfac = function PermissionDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _PermissionDirective)(ɵɵdirectiveInject(TemplateRef, 8), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(PermissionService), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(QUEUE_MANAGER));
};
_PermissionDirective.ɵdir = ɵɵdefineDirective({
  type: _PermissionDirective,
  selectors: [["", "abpPermission", ""]],
  inputs: {
    condition: [0, "abpPermission", "condition"],
    runChangeDetection: [0, "abpPermissionRunChangeDetection", "runChangeDetection"]
  },
  features: [ɵɵNgOnChangesFeature]
});
var PermissionDirective = _PermissionDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpPermission]"
    }]
  }], () => [{
    type: TemplateRef,
    decorators: [{
      type: Optional
    }]
  }, {
    type: ViewContainerRef
  }, {
    type: PermissionService
  }, {
    type: ChangeDetectorRef
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [QUEUE_MANAGER]
    }]
  }], {
    condition: [{
      type: Input,
      args: ["abpPermission"]
    }],
    runChangeDetection: [{
      type: Input,
      args: ["abpPermissionRunChangeDetection"]
    }]
  });
})();
var _ReplaceableTemplateDirective = class _ReplaceableTemplateDirective {
  constructor(injector, templateRef, vcRef, replaceableComponents, subscription) {
    this.injector = injector;
    this.templateRef = templateRef;
    this.vcRef = vcRef;
    this.replaceableComponents = replaceableComponents;
    this.subscription = subscription;
    this.providedData = {
      inputs: {},
      outputs: {}
    };
    this.context = {};
    this.defaultComponentSubscriptions = {};
    this.initialized = false;
    this.context = {
      initTemplate: (ref) => {
        this.resetDefaultComponent();
        this.defaultComponentRef = ref;
        this.setDefaultComponentInputs();
      }
    };
  }
  ngOnInit() {
    const component$ = this.replaceableComponents.get$(this.data.componentKey).pipe(filter((res = {}) => !this.initialized || !collectionCompare(res.component, this.externalComponent)));
    this.subscription.addOne(component$, (res = {}) => {
      this.vcRef.clear();
      this.externalComponent = res.component;
      if (this.defaultComponentRef) {
        this.resetDefaultComponent();
      }
      if (res.component) {
        this.setProvidedData();
        const customInjector = Injector.create({
          providers: [{
            provide: "REPLACEABLE_DATA",
            useValue: this.providedData
          }],
          parent: this.injector
        });
        const ref = this.vcRef.createComponent(res.component, {
          index: 0,
          injector: customInjector
        });
      } else {
        this.vcRef.createEmbeddedView(this.templateRef, this.context);
      }
      this.initialized = true;
    });
  }
  ngOnChanges(changes) {
    if (changes?.data?.currentValue?.inputs && this.defaultComponentRef) {
      this.setDefaultComponentInputs();
    }
  }
  setDefaultComponentInputs() {
    if (!this.defaultComponentRef || !this.data.inputs && !this.data.outputs) return;
    if (this.data.inputs) {
      for (const key in this.data.inputs) {
        if (Object.prototype.hasOwnProperty.call(this.data.inputs, key)) {
          if (!collectionCompare(this.defaultComponentRef[key], this.data.inputs[key].value)) {
            this.defaultComponentRef[key] = this.data.inputs[key].value;
          }
        }
      }
    }
    if (this.data.outputs) {
      for (const key in this.data.outputs) {
        if (Object.prototype.hasOwnProperty.call(this.data.outputs, key)) {
          if (!this.defaultComponentSubscriptions[key]) {
            this.defaultComponentSubscriptions[key] = this.defaultComponentRef[key].subscribe((value) => {
              this.data.outputs?.[key](value);
            });
          }
        }
      }
    }
  }
  setProvidedData() {
    this.providedData = __spreadProps(__spreadValues({
      outputs: {}
    }, this.data), {
      inputs: {}
    });
    if (!this.data.inputs) return;
    Object.defineProperties(this.providedData.inputs, __spreadValues({}, Object.keys(this.data.inputs).reduce((acc, key) => __spreadProps(__spreadValues({}, acc), {
      [key]: __spreadValues({
        enumerable: true,
        configurable: true,
        get: () => this.data.inputs?.[key]?.value
      }, this.data.inputs?.[key]?.twoWay && {
        set: (newValue) => {
          if (this.data.inputs?.[key]) {
            this.data.inputs[key].value = newValue;
          }
          if (this.data.outputs?.[`${key}Change`]) {
            this.data.outputs[`${key}Change`](newValue);
          }
        }
      })
    }), {})));
  }
  resetDefaultComponent() {
    Object.keys(this.defaultComponentSubscriptions).forEach((key) => {
      this.defaultComponentSubscriptions[key].unsubscribe();
    });
    this.defaultComponentSubscriptions = {};
    this.defaultComponentRef = null;
  }
};
_ReplaceableTemplateDirective.ɵfac = function ReplaceableTemplateDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ReplaceableTemplateDirective)(ɵɵdirectiveInject(Injector), ɵɵdirectiveInject(TemplateRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(ReplaceableComponentsService), ɵɵdirectiveInject(SubscriptionService));
};
_ReplaceableTemplateDirective.ɵdir = ɵɵdefineDirective({
  type: _ReplaceableTemplateDirective,
  selectors: [["", "abpReplaceableTemplate", ""]],
  inputs: {
    data: [0, "abpReplaceableTemplate", "data"]
  },
  features: [ɵɵProvidersFeature([SubscriptionService]), ɵɵNgOnChangesFeature]
});
var ReplaceableTemplateDirective = _ReplaceableTemplateDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplaceableTemplateDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpReplaceableTemplate]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: Injector
  }, {
    type: TemplateRef
  }, {
    type: ViewContainerRef
  }, {
    type: ReplaceableComponentsService
  }, {
    type: SubscriptionService
  }], {
    data: [{
      type: Input,
      args: ["abpReplaceableTemplate"]
    }]
  });
})();
var _StopPropagationDirective = class _StopPropagationDirective {
  constructor(el, subscription) {
    this.el = el;
    this.subscription = subscription;
    this.stopPropEvent = new EventEmitter();
  }
  ngOnInit() {
    this.subscription.addOne(fromEvent(this.el.nativeElement, "click"), (event) => {
      event.stopPropagation();
      this.stopPropEvent.emit(event);
    });
  }
};
_StopPropagationDirective.ɵfac = function StopPropagationDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _StopPropagationDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(SubscriptionService));
};
_StopPropagationDirective.ɵdir = ɵɵdefineDirective({
  type: _StopPropagationDirective,
  selectors: [["", "click.stop", ""]],
  outputs: {
    stopPropEvent: "click.stop"
  },
  features: [ɵɵProvidersFeature([SubscriptionService])]
});
var StopPropagationDirective = _StopPropagationDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StopPropagationDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[click.stop]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: SubscriptionService
  }], {
    stopPropEvent: [{
      type: Output,
      args: ["click.stop"]
    }]
  });
})();
var _LocalizationPipe = class _LocalizationPipe {
  constructor(localization) {
    this.localization = localization;
  }
  transform(value = "", ...interpolateParams) {
    const params = interpolateParams.reduce((acc, val) => {
      if (!acc) {
        return val;
      }
      if (!val) {
        return acc;
      }
      return Array.isArray(val) ? [...acc, ...val] : [...acc, val];
    }, []) || [];
    return this.localization.instant(value, ...params);
  }
};
_LocalizationPipe.ɵfac = function LocalizationPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _LocalizationPipe)(ɵɵdirectiveInject(LocalizationService, 16));
};
_LocalizationPipe.ɵpipe = ɵɵdefinePipe({
  name: "abpLocalization",
  type: _LocalizationPipe,
  pure: true,
  standalone: false
});
_LocalizationPipe.ɵprov = ɵɵdefineInjectable({
  token: _LocalizationPipe,
  factory: _LocalizationPipe.ɵfac
});
var LocalizationPipe = _LocalizationPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalizationPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      standalone: false,
      name: "abpLocalization"
    }]
  }], () => [{
    type: LocalizationService
  }], null);
})();
var _SafeHtmlPipe = class _SafeHtmlPipe {
  constructor() {
    this.sanitizer = inject(DomSanitizer);
  }
  transform(value) {
    if (typeof value !== "string") return "";
    return this.sanitizer.sanitize(SecurityContext.HTML, value);
  }
};
_SafeHtmlPipe.ɵfac = function SafeHtmlPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _SafeHtmlPipe)();
};
_SafeHtmlPipe.ɵpipe = ɵɵdefinePipe({
  name: "abpSafeHtml",
  type: _SafeHtmlPipe,
  pure: true,
  standalone: false
});
_SafeHtmlPipe.ɵprov = ɵɵdefineInjectable({
  token: _SafeHtmlPipe,
  factory: _SafeHtmlPipe.ɵfac
});
var SafeHtmlPipe = _SafeHtmlPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SafeHtmlPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      standalone: false,
      name: "abpSafeHtml"
    }]
  }], null, null);
})();
var _SortPipe = class _SortPipe {
  transform(value, sortOrder = "asc", sortKey) {
    sortOrder = sortOrder && sortOrder.toLowerCase();
    if (!value || sortOrder !== "asc" && sortOrder !== "desc") return value;
    let numberArray = [];
    let stringArray = [];
    if (!sortKey) {
      numberArray = value.filter((item) => typeof item === "number").sort();
      stringArray = value.filter((item) => typeof item === "string").sort();
    } else {
      numberArray = value.filter((item) => typeof item[sortKey] === "number").sort((a, b) => a[sortKey] - b[sortKey]);
      stringArray = value.filter((item) => typeof item[sortKey] === "string").sort((a, b) => {
        if (a[sortKey] < b[sortKey]) return -1;
        else if (a[sortKey] > b[sortKey]) return 1;
        else return 0;
      });
    }
    const sorted = [...numberArray, ...stringArray, ...value.filter((item) => typeof (sortKey ? item[sortKey] : item) !== "number" && typeof (sortKey ? item[sortKey] : item) !== "string")];
    return sortOrder === "asc" ? sorted : sorted.reverse();
  }
};
_SortPipe.ɵfac = function SortPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _SortPipe)();
};
_SortPipe.ɵpipe = ɵɵdefinePipe({
  name: "abpSort",
  type: _SortPipe,
  pure: true,
  standalone: false
});
_SortPipe.ɵprov = ɵɵdefineInjectable({
  token: _SortPipe,
  factory: _SortPipe.ɵfac
});
var SortPipe = _SortPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SortPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      standalone: false,
      name: "abpSort"
    }]
  }], null, null);
})();
var INJECTOR_PIPE_DATA_TOKEN = new InjectionToken("INJECTOR_PIPE_DATA_TOKEN");
var _ToInjectorPipe = class _ToInjectorPipe {
  constructor(injector) {
    this.injector = injector;
  }
  transform(value, token = INJECTOR_PIPE_DATA_TOKEN, name = "ToInjectorPipe") {
    return Injector.create({
      providers: [{
        provide: token,
        useValue: value
      }],
      parent: this.injector,
      name
    });
  }
};
_ToInjectorPipe.ɵfac = function ToInjectorPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ToInjectorPipe)(ɵɵdirectiveInject(Injector, 16));
};
_ToInjectorPipe.ɵpipe = ɵɵdefinePipe({
  name: "toInjector",
  type: _ToInjectorPipe,
  pure: true,
  standalone: false
});
var ToInjectorPipe = _ToInjectorPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ToInjectorPipe, [{
    type: Pipe,
    args: [{
      standalone: false,
      name: "toInjector"
    }]
  }], () => [{
    type: Injector
  }], null);
})();
var _ShortDatePipe = class _ShortDatePipe extends DatePipe {
  constructor(configStateService, locale, defaultTimezone) {
    super(locale, defaultTimezone);
    this.configStateService = configStateService;
  }
  transform(value, timezone, locale) {
    const format = getShortDateFormat(this.configStateService);
    return super.transform(value, format, timezone, locale);
  }
};
_ShortDatePipe.ɵfac = function ShortDatePipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ShortDatePipe)(ɵɵdirectiveInject(ConfigStateService, 16), ɵɵdirectiveInject(LOCALE_ID, 16), ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));
};
_ShortDatePipe.ɵpipe = ɵɵdefinePipe({
  name: "shortDate",
  type: _ShortDatePipe,
  pure: true,
  standalone: false
});
var ShortDatePipe = _ShortDatePipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShortDatePipe, [{
    type: Pipe,
    args: [{
      standalone: false,
      name: "shortDate",
      pure: true
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOCALE_ID]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DATE_PIPE_DEFAULT_TIMEZONE]
    }, {
      type: Optional
    }]
  }], null);
})();
var _ShortTimePipe = class _ShortTimePipe extends DatePipe {
  constructor(configStateService, locale, defaultTimezone) {
    super(locale, defaultTimezone);
    this.configStateService = configStateService;
  }
  transform(value, timezone, locale) {
    const format = getShortTimeFormat(this.configStateService);
    return super.transform(value, format, timezone, locale);
  }
};
_ShortTimePipe.ɵfac = function ShortTimePipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ShortTimePipe)(ɵɵdirectiveInject(ConfigStateService, 16), ɵɵdirectiveInject(LOCALE_ID, 16), ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));
};
_ShortTimePipe.ɵpipe = ɵɵdefinePipe({
  name: "shortTime",
  type: _ShortTimePipe,
  pure: true,
  standalone: false
});
var ShortTimePipe = _ShortTimePipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShortTimePipe, [{
    type: Pipe,
    args: [{
      standalone: false,
      name: "shortTime",
      pure: true
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOCALE_ID]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DATE_PIPE_DEFAULT_TIMEZONE]
    }, {
      type: Optional
    }]
  }], null);
})();
var _ShortDateTimePipe = class _ShortDateTimePipe extends DatePipe {
  constructor(configStateService, locale, defaultTimezone) {
    super(locale, defaultTimezone);
    this.configStateService = configStateService;
  }
  transform(value, timezone, locale) {
    const format = getShortDateShortTimeFormat(this.configStateService);
    return super.transform(value, format, timezone, locale);
  }
};
_ShortDateTimePipe.ɵfac = function ShortDateTimePipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ShortDateTimePipe)(ɵɵdirectiveInject(ConfigStateService, 16), ɵɵdirectiveInject(LOCALE_ID, 16), ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));
};
_ShortDateTimePipe.ɵpipe = ɵɵdefinePipe({
  name: "shortDateTime",
  type: _ShortDateTimePipe,
  pure: true,
  standalone: false
});
var ShortDateTimePipe = _ShortDateTimePipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShortDateTimePipe, [{
    type: Pipe,
    args: [{
      standalone: false,
      name: "shortDateTime",
      pure: true
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOCALE_ID]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DATE_PIPE_DEFAULT_TIMEZONE]
    }, {
      type: Optional
    }]
  }], null);
})();
var _UtcToLocalPipe = class _UtcToLocalPipe {
  constructor() {
    this.timezoneService = inject(TimezoneService);
    this.timeService = inject(TimeService);
    this.configState = inject(ConfigStateService);
    this.localizationService = inject(LocalizationService);
    this.locale = inject(LOCALE_ID);
  }
  transform(value, type) {
    if (!value) return "";
    const date = new Date(value);
    if (isNaN(date.getTime())) return "";
    const format = this.getFormat(type);
    try {
      if (this.timezoneService.isUtcClockEnabled) {
        const timeZone = this.timezoneService.timezone;
        return this.timeService.formatDateWithStandardOffset(date, format, timeZone);
      } else {
        return this.timeService.formatWithoutTimeZone(date, format);
      }
    } catch (err) {
      return value;
    }
  }
  getFormat(propType) {
    switch (propType) {
      case "date":
        return getShortDateFormat(this.configState);
      case "time":
        return getShortTimeFormat(this.configState);
      case "datetime":
      default:
        return getShortDateShortTimeFormat(this.configState);
    }
  }
};
_UtcToLocalPipe.ɵfac = function UtcToLocalPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _UtcToLocalPipe)();
};
_UtcToLocalPipe.ɵpipe = ɵɵdefinePipe({
  name: "abpUtcToLocal",
  type: _UtcToLocalPipe,
  pure: true
});
_UtcToLocalPipe.ɵprov = ɵɵdefineInjectable({
  token: _UtcToLocalPipe,
  factory: _UtcToLocalPipe.ɵfac
});
var UtcToLocalPipe = _UtcToLocalPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UtcToLocalPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      name: "abpUtcToLocal"
    }]
  }], null, null);
})();
var _LazyLocalizationPipe = class _LazyLocalizationPipe {
  constructor() {
    this.localizationService = inject(LocalizationService);
    this.configStateService = inject(ConfigStateService);
  }
  transform(key, ...params) {
    if (!key) {
      return of("");
    }
    const flatParams = params.reduce((acc, val) => Array.isArray(val) ? acc.concat(val) : [...acc, val], []);
    return this.configStateService.getAll$().pipe(filter((config) => !!config.localization), take(1), switchMap(() => this.localizationService.get(key, ...flatParams)), map((translation) => translation && translation !== key ? translation : ""), startWith(""), distinctUntilChanged());
  }
};
_LazyLocalizationPipe.ɵfac = function LazyLocalizationPipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _LazyLocalizationPipe)();
};
_LazyLocalizationPipe.ɵpipe = ɵɵdefinePipe({
  name: "abpLazyLocalization",
  type: _LazyLocalizationPipe,
  pure: true
});
_LazyLocalizationPipe.ɵprov = ɵɵdefineInjectable({
  token: _LazyLocalizationPipe,
  factory: _LazyLocalizationPipe.ɵfac
});
var LazyLocalizationPipe = _LazyLocalizationPipe;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LazyLocalizationPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      name: "abpLazyLocalization"
    }]
  }], null, null);
})();
var _LocalizationModule = class _LocalizationModule {
};
_LocalizationModule.ɵfac = function LocalizationModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _LocalizationModule)();
};
_LocalizationModule.ɵmod = ɵɵdefineNgModule({
  type: _LocalizationModule,
  declarations: [LocalizationPipe],
  imports: [LazyLocalizationPipe],
  exports: [LocalizationPipe, LazyLocalizationPipe]
});
_LocalizationModule.ɵinj = ɵɵdefineInjector({});
var LocalizationModule = _LocalizationModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalizationModule, [{
    type: NgModule,
    args: [{
      imports: [LazyLocalizationPipe],
      exports: [LocalizationPipe, LazyLocalizationPipe],
      declarations: [LocalizationPipe]
    }]
  }], null, null);
})();
Date.prototype.toLocalISOString = function() {
  const timezoneOffset = this.getTimezoneOffset();
  return new Date(this.getTime() - timezoneOffset * 6e4).toISOString();
};
function setLanguageToCookie() {
  const injector = inject(Injector);
  const sessionState = injector.get(SessionStateService);
  const document2 = injector.get(DOCUMENT);
  const cookieLanguageKey = injector.get(COOKIE_LANGUAGE_KEY);
  sessionState.getLanguage$().subscribe((language) => {
    const cookieValue = encodeURIComponent(`c=${language}|uic=${language}`);
    document2.cookie = `${cookieLanguageKey}=${cookieValue}`;
  });
}
var CookieLanguageProvider = provideAppInitializer(() => {
  setLanguageToCookie();
});
var LocaleId = class extends String {
  constructor(localizationService) {
    super();
    this.localizationService = localizationService;
  }
  toString() {
    const {
      currentLang
    } = this.localizationService;
    if (checkHasProp(differentLocales, currentLang)) {
      return differentLocales[currentLang];
    }
    return currentLang;
  }
  valueOf() {
    return this.toString();
  }
};
var LocaleProvider = {
  provide: LOCALE_ID,
  useClass: LocaleId,
  deps: [LocalizationService]
};
var IncludeLocalizationResourcesProvider = {
  provide: INCUDE_LOCALIZATION_RESOURCES_TOKEN,
  useValue: false
};
var _RoutesHandler = class _RoutesHandler {
  constructor(routes, router) {
    this.routes = routes;
    this.router = router;
    this.addRoutes();
  }
  addRoutes() {
    this.router?.config?.forEach(({
      path = "",
      data
    }) => {
      const routes = data?.routes;
      if (!routes) return;
      if (Array.isArray(routes)) {
        this.routes.add(routes);
      } else {
        const routesFlatten = flatRoutes([__spreadValues({
          path
        }, routes)], {
          path: ""
        });
        this.routes.add(routesFlatten);
      }
    });
  }
};
_RoutesHandler.ɵfac = function RoutesHandler_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _RoutesHandler)(ɵɵinject(RoutesService), ɵɵinject(Router, 8));
};
_RoutesHandler.ɵprov = ɵɵdefineInjectable({
  token: _RoutesHandler,
  factory: _RoutesHandler.ɵfac,
  providedIn: "root"
});
var RoutesHandler = _RoutesHandler;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoutesHandler, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RoutesService
  }, {
    type: Router,
    decorators: [{
      type: Optional
    }]
  }], null);
})();
function flatRoutes(routes, parent) {
  if (!routes) return [];
  return routes.reduce((acc, route) => {
    const _a = __spreadProps(__spreadValues({}, route), {
      parentName: parent.name,
      path: (parent.path + "/" + route.path).replace(/\/\//g, "/")
    }), {
      children
    } = _a, current = __objRest(_a, [
      "children"
    ]);
    acc.push(current, ...flatRoutes(children, current));
    return acc;
  }, []);
}
var _ApiInterceptor = class _ApiInterceptor {
  constructor(httpWaitService) {
    this.httpWaitService = httpWaitService;
  }
  getAdditionalHeaders(existingHeaders) {
    return existingHeaders || new HttpHeaders();
  }
  intercept(request, next) {
    this.httpWaitService.addRequest(request);
    return next.handle(request).pipe(finalize(() => this.httpWaitService.deleteRequest(request)));
  }
};
_ApiInterceptor.ɵfac = function ApiInterceptor_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ApiInterceptor)(ɵɵinject(HttpWaitService));
};
_ApiInterceptor.ɵprov = ɵɵdefineInjectable({
  token: _ApiInterceptor,
  factory: _ApiInterceptor.ɵfac,
  providedIn: "root"
});
var ApiInterceptor = _ApiInterceptor;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApiInterceptor, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: HttpWaitService
  }], null);
})();
var _TimezoneInterceptor = class _TimezoneInterceptor {
  constructor() {
    this.timezoneService = inject(TimezoneService);
  }
  intercept(req, next) {
    if (!this.timezoneService.isUtcClockEnabled) {
      return next.handle(req);
    }
    const timezone = this.timezoneService.timezone;
    if (timezone) {
      req = req.clone({
        setHeaders: {
          __timezone: timezone
        }
      });
    }
    return next.handle(req);
  }
};
_TimezoneInterceptor.ɵfac = function TimezoneInterceptor_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _TimezoneInterceptor)();
};
_TimezoneInterceptor.ɵprov = ɵɵdefineInjectable({
  token: _TimezoneInterceptor,
  factory: _TimezoneInterceptor.ɵfac,
  providedIn: "root"
});
var TimezoneInterceptor = _TimezoneInterceptor;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TimezoneInterceptor, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var CoreFeatureKind;
(function(CoreFeatureKind2) {
  CoreFeatureKind2[CoreFeatureKind2["Options"] = 0] = "Options";
  CoreFeatureKind2[CoreFeatureKind2["CompareFunctionFactory"] = 1] = "CompareFunctionFactory";
  CoreFeatureKind2[CoreFeatureKind2["TitleStrategy"] = 2] = "TitleStrategy";
})(CoreFeatureKind || (CoreFeatureKind = {}));
function makeCoreFeature(kind, providers) {
  return {
    ɵkind: kind,
    ɵproviders: providers
  };
}
function withOptions(options = {}) {
  return makeCoreFeature(CoreFeatureKind.Options, [{
    provide: "CORE_OPTIONS",
    useValue: options
  }, {
    provide: CORE_OPTIONS,
    useFactory: coreOptionsFactory,
    deps: ["CORE_OPTIONS"]
  }, {
    provide: TENANT_KEY,
    useValue: options.tenantKey || "__tenant"
  }, {
    provide: LOCALIZATIONS,
    multi: true,
    useValue: localizationContributor(options.localizations),
    deps: [LocalizationService]
  }, {
    provide: OTHERS_GROUP,
    useValue: options.othersGroup || "AbpUi::OthersGroup"
  }, {
    provide: DYNAMIC_LAYOUTS_TOKEN,
    useValue: options.dynamicLayouts || DEFAULT_DYNAMIC_LAYOUTS
  }]);
}
function withTitleStrategy(strategy) {
  return makeCoreFeature(CoreFeatureKind.TitleStrategy, [{
    provide: TitleStrategy,
    useExisting: strategy
  }]);
}
function withCompareFuncFactory(factory) {
  return makeCoreFeature(CoreFeatureKind.CompareFunctionFactory, [{
    provide: SORT_COMPARE_FUNC,
    useFactory: factory
  }]);
}
function provideAbpCore(...features) {
  const providers = [provideHttpClient(withInterceptorsFromDi(), withXsrfConfiguration({
    cookieName: "XSRF-TOKEN",
    headerName: "RequestVerificationToken"
  })), provideAppInitializer(() => {
    getInitialData();
    localeInitializer();
    inject(LocalizationService);
    inject(LocalStorageListenerService);
    inject(RoutesHandler);
  }), LocaleProvider, CookieLanguageProvider, {
    provide: SORT_COMPARE_FUNC,
    useFactory: compareFuncFactory
  }, {
    provide: QUEUE_MANAGER,
    useClass: DefaultQueueManager
  }, AuthErrorFilterService, IncludeLocalizationResourcesProvider, {
    provide: TitleStrategy,
    useExisting: AbpTitleStrategy
  }, {
    provide: HTTP_INTERCEPTORS,
    useClass: TimezoneInterceptor,
    multi: true
  }];
  for (const feature of features) {
    providers.push(...feature.ɵproviders);
  }
  return makeEnvironmentProviders(providers);
}
function provideAbpCoreChild(options = {}) {
  return makeEnvironmentProviders([{
    provide: LOCALIZATIONS,
    multi: true,
    useValue: localizationContributor(options.localizations),
    deps: [LocalizationService]
  }]);
}
var standaloneDirectives = [AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective];
var _BaseCoreModule = class _BaseCoreModule {
};
_BaseCoreModule.ɵfac = function BaseCoreModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _BaseCoreModule)();
};
_BaseCoreModule.ɵmod = ɵɵdefineNgModule({
  type: _BaseCoreModule,
  declarations: [AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, UtcToLocalPipe, AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective],
  exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe, AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective]
});
_BaseCoreModule.ɵinj = ɵɵdefineInjector({
  providers: [LocalizationPipe, provideHttpClient(withInterceptorsFromDi())],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule]
});
var BaseCoreModule = _BaseCoreModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BaseCoreModule, [{
    type: NgModule,
    args: [{
      exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe, ...standaloneDirectives],
      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, UtcToLocalPipe, ...standaloneDirectives],
      declarations: [AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe],
      providers: [LocalizationPipe, provideHttpClient(withInterceptorsFromDi())]
    }]
  }], null, null);
})();
var _RootCoreModule = class _RootCoreModule {
};
_RootCoreModule.ɵfac = function RootCoreModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _RootCoreModule)();
};
_RootCoreModule.ɵmod = ɵɵdefineNgModule({
  type: _RootCoreModule,
  imports: [BaseCoreModule, LocalizationModule],
  exports: [BaseCoreModule, LocalizationModule]
});
_RootCoreModule.ɵinj = ɵɵdefineInjector({
  providers: [provideHttpClient(withXsrfConfiguration({
    cookieName: "XSRF-TOKEN",
    headerName: "RequestVerificationToken"
  }))],
  imports: [BaseCoreModule, LocalizationModule, BaseCoreModule, LocalizationModule]
});
var RootCoreModule = _RootCoreModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RootCoreModule, [{
    type: NgModule,
    args: [{
      exports: [BaseCoreModule, LocalizationModule],
      imports: [BaseCoreModule, LocalizationModule],
      providers: [provideHttpClient(withXsrfConfiguration({
        cookieName: "XSRF-TOKEN",
        headerName: "RequestVerificationToken"
      }))]
    }]
  }], null, null);
})();
var _CoreModule = class _CoreModule {
  /**
   * @deprecated forRoot method is deprecated, use `provideAbpCore` *function* for config settings.
   */
  static forRoot(options = {}) {
    return {
      ngModule: RootCoreModule,
      providers: [provideAbpCore(withOptions(options))]
    };
  }
  /**
   * @deprecated forChild method is deprecated, use `provideAbpCoreChild` *function* for config settings.
   */
  static forChild(options = {}) {
    return {
      ngModule: RootCoreModule,
      providers: [provideAbpCoreChild(options)]
    };
  }
};
_CoreModule.ɵfac = function CoreModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _CoreModule)();
};
_CoreModule.ɵmod = ɵɵdefineNgModule({
  type: _CoreModule,
  imports: [BaseCoreModule],
  exports: [BaseCoreModule]
});
_CoreModule.ɵinj = ɵɵdefineInjector({
  imports: [BaseCoreModule, BaseCoreModule]
});
var CoreModule = _CoreModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CoreModule, [{
    type: NgModule,
    args: [{
      exports: [BaseCoreModule],
      imports: [BaseCoreModule]
    }]
  }], null, null);
})();
var _ShowPasswordDirective = class _ShowPasswordDirective {
  constructor() {
    this.elementRef = inject(ElementRef);
  }
  set abpShowPassword(visible) {
    const element = this.elementRef.nativeElement;
    if (!element) return;
    element.type = visible ? "text" : "password";
  }
};
_ShowPasswordDirective.ɵfac = function ShowPasswordDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _ShowPasswordDirective)();
};
_ShowPasswordDirective.ɵdir = ɵɵdefineDirective({
  type: _ShowPasswordDirective,
  selectors: [["", "abpShowPassword", ""]],
  inputs: {
    abpShowPassword: "abpShowPassword"
  }
});
var ShowPasswordDirective = _ShowPasswordDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShowPasswordDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpShowPassword]"
    }]
  }], null, {
    abpShowPassword: [{
      type: Input
    }]
  });
})();
var _TrackCapsLockDirective = class _TrackCapsLockDirective {
  constructor() {
    this.capsLock = new EventEmitter();
  }
  onKeyDown(event) {
    this.capsLock.emit(this.isCapsLockOpen(event));
  }
  onKeyUp(event) {
    this.capsLock.emit(this.isCapsLockOpen(event));
  }
  isCapsLockOpen(e) {
    const s2 = String.fromCharCode(e.which);
    if (s2.toUpperCase() === s2 && s2.toLowerCase() !== s2 && e.shiftKey || s2.toUpperCase() !== s2 && s2.toLowerCase() === s2 && e.shiftKey || e.getModifierState && e.getModifierState("CapsLock")) {
      return true;
    }
    return false;
  }
};
_TrackCapsLockDirective.ɵfac = function TrackCapsLockDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _TrackCapsLockDirective)();
};
_TrackCapsLockDirective.ɵdir = ɵɵdefineDirective({
  type: _TrackCapsLockDirective,
  selectors: [["", "abpCapsLock", ""]],
  hostBindings: function TrackCapsLockDirective_HostBindings(rf, ctx) {
    if (rf & 1) {
      ɵɵlistener("keydown", function TrackCapsLockDirective_keydown_HostBindingHandler($event) {
        return ctx.onKeyDown($event);
      }, ɵɵresolveWindow)("keyup", function TrackCapsLockDirective_keyup_HostBindingHandler($event) {
        return ctx.onKeyUp($event);
      }, ɵɵresolveWindow);
    }
  },
  outputs: {
    capsLock: "abpCapsLock"
  }
});
var TrackCapsLockDirective = _TrackCapsLockDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TrackCapsLockDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpCapsLock]"
    }]
  }], null, {
    capsLock: [{
      type: Output,
      args: ["abpCapsLock"]
    }],
    onKeyDown: [{
      type: HostListener,
      args: ["window:keydown", ["$event"]]
    }],
    onKeyUp: [{
      type: HostListener,
      args: ["window:keyup", ["$event"]]
    }]
  });
})();
var _PermissionGuard = class _PermissionGuard {
  constructor() {
    this.router = inject(Router);
    this.routesService = inject(RoutesService);
    this.authService = inject(AuthService);
    this.permissionService = inject(PermissionService);
    this.httpErrorReporter = inject(HttpErrorReporterService);
  }
  canActivate(route, state) {
    let {
      requiredPolicy
    } = route.data || {};
    if (!requiredPolicy) {
      const routeFound = findRoute(this.routesService, getRoutePath(this.router, state.url));
      requiredPolicy = routeFound?.requiredPolicy;
    }
    if (!requiredPolicy) {
      return of(true);
    }
    return this.permissionService.getGrantedPolicy$(requiredPolicy).pipe(filter(Boolean), take(1), tap((access) => {
      if (!access && this.authService.isAuthenticated) {
        this.httpErrorReporter.reportError({
          status: 403
        });
      }
    }));
  }
};
_PermissionGuard.ɵfac = function PermissionGuard_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _PermissionGuard)();
};
_PermissionGuard.ɵprov = ɵɵdefineInjectable({
  token: _PermissionGuard,
  factory: _PermissionGuard.ɵfac,
  providedIn: "root"
});
var PermissionGuard = _PermissionGuard;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var permissionGuard = (route, state) => {
  const router = inject(Router);
  const routesService = inject(RoutesService);
  const authService = inject(AuthService);
  const permissionService = inject(PermissionService);
  const httpErrorReporter = inject(HttpErrorReporterService);
  let {
    requiredPolicy
  } = route.data || {};
  if (!requiredPolicy) {
    const routeFound = findRoute(routesService, getRoutePath(router, state.url));
    requiredPolicy = routeFound?.requiredPolicy;
  }
  if (!requiredPolicy) {
    return of(true);
  }
  return permissionService.getGrantedPolicy$(requiredPolicy).pipe(filter(Boolean), take(1), tap((access) => {
    if (!access && authService.isAuthenticated) {
      httpErrorReporter.reportError({
        status: 403
      });
    }
  }));
};
var ListResultDto = class {
  constructor(initialValues = {}) {
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key)) {
        this[key] = initialValues[key];
      }
    }
  }
};
var PagedResultDto = class extends ListResultDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleObject = class {
  constructor(initialValues = {}) {
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key) && initialValues[key] !== void 0) {
        this[key] = initialValues[key];
      }
    }
  }
};
var ExtensibleEntityDto = class extends ExtensibleObject {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var LimitedResultRequestDto = class {
  constructor(initialValues = {}) {
    this.maxResultCount = 10;
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key) && initialValues[key] !== void 0) {
        this[key] = initialValues[key];
      }
    }
  }
};
var ExtensibleLimitedResultRequestDto = class extends ExtensibleEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
    this.maxResultCount = 10;
  }
};
var PagedResultRequestDto = class extends LimitedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensiblePagedResultRequestDto = class extends ExtensibleLimitedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var PagedAndSortedResultRequestDto = class extends PagedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensiblePagedAndSortedResultRequestDto = class extends ExtensiblePagedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var EntityDto = class {
  constructor(initialValues = {}) {
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key)) {
        this[key] = initialValues[key];
      }
    }
  }
};
var CreationAuditedEntityDto = class extends EntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var CreationAuditedEntityWithUserDto = class extends CreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var AuditedEntityDto = class extends CreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var AuditedEntityWithUserDto = class extends AuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var FullAuditedEntityDto = class extends AuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var FullAuditedEntityWithUserDto = class extends FullAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleCreationAuditedEntityDto = class extends ExtensibleEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleAuditedEntityDto = class extends ExtensibleCreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleAuditedEntityWithUserDto = class extends ExtensibleAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleCreationAuditedEntityWithUserDto = class extends ExtensibleCreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleFullAuditedEntityDto = class extends ExtensibleAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleFullAuditedEntityWithUserDto = class extends ExtensibleFullAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var AuthEvent = class {
  constructor(type) {
    this.type = type;
    this.type = type;
  }
};
var AuthSuccessEvent = class extends AuthEvent {
  constructor(type, info) {
    super(type);
    this.type = type;
    this.info = info;
  }
};
var AuthInfoEvent = class extends AuthEvent {
  constructor(type, info) {
    super(type);
    this.type = type;
    this.info = info;
  }
};
var AuthErrorEvent = class extends AuthEvent {
  constructor(type, reason, params) {
    super(type);
    this.type = type;
    this.reason = reason;
    this.params = params;
  }
};
var _AbpApiDefinitionService = class _AbpApiDefinitionService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.getByModel = (model, config) => this.restService.request({
      method: "GET",
      url: "/api/abp/api-definition",
      params: {
        includeTypes: model.includeTypes
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
};
_AbpApiDefinitionService.ɵfac = function AbpApiDefinitionService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _AbpApiDefinitionService)(ɵɵinject(RestService));
};
_AbpApiDefinitionService.ɵprov = ɵɵdefineInjectable({
  token: _AbpApiDefinitionService,
  factory: _AbpApiDefinitionService.ɵfac,
  providedIn: "root"
});
var AbpApiDefinitionService = _AbpApiDefinitionService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpApiDefinitionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var index = Object.freeze({
  __proto__: null
});
var ContainerStrategy = class {
  constructor(containerRef) {
    this.containerRef = containerRef;
  }
  prepare() {
  }
};
var ClearContainerStrategy = class extends ContainerStrategy {
  getIndex() {
    return 0;
  }
  prepare() {
    this.containerRef.clear();
  }
};
var InsertIntoContainerStrategy = class extends ContainerStrategy {
  constructor(containerRef, index2) {
    super(containerRef);
    this.index = index2;
  }
  getIndex() {
    return Math.min(Math.max(0, this.index), this.containerRef.length);
  }
};
var CONTAINER_STRATEGY = {
  Clear(containerRef) {
    return new ClearContainerStrategy(containerRef);
  },
  Append(containerRef) {
    return new InsertIntoContainerStrategy(containerRef, containerRef.length);
  },
  Prepend(containerRef) {
    return new InsertIntoContainerStrategy(containerRef, 0);
  },
  Insert(containerRef, index2) {
    return new InsertIntoContainerStrategy(containerRef, index2);
  }
};
var ContentSecurityStrategy = class {
  constructor(nonce) {
    this.nonce = nonce;
  }
};
var LooseContentSecurityStrategy = class extends ContentSecurityStrategy {
  constructor(nonce) {
    super(nonce);
  }
  applyCSP(element) {
    if (this.nonce) {
      element.setAttribute("nonce", this.nonce);
    }
  }
};
var NoContentSecurityStrategy = class extends ContentSecurityStrategy {
  constructor() {
    super();
  }
  applyCSP(_) {
  }
};
var CONTENT_SECURITY_STRATEGY = {
  Loose(nonce) {
    return new LooseContentSecurityStrategy(nonce);
  },
  None() {
    return new NoContentSecurityStrategy();
  }
};
var ContentStrategy = class {
  constructor(content, domStrategy = DOM_STRATEGY.AppendToHead(), contentSecurityStrategy = CONTENT_SECURITY_STRATEGY.None(), options = {}) {
    this.content = content;
    this.domStrategy = domStrategy;
    this.contentSecurityStrategy = contentSecurityStrategy;
    this.options = options;
  }
  insertElement() {
    const element = this.createElement();
    if (this.options && Object.keys(this.options).length > 0) {
      Object.keys(this.options).forEach((key) => {
        if (this.options[key]) {
          element[key] = this.options[key];
        }
      });
    }
    this.contentSecurityStrategy.applyCSP(element);
    this.domStrategy.insertElement(element);
    return element;
  }
};
var StyleContentStrategy = class extends ContentStrategy {
  createElement() {
    const element = document.createElement("style");
    element.textContent = this.content;
    return element;
  }
};
var ScriptContentStrategy = class extends ContentStrategy {
  createElement() {
    const element = document.createElement("script");
    element.textContent = this.content;
    return element;
  }
};
var CONTENT_STRATEGY = {
  AppendScriptToBody(content, options) {
    return new ScriptContentStrategy(content, DOM_STRATEGY.AppendToBody(), void 0, options);
  },
  AppendScriptToHead(content, options) {
    return new ScriptContentStrategy(content, DOM_STRATEGY.AppendToHead(), void 0, options);
  },
  AppendStyleToHead(content, options) {
    return new StyleContentStrategy(content, DOM_STRATEGY.AppendToHead(), void 0, options);
  },
  PrependStyleToHead(content, options) {
    return new StyleContentStrategy(content, DOM_STRATEGY.PrependToHead(), void 0, options);
  }
};
var ContextStrategy = class {
  constructor(context) {
    this.context = context;
  }
  setContext(componentRef) {
    return this.context;
  }
};
var NoContextStrategy = class extends ContextStrategy {
  constructor() {
    super(void 0);
  }
};
var ComponentContextStrategy = class extends ContextStrategy {
  setContext(componentRef) {
    Object.keys(this.context).forEach((key) => componentRef.instance[key] = this.context[key]);
    componentRef.changeDetectorRef.detectChanges();
    return this.context;
  }
};
var TemplateContextStrategy = class extends ContextStrategy {
  setContext() {
    return this.context;
  }
};
var CONTEXT_STRATEGY = {
  None() {
    return new NoContextStrategy();
  },
  Component(context) {
    return new ComponentContextStrategy(context);
  },
  Template(context) {
    return new TemplateContextStrategy(context);
  }
};
var LoadingStrategy = class {
  constructor(path, domStrategy = DOM_STRATEGY.AppendToHead(), crossOriginStrategy = CROSS_ORIGIN_STRATEGY.Anonymous()) {
    this.path = path;
    this.domStrategy = domStrategy;
    this.crossOriginStrategy = crossOriginStrategy;
  }
  createStream() {
    this.element = this.createElement();
    return of(null).pipe(switchMap(() => fromLazyLoad(this.element, this.domStrategy, this.crossOriginStrategy)));
  }
};
var ScriptLoadingStrategy = class extends LoadingStrategy {
  constructor(src, domStrategy, crossOriginStrategy) {
    super(src, domStrategy, crossOriginStrategy);
  }
  createElement() {
    const element = document.createElement("script");
    element.src = this.path;
    return element;
  }
};
var StyleLoadingStrategy = class extends LoadingStrategy {
  constructor(href, domStrategy, crossOriginStrategy) {
    super(href, domStrategy, crossOriginStrategy);
  }
  createElement() {
    const element = document.createElement("link");
    element.rel = "stylesheet";
    element.href = this.path;
    return element;
  }
};
var LOADING_STRATEGY = {
  AppendScriptToBody(src) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToBody(), CROSS_ORIGIN_STRATEGY.None());
  },
  AppendAnonymousScriptToBody(src, integrity) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToBody(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  AppendAnonymousScriptToHead(src, integrity) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  AppendAnonymousStyleToHead(src, integrity) {
    return new StyleLoadingStrategy(src, DOM_STRATEGY.AppendToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  PrependAnonymousScriptToHead(src, integrity) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.PrependToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  PrependAnonymousStyleToHead(src, integrity) {
    return new StyleLoadingStrategy(src, DOM_STRATEGY.PrependToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  }
};
var ProjectionStrategy = class {
  constructor(content) {
    this.content = content;
  }
};
var ComponentProjectionStrategy = class extends ProjectionStrategy {
  constructor(component, containerStrategy, contextStrategy = CONTEXT_STRATEGY.None()) {
    super(component);
    this.containerStrategy = containerStrategy;
    this.contextStrategy = contextStrategy;
  }
  injectContent(injector) {
    this.containerStrategy.prepare();
    const resolver = injector.get(ComponentFactoryResolver$1);
    const factory = resolver.resolveComponentFactory(this.content);
    const componentRef = this.containerStrategy.containerRef.createComponent(factory, this.containerStrategy.getIndex(), injector);
    this.contextStrategy.setContext(componentRef);
    return componentRef;
  }
};
var RootComponentProjectionStrategy = class extends ProjectionStrategy {
  constructor(component, contextStrategy = CONTEXT_STRATEGY.None(), domStrategy = DOM_STRATEGY.AppendToBody()) {
    super(component);
    this.contextStrategy = contextStrategy;
    this.domStrategy = domStrategy;
  }
  injectContent(injector) {
    const appRef = injector.get(ApplicationRef);
    const resolver = injector.get(ComponentFactoryResolver$1);
    const componentRef = resolver.resolveComponentFactory(this.content).create(injector);
    this.contextStrategy.setContext(componentRef);
    appRef.attachView(componentRef.hostView);
    const element = componentRef.hostView.rootNodes[0];
    this.domStrategy.insertElement(element);
    return componentRef;
  }
};
var TemplateProjectionStrategy = class extends ProjectionStrategy {
  constructor(templateRef, containerStrategy, contextStrategy = CONTEXT_STRATEGY.None()) {
    super(templateRef);
    this.containerStrategy = containerStrategy;
    this.contextStrategy = contextStrategy;
  }
  injectContent() {
    this.containerStrategy.prepare();
    const embeddedViewRef = this.containerStrategy.containerRef.createEmbeddedView(this.content, this.contextStrategy.context, this.containerStrategy.getIndex());
    embeddedViewRef.detectChanges();
    return embeddedViewRef;
  }
};
var PROJECTION_STRATEGY = {
  AppendComponentToBody(component, context) {
    return new RootComponentProjectionStrategy(component, context && CONTEXT_STRATEGY.Component(context));
  },
  AppendComponentToContainer(component, containerRef, context) {
    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Append(containerRef), context && CONTEXT_STRATEGY.Component(context));
  },
  AppendTemplateToContainer(templateRef, containerRef, context) {
    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Append(containerRef), context && CONTEXT_STRATEGY.Template(context));
  },
  PrependComponentToContainer(component, containerRef, context) {
    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Prepend(containerRef), context && CONTEXT_STRATEGY.Component(context));
  },
  PrependTemplateToContainer(templateRef, containerRef, context) {
    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Prepend(containerRef), context && CONTEXT_STRATEGY.Template(context));
  },
  ProjectComponentToContainer(component, containerRef, context) {
    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Clear(containerRef), context && CONTEXT_STRATEGY.Component(context));
  },
  ProjectTemplateToContainer(templateRef, containerRef, context) {
    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Clear(containerRef), context && CONTEXT_STRATEGY.Template(context));
  }
};
function validateMinAge({
  age = 18
} = {}) {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    return isValidMinAge(control.value, age) ? null : {
      minAge: {
        age
      }
    };
  };
}
function isValidMinAge(value, minAge) {
  const date = /* @__PURE__ */ new Date();
  date.setFullYear(date.getFullYear() - minAge);
  date.setHours(23, 59, 59, 999);
  return Number(new Date(value)) <= date.valueOf();
}
function validateCreditCard() {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    return isValidCreditCard(String(control.value)) ? null : {
      creditCard: true
    };
  };
}
function isValidCreditCard(value) {
  value = value.replace(/[ -]/g, "");
  if (!/^[0-9]{13,19}$/.test(value)) return false;
  let checksum = 0;
  let multiplier = 1;
  for (let i = value.length; i > 0; i--) {
    const digit = Number(value[i - 1]) * multiplier;
    checksum += digit % 10 + ~~(digit / 10);
    multiplier = multiplier * 2 % 3;
  }
  return checksum % 10 === 0;
}
function validateRange({
  maximum = Infinity,
  minimum = 0
} = {}) {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    const value = Number(control.value);
    return getMinError(value, minimum, maximum) || getMaxError(value, maximum, minimum);
  };
}
function getMaxError(value, max, min) {
  return value > max ? {
    range: {
      max,
      min
    }
  } : null;
}
function getMinError(value, min, max) {
  return value < min ? {
    range: {
      min,
      max
    }
  } : null;
}
function validateRequired({
  allowEmptyStrings
} = {}) {
  const required = (control) => {
    return isValidRequired(control.value, allowEmptyStrings) ? null : {
      required: true
    };
  };
  return required;
}
function isValidRequired(value, allowEmptyStrings) {
  if (value || value === 0 || value === false) return true;
  if (allowEmptyStrings && value === "") return true;
  return false;
}
function validateStringLength({
  maximumLength = Infinity,
  minimumLength = 0
} = {}) {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    const value = String(control.value);
    return getMinLengthError(value, minimumLength) || getMaxLengthError(value, maximumLength);
  };
}
function getMaxLengthError(value, requiredLength) {
  return value.length > requiredLength ? {
    maxlength: {
      requiredLength
    }
  } : null;
}
function getMinLengthError(value, requiredLength) {
  return value.length < requiredLength ? {
    minlength: {
      requiredLength
    }
  } : null;
}
function validateUniqueCharacter() {
  return (control) => {
    if (isNullOrEmpty(control.value)) return null;
    return isUnqiueCharacter(control.value) ? null : {
      uniqueCharacter: true
    };
  };
}
function isUnqiueCharacter(value) {
  const set = new Set(value.split(""));
  return set.size == value.length;
}
function validateUrl() {
  return (control) => {
    if (isNullOrEmpty(control.value)) return null;
    return isValidUrl(control.value) ? null : {
      url: true
    };
  };
}
function isValidUrl(value) {
  if (/^http(s)?:\/\/[^/]/.test(value) || /^ftp:\/\/[^/]/.test(value)) {
    const a = document.createElement("a");
    a.href = value;
    return !!a.host;
  }
  return false;
}
var onlyLetterAndNumberRegex = /^[a-zA-Z0-9]+$/;
function validateUsername({
  pattern = /.*/
} = {
  pattern: onlyLetterAndNumberRegex
}) {
  return (control) => {
    const isValid = isValidUserName(control.value, pattern);
    return isValid ? null : {
      usernamePattern: {
        actualValue: control.value
      }
    };
  };
}
function isValidUserName(value, pattern) {
  if (isNullOrEmpty(value)) return true;
  return pattern.test(value);
}
var AbpValidators = {
  creditCard: validateCreditCard,
  emailAddress: () => Validators.email,
  minAge: validateMinAge,
  range: validateRange,
  required: validateRequired,
  stringLength: validateStringLength,
  url: validateUrl,
  username: validateUsername,
  uniqueCharacter: validateUniqueCharacter
};

export {
  collectionCompare,
  AbstractNgModelComponent,
  AuthGuard,
  authGuard,
  AuthService,
  AbstractAuthErrorFilter,
  AuthErrorFilterService,
  LOCALIZATIONS,
  localizationContributor,
  localizations$,
  CORE_OPTIONS,
  coreOptionsFactory,
  getLocaleDirection,
  createLocalizer,
  createLocalizerWithFallback,
  createLocalizationPipeKeyGenerator,
  createTokenParser,
  interpolate,
  escapeHtmlChars,
  ContentProjectionService,
  pushValueTo,
  noop,
  isUndefinedOrEmptyString,
  isNullOrUndefined,
  isNullOrEmpty,
  exists,
  isObject,
  isArray,
  isObjectAndNotArray,
  isNode,
  isObjectAndNotArrayNotNode,
  checkHasProp,
  getShortDateFormat,
  getShortTimeFormat,
  getShortDateShortTimeFormat,
  deepMerge,
  InternalStore,
  EnvironmentService,
  HttpErrorReporterService,
  getRemoteEnv,
  LazyModuleFactory,
  featuresFactory,
  downloadBlob,
  isNumber2 as isNumber,
  mapEnumToOptions,
  uuid,
  generateHash,
  generatePassword,
  getPathName,
  WebHttpUrlEncodingCodec,
  AbpLocalStorageService,
  SessionStateService,
  APP_INIT_ERROR_HANDLERS,
  AbpTenantService,
  TENANT_KEY,
  IS_EXTERNAL_REQUEST,
  ExternalHttpClient,
  RestService,
  MultiTenancyService,
  TENANT_NOT_FOUND_BY_NAME,
  parseTenantFromUrl,
  CHECK_AUTHENTICATION_STATE_FN_KEY,
  getInitialData,
  localeInitializer,
  CrossOriginStrategy,
  NoCrossOriginStrategy,
  CROSS_ORIGIN_STRATEGY,
  DomStrategy,
  DOM_STRATEGY,
  fromLazyLoad,
  DefaultQueueManager,
  findRoute,
  getRoutePath,
  reloadRoute,
  BaseTreeNode,
  createTreeFromList,
  createMapFromList,
  createTreeNodeFilterCreator,
  createGroupMap,
  DomInsertionService,
  LOADER_DELAY,
  HttpWaitService,
  ResourceWaitService,
  LazyLoadService,
  LIST_QUERY_DEBOUNCE_TIME,
  ListService,
  PermissionService,
  ReplaceableComponentsService,
  NavigationEvent,
  RouterEvents,
  RouterWaitService,
  COOKIE_LANGUAGE_KEY,
  NAVIGATE_TO_MANAGE_PROFILE,
  QUEUE_MANAGER,
  INCUDE_LOCALIZATION_RESOURCES_TOKEN,
  PIPE_TO_LOGIN_FN_KEY,
  SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY,
  OTHERS_GROUP,
  SORT_COMPARE_FUNC,
  compareFuncFactory,
  DYNAMIC_LAYOUTS_TOKEN,
  DISABLE_PROJECT_NAME,
  AbstractTreeService,
  AbstractNavTreeService,
  RoutesService,
  SubscriptionService,
  trackBy,
  trackByDeep,
  TrackByService,
  AbpWindowService,
  InternetConnectionService,
  LocalStorageListenerService,
  AbpTitleStrategy,
  TimezoneService,
  TimeService,
  AbpApplicationConfigurationService,
  AbpApplicationLocalizationService,
  ConfigStateService,
  LocalizationService,
  DynamicLayoutComponent,
  ReplaceableRouteContainerComponent,
  RouterOutletComponent,
  differentLocales,
  DEFAULT_DYNAMIC_LAYOUTS,
  AutofocusDirective,
  InputEventDebounceDirective,
  ForDirective,
  FormSubmitDirective,
  InitDirective,
  PermissionDirective,
  ReplaceableTemplateDirective,
  StopPropagationDirective,
  LocalizationPipe,
  SafeHtmlPipe,
  SortPipe,
  INJECTOR_PIPE_DATA_TOKEN,
  ToInjectorPipe,
  ShortDatePipe,
  ShortTimePipe,
  ShortDateTimePipe,
  UtcToLocalPipe,
  LazyLocalizationPipe,
  LocalizationModule,
  setLanguageToCookie,
  CookieLanguageProvider,
  LocaleId,
  LocaleProvider,
  IncludeLocalizationResourcesProvider,
  ApiInterceptor,
  TimezoneInterceptor,
  CoreFeatureKind,
  withOptions,
  withTitleStrategy,
  withCompareFuncFactory,
  provideAbpCore,
  provideAbpCoreChild,
  BaseCoreModule,
  RootCoreModule,
  CoreModule,
  ShowPasswordDirective,
  TrackCapsLockDirective,
  PermissionGuard,
  permissionGuard,
  ListResultDto,
  PagedResultDto,
  ExtensibleObject,
  ExtensibleEntityDto,
  LimitedResultRequestDto,
  ExtensibleLimitedResultRequestDto,
  PagedResultRequestDto,
  ExtensiblePagedResultRequestDto,
  PagedAndSortedResultRequestDto,
  ExtensiblePagedAndSortedResultRequestDto,
  EntityDto,
  CreationAuditedEntityDto,
  CreationAuditedEntityWithUserDto,
  AuditedEntityDto,
  AuditedEntityWithUserDto,
  FullAuditedEntityDto,
  FullAuditedEntityWithUserDto,
  ExtensibleCreationAuditedEntityDto,
  ExtensibleAuditedEntityDto,
  ExtensibleAuditedEntityWithUserDto,
  ExtensibleCreationAuditedEntityWithUserDto,
  ExtensibleFullAuditedEntityDto,
  ExtensibleFullAuditedEntityWithUserDto,
  AuthEvent,
  AuthSuccessEvent,
  AuthInfoEvent,
  AuthErrorEvent,
  AbpApiDefinitionService,
  index,
  ContainerStrategy,
  ClearContainerStrategy,
  InsertIntoContainerStrategy,
  CONTAINER_STRATEGY,
  ContentSecurityStrategy,
  LooseContentSecurityStrategy,
  NoContentSecurityStrategy,
  CONTENT_SECURITY_STRATEGY,
  ContentStrategy,
  StyleContentStrategy,
  ScriptContentStrategy,
  CONTENT_STRATEGY,
  ContextStrategy,
  NoContextStrategy,
  ComponentContextStrategy,
  TemplateContextStrategy,
  CONTEXT_STRATEGY,
  LoadingStrategy,
  ScriptLoadingStrategy,
  StyleLoadingStrategy,
  LOADING_STRATEGY,
  ProjectionStrategy,
  ComponentProjectionStrategy,
  RootComponentProjectionStrategy,
  TemplateProjectionStrategy,
  PROJECTION_STRATEGY,
  validateMinAge,
  validateCreditCard,
  validateRange,
  validateRequired,
  validateStringLength,
  validateUniqueCharacter,
  validateUrl,
  AbpValidators
};
//# sourceMappingURL=chunk-MYM5KR2K.js.map
